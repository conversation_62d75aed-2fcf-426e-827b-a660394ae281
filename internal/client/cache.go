package client

import (
	"bytes"
	"errors"
)

type LatestKey struct {
	DeviceType string
	Channel    string
}

func (l LatestKey) MarshalBinary() ([]byte, error) {
	var sb bytes.Buffer
	sb.WriteString(l.DeviceType)
	sb.WriteByte('_')
	sb.WriteString(l.Channel)
	return sb.Bytes(), nil
}

func (l *LatestKey) UnmarshalBinary(data []byte) error {
	parts := bytes.Split(data, []byte{'_'})
	if len(parts) != 2 {
		return errors.New("invalid data")
	}

	l.DeviceType = string(parts[0])
	l.Channel = string(parts[1])
	return nil
}
