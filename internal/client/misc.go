package client

import (
	"fmt"
	"strconv"

	"github.com/hashicorp/go-version"
)

func VersionCMP(devType DevType, v1, b1, v2, b2 string) (int, error) {
	ver1, err := version.NewSemver(v1)
	if err != nil {
		return -1, fmt.Errorf("parse %s error: %w", v1, err)
	}
	ver2, err := version.NewSemver(v2)
	if err != nil {
		return -1, fmt.Errorf("parse %s error: %w", v2, err)
	}

	if ver1.<PERSON><PERSON>han(ver2) {
		return 1, nil
	}

	if ver1.<PERSON><PERSON><PERSON>(ver2) {
		return -1, nil
	}

	if devType == DevTypeAndroid {
		return 0, nil
	}

	// ios 对比 buildId
	i1, err := strconv.Atoi(b1)
	if err != nil {
		return -1, fmt.Erro<PERSON>("parse bid %s error: %w", b1, err)
	}

	i2, err := strconv.Atoi(b2)
	if err != nil {
		return -1, fmt.Errorf("parse bid %s error: %w", b2, err)
	}

	if i1 > i2 {
		return 1, nil
	}

	if i1 < i2 {
		return -1, nil
	}

	return 0, nil
}
