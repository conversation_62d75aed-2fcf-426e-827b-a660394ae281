package client

import (
	"strings"
	"testing"

	"github.com/hashicorp/go-version"
)

func TestVersionCMP(t *testing.T) {
	type args struct {
		devType DevType
		v1      string
		b1      string
		v2      string
		b2      string
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				devType: DevTypeAndroid,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "1",
			},
			want: 0,
		},
		{
			name: "test2",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "1",
			},
			want: 0,
		},

		{
			name: "test3",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "2",
			},
			want: -1,
		},

		{
			name: "test4",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "2",
				v2:      "1.0.0",
				b2:      "1",
			},
			want: 1,
		},

		{
			name: "test5",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.1",
				b2:      "1",
			},
			want: -1,
		},

		{
			name: "test6",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.1",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "1",
			},
			want: 1,
		},

		{
			name: "test7",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "1",
			},
			want: 0,
		},

		{
			name: "test8",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "2",
			},
			want: -1,
		},
		{
			name: "test9",
			args: args{
				devType: DevTypeAndroid,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.0",
				b2:      "2",
			},
			want: 0,
		},

		{
			name: "test10",
			args: args{
				devType: DevTypeAndroid,
				v1:      "1.0.1",
				b1:      "2",
				v2:      "1.0.0",
				b2:      "1",
			},
			want: 1,
		},
		{
			name: "test11",
			args: args{
				devType: DevTypeAndroid,
				v1:      "1.0.0",
				b1:      "1",
				v2:      "1.0.1",
				b2:      "1",
			},
			want: -1,
		},
		{
			name: "test12",
			args: args{
				devType: DevTypeIOS,
				v1:      "1.0.0",
				b1:      "1s",
				v2:      "1.0.0",
				b2:      "1",
			},
			wantErr: true,
			want:    -1,
		},

		{
			name: "test13",
			args: args{
				devType: DevTypeAndroid,
				v1:      "1.0.0-alpha",
				v2:      "1.0.0-beta",
			},
			want: -1,
		},
		{
			name: "test14",
			args: args{
				devType: DevTypeAndroid,
				v1:      "1.0.1-alpha",
				v2:      "1.0.0-beta",
			},
			want: 1,
		},
		{
			name: "test15",
			args: args{
				devType: DevTypeAndroid,
				v1:      "v1.0.1-alpha",
				v2:      "v1.0.0-beta",
			},
			want: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := VersionCMP(tt.args.devType, tt.args.v1, tt.args.b1, tt.args.v2, tt.args.b2)
			if (err != nil) != tt.wantErr {
				t.Errorf("VersionCMP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("VersionCMP() = %v, want %v", got, tt.want)
			}
		})
	}

	for i := range tests {
		if !strings.HasPrefix(tests[i].args.v1, "v") {
			tests[i].args.v1 = "v" + tests[i].args.v1
		}

		if !strings.HasPrefix(tests[i].args.v2, "v") {
			tests[i].args.v2 = "v" + tests[i].args.v2
		}
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := VersionCMP(tt.args.devType, tt.args.v1, tt.args.b1, tt.args.v2, tt.args.b2)
			if (err != nil) != tt.wantErr {
				t.Errorf("VersionCMP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("VersionCMP() = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestVersionAL(t *testing.T) {
	v1, err := version.NewSemver("1.0.0-c123")
	if err != nil {
		t.Error(err)
	}
	v2, err := version.NewSemver("1.0.0-beta")
	if err != nil {
		t.Error(err)
	}

	if v1.GreaterThan(v2) {
		t.Log("v1 > v2")
	}

	if v1.LessThan(v2) {
		t.Log("v1 < v2")
	}

	if v1.Equal(v2) {
		t.Log("v1 == v2")
	}

}
