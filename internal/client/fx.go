package client

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(mc *db.MongoClient, dm *redi.Mutex, syn cc.Sync, vnd log.Vendor) *Manager {
	mc.SyncSchema("client.versions", 1,
		db.Indexer{
			Name: "deviceType",
			Keys: bson.D{
				{Key: "deviceType", Value: 1},
			},
		},
		db.Indexer{
			Name: "deviceType_channel_published",
			Keys: bson.D{
				{Key: "deviceType", Value: 1},
				{Key: "channel", Value: 1},
				{Key: "published", Value: 1},
			},
		},
		db.Indexer{
			Name: "createdAt",
			Keys: bson.D{
				{Key: "createdAt", Value: -1},
			},
		},
	)

	m := &Manager{
		mc:     mc,
		dm:     dm,
		logger: vnd.Scope("client.version.mgr"),
	}

	m.latest = cc.New[LatestKey, *Version](
		10,
		cc.LRU,
		cc.Expiration(time.Minute),
		cc.WithSync(syn, "client.versions"),
		cc.ExportStats("client.versions"),
		cc.LoaderFunc(func(key LatestKey) (*Version, error) {
			return m.LatestVersion(context.Background(), key.DeviceType, key.Channel, lo.ToPtr(true))
		}))

	return m
}
