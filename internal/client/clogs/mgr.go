package clogs

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/redi"
)

type Manager struct {
	rc *redi.Client
}

const (
	keyCollect = "LOGS:COLLECT:%s" // userId
	ttlCollect = 7 * 24 * time.Hour
	keyStatus  = "status"
	keyTime    = "time"
)

type CollectInfo struct {
	Status int   `redis:"status"`
	Time   int64 `redis:"time"`
}

func (s *Manager) GetCollectStatus(ctx context.Context, userId string) (int, error) {
	v, _ := s.rc.HGet(ctx, fmt.Sprintf(keyCollect, userId), keyStatus).Int()
	return v, nil
}

func (s *Manager) GetCollectInfo(ctx context.Context, userId string) (*CollectInfo, error) {
	var out CollectInfo
	err := redi.Scan(s.rc.HGetAll(ctx, fmt.Sprintf(keyCollect, userId)), &out)
	return &out, err
}

func (s *Manager) SetCollectStatus(ctx context.Context, userId string, status int) error {
	key := fmt.Sprintf(keyCollect, userId)

	txp := s.rc.Pipeline()
	txp.HSet(ctx, key, map[string]any{
		keyStatus: status,
		keyTime:   time.Now().Unix(),
	})
	txp.Expire(ctx, key, ttlCollect)

	_, err := txp.Exec(ctx)
	return err
}
