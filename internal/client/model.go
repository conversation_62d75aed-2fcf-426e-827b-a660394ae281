package client

import (
	"strings"
	"time"

	"github.com/hashicorp/go-version"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	CollectionVersion = "client.versions"
)

type DevType string

const (
	DevTypeAndroid DevType = "android"
	DevTypeIOS     DevType = "ios"
)

var DevTypes = []DevType{DevTypeAndroid, DevTypeIOS}

const (
	ChannelIOS      = "app store" // app store
	ChannelGP       = "google"    // google play
	ChannelOfficial = "official"  // official
)

var DevChannels = map[DevType][]string{
	DevTypeAndroid: {ChannelGP, ChannelOfficial},
	DevTypeIOS:     {ChannelIOS},
}

type LangStrings struct {
	En string `json:"en" bson:"en"`
	Pt string `json:"pt" bson:"pt"`
	Zh string `json:"zh" bson:"zh"`
}

func newLangStrings(en, pt, zh string) *LangStrings {
	return &LangStrings{En: en, Pt: pt, Zh: zh}
}

func (l *LangStrings) ToMap() map[string]string {
	return map[string]string{
		"en": l.En,
		"pt": l.Pt,
		"zh": l.Zh,
	}
}

func (l *LangStrings) Empty() bool {
	return l.En == "" && l.Pt == "" && l.Zh == ""
}

func (l *LangStrings) Complete() bool {
	return l.En != "" && l.Pt != "" && l.Zh != ""
}

func (l *LangStrings) String() string {
	var sb strings.Builder
	sb.WriteString("en:")
	sb.WriteString(l.En)
	sb.WriteString(",pt:")
	sb.WriteString(l.Pt)
	sb.WriteString(",zh:")
	sb.WriteString(l.Zh)
	return sb.String()
}

func (l *LangStrings) Local(lang string) string {
	switch lang {
	case "pt":
		return l.Pt
	case "zh":
		return l.Zh
	default:
		return l.En
	}
}

type Version struct {
	ID               primitive.ObjectID `bson:"_id"`
	DeviceType       DevType            `bson:"deviceType"`
	Channel          string             `bson:"channel"`
	Version          string             `bson:"version"`
	BuildId          string             `bson:"buildId"` // ios only field
	SilentUpdate     bool               `json:"silentUpdate"`
	ForceUpdate      bool               `bson:"forceUpdate"`
	DownloadUrl      string             `bson:"downloadUrl"`
	MD5              string             `bson:"md5"`
	Title            LangStrings        `bson:"title"`
	UpdateContent    LangStrings        `bson:"updateContent"`
	OperatorUserId   string             `bson:"operatorUserId"`
	OperatorUserName string             `bson:"operatorUserName"`
	OperatorShowId   string             `bson:"operatorShowId"`
	Published        bool               `bson:"published"`
	PublishedAt      time.Time          `bson:"publishedAt"`
	Disabled         bool               `bson:"disabled"`
	UpdatedAt        time.Time          `bson:"updatedAt"`
	CreatedAt        time.Time          `bson:"createdAt"`
}

func (v *Version) Semver() (*version.Version, error) {
	return version.NewSemver(v.Version)
}
