package client

import (
	"context"
	"errors"
	"slices"
	"strconv"
	"time"

	"github.com/hashicorp/go-version"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type Manager struct {
	mc     *db.MongoClient
	dm     *redi.Mutex
	latest cc.Cache[LatestKey, *Version]
	logger *zap.Logger
}

func (m *Manager) LatestVersion2(ctx context.Context, devType, channel string) (*Version, error) {
	key := LatestKey{DeviceType: devType, Channel: channel}

	return m.latest.Get(key)
}

// LastVersion returns the last version of the given device type and channel.
// If no version is found, it returns nil.
func (m *Manager) LatestVersion(ctx context.Context, devType, channel string, published *bool) (*Version, error) {
	var (
		filter = bson.M{}
		sort   = bson.M{}
	)

	if devType != "" {
		filter["deviceType"] = devType
	}

	if channel != "" {
		filter["channel"] = channel
	}

	if published != nil {
		filter["published"] = *published
		sort["publishedAt"] = -1
	} else {
		sort["createdAt"] = -1
	}

	var v Version
	if err := m.mc.Collection(CollectionVersion).FindOne(ctx, filter, options.FindOne().SetSort(sort)).Decode(&v); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return &v, nil
}

type Draft struct {
	DeviceType    DevType     `bson:"deviceType"`
	Channel       string      `bson:"channel"`
	Version       string      `bson:"version"`
	BuildId       string      `bson:"buildId"` // ios only
	ForceUpdate   bool        `bson:"forceUpdate"`
	DownloadUrl   string      `bson:"downloadUrl"`
	MD5           string      `bson:"md5"`
	SilentUpdate  bool        `bson:"silentUpdate"`
	Title         LangStrings `bson:"title"`         // lang -> title
	UpdateContent LangStrings `bson:"updateContent"` // lang -> content
}

func (d *Draft) Semver() (*version.Version, error) {
	return version.NewSemver(d.Version)
}

func (d *Draft) Valid() error {
	if index := slices.Index(DevTypes, d.DeviceType); index == -1 {
		return biz.NewError(biz.ErrInvalidParam, "invalid device type")
	}

	if !slices.Contains(DevChannels[d.DeviceType], d.Channel) {
		return biz.NewError(biz.ErrInvalidParam, "invalid channel")
	}

	if d.DeviceType == DevTypeIOS {
		if d.BuildId == "" {
			return biz.NewError(biz.ErrInvalidParam, "build id is required for ios")
		}

		if _, err := strconv.Atoi(d.BuildId); err != nil {
			return biz.NewError(biz.ErrInvalidParam, "invalid build id, must be a number")
		}
	}

	if d.DeviceType == DevTypeAndroid {
		if d.ForceUpdate && d.SilentUpdate {
			return biz.NewError(biz.ErrInvalidParam, "force update and silent update cannot be true at the same time")
		}

		if d.Channel != ChannelGP {
			if d.MD5 == "" {
				return biz.NewError(biz.ErrInvalidParam, "md5 is required for android")
			}
		}
	}

	if !d.Title.Complete() {
		return biz.NewError(biz.ErrInvalidParam, "title must be complete")
	}

	if !d.UpdateContent.Complete() {
		return biz.NewError(biz.ErrInvalidParam, "update content must be complete")
	}

	if _, err := d.Semver(); err != nil {
		return biz.Legacy("invalid version")
	}

	return nil
}

func (m *Manager) Touch(ctx context.Context, dft *Draft, operator *user.Account) error {
	if err := dft.Valid(); err != nil {
		return err
	}

	l, err := m.dm.Lock(ctx, lockVersionWrite)
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	return m.doTouch(ctx, dft, operator)
}

func (m *Manager) doTouch(ctx context.Context, dft *Draft, operator *user.Account) error {
	at := time.Now()
	ver := &Version{
		ID:               primitive.NewObjectIDFromTimestamp(at),
		DeviceType:       dft.DeviceType,
		Channel:          dft.Channel,
		Version:          dft.Version,
		BuildId:          dft.BuildId,
		ForceUpdate:      dft.ForceUpdate,
		DownloadUrl:      dft.DownloadUrl,
		MD5:              dft.MD5,
		Title:            dft.Title,
		UpdateContent:    dft.UpdateContent,
		SilentUpdate:     dft.SilentUpdate,
		OperatorUserId:   operator.UserId,
		OperatorUserName: operator.Nickname,
		OperatorShowId:   operator.ShowId,
		Published:        false,
		PublishedAt:      time.Time{},
		Disabled:         false,
		UpdatedAt:        at,
		CreatedAt:        at,
	}

	if _, err := m.mc.Collection(CollectionVersion).InsertOne(ctx, ver); err != nil {
		return err
	}

	m.logger.Info("touch version",
		zap.String("id", ver.ID.Hex()),
		zap.String("deviceType", string(ver.DeviceType)),
		zap.String("channel", ver.Channel),
		zap.String("version", ver.Version),
		zap.String("buildId", ver.BuildId),
		zap.Bool("forceUpdate", ver.ForceUpdate),
		zap.String("downloadUrl", ver.DownloadUrl),
		zap.String("md5", ver.MD5),
		zap.String("title", ver.Title.String()),
		zap.String("updateContent", ver.UpdateContent.String()),
		zap.String("operatorUserid", operator.UserId),
		zap.String("operatorUserName", operator.Nickname),
		zap.String("operatorShowId", operator.ShowId),
	)

	return nil
}

func (m *Manager) Publish(ctx context.Context, id string, operator *user.Account) error {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	l, err := m.dm.Lock(ctx, lockVersionWrite)
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	// get the version
	c := m.mc.Collection(CollectionVersion)

	var current *Version
	if err := c.FindOne(ctx, bson.M{"_id": oid}).Decode(&current); err != nil {
		return biz.Legacy("version not found")
	}

	if current.Published {
		return biz.Legacy("the version has been published")
	}

	latest, err := m.LatestVersion(ctx, string(current.DeviceType), current.Channel, lo.ToPtr(true))
	if err != nil {
		return biz.Legacy("failed to get the latest version")
	}

	if latest != nil {
		c, err := VersionCMP(current.DeviceType, current.Version, current.BuildId, latest.Version, latest.BuildId)
		if err != nil {
			return err
		}

		if c <= 0 {
			return biz.Legacy("the new version must be greater than the last version")
		}
	}

	ur, err := c.UpdateOne(ctx,
		bson.M{"_id": oid, "published": false},
		bson.M{"$set": bson.M{
			"published":        true,
			"publishedAt":      time.Now(),
			"operatorUserId":   operator.UserId,
			"operatorUserName": operator.Nickname,
			"operatorShowId":   operator.ShowId,
			"updatedAt":        time.Now(),
		}},
	)
	if err != nil {
		return biz.Legacy("failed to publish the version")
	}

	if ur.ModifiedCount == 0 {
		return biz.Legacy("the version has been published")
	}

	m.latest.Remove(LatestKey{DeviceType: string(current.DeviceType), Channel: current.Channel})

	return nil
}

func (m *Manager) List(ctx context.Context, devType DevType, offset, limit int64, total *int64) ([]*Version, error) {
	c := m.mc.Collection(CollectionVersion)

	filter := bson.M{}
	if devType != "" {
		filter["deviceType"] = devType
	}

	if total != nil {
		v, err := c.CountDocuments(ctx, filter)
		if err != nil {
			return nil, err
		}
		*total = v
		if v == 0 {
			return nil, nil
		}
	}

	cur, err := c.Find(ctx, filter, options.Find().SetSort(bson.M{"createdAt": -1}).SetSkip(offset).SetLimit(limit))
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var vs []*Version
	if err := cur.All(ctx, &vs); err != nil {
		return nil, err
	}

	return vs, nil
}

func (m *Manager) Get(ctx context.Context, id string) (*Version, error) {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var v Version
	if err := m.mc.Collection(CollectionVersion).FindOne(ctx, bson.M{"_id": oid}).Decode(&v); err != nil {
		return nil, err
	}

	return &v, nil
}

func (m *Manager) Delete(ctx context.Context, id string, operator *user.Account) error {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	l, err := m.dm.Lock(ctx, lockVersionWrite)
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	var current Version
	if err := m.mc.Collection(CollectionVersion).FindOne(ctx, bson.M{"_id": oid}).Decode(&current); err != nil {
		return biz.Legacy("version not found")
	}

	if current.Published {
		return biz.Legacy("published version cannot be deleted")
	}

	ur, err := m.mc.Collection(CollectionVersion).DeleteOne(ctx, bson.M{"_id": oid})
	if err != nil {
		return err
	}

	if ur.DeletedCount == 0 {
		return biz.Legacy("version not found")
	}

	m.logger.Info("delete version",
		zap.String("id", id),
		zap.String("deviceType", string(current.DeviceType)),
		zap.String("channel", current.Channel),
		zap.String("version", current.Version),
		zap.String("buildId", current.BuildId),
		zap.Bool("forceUpdate", current.ForceUpdate),
		zap.String("downloadUrl", current.DownloadUrl),
		zap.String("title", current.Title.String()),
		zap.String("updateContent", current.UpdateContent.String()),
		zap.String("operator", operator.UserId),
		zap.String("operatorName", operator.Nickname),
	)

	return nil
}

func (m *Manager) Update(ctx context.Context, id string, doc *Draft, operator *user.Account) error {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	if err := doc.Valid(); err != nil {
		return err
	}

	l, err := m.dm.Lock(ctx, lockVersionWrite)
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	var current Version
	if err := m.mc.Collection(CollectionVersion).FindOne(ctx, bson.M{"_id": oid}).Decode(&current); err != nil {
		return biz.Legacy("version not found")
	}

	up := bson.M{
		"forceUpdate":      doc.ForceUpdate,
		"silentUpdate":     doc.SilentUpdate,
		"downloadUrl":      doc.DownloadUrl,
		"md5":              doc.MD5,
		"title":            doc.Title,
		"updateContent":    doc.UpdateContent,
		"operatorUserId":   operator.UserId,
		"operatorUserName": operator.Nickname,
		"operatorShowId":   operator.ShowId,
		"updatedAt":        time.Now(),
	}

	if !current.Published {
		up["version"] = doc.Version
		up["buildId"] = doc.BuildId
		up["deviceType"] = doc.DeviceType
		up["channel"] = doc.Channel
	}

	ur, err := m.mc.Collection(CollectionVersion).UpdateOne(ctx, bson.M{"_id": oid}, bson.M{"$set": up})
	if err != nil {
		return err
	}

	if ur.ModifiedCount == 0 {
		return biz.Legacy("version not modified")
	}

	m.latest.Remove(LatestKey{DeviceType: string(current.DeviceType), Channel: current.Channel})

	m.logger.Info("update version",
		zap.String("id", id),
		zap.String("deviceType", string(doc.DeviceType)),
		zap.String("channel", doc.Channel),
		zap.String("version", doc.Version),
		zap.String("buildId", doc.BuildId),
		zap.Bool("forceUpdate", doc.ForceUpdate),
		zap.String("downloadUrl", doc.DownloadUrl),
		zap.String("title", doc.Title.String()),
		zap.String("updateContent", doc.UpdateContent.String()),
		zap.String("operator", operator.UserId),
		zap.String("operatorName", operator.Nickname),
		zap.String("operatorShowId", operator.ShowId),
	)

	return nil
}

func (m *Manager) Disable(ctx context.Context, id string, operator *user.Account) error {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	l, err := m.dm.Lock(ctx, lockVersionWrite)
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	var current Version
	if err := m.mc.Collection(CollectionVersion).FindOne(ctx, bson.M{"_id": oid}).Decode(&current); err != nil {
		return biz.Legacy("version not found")
	}

	if !current.Published {
		return biz.Legacy("only published version can be disabled")
	}

	latest, err := m.LatestVersion(ctx, string(current.DeviceType), current.Channel, lo.ToPtr(true))
	if err != nil {
		return biz.Legacy("failed to get the latest version")
	}

	if latest.ID.Hex() != id {
		return biz.Legacy("only the latest published version can be disabled")
	}

	ur, err := m.mc.Collection(CollectionVersion).UpdateOne(
		ctx,
		bson.M{"_id": oid, "disabled": false},
		bson.M{"$set": bson.M{
			"disabled":         true,
			"operatorUserId":   operator.UserId,
			"operatorUserName": operator.Nickname,
			"operatorShowId":   operator.ShowId,
			"updatedAt":        time.Now()},
		},
	)
	if err != nil {
		return err
	}

	if ur.ModifiedCount == 0 {
		return biz.Legacy("the version has been disabled")
	}

	m.latest.Remove(LatestKey{DeviceType: string(current.DeviceType), Channel: current.Channel})
	m.logger.Info("disable version",
		zap.String("id", id),
		zap.String("deviceType", string(current.DeviceType)),
		zap.String("channel", current.Channel),
		zap.String("version", current.Version),
		zap.String("buildId", current.BuildId),
		zap.Bool("forceUpdate", current.ForceUpdate),
		zap.String("downloadUrl", current.DownloadUrl),
		zap.String("title", current.Title.String()),
		zap.String("updateContent", current.UpdateContent.String()),
		zap.String("operator", operator.UserId),
		zap.String("operatorName", operator.Nickname),
		zap.String("operatorShowId", operator.ShowId),
	)

	return nil
}
