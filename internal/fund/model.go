package fund

import (
	"time"

	"gorm.io/gorm"
)

// Account 资金账户
type Account struct {
	ID        uint      `gorm:"primaryKey"`
	UserId    string    `gorm:"not null;size:32;uniqueIndex:user_id"` // 用户ID
	Diamonds  Decimal   `gorm:"not null;type:decimal(16,0) unsigned"` // 金币余额
	Fruits    Decimal   `gorm:"not null;type:decimal(16,2) unsigned"` // 水晶余额
	UpdatedAt time.Time `gorm:"not null"`
}

func (a *Account) TableName() string {
	return "fund_accounts"
}

func fieldOf(prop PropType) string {
	switch prop {
	case PTypeDiamond:
		return "diamonds"
	case PTypeFruits:
		return "fruits"
	case PTypeTokens:
		return "balance"
	default:
		return "unknown"
	}
}

func modelOf(raw *gorm.DB, userId string, prop PropType) *gorm.DB {
	switch prop {
	case PTypeDiamond, PTypeFruits:
		return raw.Model(&Account{}).Where("user_id = ?", userId)
	case PTypeTokens:
		return raw.Model(&Wallet{}).Where("user_id = ? AND prop = ?", userId, prop)
	default:
		return nil
	}
}

func (a *Account) BVal(prop PropType) Decimal {
	switch prop {
	case PTypeDiamond:
		return a.Diamonds
	case PTypeFruits:
		return a.Fruits
	default:
		return zero()
	}
}

func (a *Account) inc(prop PropType, amount Decimal) {
	switch prop {
	case PTypeDiamond:
		Add(&a.Diamonds, amount)
	case PTypeFruits:
		Add(&a.Fruits, amount)
	}
}

func (a *Account) dec(prop PropType, amount Decimal) {
	switch prop {
	case PTypeDiamond:
		Sub(&a.Diamonds, amount)
	case PTypeFruits:
		Sub(&a.Fruits, amount)
	}
}

type Wallet struct {
	ID        uint      `gorm:"primaryKey"`
	UserId    string    `gorm:"not null;size:32;uniqueIndex:user_prop"` // 用户ID
	Prop      PropType  `gorm:"not null;size:8;uniqueIndex:user_prop"`  // 货币类型
	Balance   Decimal   `gorm:"not null;type:decimal(16,0) unsigned"`   // 钱包余额
	UpdatedAt time.Time `gorm:"not null"`
}

func (w *Wallet) TableName() string {
	return "fund_wallets"
}

func (w *Wallet) BVal(prop PropType) Decimal {
	if prop != w.Prop {
		return zero()
	}
	return w.Balance
}

func (w *Wallet) inc(prop PropType, amount Decimal) {
	if prop == w.Prop {
		Add(&w.Balance, amount)
	}
}

func (w *Wallet) dec(prop PropType, amount Decimal) {
	if prop == w.Prop {
		Sub(&w.Balance, amount)
	}
}

// Journal 账户流水
type Journal struct {
	ID        uint        `gorm:"primaryKey"`
	UserId    string      `gorm:"not null;size:32"`
	Type      JournalType `gorm:"not null;size:8;default:0"`    // 流水类型
	Prop      PropType    `gorm:"not null;size:8;default:0"`    // 货币类型
	Amount    Decimal     `gorm:"not null;size:19"`             // 流水金额
	Balance   Decimal     `gorm:"not null;size:19"`             // 账户余额
	Trade     string      `gorm:"not null;size:64;index:trade"` // 交易编号
	Details   string      `gorm:"not null;size:255;default:''"` // 详细信息
	CreatedAt time.Time   `gorm:"not null"`
}

func (j *Journal) Timed(set time.Time) {
	j.CreatedAt = set
}

func (j *Journal) TableName() string {
	return "fund_journals_" + j.CreatedAt.Format("200601")
}
