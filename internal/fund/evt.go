package fund

import (
	"time"
)

const (
	EvFundIncome = "fund.income"
	EvFundExpend = "fund.expend"
)

type JournalEv struct {
	Time   time.Time
	UserId string
	Type   JournalType
	Prop   PropType
	Amount Decimal
	Trade  string
	Detail string
	Group  string
	Extra  Extra
	Target string
}

func journalEv(opt *payOpts, userId string, typ JournalType, prop PropType, amount Decimal) *JournalEv {
	return &JournalEv{
		Time:   opt.time,
		UserId: userId,
		Type:   typ,
		Prop:   prop,
		Amount: amount,
		Trade:  opt.trade,
		Detail: opt.detail,
		Group:  opt.group,
		Extra:  opt.extra,
		Target: opt.target,
	}
}
