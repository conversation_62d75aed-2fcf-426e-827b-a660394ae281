package journal

import (
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Provide(db *db.MongoClient, qm *dq.Master, gm *gift.Manager, vnd log.Vendor) *Manager {
	db.SyncSchema(recordDB, 2, recordIdx...)
	db.SyncSchema(summaryDB, 2, summaryIdx...)
	mgr := newManager(db, gm, vnd.Scope("journal.mgr"))
	mgr.initMerger(qm)
	return mgr
}

func Invoke(evb ev.Bus, mgr *Manager) {
	evb.Watch(fund.EvFundIncome, "journal-writer", ev.<PERSON>atch<PERSON>(mgr.onFundIncome), ev.WithAsync())
	evb.Watch(fund.EvFundExpend, "journal-writer", ev.NewWatcher(mgr.onFundExpend), ev.WithAsync())
	if env.Scheduler() {
		mgr.startMerger()
	}
}
