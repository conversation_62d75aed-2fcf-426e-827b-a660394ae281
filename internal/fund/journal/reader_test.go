package journal

import (
	"context"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
)

func TestRecords(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx    = context.TODO()
			userId = "21ad0083d6104ed1b742ce821cc8395e"
			prop   = fund.PTypeDiamond
			types  []fund.JournalType
		)
		_, records, err := mgr.Records(ctx, userId, prop, types, time.Now(), "")
		if err != nil {
			return err
		}
		if len(records) > 0 {
			summary, err := mgr.Summary(ctx, userId, prop, types, records[len(records)-1].CreatedAt, records[0].CreatedAt, time.Local, Monthly)
			if err != nil {
				return err
			}
			spew.Dump(summary)
		}
		spew.Dump(records)
		return nil
	})
}

func TestSummary(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx    = context.TODO()
			userId = "21ad0083d6104ed1b742ce821cc8395e"
			prop   = fund.PTypeDiamond
			types  []fund.JournalType
		)
		summary, err := mgr.Summary(ctx, userId, prop, types, now.BeginningOfMonth(), time.Now(), time.Local, Daily)
		if err != nil {
			return err
		}
		spew.Dump(summary)
		return nil
	})
}

func TestSummaryZone(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx      = context.TODO()
			userId   = "21ad0083d6104ed1b742ce821cc8395e"
			prop     = fund.PTypeDiamond
			types    []fund.JournalType
			start, _ = time.Parse(time.RFC3339Nano, "2024-05-01T00:00:00-03:00")
			end, _   = time.Parse(time.RFC3339Nano, "2024-05-31T23:59:59-03:00")
		)
		summary, err := mgr.Summary(ctx, userId, prop, types, start.Local(), end.Local(), time.FixedZone("-03:00", -10800), Monthly)
		if err != nil {
			return err
		}
		spew.Dump(summary)
		return nil
	})
}
