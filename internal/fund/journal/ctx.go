package journal

import "context"

type ctxKey int

const (
	ctxSkipRecord ctxKey = iota
	ctxWithDeleted
	ctxPagination
)

func SkipRecord(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxSkipRecord, struct{}{})
}

func skipRecord(ctx context.Context) bool {
	_, ok := ctx.Value(ctxSkipRecord).(struct{})
	return ok
}

func WithDeleted(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxWithDeleted, struct{}{})
}

func withDeleted(ctx context.Context) bool {
	_, ok := ctx.Value(ctxWithDeleted).(struct{})
	return ok
}
