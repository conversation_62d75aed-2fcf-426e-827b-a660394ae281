package journal

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (s *Manager) Detail(ctx context.Context, id string) (*Record, error) {
	return s.details(ctx, id, 0, 1)
}

func (s *Manager) Details(ctx context.Context, id string, offset int64) (*Record, error) {
	_, pageLimit := pagination(ctx)
	return s.details(ctx, id, offset, int64(pageLimit))
}

func (s *Manager) details(ctx context.Context, id string, offset, limit int64) (*Record, error) {
	id2, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	var rec Record
	if err := s.db.Collection(recordDB(db.Timestamp(id2))).FindOne(ctx, bson.M{"_id": id2},
		options.FindOne().SetProjection(bson.M{"history": bson.M{"$slice": []int64{offset, limit}}}),
	).Decode(&rec); err != nil {
		return nil, err
	}
	return &rec, nil
}

func History[T any](rec *Record) []T {
	out := make([]T, 0, 20)
	_ = rec.History.Unmarshal(&out)
	return out
}
