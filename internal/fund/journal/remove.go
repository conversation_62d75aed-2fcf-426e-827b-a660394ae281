package journal

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	ErrMixedUserRecords = errors.New("records from different users")
	ErrMixedPropRecords = errors.New("records from different props")
)

func (s *Manager) Delete(ctx context.Context, userId string, recIds []string) error {
	ids := splitMonths(recIds)

	var (
		adjProp fund.PropType
		adjList = make(map[time.Time]map[fund.JournalType][]fund.Decimal)
	)

	opts := options.Find().SetProjection(bson.M{"userId": 1, "type": 1, "prop": 1, "amount": 1, "createdAt": 1})

	for t, ids := range ids {
		records, err := db.DecodeAll[*Record](ctx)(s.db.Collection(recordDB(t)).Find(ctx, bson.M{"_id": bson.M{"$in": ids}}, opts))
		if err != nil {
			return err
		}
		for _, rec := range records {
			// check 1
			if rec.UserId != userId {
				return ErrMixedUserRecords
			}
			// check 2
			if adjProp == 0 {
				adjProp = rec.Prop
			} else if adjProp != rec.Prop {
				return ErrMixedPropRecords
			}
			// init map
			d := rec.CreatedAt.Truncate(time.Hour)
			if len(adjList[d][rec.Type]) == 0 {
				if len(adjList[d]) == 0 {
					adjList[d] = make(map[fund.JournalType][]fund.Decimal)
				}
				adjList[d][rec.Type] = make([]fund.Decimal, 2)
			}
			// update adj
			if rec.Amount.IsPositive() {
				fund.Add(&adjList[d][rec.Type][0], rec.Amount)
			} else {
				fund.Sub(&adjList[d][rec.Type][1], rec.Amount)
			}
		}
	}

	if adjProp == 0 {
		return nil
	}

	return s.db.TryTxn(ctx, func(ctx context.Context) error {
		for t, ids := range ids {
			if resp, err := s.db.Collection(recordDB(t)).UpdateMany(ctx, bson.M{"_id": bson.M{"$in": ids}}, bson.M{"$set": bson.M{"deleted": true}}); err != nil {
				return err
			} else if resp.ModifiedCount != int64(len(ids)) {
				return errors.New("failed to delete records")
			}
			w := make([]mongo.WriteModel, 0, len(adjList))
			for d, adjS := range adjList {
				if !sameMonth(t, d) {
					continue
				}
				for jType, adj := range adjS {
					inc := make(bson.M)
					if !adj[0].IsZero() {
						inc["removed.income"] = adj[0]
					}
					if !adj[1].IsZero() {
						inc["removed.expend"] = adj[1]
					}
					w = append(w, mongo.NewUpdateOneModel().
						SetFilter(bson.M{"userId": userId, "type": jType, "prop": adjProp, "time": d}).
						SetUpdate(bson.M{"$inc": inc}),
					)
				}
			}
			if _, err := s.db.Collection(summaryDB(t)).BulkWrite(ctx, w); err != nil {
				return err
			}
		}
		return nil
	})
}

type monthlyIds map[time.Time][]primitive.ObjectID

func splitMonths(ids []string) monthlyIds {
	group := make(monthlyIds)
	for _, id := range ids {
		id2, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil
		}
		key := beginOfMonth(db.Timestamp(id2))
		group[key] = append(group[key], id2)
	}
	return group
}
