package journal

import (
	"context"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var skips []fund.JournalType

func (s *Manager) onFundIncome(ctx context.Context, ev *fund.JournalEv) error {
	if slices.Contains(skips, ev.Type) {
		return nil
	}
	return s.onFundUpdate(ctx, ev, ev.Amount)
}

func (s *Manager) onFundExpend(ctx context.Context, ev *fund.JournalEv) error {
	if slices.Contains(skips, ev.Type) {
		return nil
	}
	return s.onFundUpdate(ctx, ev, ev.Amount.Neg())
}

func (s *Manager) onFundUpdate(ctx context.Context, ev *fund.JournalEv, amount fund.Decimal) error {
	if skipRecord(ctx) {
		return nil
	}
	if ev.Group != "" {
		return s.db.NewTxnBeta(ctx, func(ctx context.Context) error {
			return s.logRecord(ctx, ev.Time, ev.Type, ev.UserId, ev.Target, ev.Group, nil, ev.Prop, amount, withTrade(ev.Trade), withDetail(ev.Detail))
		}, db.WithRetry(3))
	}
	return s.db.NewTxnBeta(ctx, func(ctx context.Context) error {
		if err := s.newRecord(ctx, ev.Time, ev.UserId, ev.Type, ev.Prop, amount, ev.Trade, ev.Detail, ev.Extra, ev.Target); err != nil {
			return err
		}
		if err := s.logSummary(ctx, ev.Time, ev.UserId, ev.Type, ev.Prop, amount); err != nil {
			return err
		}
		return nil
	}, db.WithRetry(3))
}

func (s *Manager) newRecord(ctx context.Context, at time.Time, userId string, jType fund.JournalType, prop fund.PropType, amount fund.Decimal, trade, detail string, extra fund.Extra, target string) error {
	rec := &Record{
		Id:        primitive.NewObjectIDFromTimestamp(at),
		Group:     iGroup(at),
		UserId:    userId,
		Type:      jType,
		Prop:      prop,
		Amount:    amount,
		CreatedAt: at,
		Trade:     trade,
		Remark:    detail,
		Extra:     extra,
		WithUser:  target,
	}

	if _, err := s.db.Collection(recordDB(at)).InsertOne(ctx, rec); err != nil {
		return err
	}

	return nil
}

func (s *Manager) logSummary(ctx context.Context, at time.Time, userId string, jType fund.JournalType, prop fund.PropType, amount fund.Decimal) error {
	key := bson.M{
		"userId": userId,
		"type":   jType,
		"prop":   prop,
		"time":   at.Truncate(time.Hour),
	}

	field := lo.Ternary(amount.IsPositive(), "incomes", "expends")
	update := lo.Ternary(amount.IsPositive(), amount, amount.Neg())

	upd := bson.M{
		"$setOnInsert": bson.M{
			"_id": primitive.NewObjectIDFromTimestamp(at),
		},
		"$inc": bson.M{
			field: update,
		},
	}

	if _, err := s.db.Collection(summaryDB(at)).UpdateOne(ctx, key, upd, options.Update().SetUpsert(true)); err != nil {
		return err
	}

	return nil
}
