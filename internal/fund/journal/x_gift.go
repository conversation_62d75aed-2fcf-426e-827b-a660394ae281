package journal

import (
	"context"
	"slices"
	"strconv"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func init() {
	skips = append(skips, fund.JTypeSendGift)
	mergers["gift"] = mergeGift
}

type GiftLog struct {
	history
	ComboId string    `bson:"comboId,omitempty"`
	GiftId  int       `bson:"giftId"`
	Count   int       `bson:"count"`
	Amount  int64     `bson:"amount"`
	Time    time.Time `bson:"time"`
}

func (l *GiftLog) Combo() string {
	return l.ComboId
}

func (s *Manager) SendGift(ctx context.Context, at time.Time, from, to string, sid, combo string, giftId, count int, diamond, fruit fund.Decimal) error {
	grp := xGroup("gift", sid)
	log := &GiftLog{ComboId: combo, GiftId: giftId, Count: count, Amount: diamond.Neg().IntPart(), Time: at}
	return s.db.TryTxnBeta(ctx, func(ctx context.Context) error {
		if err := s.logRecord(ctx, at, fund.JTypeSendGift, to, "", grp, nil, fund.PTypeFruits, fruit); err != nil {
			return err
		}
		return s.logRecord(ctx, at, fund.JTypeSendGift, from, to, grp, log, fund.PTypeDiamond, diamond.Neg())
	}, db.WithRetry(3))
}

func (s *Manager) RecvGift(ctx context.Context, at time.Time, to string, sid string, fruit fund.Decimal) error {
	grp := xGroup("gift", sid)
	return s.db.TryTxnBeta(ctx, func(ctx context.Context) error {
		return s.logRecord(ctx, at, fund.JTypeSendGift, to, "", grp, nil, fund.PTypeFruits, fruit)
	}, db.WithRetry(3))
}

func (s *Manager) giftRemark(ctx context.Context, rec *Record) string {
	switch rec.Prop {
	case fund.PTypeDiamond:
		if rec.Merged == 1 {
			log1 := History[*GiftLog](rec)[0]
			if gift, _ := s.gm.GiftById(log1.GiftId); gift != nil {
				return i3n.T2(ctx, "send gift {{.name}}{{.count}}", i3n.Args{"name": gift.Name, "count": log1.Count})
			}
		}
		return i3n.T(ctx, "send gift to anchor")
	case fund.PTypeFruits:
		return i3n.T(ctx, "recv gift from users")
	default:
		return ""
	}
}

func mergeGift(rec *Record, at time.Time) (reduced int, result []any) {
	logs := History[*GiftLog](rec)
	slices.SortFunc(logs, func(a, b *GiftLog) int {
		return b.Time.Compare(a.Time)
	})

	merged := make(map[string]*GiftLog)
	for i, item := range logs {
		if i == 0 && item.Time.After(at) {
			return
		}
		k := item.ComboId + "/" + strconv.Itoa(item.GiftId)
		p, has := merged[k]
		if !has {
			item.ComboId = ""
			merged[k] = item
			continue
		}
		p.ComboId = item.ComboId
		p.Count += item.Count
		p.Amount += item.Amount
		p.Time = item.Time
		reduced++
	}

	logs = lo.Values(merged)
	slices.SortFunc(logs, func(a, b *GiftLog) int {
		return b.Time.Compare(a.Time)
	})

	result = lo.ToAnySlice(logs)
	return
}
