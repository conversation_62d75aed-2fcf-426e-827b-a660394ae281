package journal

import (
	"context"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func init() {
	skips = append(skips, fund.JTypeLuckGift)
	mergers["draw"] = mergeDraw
}

type DrawLog struct {
	history
	GiftId int       `bson:"giftId"`
	Multi  int       `bson:"multi"`
	Amount int64     `bson:"amount"`
	Time   time.Time `bson:"time"`
}

func (l *DrawLog) Combo() string {
	return "1"
}

func (s *Manager) LuckDraw(ctx context.Context, at time.Time, userId, anchorId string, sid string, giftId, multi int, diamond int) error {
	grp := xGroup("draw", sid)
	log := &DrawLog{GiftId: giftId, Multi: multi, Amount: int64(diamond), Time: at}
	return s.db.TryTxnBeta(ctx, func(ctx context.Context) error {
		return s.logRecord(ctx, at, fund.JTypeLuckGift, userId, anchorId, grp, log, fund.PTypeDiamond, fund.New(diamond))
	}, db.WithRetry(3))
}

func (s *Manager) drawRemark(ctx context.Context, rec *Record) string {
	if rec.Merged == 1 {
		log1 := History[*DrawLog](rec)[0]
		if gift, _ := s.gm.GiftById(log1.GiftId); gift != nil {
			return i3n.T2(ctx, "luck gift {{.name}}{{.multi}}", i3n.Args{"name": gift.Name, "multi": log1.Multi})
		}
	}
	return i3n.T(ctx, "luck gift prizes")
}

func mergeDraw(rec *Record, at time.Time) (reduced int, result []any) {
	logs := History[*DrawLog](rec)
	slices.SortFunc(logs, func(a, b *DrawLog) int {
		return b.Time.Compare(a.Time)
	})

	for i, item := range logs {
		if i == 0 && item.Time.After(at) {
			return
		}
		break
	}

	result = lo.ToAnySlice(logs)
	return
}
