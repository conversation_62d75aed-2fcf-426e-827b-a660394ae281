package journal

import (
	"context"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/rs/xid"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/dq"
	"gitlab.sskjz.com/overseas/live/osl/sys/mq"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/fx"
)

var testMod = fx.Module("journal", fx.Provide(
	redi.Provide, unq.Provide, cron.Provide, db.ProvideGORM, db.ProvideMongo, mq.Provide, dq.Provide,
	gift.Provide, Provide,
))

func TestWriting(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx    = context.TODO()
			userId = "21ad0083d6104ed1b742ce821cc8395e"
			jType  = fund.JTypeRecharge
			prop   = fund.PTypeDiamond
		)
		at := time.Now().AddDate(0, 1, 0)
		for i := 0; i < 3; i++ {
			at = prevMonthOf(at).Add(rng.Duration(1, 10080, time.Minute))
			for j := 0; j < 10; j++ {
				ev := &fund.JournalEv{
					Time:   at.Add(time.Nanosecond * time.Duration(j)),
					UserId: userId,
					Type:   jType,
					Prop:   prop,
					Amount: fund.New(1),
				}
				if err := mgr.onFundUpdate(ctx, ev, ev.Amount); err != nil {
					return err
				}
			}
		}
		return nil
	})
}

func TestSendGift(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx     = context.TODO()
			from    = "21ad0083d6104ed1b742ce821cc8395e"
			to      = "21ad0083d6104ed1b742ce821cc8395e"
			sid     = "6627b44d1f2adaf8ce034114"
			diamond = fund.New(10)
			fruit   = fund.New(7)
		)
		if err := mgr.SendGift(ctx, time.Now(), from, to, sid, "", 1, 1, diamond, fruit); err != nil {
			return err
		}
		_, rs, err := mgr.Records(ctx, from, fund.PTypeDiamond, nil, time.Time{}, "")
		if err != nil {
			return err
		}
		var logs []*GiftLog
		if err := rs[0].History.Unmarshal(&logs); err != nil {
			return err
		}
		spew.Dump(rs[0], logs)
		return nil
	})
}

func TestEventTrigger(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		return mgr.onFundIncome(context.TODO(), &fund.JournalEv{
			Time:   time.Now(),
			UserId: "21ad0083d6104ed1b742ce821cc8395e",
			Type:   fund.JTypeKickback,
			Prop:   fund.PTypeFruits,
			Amount: fund.New(1),
			Trade:  xid.New().String(),
			Detail: "test remark",
			Group:  "test@day1",
		})
	})
}
