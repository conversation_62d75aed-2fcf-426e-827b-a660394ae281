package journal

import (
	"context"
	"strconv"
	"time"
)

func parseCursor(in string) (offset time.Time, when time.Time, err error) {
	if in == "" {
		when = time.Now()
		return
	}

	unixTs, err := strconv.ParseInt(in, 10, 64)
	if err != nil {
		return
	}

	offset = time.UnixMilli(unixTs)

	when = offset
	return
}

func makeCursor(offset time.Time) string {
	return strconv.FormatInt(offset.UnixMilli(), 10)
}

func Pagination(ctx context.Context, pageLimit int, backMonth ...int) context.Context {
	return context.WithValue(ctx, ctxPagination, append([]int{pageLimit}, backMonth...))
}

func pagination(ctx context.Context) (backMonth, pageLimit int) {
	backMonth = 3
	pageLimit = 20
	v, is := ctx.Value(ctxPagination).([]int)
	if is && len(v) > 0 {
		pageLimit = v[0]
		if len(v) > 1 {
			backMonth = v[1]
		}
	}
	return
}
