package journal

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type (
	embedLog interface{ embed() }
	stashLog interface{ Combo() string }
)

type history struct{}

func (history) embed() {}

func makeLogOpts(opts []logOpt) *logOpts {
	o := new(logOpts)
	for _, opt := range opts {
		opt(o)
	}
	return o
}

type logOpts struct {
	trade  string
	detail string
}

type logOpt func(*logOpts)

func withTrade(trade string) logOpt { return func(opts *logOpts) { opts.trade = trade } }

func withDetail(detail string) logOpt { return func(opts *logOpts) { opts.detail = detail } }

func (s *Manager) logRecord(ctx context.Context, at time.Time, typ fund.JournalType, self, peer string, group string, log embedLog, prop fund.PropType, amount fund.Decimal, opts ...logOpt) (err error) {
	key := bson.M{
		"userId": self,
		"prop":   prop,
		"group":  group,
	}

	upd := bson.M{
		"$setOnInsert": bson.M{
			"_id":  primitive.NewObjectIDFromTimestamp(at),
			"type": typ,
		},
		"$set": bson.M{"createdAt": at},
		"$inc": bson.M{"amount": amount, "merged": 1},
	}

	opt := makeLogOpts(opts)
	if opt.trade != "" {
		upd["$set"].(bson.M)["trade"] = opt.trade
	}
	if opt.detail != "" {
		upd["$setOnInsert"].(bson.M)["remark"] = opt.detail
	}

	if peer != "" {
		upd["$set"].(bson.M)["withUser"] = peer
	}

	if log != nil {
		to := "history"
		if sl, is := log.(stashLog); is && sl.Combo() != "" {
			to = "stash"
			defer func() {
				if err == nil {
					s.triggerMerge(ctx, at, self, prop, group)
				}
			}()
		}
		upd["$push"] = bson.M{
			to: bson.M{
				"$each": []any{
					log,
				},
			},
		}
		if to == "history" {
			upd["$push"].(bson.M)[to].(bson.M)["$position"] = 0
		}
	}

	if _, err = s.db.Collection(recordDB(at)).UpdateOne(ctx, key, upd, options.Update().SetUpsert(true)); err != nil {
		return
	}

	if err = s.logSummary(ctx, at, self, typ, prop, amount); err != nil {
		return
	}

	return
}
