package journal

import (
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

func newManager(db *db.MongoClient, gm *gift.Manager, log *zap.Logger) *Manager {
	return &Manager{
		db:  db,
		gm:  gm,
		log: log,
	}
}

type Manager struct {
	db  *db.MongoClient
	gm  *gift.Manager
	log *zap.Logger
	// merger
	mgp co.Pool
	mgt dq.Queue[*mergeTask]
}
