package journal

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var recordIdx = []db.Indexer{
	{
		Name: "user_group",
		Uniq: lo.ToPtr(true),
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "prop", Value: 1},
			{Key: "group", Value: 1},
		},
	},
	{
		Name: "user_view",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "prop", Value: 1},
			{Key: "createdAt", Value: -1},
			{Key: "type", Value: 1},
		},
	},
}

type Record struct {
	Id        primitive.ObjectID `bson:"_id"`
	Group     string             `bson:"group"`
	UserId    string             `bson:"userId"`
	Type      fund.JournalType   `bson:"type"`
	Prop      fund.PropType      `bson:"prop"`
	Amount    fund.Decimal       `bson:"amount"` // 有正负
	CreatedAt time.Time          `bson:"createdAt"`
	Deleted   bool               `bson:"deleted,omitempty"`
	Trade     string             `bson:"trade,omitempty"`  // from fund.Journal .Trade
	Remark    string             `bson:"remark,omitempty"` // from fund.Journal .Details
	Extra     fund.Extra         `bson:"extra,omitempty"`
	Merged    int                `bson:"merged,omitempty"`
	History   bson.RawValue      `bson:"history,omitempty"`
	WithUser  string             `bson:"withUser,omitempty"`
}

func recordDB(t time.Time) string {
	return "fund.journal." + t.Format("200601")
}

var summaryIdx = []db.Indexer{
	{
		Name: "user_index",
		Uniq: lo.ToPtr(true),
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "prop", Value: 1},
			{Key: "type", Value: 1},
			{Key: "time", Value: 1},
		},
	},
}

type Summary struct {
	Id      primitive.ObjectID `bson:"_id"`
	UserId  string             `bson:"userId"`
	Type    fund.JournalType   `bson:"type"`
	Prop    fund.PropType      `bson:"prop"`
	Time    time.Time          `bson:"time"`
	Incomes fund.Decimal       `bson:"incomes"` // 正数
	Expends fund.Decimal       `bson:"expends"` // 正数
	Removed Adjust             `bson:"removed"`
}

type Adjust struct {
	Income fund.Decimal `bson:"income"` // 正数
	Expend fund.Decimal `bson:"expend"` // 正数
}

func summaryDB(t time.Time) string {
	return "fund.summary." + t.Format("200601")
}
