package journal

import (
	"context"
	"maps"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (s *Manager) Records(ctx context.Context, userId string, prop fund.PropType, jType []fund.JournalType, endAt time.Time, cursor string, opts ...ReadOpt) (string, []*Record, error) {
	opt := newReadOpts(opts)

	backMonth, pageLimit := pagination(ctx)

	offset, when, err := parseCursor(cursor)
	if err != nil {
		return "", nil, err
	}

	filter := bson.M{
		"userId": userId,
		"prop":   prop,
	}

	if opt.modifier != nil {
		opt.modifier(filter)
	}

	if len(jType) > 0 {
		filter["type"] = bson.M{"$in": jType}
	}

	if !endAt.IsZero() {
		filter["createdAt"] = bson.M{"$lte": endAt}
	}

	if !withDeleted(ctx) {
		filter["deleted"] = nil
	}

	proj := bson.M{"history": bson.M{"$slice": 1}}
	sort := bson.M{"createdAt": -1}

	records, err := db.MonthTravel(backMonth, pageLimit, when, func(at time.Time, limit int64) ([]*Record, error) {
		filter := filter
		if !offset.IsZero() {
			filter = maps.Clone(filter)
			filter["createdAt"] = bson.M{"$lt": offset}
			offset = time.Time{}
		}
		return db.DecodeAll[*Record](ctx)(s.db.Collection(recordDB(at)).Find(ctx, filter,
			options.Find().SetProjection(proj).SetSort(sort).SetLimit(limit),
		))
	})
	if err != nil {
		return "", nil, err
	}

	cursor = ""
	if len(records) >= pageLimit {
		cursor = makeCursor(records[len(records)-1].CreatedAt)
	}

	return cursor, records, nil
}
