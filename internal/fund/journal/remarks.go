package journal

import (
	"context"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
)

func (s *Manager) Remark(ctx context.Context, rec *Record) string {
	if rec.Remark != "" {
		return rec.Remark
	}
	switch rec.Type {
	case fund.JTypeRecharge:
		return i3n.T(ctx, "recharge")
	case fund.JTypeSendGift:
		return s.giftRemark(ctx, rec)
	case fund.JTypeExchange:
		amount := rec.Amount.Abs()
		if exp := rec.Extra["expend"]; exp != "" {
			amount = fund.New(exp)
		}
		return i3n.T2(ctx, "exchange {{.amount}}", i3n.Args{"amount": amount})
	case fund.JTypeLuckGift:
		return s.drawRemark(ctx, rec)
	case fund.JTypeSalary:
		return i3n.T(ctx, "anchor salary")
	case fund.JTypeWithdraw:
		return i3n.T2(ctx, "withdraw {{.amount}}", i3n.Args{"amount": rec.Amount.Abs()})
	case fund.JTypeKickback:
		return i3n.T(ctx, "journal kickback")
	case fund.JTypeGameplay:
		if rec.Amount.IsPositive() {
			return i3n.T(ctx, "gameplay win")
		} else {
			return i3n.T(ctx, "gameplay cost")
		}
	case fund.JTypeRewards:
		switch rec.Prop {
		case fund.PTypeDiamond:
			return i3n.T(ctx, "j rewards diamond")
		case fund.PTypeFruits:
			return i3n.T(ctx, "j rewards fruits")
		default:
			return ""
		}
	case fund.JTypeOthers:
		switch rec.Prop {
		case fund.PTypeDiamond:
			if rec.Amount.IsPositive() {
				return i3n.T(ctx, "j others diamond gain")
			} else {
				return i3n.T(ctx, "j others diamond cost")
			}
		case fund.PTypeFruits:
			if rec.Amount.IsPositive() {
				return i3n.T(ctx, "j others fruits gain")
			} else {
				return i3n.T(ctx, "j others fruits cost")
			}
		default:
			return ""
		}
	default:
		return ""
	}
}
