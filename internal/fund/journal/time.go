package journal

import (
	"time"

	"github.com/jinzhu/now"
)

func beginOfHour(at time.Time) time.Time {
	return now.With(at).BeginningOfHour()
}

func beginOfDay(at time.Time) time.Time {
	return now.With(at).BeginningOfDay()
}

func beginOfMonth(at time.Time) time.Time {
	return now.With(at).BeginningOfMonth()
}

func prevMonthOf(at time.Time) time.Time {
	return now.With(at).BeginningOfMonth().AddDate(0, -1, 0)
}

func sameMonth(a, b time.Time) bool {
	y1, m1, _ := a.Date()
	y2, m2, _ := b.Date()
	return y1 == y2 && m1 == m2
}

func SameMonth(abs, cmp time.Time) bool {
	return sameMonth(abs, cmp.In(abs.Location()))
}
