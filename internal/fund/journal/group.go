package journal

import (
	"strings"
	"time"

	"github.com/rs/xid"
)

func xGroup(ns, key string) string {
	return ns + "@" + key
}

func (r *Record) GNs() string {
	if i := strings.Index(r.Group, "@"); i >= 0 {
		return r.Group[:i]
	}
	return r.Group
}

func (r *Record) GKey() string {
	if i := strings.Index(r.Group, "@"); i >= 0 {
		return r.Group[i+1:]
	}
	return r.Group
}

func iGroup(t time.Time) string {
	return xid.NewWithTime(t).String()
}
