package journal

import (
	"context"
	"maps"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

type Span int

const (
	Hourly Span = iota
	Daily
	Monthly
)

type TSummary struct {
	Id      string `bson:"_id"`
	Time    time.Time
	Incomes fund.Decimal // 总收入
	Expends fund.Decimal // 总支出
}

func (s *Manager) Summary(ctx context.Context, userId string, prop fund.PropType, jType []fund.JournalType, start, end time.Time, loc *time.Location, span Span) ([]*TSummary, error) {
	backMonth, _ := pagination(ctx)

	filter := bson.M{
		"userId": userId,
		"prop":   prop,
	}

	if len(jType) > 0 {
		filter["type"] = bson.M{"$in": jType}
	}

	head := beginOfMonth(start)
	ts, err := db.MonthTravel(backMonth, backMonth, end, func(at time.Time, _ int64) ([]*TSummary, error) {
		if at.Before(head) {
			return nil, nil
		}
		filter := maps.Clone(filter)
		if sameMonth(at, start) {
			filter["time"] = bson.M{"$gte": spanTime(span, loc, start, 0)}
		}
		if sameMonth(at, end) {
			filter["time"] = bson.M{"$lt": spanTime(span, loc, end, 1)}
		}
		pp := bson.A{bson.M{"$match": filter}}
		pp = append(pp, spanFields(span, tzName(loc))...)
		pp = append(pp, bson.M{"$project": bson.M{
			"time":    1,
			"incomes": bson.M{"$subtract": bson.A{"$incomes", bson.M{"$sum": "$removed.income"}}},
			"expends": bson.M{"$subtract": bson.A{"$expends", bson.M{"$sum": "$removed.expend"}}},
		}}, bson.M{"$group": bson.M{
			"_id":     "$time",
			"incomes": bson.M{"$sum": "$incomes"},
			"expends": bson.M{"$sum": "$expends"},
		}})
		return db.DecodeAll[*TSummary](ctx)(s.db.Collection(summaryDB(at)).Aggregate(ctx, pp))
	})
	if err != nil {
		return nil, err
	}

	for _, t := range ts {
		t.Time, _ = time.ParseInLocation("2006010215", t.Id, loc)
	}

	slices.SortFunc(ts, func(a, b *TSummary) int {
		return a.Time.Compare(b.Time)
	})

	var pt time.Time
	for i := 0; i < len(ts); i++ {
		t := ts[i]
		if t.Time.Equal(pt) {
			fund.Add(&ts[i-1].Incomes, t.Incomes)
			fund.Add(&ts[i-1].Expends, t.Expends)
			ts = slices.Delete(ts, i, i+1)
			i--
			continue
		}
		pt = t.Time
	}

	return ts, nil
}

func spanTime(p Span, tz *time.Location, in time.Time, offset int) time.Time {
	switch p {
	case Hourly:
		return beginOfHour(in.In(tz)).Add(time.Duration(offset) * time.Hour).Local()
	case Daily:
		return beginOfDay(in.In(tz)).AddDate(0, 0, offset).Local()
	case Monthly:
		return beginOfMonth(in.In(tz)).AddDate(0, offset, 0).Local()
	default:
		return in
	}
}

func spanFields(p Span, tz string) []any {
	format := "%Y010100"
	switch p {
	case Hourly:
		format = "%Y%m%d%H"
		fallthrough
	case Daily:
		format = "%Y%m%d00"
		fallthrough
	case Monthly:
		format = "%Y%m0100"
	}
	return []any{
		bson.M{"$addFields": bson.M{"time": bson.M{"$dateToString": bson.M{"date": "$time", "format": format, "timezone": tz}}}},
	}
}

func tzName(loc *time.Location) string {
	if loc == time.Local {
		return ctz.L().String()
	}
	return loc.String()
}
