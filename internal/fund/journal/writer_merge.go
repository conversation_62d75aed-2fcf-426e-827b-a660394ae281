package journal

import (
	"context"
	"errors"
	"time"

	"github.com/cespare/xxhash/v2"
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ob/meter"
	"gitlab.sskjz.com/go/sp"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const mergeWait = 5 * time.Second

type mergeFn func(rec *Record, at time.Time) (reduced int, merged []any)

var mergers = make(map[string]mergeFn)

type mergeTask struct {
	At    time.Time     `json:"t"`
	User  string        `json:"u"`
	Prop  fund.PropType `json:"p"`
	Group string        `json:"g"`
}

func (s *Manager) initMerger(dm *dq.Master) {
	s.mgp = co.Apply(co.Named("journal.merge.trigger"))
	s.mgt = dq.NewWith[*mergeTask](dm, "journal.merge.tasks")
}

func (s *Manager) startMerger() {
	s.mgt.Register(sp.Worker(
		sp.New(sp.Named("trade.journal.w"), sp.Size(128)),
		func(job *mergeTask) uint32 { return uint32(xxhash.Sum64String(job.User)) },
		s.invokeMergeTask,
	),
		dq.LogCost("fund.journal.merging"),
		dq.Concurrency(32),
	)
}

func (s *Manager) triggerMerge(ctx context.Context, at time.Time, userId string, prop fund.PropType, group string) {
	s.mgp.Submit(func() {
		if err := s.mgt.Submit(ctx, mergeWait, &mergeTask{
			At:    at,
			User:  userId,
			Prop:  prop,
			Group: group,
		}); err != nil {
			s.log.Warn("submit merge task failed", zap.Error(err))
		}
	})
}

func (s *Manager) invokeMergeTask(ctx context.Context, task *mergeTask) error {
	filter := bson.M{
		"userId":    task.User,
		"prop":      task.Prop,
		"group":     task.Group,
		"createdAt": bson.M{"$lte": task.At},
	}

	rec, err := db.DecodeOne[Record](ctx)(s.db.Collection(recordDB(task.At)).Aggregate(ctx, bson.A{
		bson.M{"$match": filter},
		bson.M{"$project": bson.M{
			"merged":  1,
			"history": "$stash",
		}},
	}))
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return err
	}

	rec.Group = task.Group
	merger := mergers[rec.GNs()]
	if merger == nil {
		return errors.New("unknown merger")
	}

	hBytes := meter.NewCounter[int]("journal.merge.history.bytes", meter.Singleton())

	reduced, merged := merger(&rec, task.At)
	if len(merged) == 0 {
		hBytes.Add(len(rec.History.Value), "act", "skip")
		return nil
	}

	delete(filter, "createdAt")
	filter["merged"] = rec.Merged
	if res, err := s.db.Collection(recordDB(task.At)).UpdateOne(ctx, filter, bson.M{
		"$set": bson.M{"createdAt": task.At},
		"$inc": bson.M{"merged": -reduced},
		"$push": bson.M{"history": bson.M{
			"$each":     merged,
			"$position": 0,
		}},
		"$pull": bson.M{"stash": bson.M{
			"time": bson.M{"$lte": task.At},
		}},
	}); err != nil {
		return err
	} else if res.MatchedCount == 0 {
		s.log.Info("raced to merge record", zap.Any("task", task))
	} else if res.ModifiedCount == 0 {
		s.log.Warn("merge record failed", zap.Any("task", task))
	}

	hBytes.Add(len(rec.History.Value), "act", "done")
	return nil
}
