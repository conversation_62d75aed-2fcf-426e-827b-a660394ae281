package fund

import (
	"context"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.Client, dm *redi.Mutex, ev ev.Bus, users user.Hook, vnd log.Vendor) (*Manager, Getter) {
	mgr := newManager(db, dm, ev, newJournals(db), vnd.Scope("fund.mgr"))
	users.PostCreate(func(ctx context.Context, acc *user.Account) error {
		_, err := mgr.Create(ctx, acc.UserId)
		return err
	})
	return mgr, mgr
}
