package fund

import "time"

type Extra map[string]string

type payOpts struct {
	time   time.Time
	trade  string
	detail string
	group  string
	extra  Extra
	target string
}

func (p *payOpts) Clone() *payOpts {
	p2 := new(payOpts)
	*p2 = *p
	return p2
}

func makePayOpts(in []PayOpt) *payOpts {
	o := new(payOpts)
	for _, opt := range in {
		opt(o)
	}
	if o.time.IsZero() {
		o.time = time.Now()
	}
	return o
}

type PayOpt func(*payOpts)

func WithTime(at time.Time) PayOpt {
	return func(opts *payOpts) {
		opts.time = at
	}
}

func WithTrade(trade string) PayOpt {
	return func(opts *payOpts) {
		opts.trade = trade
	}
}

func WithDetail(detail string) PayOpt {
	return func(opts *payOpts) {
		opts.detail = detail
	}
}

func WithGroup(ns, key string) PayOpt {
	return func(opts *payOpts) {
		opts.group = ns + "@" + key
	}
}

func WithExtra(extra Extra) PayOpt {
	return func(opts *payOpts) {
		opts.extra = extra
	}
}

func WithTarget(userId string) PayOpt {
	return func(opts *payOpts) {
		opts.target = userId
	}
}
