package fund

import (
	"fmt"

	"github.com/shopspring/decimal"
)

type Decimal = decimal.Decimal

func zero() Decimal {
	return decimal.Zero
}

func New(from any) Decimal {
	return v2dec(from)
}

func v2dec(in any) Decimal {
	switch v := in.(type) {
	case nil:
		return zero()
	case int:
		if v == 0 {
			return zero() // fast path
		}
		return decimal.NewFromInt(int64(v))
	case int64:
		if v == 0 {
			return zero() // fast path
		}
		return decimal.NewFromInt(v)
	case float32:
		if v == 0 {
			return zero() // fast path
		}
		return decimal.NewFromFloat32(v)
	case float64:
		if v == 0 {
			return zero() // fast path
		}
		return decimal.NewFromFloat(v)
	case string:
		if v == "" || v == "0" {
			return zero() // fast path
		}
		return decimal.RequireFromString(v)
	case Decimal:
		return v
	default:
		panic(fmt.Sprintf("invalid input type: %T", in))
	}
}

func Add(s *Decimal, v Decimal) {
	*s = s.Add(v)
}

func Sub(s *Decimal, v Decimal) {
	*s = s.Sub(v)
}
