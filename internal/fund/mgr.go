package fund

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrAccountNotFound = errors.New("account not found")
)

func newManager(db *db.Client, dm *redi.Mutex, ev ev.Bus, js *Journals, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Account{}, &Wallet{})
	return &Manager{
		db:  db,
		dm:  dm,
		ev:  ev,
		js:  js,
		log: log,
	}
}

type Manager struct {
	db  *db.Client
	dm  *redi.Mutex
	ev  ev.Bus
	js  *Journals
	log *zap.Logger
}

func (m *Manager) Create(ctx context.Context, userId string) (*Account, error) {
	acc := &Account{
		UserId:   userId,
		Diamonds: zero(), // 金币
		Fruits:   zero(), // 水晶
	}

	if err := db.UseTx(ctx, m.db).Create(acc).Error; err != nil {
		return nil, err
	}

	return acc, nil
}

func (m *Manager) Create2(ctx context.Context, userId string, prop PropType) (*Wallet, error) {
	acc := &Wallet{
		UserId:  userId,
		Prop:    prop,
		Balance: zero(),
	}

	if err := db.UseTx(ctx, m.db).Create(acc).Error; err != nil {
		return nil, err
	}

	return acc, nil
}

func (m *Manager) Take(ctx context.Context, userId string) (*Account, error) {
	return m.basic(ctx, userId)
}

func (m *Manager) basic(ctx context.Context, userId string) (*Account, error) {
	if acc, has := getAccount(ctx); has {
		return acc.(*Account), nil
	}

	var acc Account
	if err := db.UseTx(ctx, m.db).Where("user_id = ?", userId).Take(&acc).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAccountNotFound
		}
		return nil, err
	}

	return &acc, nil
}

func (m *Manager) Wallet(ctx context.Context, userId string, prop PropType) (*Wallet, error) {
	return m.wallet(ctx, userId, prop)
}

func (m *Manager) wallet(ctx context.Context, userId string, prop PropType) (*Wallet, error) {
	if acc, has := getAccount(ctx); has {
		return acc.(*Wallet), nil
	}

	var acc Wallet
	if err := db.UseTx(ctx, m.db).Where("user_id = ? AND prop = ?", userId, prop).Take(&acc).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAccountNotFound
		}
		return nil, err
	}

	return &acc, nil
}
