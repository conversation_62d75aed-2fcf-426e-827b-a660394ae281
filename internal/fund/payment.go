package fund

import (
	"context"
	"fmt"
)

const (
	opLock = "FUND:UPDATE:%s:%d" // userId, propType
)

// Income 收入
func (m *Manager) Income(ctx context.Context, userId string, jType JournalType, prop PropType, amount any, opts ...PayOpt) error {
	if withJournal(ctx) {
		if l, err := m.dm.Lock(ctx, fmt.Sprintf(opLock, userId, prop)); err != nil {
			return err
		} else {
			defer l.MustUnlock()
		}
	}
	opt := makePayOpts(opts)
	_, err := m.balanceInc(ctx, userId, prop, v2dec(amount), jType, opt)
	return err
}

// Expend 支出
func (m *Manager) Expend(ctx context.Context, userId string, jType JournalType, prop PropType, amount any, opts ...PayOpt) error {
	if withJournal(ctx) {
		if l, err := m.dm.Lock(ctx, fmt.Sprintf(opLock, userId, prop)); err != nil {
			return err
		} else {
			defer l.MustUnlock()
		}
	}
	opt := makePayOpts(opts)
	_, err := m.balanceDec(ctx, userId, prop, v2dec(amount), jType, opt)
	return err
}
