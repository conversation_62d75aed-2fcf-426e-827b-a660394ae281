package fund

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestOperator(t *testing.T) {
	v1 := New(1)
	Add(&v1, New(1))
	assert.Equal(t, "2", v1.String())
	Sub(&v1, New(1))
	assert.Equal(t, "1", v1.String())
}

func TestOperatorZero(t *testing.T) {
	v1 := New(0)
	Add(&v1, New(1))
	assert.Equal(t, "1", v1.String())
	v2 := New(0)
	assert.Equal(t, "0", v2.String())
}

func BenchmarkNew(b *testing.B) {
	for v := 0; v <= 1; v++ {
		b.Run(strconv.Itoa(v), func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				_ = New(v)
			}
			b.ReportAllocs()
		})
	}
}

func BenchmarkNewZero(b *testing.B) {
	b.Run("nil", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = New(nil)
		}
		b.ReportAllocs()
	})
	b.Run("int0", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = New(0)
		}
		b.ReportAllocs()
	})
	b.Run("float0", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = New(0.0)
		}
		b.ReportAllocs()
	})
	b.Run("empty", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = New("")
		}
		b.ReportAllocs()
	})
}
