package fund

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func newJournals(db *db.Client) *Journals {
	db.SyncTable(&Journal{})
	return &Journals{db: db}
}

type Journals struct {
	db *db.Client
}

func (j *Journals) record(ctx context.Context, userId string, acc bValue, jType JournalType, pType PropType, amount Decimal, at time.Time, trade, details string) error {
	jd := &Journal{
		UserId:    userId,
		Type:      jType,
		Prop:      pType,
		Amount:    amount,
		Balance:   acc.BVal(pType),
		Trade:     trade,
		Details:   details,
		CreatedAt: at.Truncate(time.Millisecond),
	}
	return db.UseTx(ctx, j.db).Table(jd.TableName()).Create(jd).Error
}
