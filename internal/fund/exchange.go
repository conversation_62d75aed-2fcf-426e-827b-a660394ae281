package fund

import (
	"context"
	"fmt"
	"math"

	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrWrongExchangeAmount = biz.NewError(biz.ErrIllegalExchange, "invalid exchange amount")
	ErrExchangeUnsupported = biz.NewError(biz.ErrIllegalExchange, "unsupported exchange type")
)

type exchangeConf map[PropType]map[PropType]float64

// 货币兑换比例
var exchangeRatio = exchangeConf{
	// 水晶可兑换
	PTypeFruits: {
		PTypeDiamond: 1, // 金币
	},
}

// ExRatio 获取兑换比例
func ExRatio(from PropType, to PropType) (float64, error) {
	ratio := exchangeRatio[from][to]
	if ratio == 0 {
		return 0, fmt.Errorf("%w: %d->%d", ErrExchangeUnsupported, from, to)
	}
	return ratio, nil
}

// Exchange 货币兑换接口
func (m *Manager) Exchange(ctx context.Context, userId string, from PropType, to PropType, amount int) (*Account, error) {
	if amount <= 0 {
		return nil, ErrWrongExchangeAmount
	}

	ratio, err := ExRatio(from, to)
	if err != nil {
		return nil, err
	}

	income := float64(amount) * ratio
	if math.Mod(income, 1.0) != 0 {
		return nil, ErrWrongExchangeAmount
	}

	if l, err := m.dm.Lock(ctx, fmt.Sprintf(opLock, userId, from)); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}

	if l, err := m.dm.Lock(ctx, fmt.Sprintf(opLock, userId, to)); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}

	var (
		acc *Account
		opt = makePayOpts(nil)
	)

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, _ *gorm.DB) error {
		// check user account
		uac, err := m.basic(ctx, userId)
		if err != nil {
			return err
		} else {
			acc = uac
		}

		// set account to operate ctx
		ctx = WithAccount(ctx, uac)

		// dec from balance1
		if _, err := m.balanceDec(ctx, userId, from, v2dec(amount), JTypeExchange, opt); err != nil {
			return err
		}

		// inc to balance2
		if _, err := m.balanceInc(ctx, userId, to, v2dec(income), JTypeExchange, opt); err != nil {
			return err
		}

		// done
		return nil
	}); err != nil {
		return nil, err
	}

	return acc, nil
}
