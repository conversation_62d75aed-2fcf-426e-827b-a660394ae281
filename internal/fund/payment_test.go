package fund

import (
	"context"
	"testing"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"go.uber.org/fx"
)

var testMod = fx.Module("fund", fx.Provide(
	redi.Provide, cron.Provide, db.ProvideGORM,
	ev.Provide, cc.Provide, user.Provide,

	Provide,
))

func TestSkipJournal(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx    = context.TODO()
			userId = "1dc5860fb3f84c4cb38ac639897376c3"
		)
		if err := mgr.Income(SkipJournal(ctx), userId, JTypeRecharge, PTypeDiamond, 1); err != nil {
			t.Fatal(err)
		}
		if err := mgr.Expend(SkipJournal(ctx), userId, JTypeRecharge, PTypeDiamond, 1); err != nil {
			t.Fatal(err)
		}
		return nil
	})
}
