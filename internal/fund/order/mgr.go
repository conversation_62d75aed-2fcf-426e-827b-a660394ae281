package order

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrOrderNotExists = errors.New("order not exists")
)

func newManager(db *db.Client, fm *fund.Manager, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Order{})
	return &Manager{
		db:  db,
		fm:  fm,
		log: log,
	}
}

type Manager struct {
	db  *db.Client
	fm  *fund.Manager
	log *zap.Logger
}

func (s *Manager) Balance(ctx context.Context, userId string, prop fund.PropType) (fund.Decimal, error) {
	fac, err := s.fm.Take(ctx, userId)
	if err != nil {
		return fund.New(nil), err
	}
	return fac.BVal(prop), nil
}

func (s *Manager) Query(ctx context.Context, appId, tradeNo string) (*Order, error) {
	var order Order
	if err := db.UseTx(ctx, s.db).Where("app_id = ? AND trade_no = ?", appId, tradeNo).Take(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrOrderNotExists
		}
		return nil, err
	}
	return &order, nil
}
