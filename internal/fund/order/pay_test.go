package order

import (
	"context"
	"errors"
	"testing"

	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.uber.org/fx"
)

var testMod = fx.Module("fund.order", ctl.DB, ctl.USR, ctl.FUND, fx.Provide(Provide))

func TestPayment(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx     = context.TODO()
			appId   = "test_app"
			tradeNo = "trade_1"
			userId  = "21ad0083d6104ed1b742ce821cc8395e"
		)
		if err := mgr.Income(ctx, appId, tradeNo, userId, fund.JTypeGameplay, fund.PTypeDiamond, 1); err != nil {
			if errors.Is(err, ErrOrderDuplicated) {
				order, err := mgr.Query(ctx, appId, tradeNo)
				if err != nil {
					return err
				}
				t.Logf("dup order: %+v", order)
				return nil
			}
			return err
		}
		return nil
	})
}
