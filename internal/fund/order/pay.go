package order

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

var (
	ErrOrderDuplicated = errors.New("duplicated order")
)

type act int

const (
	income act = iota
	expend
)

func (s *Manager) Income(ctx context.Context, appId, tradeNo string, userId string, jType fund.JournalType, prop fund.PropType, amount any, opts ...fund.PayOpt) error {
	return s.doPay(ctx, appId, tradeNo, userId, jType, prop, amount, income, s.fm.Income, opts)
}

func (s *Manager) Expend(ctx context.Context, appId, tradeNo string, userId string, jType fund.JournalType, prop fund.PropType, amount any, opts ...fund.PayOpt) error {
	return s.doPay(ctx, appId, tradeNo, userId, jType, prop, amount, expend, s.fm.Expend, opts)
}

type payAPI func(ctx context.Context, userId string, jType fund.JournalType, prop fund.PropType, amount any, opts ...fund.PayOpt) error

func (s *Manager) doPay(ctx context.Context, appId, tradeNo string, userId string, jType fund.JournalType, prop fund.PropType, amount any, act act, api payAPI, opts []fund.PayOpt) error {
	if jType == fund.JTypeGameplay {
		ctx = fund.SkipJournal(ctx)
		ctx = fund.WithEvent(ctx)
	}
	amt := fund.New(amount)
	if act == expend {
		amt = amt.Neg()
	}
	return db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Create(&Order{
			AppId:   appId,
			TradeNo: tradeNo,
			UserId:  userId,
			Type:    jType,
			Prop:    prop,
			Amount:  amt,
			Status:  StatusSuccess,
		}).Error; err != nil {
			if db.IsDuplicate(err, "orders.app_trade") {
				return ErrOrderDuplicated
			}
			return err
		}
		return api(ctx, userId, jType, prop, amount, opts...)
	})
}
