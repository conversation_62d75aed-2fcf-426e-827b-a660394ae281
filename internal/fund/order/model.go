package order

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
)

type Status int

const (
	StatusSuccess Status = 2 // 成功（目前只有这个状态）
)

type Order struct {
	ID        uint             `gorm:"primaryKey"`
	AppId     string           `gorm:"not null;size:16;uniqueIndex:app_trade"` // 应用渠道
	TradeNo   string           `gorm:"not null;size:64;uniqueIndex:app_trade"` // 交易编号
	UserId    string           `gorm:"not null;size:32"`                       // 关联用户
	Type      fund.JournalType `gorm:"not null;size:8;default:0"`              // 流水类型
	Prop      fund.PropType    `gorm:"not null;size:8;default:0"`              // 货币类型
	Amount    fund.Decimal     `gorm:"not null;type:decimal(10,2)"`            // 流水金额
	Status    Status           `gorm:"not null;size:8;default:0"`              // 订单状态
	Details   string           `gorm:"not null;size:255;default:''"`           // 详细信息
	CreatedAt time.Time        `gorm:"not null"`
}

func (o *Order) TableName() string {
	return "fund_orders"
}
