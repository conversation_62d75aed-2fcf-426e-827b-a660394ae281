package fund

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrIllegalOpsAmount = errors.New("illegal ops amount")
	ErrBalanceOpsFailed = errors.New("balance ops failed")
)

type bValue interface {
	BVal(prop PropType) Decimal
	inc(prop PropType, amount Decimal)
	dec(prop PropType, amount Decimal)
}

func (m *Manager) bAcc(ctx context.Context, userId string, prop PropType) (acc bValue, err error) {
	if prop < basicMax {
		acc, err = m.basic(ctx, userId)
	} else {
		acc, err = m.wallet(ctx, userId, prop)
	}
	return
}

type balanceOps struct {
	acc    bValue   // 结算后账户信息
	prop   PropType // 结算货币类型
	amount Decimal  // 实际操作数额
}

func (m *Manager) balanceInc(ctx context.Context, userId string, prop PropType, amount Decimal, jType JournalType, opt *payOpts) (*balanceOps, error) {
	if amount.IsNegative() || amount.IsZero() {
		return nil, ErrIllegalOpsAmount
	}

	var acc bValue
	if withJournal(ctx) {
		var err error
		acc, err = m.bAcc(ctx, userId, prop)
		if err != nil {
			return nil, err
		}
	}

	field := fieldOf(prop)
	updated := modelOf(db.UseTx(ctx, m.db), userId, prop).Update(field, gorm.Expr(fmt.Sprintf("%s + ?", field), amount))
	if updated.Error != nil {
		return nil, updated.Error
	} else if updated.RowsAffected == 0 {
		return nil, ErrBalanceOpsFailed
	}

	if acc != nil {
		acc.inc(prop, amount)
		if err := m.js.record(ctx, userId, acc, jType, prop, amount, opt.time, opt.trade, opt.detail); err != nil {
			return nil, err
		}
	}

	if acc != nil || withEvent(ctx) {
		db.AfterCommit(ctx, func(ctx context.Context) {
			m.ev.Emit(ctx, EvFundIncome, journalEv(opt, userId, jType, prop, amount))
		})
	}

	return &balanceOps{acc: acc, prop: prop, amount: amount}, nil
}

func (m *Manager) balanceDec(ctx context.Context, userId string, prop PropType, amount Decimal, jType JournalType, opt *payOpts) (*balanceOps, error) {
	if amount.IsNegative() || amount.IsZero() {
		return nil, ErrIllegalOpsAmount
	}

	var acc bValue
	if withJournal(ctx) {
		var err error
		acc, err = m.bAcc(ctx, userId, prop)
		if err != nil {
			return nil, err
		}
	}

	if acc != nil {
		if balance := acc.BVal(prop); balance.LessThan(amount) {
			return nil, BalanceNotEnoughErr(prop, amount, balance)
		}
	}

	field := fieldOf(prop)
	updated := modelOf(db.UseTx(ctx, m.db), userId, prop).Where(fmt.Sprintf("%s >= ?", field), amount).Update(field, gorm.Expr(fmt.Sprintf("%s - ?", field), amount))
	if updated.Error != nil {
		return nil, updated.Error
	} else if updated.RowsAffected == 0 {
		if acc == nil {
			return nil, ErrBalanceNotEnough
		}
		return nil, ErrBalanceOpsFailed
	}

	if acc != nil {
		acc.dec(prop, amount)
		if err := m.js.record(ctx, userId, acc, jType, prop, amount.Neg(), opt.time, opt.trade, opt.detail); err != nil {
			return nil, err
		}
	}

	if acc != nil || withEvent(ctx) {
		db.AfterCommit(ctx, func(ctx context.Context) {
			m.ev.Emit(ctx, EvFundExpend, journalEv(opt, userId, jType, prop, amount))
		})
	}

	return &balanceOps{acc: acc, prop: prop, amount: amount}, nil
}
