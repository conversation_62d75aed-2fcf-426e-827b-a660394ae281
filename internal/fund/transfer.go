package fund

import (
	"context"
	"errors"
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

type Ticket struct {
	UserId string
	Prop   PropType
	Amount any
}

func (s Ticket) sameAcc(t Ticket) bool {
	return s.UserId == t.UserId && s.Prop == t.Prop
}

func (m *Manager) Transfer(ctx context.Context, jType JournalType, from, to Ticket, opts ...PayOpt) error {
	if from.UserId == to.UserId && from.Prop == to.Prop {
		return errors.New("invalid ticket")
	}

	opt := makePayOpts(opts)

	if withJournal(ctx) {
		if l, err := m.dm.Lock(ctx, fmt.Sprintf(opLock, from.UserId, from.Prop)); err != nil {
			return err
		} else {
			defer l.MustUnlock()
		}
		if l, err := m.dm.Lock(ctx, fmt.Sprintf(opLock, to.UserId, to.Prop)); err != nil {
			return err
		} else {
			defer l.MustUnlock()
		}
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, _ *gorm.DB) error {
		// check user account
		var acc *Account
		if from.sameAcc(to) && withJournal(ctx) {
			var err error
			acc, err = m.basic(ctx, to.UserId)
			if err != nil {
				return err
			}
		}

		// set account to operate ctx
		if acc != nil {
			ctx = WithAccount(ctx, acc)
		}

		if opt.target != "" { // reset target
			opt.target = to.UserId
		}

		// dec from balance1
		if _, err := m.balanceDec(ctx, from.UserId, from.Prop, v2dec(from.Amount), jType, opt); err != nil {
			return err
		}

		opt2 := opt.Clone()

		if opt2.target != "" { // reset target
			opt2.target = from.UserId
		}

		// inc to balance2
		if _, err := m.balanceInc(ctx, to.UserId, to.Prop, v2dec(to.Amount), jType, opt2); err != nil {
			return err
		}

		// done
		return nil
	}); err != nil {
		return err
	}

	return nil
}
