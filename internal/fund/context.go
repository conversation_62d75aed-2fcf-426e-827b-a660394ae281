package fund

import (
	"context"
)

type ctxKey int

const (
	ctxAcc  ctxKey = iota // user account
	ctxNoj                // skip journal
	ctxEmit               // but with event
)

func WithAccount(ctx context.Context, acc bValue) context.Context {
	return context.WithValue(ctx, ctxAcc, acc)
}

func getAccount(ctx context.Context) (bValue, bool) {
	if acc := ctx.Value(ctxAcc); acc != nil {
		return acc.(bValue), true
	}
	return nil, false
}

func SkipJournal(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxNoj, struct{}{})
}

func withJournal(ctx context.Context) bool {
	_, ok := ctx.Value(ctxNoj).(struct{})
	return !ok
}

// WithEvent only works if SkipJournal
func WithEvent(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxEmit, struct{}{})
}

func withEvent(ctx context.Context) bool {
	_, ok := ctx.Value(ctxEmit).(struct{})
	return ok
}
