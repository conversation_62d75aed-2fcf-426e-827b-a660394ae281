package fund

import (
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

var (
	ErrBalanceNotEnough = biz.NewError(biz.ErrBalanceNotEnough, "balance not enough")
)

type BalanceNotEnoughError struct {
	raw    error
	Prop   PropType
	Expect Decimal
	Actual Decimal
}

func (e *BalanceNotEnoughError) Unwrap() error {
	return e.raw
}

func (e *BalanceNotEnoughError) Error() string {
	return fmt.Sprintf("balance not enough: expect %s, actual %s", e.Expect, e.Actual)
}

func BalanceNotEnoughErr(prop PropType, expect, actual Decimal) *BalanceNotEnoughError {
	return &BalanceNotEnoughError{
		raw:    ErrBalanceNotEnough,
		Prop:   prop,
		Expect: expect,
		Actual: actual,
	}
}
