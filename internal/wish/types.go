package wish

type Wish struct {
	<PERSON>N<PERSON>     string `json:"wishName"`     // 心愿名称
	WishStatus   int    `json:"wishStatus"`   // 心愿状态
	CurrentCount int    `json:"currentCount"` // 心愿当前值
	TargetCount  int    `json:"targetCount"`  // 心愿目标值
	GiftId       uint   `json:"giftId"`       // 礼物ID
	GiftIcon     string `json:"giftIcon"`     // 礼物图标
	GiftDiamond  int64  `json:"giftDiamond"`  // 礼物钻石价值
}
