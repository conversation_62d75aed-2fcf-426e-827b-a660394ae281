package game

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Store struct {
	mc    *db.MongoClient
	cache cc.Cache[string, *User]
}

func (m *Store) Played(ctx context.Context, userId string) ([]string, error) {
	user, err := m.User(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("find user failed: %w", err)
	}

	return user.Played(), nil
}

func (m *Store) User(ctx context.Context, userId string) (*User, error) {
	return m.cache.Get(userId)
}

func (m *Store) SetUserRole(ctx context.Context, userId string, role Role, at time.Time) (*User, error) {
	return m.ensureUser(ctx, userId, bson.M{"role": role}, nil, at)
}

func (m *Store) ensureUser(ctx context.Context, userId string, set, inc bson.M, at time.Time) (*User, error) {
	onInsert := bson.M{
		"_id":        userId,
		"role":       RoleNormal,
		"updated_at": at,
		"created_at": at,
	}

	if len(set) > 0 || len(inc) > 0 {
		if set == nil {
			set = bson.M{}
		}
		set["updated_at"] = at
	}

	for k := range set {
		delete(onInsert, k)
	}

	up := bson.M{"$setOnInsert": onInsert}

	if len(set) > 0 {
		up["$set"] = set
	}

	if len(inc) > 0 {
		up["$inc"] = inc
	}

	var out User
	if err := m.mc.Collection(UserCollection).FindOneAndUpdate(
		ctx,
		bson.M{"_id": userId},
		up,
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true)).Decode(&out); err != nil {
		return nil, fmt.Errorf("find user failed: %w", err)
	}

	m.cache.Remove(userId)
	return &out, nil
}

func (m *Store) OpenGame(ctx context.Context, acc *user.Account) bool {
	u, err := m.User(ctx, acc.UserId)
	if err != nil || u == nil {
		return false
	}

	switch u.Role {
	case RoleBlacklist:
		return false
	case RoleDeveloper, RoleWhitelist:
		return true
	case RoleNormal:
		return acc.Level >= 5 || (u.Recharged.Count >= minCount && u.Recharged.Money.GreaterThanOrEqual(minRecharged))
	default:
		return false
	}
}
