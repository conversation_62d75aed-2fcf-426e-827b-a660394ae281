package ucode

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"errors"
	"time"
)

const (
	CodeExpire = 15 * time.Second
)

func MakeUserCode(userId, appKey string) (string, error) {
	// 使用 appKey 的前16位作为 AES key
	key := []byte(appKey)
	if len(key) > 16 {
		key = key[:16]
	} else {
		// 如果 key 不足16位，进行填充
		newKey := make([]byte, 16)
		copy(newKey, key)
		key = newKey
	}

	// 创建随机盐值
	salt := make([]byte, 8)
	if _, err := rand.Read(salt); err != nil {
		return "", err
	}

	// 加密 userId
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 使用 CBC 模式
	iv := make([]byte, aes.BlockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}

	// PKCS7 填充
	padding := aes.BlockSize - (len(userId) % aes.BlockSize)
	padtext := make([]byte, len(userId)+padding)
	copy(padtext, userId)
	for i := len(userId); i < len(padtext); i++ {
		padtext[i] = byte(padding)
	}

	// 加密
	encrypted := make([]byte, len(padtext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(encrypted, padtext)

	// 组装最终数据: |8byte salt|16byte iv|encrypted userId|8byte ts|20byte hmac|
	ts := uint64(time.Now().UnixMilli())
	buf := make([]byte, 8+16+len(encrypted)+8+20)

	copy(buf[0:], salt)
	copy(buf[8:], iv)
	copy(buf[24:], encrypted)
	binary.BigEndian.PutUint64(buf[len(buf)-28:], ts)

	// 计算 HMAC
	h := hmac.New(sha1.New, []byte(appKey))
	h.Write(buf[:len(buf)-20])
	copy(buf[len(buf)-20:], h.Sum(nil))

	return base64.StdEncoding.EncodeToString(buf), nil
}

type ParsedAppCode struct {
	UserId string
	AppId  string
	Ts     int64
}

func ParseAppCode(code, appKey string) (*ParsedAppCode, error) {
	// 解码 base64
	buf, err := base64.StdEncoding.DecodeString(code)
	if err != nil {
		return nil, errors.New("invalid code")
	}

	// 检查最小长度: 8(salt) + 16(iv) + 16(min AES block) + 8(ts) + 20(hmac) = 68
	if len(buf) < 68 {
		return nil, errors.New("invalid code")
	}

	// 验证 HMAC
	h := hmac.New(sha1.New, []byte(appKey))
	h.Write(buf[:len(buf)-20])
	if !hmac.Equal(h.Sum(nil), buf[len(buf)-20:]) {
		return nil, errors.New("invalid code")
	}

	// 提取时间戳
	ts := binary.BigEndian.Uint64(buf[len(buf)-28 : len(buf)-20])

	// 检查是否过期
	if time.Now().UnixMilli()-int64(ts) > CodeExpire.Milliseconds() {
		return nil, errors.New("code expired")
	}

	// 准备 AES key
	key := []byte(appKey)
	if len(key) > 16 {
		key = key[:16]
	} else {
		newKey := make([]byte, 16)
		copy(newKey, key)
		key = newKey
	}

	// 创建解密器
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, errors.New("invalid key")
	}

	// 提取 IV
	iv := buf[8:24]

	// 获取加密的数据
	encryptedData := buf[24 : len(buf)-28]
	if len(encryptedData)%aes.BlockSize != 0 {
		return nil, errors.New("invalid encrypted data")
	}

	// 解密
	mode := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(encryptedData))
	mode.CryptBlocks(decrypted, encryptedData)

	// 移除 PKCS7 填充
	padding := int(decrypted[len(decrypted)-1])
	if padding > aes.BlockSize || padding < 1 {
		return nil, errors.New("invalid padding")
	}

	// 验证填充
	for i := len(decrypted) - padding; i < len(decrypted); i++ {
		if decrypted[i] != byte(padding) {
			return nil, errors.New("invalid padding")
		}
	}

	userId := string(decrypted[:len(decrypted)-padding])

	return &ParsedAppCode{
		UserId: userId,
		Ts:     int64(ts),
	}, nil
}
