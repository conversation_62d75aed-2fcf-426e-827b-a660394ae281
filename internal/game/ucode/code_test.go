package ucode

import (
	"testing"
	"time"
)

func TestUserCode(t *testing.T) {
	testCases := []struct {
		name     string
		userId   string
		appKey   string
		wantErr  bool
		checkErr string
	}{
		{
			name:    "normal case",
			userId:  "test_user_123",
			appKey:  "test_app_key_12345",
			wantErr: false,
		},
		{
			name:    "empty user id",
			userId:  "",
			appKey:  "test_app_key_12345",
			wantErr: false,
		},
		{
			name:     "empty app key",
			userId:   "test_user_123",
			appKey:   "",
			wantErr:  true,
			checkErr: "invalid key",
		},
		{
			name:    "short app key",
			userId:  "test_user_123",
			appKey:  "short",
			wantErr: false,
		},
		{
			name:    "long app key",
			userId:  "test_user_123",
			appKey:  "this_is_a_very_long_app_key_that_exceeds_16_bytes",
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 生成代码
			code, err := MakeUserCode(tc.userId, tc.appKey)
			if err != nil {
				if !tc.wantErr {
					t.Errorf("MakeUserCode() error = %v, wantErr %v", err, tc.wantErr)
				}
				if tc.checkErr != "" && err.Error() != tc.checkErr {
					t.Errorf("MakeUserCode() error = %v, want error %v", err, tc.checkErr)
				}
				return
			}

			// 解析代码
			parsed, err := ParseAppCode(code, tc.appKey)
			if err != nil {
				if !tc.wantErr {
					t.Errorf("ParseAppCode() error = %v, wantErr %v", err, tc.wantErr)
				}
				if tc.checkErr != "" && err.Error() != tc.checkErr {
					t.Errorf("ParseAppCode() error = %v, want error %v", err, tc.checkErr)
				}
				return
			}

			// 验证结果
			if parsed.UserId != tc.userId {
				t.Errorf("ParseAppCode() userId = %v, want %v", parsed.UserId, tc.userId)
			}

			// 验证时间戳
			now := time.Now().UnixMilli()
			if parsed.Ts > now || now-parsed.Ts > 1000 { // 允许1秒的误差
				t.Errorf("ParseAppCode() timestamp is not recent: %v", parsed.Ts)
			}
		})
	}
}

func TestInvalidCode(t *testing.T) {
	testCases := []struct {
		name    string
		code    string
		appKey  string
		wantErr string
	}{
		{
			name:    "invalid base64",
			code:    "invalid base64!@#$",
			appKey:  "test_app_key",
			wantErr: "invalid code",
		},
		{
			name:    "too short after decode",
			code:    "aGVsbG8=", // "hello" in base64
			appKey:  "test_app_key",
			wantErr: "invalid code",
		},
		{
			name:    "wrong app key",
			code:    "", // 将在运行时生成
			appKey:  "wrong_app_key",
			wantErr: "invalid code",
		},
	}

	// 生成一个有效的代码用于测试错误的 appKey
	validCode, err := MakeUserCode("test_user", "test_app_key")
	if err != nil {
		t.Fatalf("Failed to generate valid code: %v", err)
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			code := tc.code
			if tc.name == "wrong app key" {
				code = validCode
			}

			_, err := ParseAppCode(code, tc.appKey)
			if err == nil {
				t.Error("ParseAppCode() should return error")
				return
			}
			if err.Error() != tc.wantErr {
				t.Errorf("ParseAppCode() error = %v, want %v", err, tc.wantErr)
			}
		})
	}
}
