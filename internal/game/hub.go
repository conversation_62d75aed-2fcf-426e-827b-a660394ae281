package game

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type GameUpdate struct {
	ID string
	Up bson.M
}

func (m *Manager) UpdateGame(ctx context.Context, up GameUpdate) error {
	_, err := m.mc.Collection(Collection).UpdateOne(ctx, bson.M{"_id": up.ID}, up.Up)
	if err != nil {
		return fmt.Errorf("update game failed: %w", err)
	}

	return nil
}

func (m *Manager) UpdateGames(ctx context.Context, lst []GameUpdate) error {
	return m.mc.TryTxn(ctx, func(ctx context.Context) error {
		for _, item := range lst {
			if err := m.UpdateGame(ctx, item); err != nil {
				return err
			}
		}
		return nil
	})
}

func (m *Manager) ListAllGame(ctx context.Context) ([]*ListItem, error) {
	cur, err := m.mc.Collection(Collection).Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("list all game failed: %w", err)
	}

	var lst []*ListItem
	if err := cur.All(ctx, &lst); err != nil {
		return nil, fmt.Errorf("list all game failed: %w", err)
	}

	return lst, nil
}

func (m *Manager) GetGame(ctx context.Context, id string) (*ListItem, error) {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("get game failed: %w", err)
	}

	itm := &ListItem{}
	if err := m.mc.Collection(Collection).FindOne(ctx, bson.M{"_id": oid}).Decode(itm); err != nil {
		return nil, fmt.Errorf("get game failed: %w", err)
	}

	return itm, nil
}
