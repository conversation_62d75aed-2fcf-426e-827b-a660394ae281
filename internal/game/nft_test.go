package game

import (
	"fmt"
	"testing"
	"time"
)

func TestEnqueuePick(t *testing.T) {
	s := &Session{
		gameMsgs: make(map[string][]Msg),
	}

	s.<PERSON>queue("platform", "gameIdA", "userId1", "roomId")
	s.<PERSON>queue("platform", "gameIdB", "userId2", "roomId")
	s.<PERSON>("platform", "gameIdC", "userId3", "roomId")
	s.<PERSON>("platform", "gameIdD", "userId4", "roomId")
	s.<PERSON>queue("platform", "gameIdC", "userId1", "roomId")
	s.<PERSON>queue("platform", "gameIdD", "userId3", "roomId")
	s.<PERSON>queue("platform", "gameIdA", "userId2", "roomId")
	s.<PERSON>queue("platform", "gameIdC", "userId1", "roomId")
	s.<PERSON>queue("platform", "gameIdC", "userId9", "roomId")

	for i := 0; i < 9; i++ {
		msg := s.<PERSON>(time.Now())
		if msg == nil {
			t.Fatal("nil msg")
		}

		t.Logf("msg: %s %s, sent:%v", msg.GameId, msg.UserId, s.sentGames)
	}

	s.Enqueue("platform", "gameIdC", "testRemove", "roomId")

	time.Sleep(time.Second * 10)
	s.PickSend(time.Now())

	if len(s.queuedGames) != 0 {
		t.Errorf("queued games: %v", s.queuedGames)
	}
}

func TestEnqueuePick2(t *testing.T) {
	s := &Session{
		gameMsgs: make(map[string][]Msg),
	}

	s.Enqueue("platform", "A", "userId1", "roomId")
	s.Enqueue("platform", "C", "userId2", "roomId")
	s.Enqueue("platform", "D", "userId3", "roomId")
	s.Enqueue("platform", "B", "userId4", "roomId")
	s.Enqueue("platform", "G", "userId1", "roomId")
	s.Enqueue("platform", "G", "userId3", "roomId")
	s.Enqueue("platform", "A", "userId2", "roomId")
	s.Enqueue("platform", "H", "userId1", "roomId")
	s.Enqueue("platform", "A", "userId9", "roomId")
	s.Enqueue("platform", "D", "userId1", "roomId")
	s.Enqueue("platform", "F", "userId2", "roomId")
	s.Enqueue("platform", "H", "userId3", "roomId")
	s.Enqueue("platform", "H", "userId4", "roomId")
	s.Enqueue("platform", "H", "userId1", "roomId")
	s.Enqueue("platform", "C", "userId3", "roomId")
	s.Enqueue("platform", "C", "userId2", "roomId")
	s.Enqueue("platform", "D", "userId1", "roomId")
	s.Enqueue("platform", "D", "userId9", "roomId")

	var msgs []*Msg
	for i := 0; i < 19; i++ {
		msg := s.PickSend(time.Now())
		if msg != nil {
			msgs = append(msgs, msg)
		}
	}

	for _, msg := range msgs {
		fmt.Printf("%s", msg.GameId)
	}
}

func TestEnqueuePick3(t *testing.T) {
	s := &Session{
		gameMsgs: make(map[string][]Msg),
	}

	// CFDABBDDACDCADHG
	s.Enqueue("platform", "C", "userId1", "roomId")
	s.Enqueue("platform", "F", "userId2", "roomId")
	s.Enqueue("platform", "D", "userId3", "roomId")
	s.Enqueue("platform", "A", "userId4", "roomId")
	s.Enqueue("platform", "B", "userId1", "roomId")
	s.Enqueue("platform", "B", "userId3", "roomId")
	s.Enqueue("platform", "D", "userId2", "roomId")
	s.Enqueue("platform", "D", "userId1", "roomId")
	s.Enqueue("platform", "A", "userId9", "roomId")
	s.Enqueue("platform", "C", "userId1", "roomId")
	s.Enqueue("platform", "D", "userId2", "roomId")
	s.Enqueue("platform", "C", "userId3", "roomId")
	s.Enqueue("platform", "A", "userId4", "roomId")
	s.Enqueue("platform", "D", "userId1", "roomId")
	s.Enqueue("platform", "H", "userId3", "roomId")
	s.Enqueue("platform", "G", "userId2", "roomId")

	var msgs []*Msg
	for i := 0; i < 19; i++ {
		msg := s.PickSend(time.Now())
		if msg != nil {
			msgs = append(msgs, msg)
		}
	}

	for _, msg := range msgs {
		fmt.Printf("%s", msg.GameId)
	}
}
