package game

type Game interface {
	Platform() string
	Id() string
	Title() string
	Icon() string
	Mode() []Mode
}

type SessionCodeMaker interface {
	MakeSessionCode(userId string) (string, error)
}

type Platform interface {
	AppId() string
	Name() string
	List() ([]Game, error)
	Config() any
}

type Registry struct {
	platforms map[string]Platform
}

func NewRegistry() *Registry {
	return &Registry{
		platforms: make(map[string]Platform),
	}
}

func (r *Registry) RegisterPlatform(p Platform) {
	r.platforms[p.Name()] = p
}

func (r *Registry) ListGames() ([]Game, error) {
	var games []Game
	for _, p := range r.platforms {
		g, err := p.List()
		if err != nil {
			return nil, err
		}
		games = append(games, g...)
	}
	return games, nil
}

func (r *Registry) Platforms() []string {
	var platforms []string
	for k := range r.platforms {
		platforms = append(platforms, k)
	}
	return platforms
}

func (r *Registry) Platform(platform string) Platform {
	return r.platforms[platform]
}

func (r *Registry) PlatformByAppId(appId string) Platform {
	for _, p := range r.platforms {
		if p.AppId() == appId {
			return p
		}
	}
	return nil
}
