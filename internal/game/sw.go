package game

import (
	"slices"
	"strconv"

	"github.com/hashicorp/go-version"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var gameBlackUserIds []string = []string{
	"f98641461dc443aea6462cf448a22860", // android 审核
	"85af179c051b4c27b67fc7e9d68440e5", // ios 审核
	"d427c017d10b4ac7915936d6d5ce9925", // ios 审核 巴西
}

var devUserShowIds []string = []string{
	"********", // 陈阳
}

var gameWhiteShowIds []string = []string{
	"********",
	"********",
	"********",
	"********",
}

var gameBlackShowIds []string = []string{
	"********",
	"********",
	"********",
	"********",
	"********",
	"********",
	"********",
}

func ReleaseGame(uac *user.Account, devType string, ver *version.Version, buildId string) bool {
	if uac != nil {
		if slices.Contains(gameBlackUserIds, uac.UserId) {
			return false
		}
	}
	return ReleaseGame2(devType, ver, buildId)
}

func ReleaseGame2(devType string, ver *version.Version, buildId string) bool {
	switch devType {
	case "ios":
		if ver.GreaterThanOrEqual(app.V110) {
			return true
		}
		if ver.Equal(app.V100) {
			bid, err := strconv.Atoi(buildId)
			if err != nil {
				return false
			}

			if bid >= 22 {
				return true
			}
		}
	case "android":
		if ver.GreaterThan(app.V110) {
			return true
		}
	default:
		return false
	}
	return false
}
