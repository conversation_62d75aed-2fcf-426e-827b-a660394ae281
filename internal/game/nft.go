package game

import (
	"context"
	"slices"
	"sync"
	"time"

	"github.com/samber/lo"

	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"go.uber.org/zap"
)

var (
	msgExpire = time.Second * 10
)

type Msg struct {
	UserId   string
	RoomId   string
	Platform string
	GameId   string
	At       time.Time
}

type Session struct {
	mu          sync.Mutex
	sentGames   []string
	queuedGames []string
	gameMsgs    map[string][]Msg
	updatedAt   time.Time
}

func (s *Session) Enqueue(platform, gameId, userId, roomId string) {
	var (
		pid = MakePGID(platform, gameId)
		now = time.Now()
	)

	s.mu.Lock()
	defer s.mu.Unlock()

	s.gameMsgs[pid] = append(s.gameMsgs[pid], Msg{
		UserId:   userId,
		Platform: platform,
		GameId:   gameId,
		RoomId:   roomId,
		At:       now,
	})
	if !slices.Contains(s.queuedGames, pid) {
		s.queuedGames = append(s.queuedGames, pid)
	}

	s.updatedAt = now
}

func (s *Session) putSent(g string) {
	if index := slices.Index(s.sentGames, g); index != -1 {
		s.sentGames = append(s.sentGames[:index], s.sentGames[index+1:]...)
	}
	s.sentGames = append(s.sentGames, g)
}

func (s *Session) PickSend(at time.Time) *Msg {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.cleanUp(at)

	if len(s.queuedGames) == 0 {
		return nil
	}

	// 尽量发没有发过的
	for _, g := range s.queuedGames {
		if len(s.sentGames) > 0 && slices.Contains(s.sentGames, g) {
			continue
		}

		if msg := s.pickGameMsg(g); msg != nil {
			s.putSent(g)
			return msg
		}
	}

	if len(s.sentGames) > 0 {
		last := s.sentGames[len(s.sentGames)-1]

		for _, g := range s.sentGames {
			if g == last {
				continue
			}

			if index := slices.Index(s.queuedGames, g); index != -1 {
				if msg := s.pickGameMsg(g); msg != nil {
					s.putSent(g)
					return msg
				}
			}
		}
	}

	g := s.queuedGames[0]
	if msg := s.pickGameMsg(g); msg != nil {
		s.putSent(g)
		return msg
	}

	return nil
}

func (s *Session) pickGameMsg(g string) *Msg {
	msgs := s.gameMsgs[g]
	out := &msgs[len(msgs)-1]
	s.gameMsgs[g] = msgs[:len(msgs)-1]
	return out
}

func (s *Session) cleanUp(at time.Time) {
	for g, msgs := range s.gameMsgs {
		s.gameMsgs[g] = lo.Filter(msgs, func(item Msg, _ int) bool {
			return item.At.Add(msgExpire).After(at)
		})

		if len(s.gameMsgs[g]) == 0 {
			delete(s.gameMsgs, g)
			index := slices.Index(s.queuedGames, g)
			if index != -1 {
				s.queuedGames = append(s.queuedGames[:index], s.queuedGames[index+1:]...)
			}
		}
	}
}

type Status struct {
	MsgCount int
	Sent     int
	Queued   int
}

func (s *Session) Status() Status {
	s.mu.Lock()
	defer s.mu.Unlock()

	var cnt int
	for _, msgs := range s.gameMsgs {
		cnt += len(msgs)
	}

	return Status{
		MsgCount: cnt,
		Sent:     len(s.sentGames),
		Queued:   len(s.queuedGames),
	}
}

type Notifier struct {
	rc *redi.Client
	lm *live.Manager

	mu       sync.RWMutex
	sessions map[string]*Session

	msgQ dq.Queue[*Msg]

	logger *zap.Logger
}

func (n *Notifier) RegisterMsgQHandler() {
	n.msgQ.Register(func(ctx context.Context, msg *Msg) error {
		n.enqueue(ctx, msg.Platform, msg.GameId, msg.UserId, msg.RoomId)
		return nil
	})
}

func (n *Notifier) Enqueue(ctx context.Context, platform, gameId, userId, roomId string) {
	if err := n.msgQ.Submit(ctx, time.Millisecond*1500, &Msg{
		UserId:   userId,
		Platform: platform,
		GameId:   gameId,
		RoomId:   roomId,
		At:       time.Now(),
	}); err != nil {
		n.logger.Error("submit msg failed", zap.Error(err))
	}
	MetricTipMsgTotalCount.Add(1, "platform", platform)
}

func (n *Notifier) enqueue(ctx context.Context, platform, gameId, userId, roomId string) {
	ri, err := n.lm.Room2(roomId)
	if err != nil {
		n.logger.Error("get room info failed", zap.Error(err), zap.String("roomId", roomId))
		return
	}

	sid := ri.SessionId.Hex()

	n.mu.RLock()
	sm, ok := n.sessions[sid]
	n.mu.RUnlock()
	if ok {
		sm.Enqueue(platform, gameId, userId, roomId)
		return
	}

	n.mu.Lock()
	sm, ok = n.sessions[sid]
	if ok {
		n.mu.Unlock()
		sm.Enqueue(platform, gameId, userId, roomId)
		return
	}

	sm = &Session{gameMsgs: make(map[string][]Msg)}
	n.sessions[sid] = sm
	sm.Enqueue(platform, gameId, userId, roomId)
	n.mu.Unlock()
	n.logger.Debug("enqueue game msg",
		zap.String("sessionId", sid),
		zap.String("roomId", roomId),
		zap.String("userId", userId),
		zap.String("platform", platform),
		zap.String("gameId", gameId),
	)
}

func (n *Notifier) CleanUp() {
	at := time.Now()
	n.mu.Lock()

	for k, session := range n.sessions {
		if session.updatedAt.Add(msgExpire).Before(at) {
			delete(n.sessions, k)
		}
	}

	n.mu.Unlock()
}

func (n *Notifier) PickMsgs() []*Msg {
	at := time.Now()
	var msgs []*Msg
	n.mu.RLock()
	for _, s := range n.sessions {
		msg := s.PickSend(at)
		if msg != nil {
			msgs = append(msgs, msg)
		}
	}
	n.mu.RUnlock()

	return msgs
}

type NStatus struct {
	Sessions int
	MsgCount int
}

func (n *Notifier) Status() NStatus {
	n.mu.RLock()
	defer n.mu.RUnlock()

	ns := NStatus{
		Sessions: len(n.sessions),
	}

	for _, s := range n.sessions {
		st := s.Status()
		ns.MsgCount += st.MsgCount
	}

	return ns
}
