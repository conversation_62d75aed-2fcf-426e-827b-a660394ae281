package game

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.uber.org/zap"
)

const (
	keyWinningQueue    = "GAME:WINNING:QUEUE"
	keyWinningQueueMsg = "GAME:WINNING:QUEUE:MSG:%s"
	ttlWinningQueue    = time.Hour * 24

	thresholdWinningMsgQueue          = 3600
	thresholdWinningMsgQueueExtraKeep = 100
)

func (m *Manager) calcWinningNotifyWeight(amount int64, at time.Time) float64 {
	// 金额作为主要权重，时间戳作为次要因素
	return float64(amount)*1000000000.0 + float64(at.Unix())
}

func (m *Manager) calcNotifyLevel(amt int64) protocol.GameWinningLevel {
	switch {
	case amt >= 10000:
		return protocol.GameWinningLevelHigh
	case amt >= 3000:
		return protocol.GameWinningLevelMid
	case amt >= 1000:
		return protocol.GameWinningLevelLow
	default:
		return protocol.GameWinningLevelLow
	}
}

func (m *Manager) EnqueueWinning(ctx context.Context, userId, roomId, platform, gameId string, amount int64, winType WinType, at time.Time) {
	if amount < 1000 {
		return
	}

	g, err := m.Game(platform, gameId)
	if err != nil {
		m.logger.Error("get game failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
		return
	}

	if !slices.Contains(g.GameMode, ModeLive) {
		return
	}

	clientGameInfo := &protocol.Game{
		Id:         g.ID.Hex(),
		Name:       g.Name,
		Desc:       g.Desc,
		Tags:       []string{},
		Icon:       g.Icon,
		Fullscreen: false,
		Landscape:  false,
		Sort:       g.Sort,
		Config:     m.Config(platform),
		Entry:      g.Data,
	}

	if icon := WinningGameIcon(platform, gameId); icon != "" {
		clientGameInfo.Icon = icon
	}

	data, err := m.rm.BuildGameWinningNotify(ctx, userId, roomId, platform, clientGameInfo, amount, m.calcNotifyLevel(amount), winType.Int())
	if err != nil {
		m.logger.Error("build game winning notify failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
		return
	}
	bs, err := json.Marshal(data)
	if err != nil {
		m.logger.Error("marshal game winning notify failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
		return
	}

	weight := m.calcWinningNotifyWeight(amount, at)

	tx := m.rc.TxPipeline()
	tx.Set(ctx, fmt.Sprintf(keyWinningQueueMsg, data.Id), bs, ttlWinningQueue)
	tx.ZAdd(ctx, keyWinningQueue, redis.Z{Score: weight, Member: data.Id})
	pending := tx.ZCard(ctx, keyWinningQueue)
	tx.Expire(ctx, keyWinningQueue, ttlWinningQueue)
	if _, err := tx.Exec(ctx); err != nil {
		m.logger.Error("enqueue winning failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
	}

	if pending.Val() > thresholdWinningMsgQueue+thresholdWinningMsgQueueExtraKeep {
		tx := m.rc.Pipeline()
		rc := tx.ZRemRangeByRank(ctx, keyWinningQueue, 0, thresholdWinningMsgQueue-1)
		if _, err := tx.Exec(ctx); err != nil {
			m.logger.Error("enqueue winning failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
		} else {
			m.logger.Debug("winning queue is full, remove old ones", zap.Int64("pending", pending.Val()), zap.Int64("removed", rc.Val()))
		}
	}
}

func (m *Manager) SendWinningNotify(ctx context.Context) error {
	tx := m.rc.TxPipeline()
	idc := tx.ZRevRange(ctx, keyWinningQueue, 0, 0)
	pending := tx.ZCard(ctx, keyWinningQueue)
	if _, err := tx.Exec(ctx); err != nil {
		return fmt.Errorf("scan winning notify failed: %w", err)
	}

	msgIds, err := idc.Result()
	if err != nil {
		return fmt.Errorf("scan winning notify failed: %w", err)
	}

	if len(msgIds) == 0 {
		return nil
	}

	m.logger.Info("send game winning notify", zap.Int64("pending", pending.Val()))

	keys := lo.Map(msgIds, func(msgId string, _ int) string {
		return fmt.Sprintf(keyWinningQueueMsg, msgId)
	})

	bss, err := m.rc.MGet(ctx, keys...).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return fmt.Errorf("get luck draw notify failed: %w", err)
		}
	}

	pop := func(id string) {
		if err := m.rc.ZRem(ctx, keyWinningQueue, id).Err(); err != nil {
			m.logger.Error("remove winning notify failed", zap.Error(err), zap.String("id", id))
			return
		}

		_ = m.rc.Del(ctx, fmt.Sprintf(keyWinningQueueMsg, id))
	}

	msgs := make([]protocol.GameWinningNotify, 0, len(bss))
	for index, v := range bss {
		if v == nil {
			m.logger.Debug("get nil winning id notify", zap.String("data", fmt.Sprintf("%v", v)))
			pop(msgIds[index])
			continue
		}
		dataStr, is := v.(string)
		if !is {
			pop(msgIds[index])
			m.logger.Error("get winning notify failed: data not string", zap.String("data", fmt.Sprintf("%v", v)))
			continue
		}

		var data protocol.GameWinningNotify
		if err := sonic.UnmarshalString(dataStr, &data); err != nil {
			m.logger.Error("unmarshal winning notify failed", zap.Error(err), zap.String("data", dataStr))
			pop(msgIds[index])
			continue
		}

		msgs = append(msgs, data)
	}

	for _, data := range msgs {
		data := data
		m.rm.Post(func() {
			if err := m.rm.SendAll(context.Background(), protocol.NotifyGameWinning, data); err != nil {
				m.logger.Error("send winning notify failed", zap.Error(err), zap.Any("data", data))
				return
			}
			pop(data.Id)
			m.logger.Info("send game winning notify success", zap.Any("data", data))
		})
	}

	return nil
}
