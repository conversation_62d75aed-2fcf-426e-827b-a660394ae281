package game

import (
	"context"
	"slices"
	"time"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	Collection     = "game.list.items"
	UserCollection = "game.user"
)

func MakePGID(platform string, gameId string) string {
	return platform + "." + gameId
}

type Mode int

var (
	ModeLive       = Mode(2)
	ModeFullscreen = Mode(3)
)

type ListItem struct {
	ID         primitive.ObjectID  `bson:"_id"`
	Platform   string              `bson:"platform"`
	GameID     string              `bson:"game_id"`
	PGID       string              `bson:"pgid"` // platform + . + game id
	Show       bool                `bson:"show"`
	Name       string              `bson:"name"`
	RegionName map[string]string   `bson:"region_name"`
	Cover      string              `bson:"cover"` // 封面
	Icon       string              `bson:"icon"`
	GameMode   []Mode              `bson:"game_mode"`
	FV         bool                `bson:"fv"` // 全屏 处于竖屏模式
	Desc       string              `bson:"desc"`
	RegionDesc map[string]string   `bson:"region_desc"`
	Tags       []string            `bson:"tags"`
	RegionTags map[string][]string `bson:"region_tags"`
	Sorts      map[string]int      `bson:"sorts"` // 在不同分类中的顺序，如果为<0或者key没有这个分类，则不显示
	Sort       int                 `bson:"sort"`
	Offline    bool                `bson:"offline"` // 下线了
	Data       []byte              `bson:"data"`    // 列表数据
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

func (l *ListItem) LocalName(ctx context.Context) string {
	lang := i3n.UnWarp(ctx)
	if name, ok := l.RegionName[lang]; ok {
		return name
	}

	return l.RegionName["en"]
}

func (l *ListItem) LocalDesc(ctx context.Context) string {
	lang := i3n.UnWarp(ctx)
	if desc, ok := l.RegionDesc[lang]; ok {
		return desc
	}

	return l.RegionDesc["en"]
}

func (l *ListItem) LocalTags(ctx context.Context) []string {
	lang := i3n.UnWarp(ctx)
	if tags, ok := l.RegionTags[lang]; ok {
		return tags
	}

	return l.RegionTags["en"]
}

type Recharged struct {
	Money fund.Decimal `bson:"money"`
	Count int64        `bson:"count"`
}

type Role uint32

const (
	RoleNormal Role = iota
	RoleDeveloper
	RoleBlacklist
	RoleWhitelist
	RoleMax
)

func (r Role) Valid() bool {
	return r >= RoleNormal && r < RoleMax
}

func (r Role) String() string {
	if r >= RoleMax {
		return "unknown"
	}
	return []string{"normal", "developer", "blacklist", "whitelist"}[r]
}

type User struct {
	Id           string               `bson:"_id"`           // 用户id
	RecentPlayed map[string]time.Time `bson:"recent_played"` // 最近玩过的游戏, gameId -> 玩过的时间
	Recharged    Recharged            `bson:"recharged"`     // 充值金额
	Role         Role                 `bson:"role"`          // 角色, 0: 普通用户, 1: 开发者, 2: 黑名单, 3: 白名单
	CreatedAt    time.Time            `bson:"created_at"`
	UpdatedAt    time.Time            `bson:"updated_at"`
}

func (u *User) Played() []string {
	keys := make([]string, 0, len(u.RecentPlayed))
	for k := range u.RecentPlayed {
		keys = append(keys, k)
	}

	slices.SortFunc(keys, func(a, b string) int {
		return int(u.RecentPlayed[b].UnixMilli() - u.RecentPlayed[a].UnixMilli())
	})

	return keys
}

var (
	minRecharged = fund.New(2)
)

const (
	minCount = 2
)
