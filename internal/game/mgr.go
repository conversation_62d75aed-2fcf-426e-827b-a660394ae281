package game

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type ItemKey struct {
	Platform string
	GameID   string
}

type Manager struct {
	*Registry
	mc    *db.MongoClient
	rc    *redi.Client
	evb   ev.Bus
	rm    *room.Manager
	store *Store

	items    cc.Cache[ItemKey, *ListItem]
	list     cc.Cache[int, []*ListItem]
	notifier *Notifier

	exec   co.Pool
	logger *zap.Logger
}

func (m *Manager) Register(p Platform) {
	m.logger.Debug("register game platform", zap.String("platform", p.Name()))
	m.Registry.RegisterPlatform(p)
}

func (m *Manager) GetPlatform(appId string) Platform {
	return m.Registry.PlatformByAppId(appId)
}

func (m *Manager) MakeSessionCode(userId, appId string) (string, error) {
	p := m.GetPlatform(appId)
	if p == nil {
		return "", fmt.Errorf("platform not found")
	}

	if om, ok := p.(SessionCodeMaker); ok {
		return om.MakeSessionCode(userId)
	}

	return "", fmt.Errorf("platform %s does not support session code", p.Name())
}

func (m *Manager) ListItems(ctx context.Context) ([]*ListItem, error) {
	cur, err := m.mc.Collection(Collection).Find(ctx, bson.M{}, options.Find().SetSort(bson.M{"created_at": 1}))
	if err != nil {
		return nil, fmt.Errorf("find items failed: %w", err)
	}
	defer cur.Close(ctx)

	var items []*ListItem
	if err := cur.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("decode items failed: %w", err)
	}

	return items, nil
}

func (m *Manager) UpdateItem(ctx context.Context, id string, item *ListItem, at time.Time) error {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid id: %w", err)
	}

	ur, err := m.mc.Collection(Collection).UpdateByID(ctx, oid, bson.M{"$set": bson.M{
		"name":       item.Name,
		"show":       item.Show,
		"desc":       item.Desc,
		"tags":       item.Tags,
		"sort":       item.Sort,
		"updated_at": at,
	}})
	if err != nil {
		return fmt.Errorf("update item failed: %w", err)
	}

	m.logger.Info("update game item", zap.String("id", id), zap.Int64("updated", ur.ModifiedCount), zap.Time("at", at), zap.Any("item", item))
	return nil
}

func (m *Manager) ListShowItems(ctx context.Context, show *bool) ([]*ListItem, error) {
	vv, err := m.list.Get(0)
	if err != nil {
		return nil, err
	}

	if show != nil {
		vv = lo.Filter(vv, func(item *ListItem, _ int) bool {
			return item.Show == *show
		})
	}

	return vv, nil
}

func (m *Manager) SyncGames(ctx context.Context) error {
	games, err := m.Registry.ListGames()
	if err != nil {
		return err
	}

	at := time.Now()

	for _, game := range games {
		if err := m.SyncGame(ctx, game, at); err != nil {
			m.logger.Error("sync game failed", zap.Error(err), zap.String("game_id", game.Id()), zap.String("platform", game.Platform()))
		}
	}

	return nil
}

func (m *Manager) SyncGame(ctx context.Context, game Game, at time.Time) error {
	c := m.mc.Collection(Collection)

	bs, err := json.Marshal(game)
	if err != nil {
		return fmt.Errorf("marshal game failed: %w", err)
	}

	ur, err := c.UpdateOne(
		ctx,
		bson.M{"pgid": MakePGID(game.Platform(), game.Id())},
		bson.M{
			"$set": bson.M{
				"data":       bs,
				"offline":    false,
				"game_mode":  game.Mode(),
				"updated_at": at,
			},
			"$setOnInsert": bson.M{
				"_id":         primitive.NewObjectIDFromTimestamp(at),
				"platform":    game.Platform(),
				"game_id":     game.Id(),
				"name":        game.Title(),
				"icon":        game.Icon(),
				"show":        false,
				"desc":        "",
				"tags":        []string{},
				"region_name": map[string]string{},
				"region_desc": map[string]string{},
				"region_tags": map[string][]string{},
				"sort":        9999,
				"created_at":  at,
			},
		},
		options.Update().SetUpsert(true),
	)

	if err != nil {
		return fmt.Errorf("sync game failed: %w", err)
	}

	logger := m.logger.With(zap.String("game_id", game.Id()), zap.String("platform", game.Platform()), zap.String("name", game.Title()))
	if ur.UpsertedCount > 0 {
		logger.Info("insert game", zap.Any("game", game))
	} else if ur.ModifiedCount > 0 {
		logger.Info("update game", zap.Any("game", game))
	}

	return nil
}

func (m *Manager) Game(platform, gameId string) (*ListItem, error) {
	return m.items.Get(ItemKey{Platform: platform, GameID: gameId})
}

func (m *Manager) Config(platform string) any {
	p := m.Registry.Platform(platform)
	if p == nil {
		return struct{}{}
	}

	return p.Config()
}

func (m *Manager) OnWin(ctx context.Context, platform, gameId, userId, roomId string, amount int64) {
	m.exec.Submit(func() {
		m.onWin(ctx, platform, gameId, userId, roomId, amount)
	})
}

func (m *Manager) onWin(ctx context.Context, platform, gameId, userId, roomId string, amount int64) {
	at := time.Now()

	m.evb.Emit(ctx, evt.EvGameWin, &evt.WinGame{
		Platform: platform,
		GameId:   gameId,
		UserId:   userId,
		Amount:   amount,
		At:       at,
	})
}

func (m *Manager) OnPaid(ctx context.Context, platform, gameId, userId, roomId string, amount int64) {
	m.exec.Submit(func() {
		m.onPaid(ctx, platform, gameId, userId, roomId, amount)
	})
}

func (m *Manager) onPaid(ctx context.Context, platform, gameId, userId, roomId string, amount int64) {
	at := time.Now()

	if amount > 0 {
		m.evb.Emit(ctx, evt.EvGamePlay, &evt.PlayGame{
			Platform: platform,
			GameId:   gameId,
			UserId:   userId,
			At:       at,
		})

		m.evb.Emit(ctx, evt.EvGamePaid, &evt.PaidInGame{
			Platform: platform,
			GameId:   gameId,
			UserId:   userId,
			Amount:   amount,
			At:       at,
		})
	}

	m.markUserPlayed(ctx, userId, platform, gameId, at)

	if roomId != "" {
		key := fmt.Sprintf("GAME:%s:USER:TIP_INTERVAL:%s", platform+"."+gameId, userId)
		if m.rc.SetNX(ctx, key, 1, time.Minute*3).Val() {
			m.notifier.Enqueue(ctx, platform, gameId, userId, roomId)
		}
	}
}

func (m *Manager) OnGameStart(ctx context.Context, platform, gameId, userId, roomId string) {
	m.exec.Submit(func() {
		m.onGameStart(ctx, platform, gameId, userId, roomId)
	})
}

func (m *Manager) onGameStart(ctx context.Context, platform, gameId, userId, roomId string) {
	if roomId == "" {
		return
	}

	m.notifier.Enqueue(ctx, platform, gameId, userId, roomId)
}

func (m *Manager) sendMsg(ctx context.Context, msg *Msg) {
	g, err := m.Game(msg.Platform, msg.GameId)
	if err != nil {
		m.logger.Error("get game failed", zap.Error(err), zap.String("platform", msg.Platform), zap.String("gameId", msg.GameId))
		return
	}

	if !slices.Contains(g.GameMode, ModeLive) {
		return
	}

	gi := &protocol.Game{
		Id:         g.ID.Hex(),
		Name:       g.Name,
		Desc:       g.Desc,
		Tags:       []string{},
		Icon:       g.Icon,
		Fullscreen: false,
		Landscape:  false,
		Sort:       g.Sort,
		Config:     m.Config(msg.Platform),
		Entry:      g.Data,
	}

	if err := m.rm.SendUserPlayingGame(ctx, msg.UserId, msg.RoomId, msg.Platform, gi); err != nil {
		m.logger.Error(
			"send user playing game failed",
			zap.Error(err),
			zap.String("userId", msg.UserId),
			zap.String("roomId", msg.RoomId),
			zap.String("platform", msg.Platform),
			zap.String("gameId", msg.GameId),
		)
	}
}

func (m *Manager) SendGamePlaying(ctx context.Context) error {
	m.notifier.CleanUp()

	msgs := m.notifier.PickMsgs()
	m.rm.Post(func() {
		for _, msg := range msgs {
			m.sendMsg(context.TODO(), msg)
		}
	})

	st := m.notifier.Status()
	MetricTipSessionCount.Set(int64(st.Sessions))
	MetricTipRemainCount.Set(int64(st.MsgCount))
	return nil
}

func (m *Manager) SendGameWinning(ctx context.Context, userId, roomId, platform, gameId string, amount int64, winType WinType) error {
	g, err := m.Game(platform, gameId)
	if err != nil {
		m.logger.Error("get game failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
		return nil
	}

	if !slices.Contains(g.GameMode, ModeLive) {
		return nil
	}

	m.EnqueueWinning(ctx, userId, roomId, platform, gameId, amount, winType, time.Now())
	return nil
}

func (m *Manager) markUserPlayed(ctx context.Context, userId, platform, gameId string, at time.Time) {
	g, err := m.Game(platform, gameId)
	if err != nil {
		m.logger.Error("get game failed", zap.Error(err), zap.String("platform", platform), zap.String("gameId", gameId))
		return
	}

	if _, err := m.store.ensureUser(ctx, userId, bson.M{"recent_played." + g.ID.Hex(): at}, nil, at); err != nil {
		m.logger.Error("mark user played failed", zap.Error(err), zap.String("userId", userId), zap.String("platform", platform), zap.String("gameId", gameId))
	}
}
