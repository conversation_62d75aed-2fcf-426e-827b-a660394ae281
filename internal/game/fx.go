package game

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"go.uber.org/zap"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(mc *db.MongoClient, rc *redi.Client, syn cc.Sync, evb ev.Bus, rm *room.Manager, notifier *Notifier, store *Store, vnd log.Vendor) *Manager {
	mc.SyncSchema(Collection, 1,
		db.Indexer{
			Name: "platform_game_id",
			Uniq: lo.ToPtr(true),
			Keys: bson.D{
				{Key: "platform", Value: 1},
				{Key: "game_id", Value: 1},
			},
		},
		db.Indexer{
			Name: "platform_game_id_str",
			Uniq: lo.ToPtr(true),
			Keys: bson.D{
				{Key: "pgid", Value: 1},
			},
		},
	)
	m := &Manager{
		Registry: NewRegistry(),
		mc:       mc,
		rc:       rc,
		evb:      evb,
		rm:       rm,
		store:    store,
		exec:     co.Apply(co.Size(32), co.Named("game.exec"), co.LogCost("game.exec")),
		notifier: notifier,
		logger:   vnd.Scope("game"),
	}

	m.items = cc.New[ItemKey, *ListItem](1024, cc.LRU, cc.Expiration(5*time.Minute), cc.LoaderFunc(func(key ItemKey) (*ListItem, error) {
		var item ListItem
		f := bson.M{"platform": key.Platform, "game_id": key.GameID}
		if err := mc.Collection(Collection).FindOne(context.Background(), f).Decode(&item); err != nil {
			return nil, err
		}
		return &item, nil
	}))

	m.list = cc.New[int, []*ListItem](16, cc.LRU, cc.Expiration(5*time.Minute), cc.LoaderFunc(func(_ int) ([]*ListItem, error) {
		return m.ListAllGame(context.Background())
	}))

	return m
}

func ProvideStore(mc *db.MongoClient, syn cc.Sync) *Store {
	s := &Store{mc: mc}

	{
		loader := func(userId string) (*User, error) {
			return s.ensureUser(context.Background(), userId, nil, nil, time.Now())
		}

		s.cache = cc.New[string, *User](1024, cc.LRU, cc.Expiration(5*time.Minute), cc.LoaderFunc(loader), cc.WithSync(syn, "game.users"))
	}

	return s
}

func ProvideNotifier(
	rc *redi.Client,
	lm *live.Manager,
	dm *dq.Master,
	vnd log.Vendor,
) *Notifier {
	return &Notifier{
		rc:       rc,
		lm:       lm,
		msgQ:     dq.NewWith[*Msg](dm, "game.notify"),
		sessions: make(map[string]*Session),
		logger:   vnd.Scope("game.notify"),
	}
}

func InvokeSyncGames(m *Manager, sch *cron.Scheduler) error {
	task := sch.Exclusive("sync-games", func(ctx context.Context) error {
		if err := m.SyncGames(ctx); err != nil {
			m.logger.Error("sync games failed", zap.Error(err))
		}
		return nil
	})

	sch.Every(time.Minute).Do(task)
	return nil
}

func InvokeSendGamePlaying(m *Manager, sch *cron.Scheduler, n *Notifier) error {
	n.RegisterMsgQHandler()

	task := sch.Exclusive("send-game-playing", func(ctx context.Context) error {
		if err := m.SendGamePlaying(ctx); err != nil {
			m.logger.Error("send game playing failed", zap.Error(err))
		}
		return nil
	})

	sch.Every(time.Second).Do(task)

	return nil
}

func InvokeSendWinningNotify(m *Manager, sch *cron.Scheduler) error {
	task := sch.Exclusive("send-winning-notify", func(ctx context.Context) error {
		if err := m.SendWinningNotify(ctx); err != nil {
			m.logger.Error("send winning notify failed", zap.Error(err))
		}
		return nil
	})

	sch.CronWithSeconds("* * * * * *").Do(task)

	return nil
}

func InvokeEvt(evb ev.Bus, m *Manager, vnd log.Vendor) {
	evb.Watch(evt.UserRecharge, "game.onPaid", ev.NewWatcher(m.OnEvRecharge), ev.WithAsync())
	evb.Watch(evt.SellerTransfer, "game.onSellerTransfer", ev.NewWatcher(m.OnEvSellerTransfer), ev.WithAsync())
}
