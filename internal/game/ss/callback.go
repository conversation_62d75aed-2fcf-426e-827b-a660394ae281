package ss

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ds"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"

	"go.uber.org/zap"
)

type Callback struct {
	appId  string
	appKey string
	apiJwt *auth.JWT
	rc     *redi.Client
	dm     *redi.Mutex
	dc     *db.Client
	ug     user.Getter
	fg     fund.Getter
	fom    *order.Manager
	lm     *level.Manager
	gm     *game.Manager
	logger *zap.Logger
}

func (c *Callback) AppId() string {
	return c.appId
}

type SessionReq struct {
	GameId string `json:"gameId"`
	Code   string `json:"code" binding:"required"`
}

type SessionResp struct {
	OpenId   string `json:"openId"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

func (c *Callback) Session(ctx context.Context, req SessionReq) (*SessionResp, error) {
	logger := c.logger.With(
		zap.String("code", req.Code),
		zap.String("gameId", req.GameId),
	)

	uac, err := c.apiJwt.ParseToken(ctx, req.Code)
	if err != nil {
		logger.Error("parse token failed", zap.Error(err))
		return nil, err
	}

	return &SessionResp{OpenId: uac.UserId, Nickname: uac.Nickname, Avatar: uac.Avatar}, nil
}

type BalanceReq struct {
	RoomId string `json:"roomId"`
	GameId string `json:"gameId"`
	OpenId string `json:"openId" binding:"required"`
}

type BalanceResp struct {
	Balance int64 `json:"balance"`
}

func (c *Callback) Balance(ctx context.Context, req BalanceReq) (*BalanceResp, error) {
	bal, err := c.fom.Balance(ctx, req.OpenId, fund.PTypeDiamond)
	if err != nil {
		c.logger.Error("get balance failed", zap.Error(err), zap.String("openId", req.OpenId), zap.String("gameId", req.GameId), zap.String("roomId", req.RoomId))
		return nil, err
	}

	return &BalanceResp{Balance: bal.IntPart()}, nil
}

type UpdateBalanceRequest struct {
	GameId  string `json:"gameId"`
	OpenId  string `json:"openId"`
	RoomId  string `json:"roomId"` // 直播间id
	Amount  int64  `json:"amount"` // 正数为加，负数为减
	OrderId string `json:"orderId"`
	RoundId string `json:"roundId"` // 回合ID
	Desc    string `json:"desc"`
}

type UpdateBalanceResp struct {
	Balance int64 `json:"balance"`
}

func (c *Callback) ChangeBalance(ctx context.Context, req UpdateBalanceRequest) (*UpdateBalanceResp, error) {
	logger := c.logger.With(
		zap.String("userId", req.OpenId),
		zap.String("gameId", req.GameId),
		zap.String("roomId", req.RoomId),
		zap.Int64("currencyDiff", req.Amount),
		zap.String("orderId", req.OrderId),
		zap.String("roundId", req.RoundId),
		zap.String("desc", req.Desc),
	)

	g, err := c.gm.Game(Platform, req.GameId)
	if err != nil {
		return nil, fmt.Errorf("get game failed: %w", err)
	}

	l, err := c.dm.Lock(ctx, fmt.Sprintf(keyUserLock, req.OpenId))
	if err != nil {
		logger.Error("lock failed", zap.Error(err))
		return nil, err
	}
	defer l.MustUnlock()

	var (
		delta = ds.Abs(req.Amount)
		extra = fund.Extra{
			"renderRemark":  "true",
			"game.platform": Platform,
			"game.id":       req.GameId,
			"game.name":     g.Name,
			"game.roundId":  req.RoundId,
		}
		dupOrder bool
	)

	if req.Amount > 0 {
		err = c.fom.Income(context.TODO(), c.AppId(), req.OrderId, req.OpenId, fund.JTypeGameplay, fund.PTypeDiamond, delta, fund.WithExtra(extra))
	} else {
		err = c.fom.Expend(context.TODO(), c.AppId(), req.OrderId, req.OpenId, fund.JTypeGameplay, fund.PTypeDiamond, delta, fund.WithExtra(extra))
	}

	if err != nil {
		if !errors.Is(err, order.ErrOrderDuplicated) {
			return nil, fmt.Errorf("change balance failed: %w", err)
		}

		logger.Warn("order duplicated")
		dupOrder = true
	}

	bal, err := c.fom.Balance(ctx, req.OpenId, fund.PTypeDiamond)
	if err != nil {
		return nil, err
	}

	if !dupOrder {
		logger.Info("change balance success", zap.Int64("balance", bal.IntPart()))

		if req.Amount < 0 {
			c.gm.OnPaid(context.TODO(), Platform, req.GameId, req.OpenId, req.RoomId, delta)
		}

		if req.Amount > 0 {
			if err := c.gm.SendGameWinning(ctx, req.OpenId, req.RoomId, Platform, req.GameId, req.Amount, game.WinTypeNone); err != nil {
				logger.Error("send game winning failed", zap.Error(err))
			}

			c.gm.OnWin(ctx, Platform, req.GameId, req.OpenId, req.RoomId, req.Amount)
		}

	}

	return &UpdateBalanceResp{Balance: bal.IntPart()}, nil
}

type QueryOrderRequest struct {
	GameId  string `json:"gameId"`
	OpenId  string `json:"openId"`
	OrderId string `json:"orderId"`
}

type QueryOrderResponse struct {
	Status order.Status `json:"status"` // 订单状态
}

func (c *Callback) QueryOrder(ctx context.Context, req QueryOrderRequest) (*QueryOrderResponse, error) {
	order, err := c.fom.Query(context.TODO(), req.OpenId, req.OrderId)
	if err != nil {
		c.logger.Error("query order failed", zap.Error(err), zap.String("openId", req.OpenId), zap.String("orderId", req.OrderId))
		return nil, err
	}
	return &QueryOrderResponse{Status: order.Status}, nil
}

type PushEventRequest struct {
	GameId string `json:"gameId"`
	OpenId string `json:"openId"`
	RoomId string `json:"roomId"`
	Event  string `json:"event"`
	Data   string `json:"data"`
	Ts     int64  `json:"ts"` // ms timestamp
}

type PushEventResponse struct {
}

func (c *Callback) OnPushEvent(ctx context.Context, req PushEventRequest) (*PushEventResponse, error) {
	c.logger.Info("on push event",
		zap.String("openId", req.OpenId),
		zap.String("gameId", req.GameId),
		zap.String("event", req.Event),
		zap.String("data", req.Data),
		zap.Int64("ts", req.Ts),
	)

	if req.Event == "evtOpenGame" {
		evt := struct {
			RoomId string `json:"roomId"`
		}{}

		if err := json.Unmarshal([]byte(req.Data), &evt); err != nil {
			c.logger.Error("parse open game data faied", zap.Error(err))
		} else {
			c.gm.OnGameStart(ctx, Platform, req.GameId, req.OpenId, evt.RoomId)
		}
	}

	return &PushEventResponse{}, nil
}
