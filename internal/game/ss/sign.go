package ss

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
)

// SignFunc 签名函数类型
type SignFunc func(timestamp string, path string, body []byte) string

// NewSigner 创建一个签名器
func NewSigner(appId, appKey string) SignFunc {
	return func(timestamp string, path string, body []byte) string {
		// 1. 组织待签名字符串
		elements := []string{
			fmt.Sprintf("appId=%s", appId),
			fmt.Sprintf("timestamp=%s", timestamp),
			fmt.Sprintf("path=%s", path),
			fmt.Sprintf("body=%s", string(body)),
		}

		// 2. 按字典序排序
		sort.Strings(elements)

		// 3. 用&连接所有参数
		signContent := strings.Join(elements, "&")

		// 4. 使用 HMAC-SHA256 算法签名
		h := hmac.New(sha256.New, []byte(appKey))
		h.Write([]byte(signContent))

		// 5. 转为16进制字符串
		return hex.EncodeToString(h.Sum(nil))
	}
}

// VerifyFunc 签名验证函数类型
type VerifyFunc func(timestamp string, path string, body []byte, signature string) bool

// NewVerifier 创建一个签名验证器
func NewVerifier(appId, appKey string) VerifyFunc {
	signer := NewSigner(appId, appKey)

	return func(timestamp string, path string, body []byte, signature string) bool {
		expectedSign := signer(timestamp, path, body)
		return hmac.Equal([]byte(expectedSign), []byte(signature))
	}
}

// 添加错误常量
const (
	ErrMissingTimestamp = "missing timestamp"
	ErrMissingSignature = "missing signature"
	ErrInvalidSignature = "invalid signature"
)

// NewVerifierMiddleware 创建一个签名验证中间件
//
//	r.Header.Set("X-App-Id", c.appId)
//	r.Header.Set("X-Timestamp", timestamp)
//	r.Header.Set("X-Sign", signature)
func NewVerifierMiddleware(appId, appKey string) gin.HandlerFunc {
	verifier := NewVerifier(appId, appKey)
	return func(ctx *gin.Context) {
		appId2 := ctx.GetHeader("X-App-Id")
		if appId2 == "" {
			ctx.AbortWithStatusJSON(http.StatusBadRequest, Response[struct{}]{
				Code: 40000,
				Msg:  "missing app id",
			})
			return
		}
		if appId2 != appId {
			ctx.AbortWithStatusJSON(http.StatusBadRequest, Response[struct{}]{
				Code: 40000,
				Msg:  "app id not match",
			})
			return
		}

		timestamp := ctx.GetHeader("X-Timestamp")
		if timestamp == "" {
			ctx.AbortWithStatusJSON(http.StatusBadRequest, Response[struct{}]{
				Code: 40001,
				Msg:  ErrMissingTimestamp,
			})
			return
		}

		signature := ctx.GetHeader("X-Sign")
		if signature == "" {
			ctx.AbortWithStatusJSON(http.StatusBadRequest, Response[struct{}]{
				Code: 40002,
				Msg:  ErrMissingSignature,
			})
			return
		}

		// 读取和重置 body
		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			ctx.AbortWithStatusJSON(http.StatusBadRequest, Response[struct{}]{
				Code: 40003,
				Msg:  err.Error(),
			})
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		// 验证签名
		if !verifier(timestamp, ctx.Request.URL.Path, body, signature) {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, Response[struct{}]{
				Code: 40004,
				Msg:  ErrInvalidSignature,
			})
			return
		}

		ctx.Next()
	}
}
