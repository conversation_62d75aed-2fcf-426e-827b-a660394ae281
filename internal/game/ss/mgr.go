package ss

import (
	"fmt"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/internal/game/ucode"
)

type Manager struct {
	cfg    *conf.SSGame
	gc     cc.Cache[int, []GameItem]
	logger *zap.Logger
}

func (m *Manager) Name() string {
	return Platform
}

func (m *Manager) AppId() string {
	return m.cfg.AppId
}

func (m *Manager) list() ([]GameItem, error) {
	forest := GameItem{
		Name:        "Forest",
		GameID:      "9001",
		DownloadURL: "https://godzilla-live-oss.kako.live/game/files/slwh_20250508.zip",
		GameVersion: "0.5.8",
		MD5:         "68da4d88e464510a04e97a2997a93d2b",
	}

	if dbg.Ing() {
		forest.DownloadURL = "https://godzilla-live-oss.kako.live/game/files/slwh-test.zip"
		forest.GameVersion = "0.0.4"
		forest.MD5 = "a9884858b620f2dd3f179147a3306484"
	}

	gopher := GameItem{
		Name:   "Gopher",
		GameID: "9002",
	}

	return []GameItem{forest, gopher}, nil
}

func (m *Manager) List2() ([]GameItem, error) {
	return m.gc.Get(0)
}

func (m *Manager) Game(gameId string) (*GameItem, error) {
	items, err := m.List2()
	if err != nil {
		return nil, err
	}
	for _, item := range items {
		if item.GameID == gameId {
			return &item, nil
		}
	}
	return nil, fmt.Errorf("game %s not found", gameId)
}

type GameOverview = GameItem

func (g GameOverview) Platform() string {
	return Platform
}

func (g GameOverview) Title() string {
	return g.Name
}

func (g GameOverview) Id() string {
	return g.GameID
}

func (g GameOverview) Icon() string {
	return ""
}

func (g GameOverview) Mode() []game.Mode {
	return []game.Mode{game.ModeLive}
}

func (m *Manager) List() ([]game.Game, error) {
	items, err := m.gc.Get(0)
	if err != nil {
		return nil, err
	}

	games := make([]game.Game, 0, len(items))
	for _, item := range items {
		games = append(games, GameOverview(item))
	}

	return games, nil
}

type Config struct {
	AppId string `json:"app_id"`
}

func (m *Manager) Config() any {
	return &Config{
		AppId: m.cfg.AppId,
	}
}

func (m *Manager) MakeSessionCode(userId string) (string, error) {
	return ucode.MakeUserCode(userId, m.cfg.AppKey)
}
