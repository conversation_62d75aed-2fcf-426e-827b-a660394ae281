package ss

import (
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(desc *conf.Setting, vnd log.Vendor) *Manager {
	config := desc.Game.SS

	m := &Manager{cfg: &config, logger: vnd.Scope("game.ss")}
	m.gc = cc.New[int, []GameItem](0, cc.Simple, cc.LoaderExpireFunc(func(_ int) ([]GameItem, time.Duration, error) {
		lst, err := m.list()
		if err != nil {
			return nil, 0, err
		}

		if len(lst) == 0 {
			return lst, 0, nil
		}

		return lst, time.Minute * 30, nil
	}))

	return m
}

func Invoke(
	desc *conf.Setting,
	rc *redi.Client,
	dc *db.Client,
	dm *redi.Mutex,
	apiJwt *auth.JWT,
	r *api.Router,
	ug user.Getter,
	om *order.Manager,
	lm *level.Manager,
	fg fund.Getter,
	gm *game.Manager,
	vnd log.Vendor,
) error {
	cb := &Callback{
		appId:  desc.Game.SS.AppId,
		appKey: desc.Game.SS.AppKey,
		rc:     rc,
		dc:     dc,
		dm:     dm,
		apiJwt: apiJwt,
		ug:     ug,
		fg:     fg,
		fom:    om,
		lm:     lm,
		gm:     gm,
		logger: vnd.Scope("api.ss"),
	}

	g := r.Group("/game/ss/", NewVerifierMiddleware(desc.Game.SS.AppId, desc.Game.SS.AppKey))
	{
		g.POST("/session", Generic(cb.Session))
		g.POST("/balance", Generic(cb.Balance))
		g.POST("/balance/change", Generic(cb.ChangeBalance))
		g.POST("/order/query", Generic(cb.QueryOrder))
		g.POST("/event/push", Generic(cb.OnPushEvent))
	}

	return nil
}

func Register(gm *game.Manager, mgr *Manager) {
	gm.RegisterPlatform(mgr)
	game.RegisterWinningIcon(Platform, winningGameIcons())
}
