package bs

import (
	"fmt"
	"strconv"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

type Manager struct {
	cli    *Client
	cfg    *conf.BaiShun
	gc     cc.Cache[int, []GameItem]
	logger *zap.Logger
}

func (m *Manager) Name() string {
	return Platform
}

func (m *Manager) AppId() string {
	return strconv.Itoa(m.cfg.AppId)
}

func (m *Manager) list() ([]GameItem, error) {
	resp, err := m.cli.GameList(GameTypeLive)
	if err != nil {
		return nil, fmt.Errorf("%s game list failed: %w", Platform, err)
	}

	m.logger.Info("game list", zap.Any("resp", resp))

	return resp.Data, nil
}

func (m *Manager) List2() ([]GameItem, error) {
	return m.gc.Get(0)
}

func (m *Manager) Game(gameId int) (*GameItem, error) {
	items, err := m.List2()
	if err != nil {
		return nil, err
	}
	for _, item := range items {
		if item.GameID == gameId {
			return &item, nil
		}
	}
	return nil, fmt.Errorf("game %d not found", gameId)
}

type GameOverview = GameItem

func (g GameOverview) Platform() string {
	return Platform
}

func (g GameOverview) Title() string {
	return g.Name
}

func (g GameOverview) Id() string {
	return strconv.Itoa(g.GameID)
}

func (g GameOverview) Icon() string {
	return g.PreviewURL
}

func (g GameOverview) Mode() []game.Mode {
	return []game.Mode{game.ModeLive}
}

func (m *Manager) List() ([]game.Game, error) {
	items, err := m.gc.Get(0)
	if err != nil {
		return nil, err
	}

	games := make([]game.Game, 0, len(items))
	for _, item := range items {
		games = append(games, GameOverview(item))
	}

	return games, nil
}

type Config struct {
	AppId      int    `json:"app_id"`
	AppChannel string `json:"app_channel"`
	GSP        int    `json:"gsp"`
}

func (m *Manager) Config() any {
	return &Config{
		AppId:      m.cfg.AppId,
		GSP:        m.cfg.GSP,
		AppChannel: m.cfg.Channel,
	}
}
