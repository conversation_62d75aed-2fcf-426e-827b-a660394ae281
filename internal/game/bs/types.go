package bs

import (
	"fmt"
)

type RequestSignature struct {
	AppId int `json:"app_id" binding:"required"`
	Signature
}

func (b RequestSignature) Check(appId int, appKey string) error {
	if b.AppId != appId {
		return fmt.<PERSON><PERSON>rf("app id not match")
	}

	if !b.Signature.Valid(appKey) {
		return fmt.<PERSON><PERSON>rf("signature invalid")
	}

	return nil
}

type GameType int

const (
	GameTypeLive GameType = 2
	GameTypeGame GameType = 3
)

type GameItem struct {
	GameID          int    `json:"game_id"`
	Name            string `json:"name"`
	PreviewURL      string `json:"preview_url"`
	GameVersion     string `json:"game_version"`
	DownloadURL     string `json:"download_url"`
	GameMode        []int  `json:"game_mode"`
	GameOrientation int    `json:"game_orientation"`
	SafeHeight      int    `json:"safe_height"`
	VenueLevel      []int  `json:"venue_level"`
}
