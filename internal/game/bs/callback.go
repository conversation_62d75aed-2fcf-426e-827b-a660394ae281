package bs

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ds"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	SafeDeposit = 1000000
	keyDeposit  = "GAME:BS:DEPOSIT"
)

var (
	ErrUserNotMatch = biz.NewError(biz.ErrInvalidParam, "user_id not match")
)

type Callback struct {
	appId  int
	appKey string
	apiJwt *auth.JWT
	ssJwt  *auth.JWT
	rc     *redi.Client
	dm     *redi.Mutex
	dc     *db.Client
	fg     fund.Getter
	om     *order.Manager
	lm     *level.Manager
	gm     *game.Manager
	logger *zap.Logger
}

func (c *Callback) BSAppId() string {
	return "BS_" + strconv.Itoa(c.appId)
}

type GetSSTokenReq struct {
	UserId string `json:"user_id" binding:"required"`
	Code   string `json:"code" binding:"required"`
	RequestSignature
}

type SSToken struct {
	SSToken    string `json:"ss_token"`
	ExpireDate int64  `json:"expire_date"` // ms timestamp
}

func (c *Callback) GetSSToken(ctx context.Context, req GetSSTokenReq) (*SSToken, error) {
	logger := c.logger.With(
		zap.String("userId", req.UserId),
		zap.String("code", req.Code),
	)
	logger.Debug("get ss token")

	uac, err := c.apiJwt.ParseToken(ctx, req.Code)
	if err != nil {
		logger.Error("parse token failed", zap.Error(err))
		return nil, err
	}

	if req.UserId != uac.UserId {
		logger.Error("user not match")
		return nil, ErrUserNotMatch
	}

	token, t, err := c.ssJwt.TokenGenerator(ctx, uac)
	if err != nil {
		logger.Error("generate token failed", zap.Error(err))
		return nil, err
	}

	return &SSToken{SSToken: token, ExpireDate: t.UnixMilli()}, nil
}

type UpdateSSTokenReq struct {
	UserId  string `json:"user_id" binding:"required"`
	SSToken string `json:"ss_token" binding:"required"`
	RequestSignature
}

func (c *Callback) UpdateSSToken(ctx context.Context, req UpdateSSTokenReq) (*SSToken, error) {
	logger := c.logger.With(
		zap.String("userId", req.UserId),
		zap.String("ssToken", req.SSToken),
	)
	logger.Debug("update ss token")

	uac, err := c.ssJwt.ParseToken(ctx, req.SSToken)
	if err != nil {
		logger.Error("parse token failed", zap.Error(err))
		return nil, err
	}

	if req.UserId != uac.UserId {
		logger.Error("user not match")
		return nil, ErrUserNotMatch
	}

	token, t, err := c.ssJwt.TokenGenerator(ctx, uac)
	if err != nil {
		logger.Error("generate token failed", zap.Error(err))
		return nil, err
	}

	return &SSToken{SSToken: token, ExpireDate: t.UnixMilli()}, nil
}

type BalanceInfo struct {
	Name           string  `json:"name"`            // 货币名称
	CurrencyType   int     `json:"currency_type"`   // 货币类型
	CurrencyAmount float64 `json:"currency_amount"` // 货币余额
}

type User struct {
	UserId      string       `json:"user_id"`
	UserName    string       `json:"user_name"`
	UserAvatar  string       `json:"user_avatar"`
	Balance     float64      `json:"balance"`
	BalanceList *BalanceInfo `json:"balance_list,omitempty"`
	UserType    int          `json:"user_type"`              // 1:普通⽤⼾，2:⽩名单⽤⼾，3:⿊名单⽤⼾
	ReleaseCond *int         `json:"release_cond,omitempty"` // 该值⼤于等于0，user_type 在⾮0条件下，转变为普通⽤⼾的输赢游戏币数量
}

type GetUserInfoReq struct {
	UserId   string `json:"user_id"`
	SSToken  string `json:"ss_token"`
	ClientIP string `json:"client_ip"`
	GameId   int    `json:"game_id"`
	RequestSignature
}

func (c *Callback) GetUserInfo(ctx context.Context, req GetUserInfoReq) (*User, error) {
	logger := c.logger.With(
		zap.String("userId", req.UserId),
		zap.String("ssToken", req.SSToken),
		zap.String("clientIP", req.ClientIP),
		zap.Int("gameId", req.GameId),
	)
	logger.Debug("get user info")

	uac, err := c.ssJwt.ParseToken(ctx, req.SSToken)
	if err != nil {
		logger.Error("parse token failed", zap.Error(err))
		return nil, err
	}

	if req.UserId != uac.UserId {
		logger.Error("user not match")
		return nil, ErrUserNotMatch
	}

	acc, err := c.fg.Take(ctx, uac.UserId)
	if err != nil {
		logger.Error("take account failed", zap.Error(err))
		return nil, err
	}

	return &User{
		UserId:     uac.UserId,
		UserName:   uac.Nickname,
		UserAvatar: uac.Avatar,
		Balance:    float64(acc.BVal(fund.PTypeDiamond).IntPart()),
		UserType:   1,
	}, nil
}

// 特别注意：任何错误，错误码（code）都不要为 0，错误码 0 代表成功，余额不⾜错误码为：1008。
// 特别注意：该接⼝要做并发处理的保护，对于单个⽤⼾，该接⼝可能在⼀秒钟内多次调⽤，玩家游戏
// 币的修改要有锁机制。

type ChangeBalanceRequest struct {
	UserId       string `json:"user_id" binding:"required"`
	SSToken      string `json:"ss_token" binding:"required"`
	CurrencyDiff int64  `json:"currency_diff" binding:"required"`  // 变更的游戏币，负值减少， 正值增加
	DiffMsg      string `json:"diff_msg" binding:"required"`       // 变化原因，"bet"，"result", "refund"
	GameId       int    `json:"game_id" binding:"required"`        // 游戏 id， BAISHUN 提供的⼩游戏 ID
	GameRoundId  string `json:"game_round_id"`                     // 游戏局 ID
	RoomId       string `json:"room_id" binding:"required"`        // 房间 ID
	ChangeTimeAt int64  `json:"change_time_at" binding:"required"` // 变更时间，ms 时间戳
	OrderID      string `json:"order_id" binding:"required"`       // 订单唯一id
	Extend       string `json:"extend"`                            // 扩展字段
	MsgType      string `json:"msg_type"`                          // 消息类型，"bet"，"result", "refund"
	CurrencyType int    `json:"currency_type"`                     // 货币类型
	RequestSignature
}

type ChangeBalanceRespData struct {
	CurrencyBalance float64 `json:"currency_balance"`
}

func (c *Callback) ChangeBalance(ctx context.Context, req ChangeBalanceRequest) (*ChangeBalanceRespData, error) {
	logger := c.logger.With(
		zap.String("userId", req.UserId),
		zap.String("ssToken", req.SSToken),
		zap.Int64("currencyDiff", req.CurrencyDiff),
		zap.String("diffMsg", req.DiffMsg),
		zap.Int("gameId", req.GameId),
		zap.String("gameRoundId", req.GameRoundId),
		zap.String("roomId", req.RoomId),
		zap.Int64("changeTimeAt", req.ChangeTimeAt),
		zap.String("orderId", req.OrderID),
		zap.String("extend", req.Extend),
		zap.String("msgType", req.MsgType),
		zap.Int("currencyType", req.CurrencyType),
	)

	logger.Debug("change balance")

	uac, err := c.ssJwt.ParseToken(ctx, req.SSToken)
	if err != nil {
		logger.Error("parse token failed", zap.Error(err))
		return nil, err
	}

	if uac.UserId != req.UserId {
		logger.Error("user not match")
		return nil, ErrUserNotMatch
	}

	game, err := c.gm.Game(Platform, strconv.Itoa(req.GameId))
	if err != nil {
		return nil, fmt.Errorf("get game failed: %w", err)
	}

	l, err := c.dm.Lock(ctx, fmt.Sprintf(keyUserLock, req.UserId))
	if err != nil {
		logger.Error("lock failed", zap.Error(err))
		return nil, err
	}
	defer l.MustUnlock()

	var (
		balance float64
		dup     bool
	)

	extra := fund.Extra{
		"renderRemark":  "true",
		"game.platform": Platform,
		"game.id":       strconv.Itoa(req.GameId),
		"game.name":     game.Name,
		"game.roundId":  req.GameRoundId,
	}

	if err := db.Transaction(ctx, c.dc, func(ctx context.Context, tx *gorm.DB) error {
		var err2 error
		if req.CurrencyDiff > 0 {
			err2 = c.om.Income(ctx,
				c.BSAppId(),
				req.OrderID,
				req.UserId,
				fund.JTypeGameplay,
				fund.PTypeDiamond,
				req.CurrencyDiff,
				fund.WithExtra(extra),
			)
		} else {
			err2 = c.om.Expend(ctx,
				c.BSAppId(),
				req.OrderID,
				req.UserId,
				fund.JTypeGameplay,
				fund.PTypeDiamond,
				-req.CurrencyDiff,
				fund.WithExtra(extra),
			)
		}
		if err2 != nil {
			if !errors.Is(err2, order.ErrOrderDuplicated) {
				return fmt.Errorf("change balance failed: %w", err2)
			}
			logger.Warn("order duplicated")
			dup = true
		} else {
			if req.DiffMsg == "bet" && req.CurrencyDiff < 0 {
				if exp := int(-req.CurrencyDiff) / 10; exp > 0 {
					if _, err := c.lm.AddExpInRoom(ctx, req.UserId, exp, req.RoomId); err != nil {
						logger.Error("add exp in room failed", zap.Error(err))
						return fmt.Errorf("change balance failed: %w", err)
					}
				}
			}
		}

		acc, err := c.fg.Take(ctx, req.UserId)
		if err != nil {
			return fmt.Errorf("retake account failed: %w", err)
		}
		balance = float64(acc.BVal(fund.PTypeDiamond).IntPart())
		return nil
	}); err != nil {
		if errors.Is(err, fund.ErrBalanceNotEnough) {

			logger.Info("balance not enough", zap.Error(err))
			return nil, biz.NewError(1008, "balance not enough")
		}

		logger.Error("change balance failed", zap.Error(err))

		return nil, err
	}

	if !dup {
		if nv, err := c.rc.IncrBy(ctx, keyDeposit, req.CurrencyDiff).Result(); err != nil {
			logger.Error("incr deposit failed", zap.Error(err))
		} else {
			if ds.Abs(nv) > SafeDeposit {
				logger.Info("deposit is not safe", zap.Int64("deposit", nv))
			}
		}
		logger.Info("change balance success", zap.Float64("balance", balance))
	}

	if req.DiffMsg == "bet" {
		c.gm.OnPaid(ctx, Platform, strconv.Itoa(req.GameId), req.UserId, req.RoomId, -req.CurrencyDiff)
	}

	return &ChangeBalanceRespData{CurrencyBalance: balance}, nil
}

type GameEvent struct {
	UserId string `json:"uid"`
	GameId string `json:"gameId"`
	Event  string `json:"event"`
	RoomId string `json:"roomID"`
	RequestSignature
}

func (c *Callback) onEvent(ctx context.Context, req GameEvent) (*api.EmptyResp, error) {
	c.logger.Info("on event", zap.Any("event", req))

	if req.Event == "game_start" {
		c.gm.OnGameStart(ctx, Platform, req.GameId, req.UserId, req.RoomId)
	}

	return nil, nil
}
