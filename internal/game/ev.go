package game

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var (
	d2usd = fund.New(10000)
)

// on event: evt.UserRecharge
func (m *Manager) OnEvRecharge(ctx context.Context, o *evt.UserRechargeData) error {
	m.incrUserRecharged(
		ctx,
		o.UserId,
		fund.New(o.Amount).Div(d2usd),
		"OnPaid",
		o.At,
	)
	return nil
}

// evt.SellerTransfer
func (m *Manager) OnEvSellerTransfer(ctx context.Context, o *evt.SellerTransferData) error {
	m.incrUserRecharged(
		ctx,
		o.To,
		fund.New(o.Amount).Div(d2usd),
		"OnSellerTransfer",
		o.At,
	)
	return nil
}

func (m *Manager) incrUserRecharged(ctx context.Context, userId string, money fund.Decimal, scene string, at time.Time) {
	if _, err := m.store.ensureUser(ctx, userId, nil, bson.M{"recharged.money": money, "recharged.count": 1}, at); err != nil {
		m.logger.Error("update user recharged failed", zap.Error(err), zap.String("userId", userId), zap.String("money", money.String()), zap.String("scene", scene))
	} else {
		m.logger.Info("update user recharged", zap.String("userId", userId), zap.String("money", money.String()), zap.String("scene", scene))
	}
}
