package jing

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
)

const (
	PirateParkGameId = 9999
)

type Manager struct {
}

func (m *Manager) List() ([]game.Game, error) {
	g := GameItem{
		GameID:     PirateParkGameId,
		Name:       "Pirate Park",
		PreviewURL: "https://godzilla-live-oss.kako.live/banner/laba.png",
		IconUrl:    "https://godzilla-live-oss.kako.live/banner/laba-icon.png",
		Url:        "http://laba.kako.live/login/kako/laba/Default.aspx",
	}

	return []game.Game{GameOverview(g)}, nil
}

func (*Manager) Name() string {
	return Platform
}

func (m *Manager) AppId() string {
	return "_jing_"
}

func (*Manager) Config() any {
	return struct{}{}
}
