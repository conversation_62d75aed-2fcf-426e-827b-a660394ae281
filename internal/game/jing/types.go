package jing

import (
	"strconv"

	"gitlab.sskjz.com/overseas/live/osl/internal/game"
)

type GameItem struct {
	GameID     int    `json:"game_id"`
	Name       string `json:"name"`
	IconUrl    string `json:"icon"`
	PreviewURL string `json:"preview_url"`
	Url        string `json:"url"`
}

type GameOverview = GameItem

func (g GameOverview) Platform() string {
	return Platform
}

func (g GameOverview) Title() string {
	return g.Name
}

func (g GameOverview) Id() string {
	return strconv.Itoa(g.GameID)
}

func (g GameOverview) Icon() string {
	return g.IconUrl
}

func (g GameOverview) Mode() []game.Mode {
	return []game.Mode{game.ModeFullscreen}
}
