package bs2

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

func Post[RESP any](r *resty.Client, url string, req any) (*RESP, error) {
	var out RESP
	resp, err := r.R().SetHeader("Content-Type", "application/json").SetBody(req).SetResult(&out).Post(url)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode()/100 != 2 {
		return nil, fmt.Errorf("status code: %d", resp.StatusCode())
	}

	return &out, nil
}

type Response[T any] struct {
	Code     int    `json:"code"`
	Msg      string `json:"msg"`
	UniqueId string `json:"unique_id"`
	Data     T      `json:"data"`
}

const (
	keyApp = "_bs_app_info"
)

type appInfo struct {
	id  int
	key string
}

func WithAppInfo(id int, key string) func(*gin.Context) {
	return func(ctx *gin.Context) {
		ctx.Set(keyApp, appInfo{id: id, key: key})
		ctx.Next()
	}
}

func getApp(ctx *gin.Context) appInfo {
	v, has := ctx.Get(keyApp)
	if !has {
		return appInfo{}
	}
	return v.(appInfo)
}

type Handler[REQ, RESP any] func(context.Context, REQ) (RESP, error)

type Validator interface {
	Check(appId int, appKey string) error
}

func Generic[REQ Validator, RESP any](fn Handler[REQ, RESP]) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		id := strings.Replace(uuid.New().String(), "-", "", -1)

		var req REQ
		if err := ctx.ShouldBindJSON(&req); err != nil {
			ctx.JSON(200, &Response[struct{}]{Code: biz.ErrInvalidParam, Msg: "invalid param", UniqueId: id})
			return
		}

		app := getApp(ctx)
		if app.id == 0 || app.key == "" {
			ctx.JSON(200, &Response[struct{}]{Code: biz.ErrSystem, Msg: "internal error", UniqueId: id})
			return
		}

		if err := req.Check(app.id, app.key); err != nil {
			ctx.JSON(200, &Response[struct{}]{Code: biz.ErrInvalidParam, Msg: "invalid signature", UniqueId: id})
			return
		}

		resp, err := fn(ctx, req)
		if err != nil {
			if be, is := biz.As(err); is {
				ctx.JSON(200, &Response[struct{}]{Code: be.Code, Msg: be.Msg, UniqueId: id})
				return
			}

			ctx.JSON(200, &Response[struct{}]{Code: biz.ErrSystem, Msg: "internal error", UniqueId: id})
			return
		}
		ctx.JSON(200, &Response[RESP]{Code: 0, Msg: "succeed", Data: resp, UniqueId: id})
	}
}
