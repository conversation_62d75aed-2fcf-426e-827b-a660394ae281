package bs2

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

type Client struct {
	baseUrl string
	channel string
	appId   int
	appKey  string
	rc      *resty.Client
}

func NewClient(baseUrl, channel string, appId int, appKey string, logger *zap.Logger) *Client {
	return &Client{
		baseUrl: baseUrl,
		channel: channel,
		appId:   appId,
		appKey:  appKey,
		rc:      resty.New().SetDebug(false).SetTimeout(5 * time.Second).SetRetryCount(3).SetBaseURL(baseUrl).SetLogger(logger.Sugar()),
	}
}

type GameListRequest struct {
	GameListType GameType `json:"game_list_type"`
	AppChannel   string   `json:"app_channel"`
	AppId        int      `json:"app_id"`
	Signature
}

func (c *Client) Signature() Signature {
	tempByte := make([]byte, 8)
	rand.Read(tempByte)
	signatureNonce := hex.EncodeToString(tempByte)

	timestamp := time.Now().Unix()
	signature := GenerateSignature(signatureNonce, c.appKey, timestamp)
	return Signature{Signature: signature, SignatureNonce: signatureNonce, Timestamp: timestamp}
}

type GameListResponse Response[[]GameItem]

func (c *Client) GameList(gameType GameType) (*GameListResponse, error) {
	req := &GameListRequest{
		GameListType: gameType,
		AppChannel:   c.channel,
		AppId:        c.appId,
		Signature:    c.Signature(),
	}

	resp, err := Post[GameListResponse](c.rc, "v1/api/gamelist", req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
