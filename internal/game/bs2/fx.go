package bs2

import (
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(desc *conf.Setting, vnd log.Vendor) *Manager {
	config := desc.Game.BaiShun2

	m := &Manager{cli: NewClient(config.Host, config.Channel, config.AppId, config.AppKey, vnd.Scope("game.bs2")), cfg: &config, logger: vnd.Scope("game.bs2")}
	m.gc = cc.New[int, []GameItem](0, cc.Simple, cc.LoaderExpireFunc(func(_ int) ([]GameItem, time.Duration, error) {
		lst, err := m.list()
		if err != nil {
			return nil, 0, err
		}

		if len(lst) == 0 {
			return lst, 0, nil
		}

		return lst, time.Minute * 30, nil
	}))

	return m
}

func Invoke(
	desc *conf.Setting,
	rc *redi.Client,
	dc *db.Client,
	dm *redi.Mutex,
	apiJwt *auth.JWT,
	r *api.Router,
	ug user.Getter,
	om *order.Manager,
	lm *level.Manager,
	fg fund.Getter,
	gm *game.Manager,
	vnd log.Vendor,
) error {
	tokenJwt, err := auth.NewJWT(desc.Game.BaiShun2.TokenKey, ug)
	if err != nil {
		return err
	}

	cb := &Callback{
		appId:  desc.Game.BaiShun2.AppId,
		appKey: desc.Game.BaiShun2.AppKey,
		apiJwt: apiJwt,
		ssJwt:  tokenJwt,
		rc:     rc,
		dm:     dm,
		dc:     dc,
		fg:     fg,
		om:     om,
		lm:     lm,
		gm:     gm,
		logger: vnd.Scope("api.bs2"),
	}

	g := r.Group("/game/bs2/", WithAppInfo(desc.Game.BaiShun2.AppId, desc.Game.BaiShun2.AppKey))
	{
		g.POST("/get_sstoken", Generic(cb.GetSSToken))
		g.POST("/update_sstoken", Generic(cb.GetSSToken))
		g.POST("/get_user_info", Generic(cb.GetUserInfo))
		g.POST("/change_balance", Generic(cb.ChangeBalance))
		g.POST("/event", Generic(cb.onEvent))

	}

	return nil
}

func Register(gm *game.Manager, mgr *Manager) {
	gm.RegisterPlatform(mgr)

	game.RegisterWinningIcon(Platform, winningGameIcons())
}
