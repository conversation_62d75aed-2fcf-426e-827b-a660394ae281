package bs2

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"
)

type Signature struct {
	Signature      string `json:"signature" binding:"required"`
	SignatureNonce string `json:"signature_nonce" binding:"required"`
	Timestamp      int64  `json:"timestamp" binding:"required"` // 秒级时间戳
}

func (s *Signature) Valid(appKey string) bool {
	if s.SignatureNonce == "" || s.Signature == "" || s.Timestamp == 0 {
		return false
	}

	return s.Signature == GenerateSignature(s.SignatureNonce, appKey, s.Timestamp)
}

func GenerateSignature(signatureNonce string, appKey string, timestamp int64) string {
	data := fmt.Sprintf("%s%s%d", signatureNonce, appKey, timestamp)
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func GenerateSignature2(appKey string) string {
	tempByte := make([]byte, 8)
	rand.Read(tempByte)
	signatureNonce := hex.EncodeToString(tempByte)
	return GenerateSignature(signatureNonce, appKey, time.Now().Unix())
}
