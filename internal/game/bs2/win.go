package bs2

import (
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/internal/game"
)

func w(amt int) int {
	return amt * 10000
}

var gameWinType = map[string][3]int{
	"1053": {w(6), w(10), w(20)},
	"1041": {w(6), w(10), w(20)},
	"1022": {w(6), w(10), w(20)},
	"1051": {w(6), w(10), w(20)},
	"1023": {w(6), w(10), w(20)},
	"1047": {w(6), w(10), w(20)},
	"1048": {w(6), w(10), w(20)},
	"1031": {w(6), w(10), w(20)},
	"1043": {w(6), w(10), w(20)},
	"1073": {w(6), w(10), w(20)},
}

var gameWinTupeIndex = []game.WinType{
	game.WinTypeBigWin,
	game.WinTypeMagawin,
	game.WinTypeSupewin,
}

func WinType(gameId string, amt int64) game.WinType {
	lvs := gameWinType[gameId]

	for i := len(lvs) - 1; i >= 0; i-- {
		if amt >= int64(lvs[i]) {
			return gameWinTupeIndex[i]
		}
	}

	return game.WinTypeNone
}

func winningGameIcons() game.GameIcons {
	var (
		gameIds = []string{
			"1022",
			"1023",
			"1031",
			"1034",
			"1037",
			"1041",
			"1042",
			"1043",
			"1047",
			"1048",
			"1051",
			"1052",
			"1053",
			"1073",
			"1074",
			"1079",
			"1096",
			"1098",
			"1103",
			"1107",
			"1018",
			"1100",
		}
		icons = make(game.GameIcons)
	)
	for _, id := range gameIds {
		icons[id] = fmt.Sprintf("https://godzilla-live-oss.kako.live/game/winning/icons/%s/%s.png", Platform, id)
	}
	return icons
}
