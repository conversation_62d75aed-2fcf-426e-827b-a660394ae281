package game

type WinType int

const (
	WinTypeNone    WinType = 0
	WinTypeBigWin  WinType = 1
	WinTypeMagawin WinType = 2
	WinTypeSupewin WinType = 3
)

func (w WinType) String() string {
	switch w {
	case WinTypeNone:
		return "none"
	case WinTypeBigWin:
		return "bigwin"
	case WinTypeMagawin:
		return "magawin"
	case WinTypeSupewin:
		return "supewin"
	default:
		return "unknown"
	}
}

func (w WinType) Int() int {
	return int(w)
}
