package sticker

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

func newManager(db *db.Client, dbmc *db.MongoClient, lm *live.Manager, rm *room.Manager, log *zap.Logger) *Manager {
	return &Manager{db: db, dbmc: dbmc, lm: lm, rm: rm, log: log}
}

type Manager struct {
	db   *db.Client
	dbmc *db.MongoClient
	lm   *live.Manager
	rm   *room.Manager
	log  *zap.Logger
}
