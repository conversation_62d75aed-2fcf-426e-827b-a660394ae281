package fclub

import (
	"context"
	"errors"
	"time"
)

type Getter interface {
	Info(ctx context.Context, anchorId, userId string) (*LevelInfo, error) // 等级信息
	Level(ctx context.Context, anchorId, userId string) (int, error)       // 成员等级（未加入返回0）
	ALevel(ctx context.Context, anchorId, userId string) (int, error)      // 活跃等级（熄灭时返回0）
	Members(ctx context.Context, anchorId string) (int, error)             // 成员数量
}

func (s *Manager) LevelInfo(ctx context.Context, anchorId, userId string) (*LevelInfo, error) {
	mm, err := s.takeMember(ctx, anchorId, userId)
	if err != nil {
		if errors.Is(err, ErrMemberNotExists) {
			return &LevelInfo{}, nil
		}
		return nil, err
	}
	return mm.LevelInfo(time.Now()), nil
}
