package fclub

import (
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	rc *redi.Client, dm *redi.Mutex, db *db.Client, ev ev.Bus,
	ug user.Getter, lm *live.Manager, gm *gift.Manager, fm *fund.Manager, jm *journal.Manager, pm *profitsharing.Manager,
	syn cc.Sync, vnd log.Vendor,
) (*Manager, Getter) {
	db.ApplyMigrate(&Member{})
	mgr := &Manager{
		rc:  rc,
		dm:  dm,
		db:  db,
		ev:  ev,
		ug:  ug,
		lm:  lm,
		gm:  gm,
		fm:  fm,
		jm:  jm,
		pm:  pm,
		log: vnd.Scope("fclub.mgr"),
	}
	mgr.initCache(syn)
	return mgr, mgr
}

func InvokeInScheduler(sch *cron.Scheduler, m *Manager) {
	sch.Periodic(time.Hour, sch.Exclusive("fclub.inactives.scanner", m.scanInactives))
}
