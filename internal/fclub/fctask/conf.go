package fctask

import "gitlab.sskjz.com/overseas/live/osl/pkg/dbg"

const (
	Gift1Id = 43
	Gift2Id = 10066
)

const (
	gift1Step  = 500  // 当每日送出达到500个时
	gift1Gain  = 100  // 增加100亲密度
	gift2Gain  = 200  // 送出一个，增加200亲密度
	gift3Lucky = 1000 // 送1000金币幸运礼物，亲密度+1
	gift3Gift  = 100  // 送100金币专属礼物，亲密度+1
	watchTime  = 15   // 每观看15分钟
	watchGain  = 100  // 增加100亲密度
	watchGMax  = 200  // 每日最多+200
)

var (
	gift3DayMax = map[int]int{
		// 不同粉丝团等级的每日增加亲密度上限值不同
		1: 700, 2: 700, 3: 700, 4: 700, 5: 700, 6: 700, 7: 700, 8: 700, 9: 700,
		10: 2500, 11: 2500, 12: 2500, 13: 2500, 14: 2500, 15: 2500,
		16: 20000, 17: 20000, 18: 20000, 19: 20000, 20: 20000,
	}
)

func init() {
	if dbg.Ing() {
		for lv := range gift3DayMax {
			gift3DayMax[lv] = 2918900
		}
	}
}
