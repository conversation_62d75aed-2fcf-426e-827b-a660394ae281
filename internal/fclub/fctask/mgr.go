package fctask

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	rc  *redi.Client
	ug  user.Getter
	fc  *fclub.Manager
	lm  *live.Manager
	log *zap.Logger
	cached
}

type Info struct {
	Status int
	Gain   int
	Curr   int
	Max    int
}

func (s *Manager) TaskInfo(ctx context.Context, at time.Time, anchorId, userId string, tt Type) (*Info, error) {
	var out Info

	done, _ := s.chkIsDone(ctx, at, tt, anchorId, userId)
	out.Status = lo.Ternary(done, 1, 0)

	switch tt {
	case TTGift1, TTGift2:
		out.Gain, _ = s.getIntimacy(ctx, at, tt, anchorId, userId)
	case TTGift3:
		lv, err := s.fc.Level(ctx, anchorId, userId)
		if err != nil {
			return nil, err
		}
		out.Gain, _ = s.getIntimacy(ctx, at, tt, anchorId, userId)
		out.Curr = out.Gain
		out.Max = gift3DayMax[lv]
	case TTWatch:
		out.Gain, _ = s.getIntimacy(ctx, at, tt, anchorId, userId)
		out.Curr, _ = s.getTaskStep(ctx, at, tt, anchorId, userId)
		out.Max = watchTime * (watchGMax / watchGain)
	}

	return &out, nil
}
