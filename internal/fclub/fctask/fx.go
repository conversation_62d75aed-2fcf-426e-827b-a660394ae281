package fctask

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(rc *redi.Client, ug user.Getter, fc *fclub.Manager, lm *live.Manager, vnd log.Vendor) *Manager {
	m := &Manager{rc: rc, ug: ug, fc: fc, lm: lm, log: vnd.Scope("fclub.task.mgr")}
	m.initCache()
	return m
}

func InvokeInAPI(evb ev.Bus, mgr *Manager) {
	evb.Watch(evt.GiftSend, "fclub.gift.process", ev.<PERSON>atcher(mgr.onGifting), ev.WithAsync())
	evb.Watch(evt.FansclubUserJoin, "fclub.group.process", ev.NewWatcher(mgr.onUserJoin), ev.WithAsync())
}

func InvokeInRoom(evb ev.Bus, mgr *Manager) {
	evb.Watch(evt.UserSessionOnlineTopic, "fclub.watch.process", ev.NewWatcher(mgr.onWatching), ev.WithAsync())
}
