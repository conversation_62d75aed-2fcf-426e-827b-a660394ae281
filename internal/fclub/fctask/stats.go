package fctask

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
)

const (
	keyStatsMembers  = "FC:STATS:MEMBERS:%s:%s"      // date, anchorId
	keyStatsIntimacy = "FC:STATS:INTIMACY:%s:%s:%s"  // date, anchorId, userId
	keyStatsTaskDone = "FC:STATS:TASK:DONE:%s:%s:%d" // date, anchorId, Type
)

func (s *Manager) onUserJoin(ctx context.Context, evd *evt.UserJoinFansclub) error {
	at, anchorId := time.Now(), evd.AnchorId
	key := fmt.Sprintf(keyStatsMembers, dateOf(at, s.tz(anchorId)), anchorId)
	txp := s.rc.Pipeline()
	txp.IncrBy(ctx, key, 1)
	txp.Expire(ctx, key, dayTTL(at, s.tz(anchorId)))
	if _, err := txp.Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (s *Manager) TodayMembers(ctx context.Context, at time.Time, anchorId string) (int, error) {
	return s.rc.Get(ctx, fmt.Sprintf(keyStatsMembers, dateOf(at, s.tz(anchorId)), anchorId)).Int()
}

func (s *Manager) onIntimacy(ctx context.Context, at time.Time, anchorId, userId string, value int) {
	key := fmt.Sprintf(keyStatsIntimacy, dateOf(at, s.tz(userId)), anchorId, userId)
	txp := s.rc.Pipeline()
	txp.IncrBy(ctx, key, int64(value))
	txp.Expire(ctx, key, dayTTL(at, s.tz(userId)))
	if _, err := txp.Exec(ctx); err != nil {
		s.log.Warn("failed to update intimacy stats", zap.Error(err))
	}
}

func (s *Manager) TodayIntimacy(ctx context.Context, at time.Time, anchorId, userId string) (int, error) {
	return s.rc.Get(ctx, fmt.Sprintf(keyStatsIntimacy, dateOf(at, s.tz(userId)), anchorId, userId)).Int()
}

func (s *Manager) onTaskDone(ctx context.Context, at time.Time, tt Type, anchorId string) {
	key := fmt.Sprintf(keyStatsTaskDone, dateOf(at, s.tz(anchorId)), anchorId, tt)
	txp := s.rc.Pipeline()
	txp.IncrBy(ctx, key, 1)
	txp.Expire(ctx, key, dayTTL(at, s.tz(anchorId)))
	if _, err := txp.Exec(ctx); err != nil {
		s.log.Warn("failed to update task done stats", zap.Error(err))
	}
}

func (s *Manager) TodayTaskDone(ctx context.Context, at time.Time, anchorId string, tt Type) (int, error) {
	return s.rc.Get(ctx, fmt.Sprintf(keyStatsTaskDone, dateOf(at, s.tz(anchorId)), anchorId, tt)).Int()
}
