package fctask

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

func (s *Manager) initCache() {
	s.tdc = cc.New[taskKey, bool](10000, cc.LRU)
	s.utz = cc.New[string, *time.Location](10000, cc.LRU, cc.LoaderFunc(func(userId string) (*time.Location, error) {
		uac, err := s.ug.Account(context.TODO(), userId)
		if err != nil {
			return nil, err
		}
		return ctz.Timezone(uac.Timezone), nil
	}))
}

type cached struct {
	tdc cc.Cache[taskKey, bool]
	utz cc.Cache[string, *time.Location]
}

func (s *cached) tz(userId string) *time.Location {
	tz, _ := s.utz.Get(userId)
	if tz == nil {
		return ctz.L()
	}
	return tz
}

type taskKey struct {
	tt       Type
	anchorId string
	userId   string
}

func (s *cached) isDone(tt Type, anchorId, userId string) bool {
	v, _ := s.tdc.Get(taskKey{tt, anchorId, userId})
	return v
}

func (s *cached) setDone(at time.Time, tt Type, anchorId, userId string) {
	s.tdc.SetWithExpire(taskKey{tt, anchorId, userId}, true, dayTTL(at, s.tz(userId)))
}
