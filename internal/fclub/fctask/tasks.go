package fctask

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

func (s *Manager) onGifting(ctx context.Context, evd *evt.SendGift) error {
	roomId, anchorId, userId, at := evd.RoomId, evd.AnchorId, evd.UserId, evd.At

	lv, err := s.fc.Level(ctx, anchorId, userId)
	if err != nil {
		return err
	} else if lv == 0 {
		return nil
	}

	if evd.GiftId == Gift1Id {
		if done, err := s.chkIsDone(ctx, at, TTGift1, anchorId, userId); err == nil && !done {
			if next, _ := s.incTaskStep(ctx, at, TTGift1, anchorId, userId, evd.Count); next >= gift1Step {
				_ = s.markAsDone(ctx, at, TTGift1, roomId, anchorId, userId, gift1Gain)
			}
		}
	}

	if evd.GiftId == Gift2Id {
		if done, err := s.chkIsDone(ctx, at, TTGift2, anchorId, userId); err == nil && !done {
			_ = s.markAsDone(ctx, at, TTGift2, roomId, anchorId, userId, gift2Gain)
		}
	}

	if done, err := s.chkIsDone(ctx, at, TTGift3, anchorId, userId); err == nil && !done {
		var intimacy int
		if evd.Gift.Lucky {
			if next, _ := s.incTaskStep(ctx, at, ttGift31, anchorId, userId, evd.Diamond); next >= gift3Lucky {
				intimacy = next / gift3Lucky
				_, _ = s.incTaskStep(ctx, at, ttGift31, anchorId, userId, -intimacy*gift3Lucky)
			}
		} else {
			if next, _ := s.incTaskStep(ctx, at, ttGift32, anchorId, userId, evd.Diamond); next >= gift3Gift {
				intimacy = next / gift3Gift
				_, _ = s.incTaskStep(ctx, at, ttGift32, anchorId, userId, -intimacy*gift3Gift)
			}
		}
		if intimacy > 0 {
			current, _ := s.getIntimacy(ctx, at, TTGift3, anchorId, userId)
			intimacy = min(intimacy, gift3DayMax[lv]-current)
			if next, _ := s.addIntimacy(ctx, at, TTGift3, roomId, anchorId, userId, intimacy); next >= gift3DayMax[lv] {
				return s.markAsDone(ctx, at, TTGift3, roomId, anchorId, userId, 0)
			}
		}
	}

	return nil
}

func (s *Manager) onWatching(ctx context.Context, evd *evt.UserSessionOnline) error {
	ri, err := s.lm.Session2(evd.SessionId)
	if err != nil {
		return err
	}

	roomId, anchorId, userId, at := ri.RoomId, ri.UserId, evd.UserId, evd.At

	lv, err := s.fc.Level(ctx, anchorId, userId)
	if err != nil {
		return err
	} else if lv == 0 {
		return nil
	}

	if done, err := s.chkIsDone(ctx, at, TTWatch, anchorId, userId); err == nil && !done {
		next, _ := s.incTaskStep(ctx, at, TTWatch, anchorId, userId, 1)
		if next%watchTime == 0 {
			next2, _ := s.addIntimacy(ctx, at, TTWatch, roomId, anchorId, userId, watchGain)
			if next2 >= watchGMax {
				_ = s.markAsDone(ctx, at, TTWatch, roomId, anchorId, userId, 0)
			}
		}
	}

	return nil
}
