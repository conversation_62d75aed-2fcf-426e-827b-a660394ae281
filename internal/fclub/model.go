package fclub

import (
	"time"
)

type Member struct {
	ID        uint      `gorm:"primaryKey"`
	AnchorId  string    `gorm:"not null;size:32;uniqueIndex:member,priority:2;index:listing"`               // 主播Id
	UserId    string    `gorm:"not null;size:32;uniqueIndex:member,priority:1"`                             // 用户Id
	Intimacy  int       `gorm:"not null;type:int unsigned;default:0;index:listing,sort:desc;index:scanner"` // 亲密度
	RecordAt  time.Time `gorm:"not null;type:datetime(0);index:scanner"`                                    // 记录时间 UTC 23:59:59
	UpdatedAt time.Time `gorm:"not null;type:datetime(0)"`                                                  // 更新时间
	CreatedAt time.Time `gorm:"not null;type:datetime(0)"`                                                  // 创建时间
}

func (s *Member) TableName() string {
	return "fansclub"
}

func (s *Member) LevelInfo(at time.Time) *LevelInfo {
	return newLevelInfo(at, s)
}
