package fclub

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/lv"
)

type LevelInfo struct {
	Raw      int  // 原始亲密度
	Adj      int  // 衰减亲密度
	Level    int  // 当前等级
	Active   bool // 活跃状态
	JoinTime time.Time
}

func (l *LevelInfo) Valid() bool {
	return l.Level > 0
}

func (l *LevelInfo) Detail() *lv.Info {
	return lvInfo(l.Raw - l.Adj)
}

func newLevelInfo(at time.Time, mm *Member) *LevelInfo {
	raw := mm.Intimacy
	adj := adjIntimacy(raw, mm.RecordAt, at)
	return &LevelInfo{
		Raw:      raw,
		Adj:      adj,
		Level:    level(raw - adj),
		Active:   isActive(mm.RecordAt, at),
		JoinTime: mm.CreatedAt,
	}
}
