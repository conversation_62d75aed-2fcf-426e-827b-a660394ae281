package fclub

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrMemberAlreadyJoin = errors.New("fansclub member already join")
	ErrMemberJustLeaved  = biz.Legacy("fansclub member just leaved")
)

const (
	JoinPrice = 1000 // 加入粉丝团价格
)

func (s *Manager) Join(ctx context.Context, at time.Time, roomId, userId string) error {
	ri, err := s.lm.Room2(roomId)
	if err != nil {
		return err
	}

	if ri.UserId == userId {
		return ErrMemberAlreadyJoin
	}

	if lv, err := s.Level(ctx, ri.UserId, userId); err != nil {
		return err
	} else if lv > 0 {
		return ErrMemberAlreadyJoin
	}

	if s.rc.Exists(ctx, fmt.Sprintf(keyLeaved, ri.UserId, userId)).Val() > 0 {
		return ErrMemberJustLeaved
	}

	uac, err := s.ug.Account(ctx, userId)
	if err != nil {
		return err
	}

	ratio, err := s.pm.Get(ri.UserId, ri.SessionId.Hex())
	if err != nil {
		return err
	}

	var (
		diamond  = fund.New(JoinPrice)
		fruit    = diamond.Mul(fund.New(ratio.GiftRatioGift))
		agency   = ratio.AgencyUserId
		kickback = fruit.Mul(fund.New(ratio.GiftRatioAgency)).RoundFloor(2)
	)

	cntBefore, _ := s.membersCnt(ctx, ri.UserId)

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := s.fm.Expend(ctx, userId, fund.JTypeOthers, fund.PTypeDiamond, diamond,
			fund.WithTarget(ri.UserId),
			fund.WithDetail("Entrada no Fã-Clube"),
			fund.WithTime(at),
		); err != nil {
			return err
		}

		if err := s.fm.Income(ctx, ri.UserId, fund.JTypeSendGift, fund.PTypeFruits, fruit,
			fund.WithGroup("gift", ri.SessionId.Hex()),
			fund.WithTime(at),
		); err != nil {
			return err
		}

		db.AfterCommit(ctx, func(ctx context.Context) {
			if err := s.jm.RecvGift(ctx, at, ri.UserId, ri.SessionId.Hex(), fruit); err != nil {
				s.log.Error("mark recv gift failed", zap.Error(err))
			}
		})

		if agency != "" && !kickback.IsZero() {
			db.AfterCommit(ctx, func(ctx context.Context) {
				if err := s.fm.Income(fund.SkipJournal(ctx), agency, fund.JTypeKickback, fund.PTypeFruits, kickback, fund.WithTime(at)); err != nil {
					s.log.Error("income kickback failed", zap.String("userId", agency), zap.Error(err))
				}
			})
		}

		return tx.Create(&Member{
			AnchorId:  ri.UserId,
			UserId:    userId,
			RecordAt:  recordAt(uac.Timezone, at),
			UpdatedAt: at,
			CreatedAt: at,
		}).Error
	}); err != nil {
		return err
	}

	s.invalidCache(ri.UserId, userId)

	s.ev.Emit(ctx, evt.FansclubUserJoin, &evt.UserJoinFansclub{
		RoomId:   roomId,
		AnchorId: ri.UserId,
		UserId:   userId,
		Number:   cntBefore + 1,
		Cost:     JoinPrice,
		At:       at,
	})

	return nil
}

const (
	keyLeaved = "FC:LEAVE:%s:%s" // anchorId, userId
	ttlLeaved = 24 * time.Hour
)

func (s *Manager) Leave(ctx context.Context, anchorId, userId string) error {
	if lv, err := s.Level(ctx, anchorId, userId); err != nil {
		return err
	} else if lv == 0 {
		return ErrMemberJustLeaved
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		return tx.Where("anchor_id = ? AND user_id = ?", anchorId, userId).Delete(&Member{}).Error
	}); err != nil {
		return err
	}

	s.rc.Set(ctx, fmt.Sprintf(keyLeaved, anchorId, userId), 1, ttlLeaved)

	s.invalidCache(anchorId, userId)

	return nil
}
