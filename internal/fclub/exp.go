package fclub

import "gitlab.sskjz.com/overseas/live/osl/pkg/lv"

var grades = []int{
	0, 420, 1020, 1820, 2940, 4440, 6360, 8900, 12900, 22900,
	38900, 68900, 118900, 168900, 218900, 418900, 918900, 1418900, 1918900, 2918900,
}

func level(exp int) int {
	return lv.Lv(grades, exp)
}

func lvExp(at int) (min, max int) {
	return lv.Exp(grades, at)
}

func lvInfo(exp int) *lv.Info {
	return lv.Make(grades, exp)
}
