package fclub

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func (s *Manager) scanInactives(ctx context.Context) error {
	at := time.Now()

	rows, err := s.db.Model(&Member{}).Where("intimacy > 0 AND record_at < ?", downgradeRecord(at)).Rows()
	if err != nil {
		return err
	}
	defer rows.Close()

	var errs []error
	for rows.Next() {
		var mm Member
		if err := s.db.ScanRows(rows, &mm); err != nil {
			return err
		}
		if err := s.downgrade(ctx, at, &mm); err != nil {
			errs = append(errs, err)
		}
	}

	return errors.Join(errs...)
}

func (s *Manager) downgrade(ctx context.Context, at time.Time, mm *Member) error {
	lv := mm.LevelInfo(at)
	if lv.Adj == 0 {
		return nil
	}

	if err := db.UseTx(ctx, s.db).Model(mm).Updates(map[string]any{
		"intimacy":  gorm.Expr("intimacy - ?", lv.Adj),
		"record_at": mm.RecordAt.AddDate(0, 0, ddDays(mm.RecordAt, at)),
	}).Error; err != nil {
		return err
	}

	s.log.Info("sub exp", zap.String("userId", mm.UserId), zap.String("anchorId", mm.AnchorId),
		zap.Int("exp", lv.Adj),
	)

	return nil
}
