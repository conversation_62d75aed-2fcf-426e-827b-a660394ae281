package fclub

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	rc  *redi.Client
	dm  *redi.Mutex
	db  *db.Client
	ev  ev.Bus
	ug  user.Getter
	lm  *live.Manager
	gm  *gift.Manager
	fm  *fund.Manager
	jm  *journal.Manager
	pm  *profitsharing.Manager
	log *zap.Logger
	cached
}
