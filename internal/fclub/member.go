package fclub

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

var (
	ErrMemberNotExists = errors.New("member not exists")
)

func (s *Manager) takeMember(ctx context.Context, anchorId, userId string) (*Member, error) {
	var member Member
	if err := db.UseTx(ctx, s.db).Where("anchor_id = ? AND user_id = ?", anchorId, userId).Take(&member).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrMemberNotExists
		}
		return nil, err
	}
	return &member, nil
}

func (s *Manager) membersCnt(ctx context.Context, anchorId string) (int, error) {
	var cnt int64
	if err := db.UseTx(ctx, s.db).Model(&Member{}).Where("anchor_id = ?", anchorId).Count(&cnt).Error; err != nil {
		return 0, err
	}
	return int(cnt), nil
}

func (s *Manager) ListMembers(ctx context.Context, anchorId string, filter bool, offset, limit int) ([]*Member, error) {
	var out []*Member

	query := db.UseTx(ctx, s.db).Where("anchor_id = ?", anchorId).Order("intimacy DESC")
	if filter {
		query = query.Where("record_at > ?", inactiveRecord(time.Now()))
	}
	query = query.Offset(offset).Limit(limit)

	if err := query.Find(&out).Error; err != nil {
		return nil, err
	}

	return out, nil
}
