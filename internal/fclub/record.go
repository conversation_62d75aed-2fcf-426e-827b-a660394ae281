package fclub

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/lv"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	keyUserMutex = "FC:LV:MU:%s:%s" // anchorId, userId
)

func (s *Manager) AddExpInRoom(ctx context.Context, roomId, anchorId, userId string, amount int) (*lv.Info, error) {
	exp, err := s.addExp(ctx, anchorId, userId, amount)
	if err != nil {
		return nil, err
	}

	if !exp.live {
		s.ev.Emit(ctx, evt.FansclubActivate, &evt.ActivateFansclub{
			RoomId:   roomId,
			AnchorId: anchorId,
			UserId:   userId,
		})
	}

	if pLv, cLv := level(exp.prev), level(exp.curr); cLv > pLv {
		s.ev.Emit(ctx, evt.FansclubLvUpgrade, &evt.FansLevelUpgrade{
			RoomId:   roomId,
			AnchorId: anchorId,
			UserId:   userId,
			Level:    cLv,
			Prev:     pLv,
		})
	}

	return lvInfo(exp.curr), nil
}

type addExp struct {
	prev, curr int
	live       bool
}

func (s *Manager) addExp(ctx context.Context, anchorId, userId string, exp int) (*addExp, error) {
	l, err := s.dm.Lock(ctx, fmt.Sprintf(keyUserMutex, anchorId, userId))
	if err != nil {
		return nil, err
	}
	defer l.MustUnlock()

	at := time.Now()

	mm, err := s.takeMember(ctx, anchorId, userId)
	if err != nil {
		return nil, err
	}

	update := map[string]any{
		"intimacy": gorm.Expr("intimacy + ?", exp),
	}

	prev := mm.Intimacy
	curr := prev + exp
	live := isActive(mm.RecordAt, at)

	if !sameRecord(mm.RecordAt, at) {
		update["record_at"] = recordAt(userTz(mm.RecordAt), at)
	}

	if err := db.UseTx(ctx, s.db).Model(mm).Updates(update).Error; err != nil {
		return nil, err
	}

	if pLv, cLv := level(prev), level(curr); pLv != cLv || !live {
		s.invalidCache(anchorId, userId)
	}

	s.log.Debug("add exp", zap.String("userId", userId), zap.String("anchorId", anchorId),
		zap.Int("exp", exp), zap.Int("curr", curr),
	)

	return &addExp{prev, curr, live}, nil
}
