package fclub

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/go/cc"
)

func (s *Manager) initCache(syn cc.Sync) {
	s.lc = cc.New[levelKey, *LevelInfo](
		40000, cc.LRU,
		cc.LoaderFunc(func(vk levelKey) (*LevelInfo, error) {
			return s.LevelInfo(context.TODO(), vk.AnchorId, vk.UserId)
		}),
		cc.Expiration(time.Hour),
		cc.ExportStats("fclub.level"),
		cc.WithSync(syn, "fclub.level"),
	)
	s.mc = cc.New[string, int](
		10000, cc.LRU,
		cc.LoaderFunc(func(anchorId string) (int, error) {
			return s.membersCnt(context.TODO(), anchorId)
		}),
		cc.Expiration(time.Hour),
		cc.ExportStats("fclub.members"),
		cc.WithSync(syn, "fclub.members"),
	)
}

type levelKey struct{ AnchorId, UserId string }

func (k levelKey) MarshalBinary() ([]byte, error) {
	return sonic.Marshal(k)
}

func (k *levelKey) UnmarshalBinary(data []byte) error {
	return sonic.Unmarshal(data, k)
}

type cached struct {
	lc cc.Cache[levelKey, *LevelInfo]
	mc cc.Cache[string, int]
}

func (s *cached) Info(ctx context.Context, anchorId, userId string) (*LevelInfo, error) {
	return s.lc.Get(levelKey{anchorId, userId})
}

func (s *cached) Level(ctx context.Context, anchorId, userId string) (int, error) {
	i, err := s.Info(ctx, anchorId, userId)
	if err != nil {
		return 0, err
	}
	return i.Level, nil
}

func (s *cached) ALevel(ctx context.Context, anchorId, userId string) (int, error) {
	i, err := s.Info(ctx, anchorId, userId)
	if err != nil {
		return 0, err
	} else if !i.Active {
		return 0, nil
	}
	return i.Level, nil
}

func (s *cached) Members(ctx context.Context, anchorId string) (int, error) {
	return s.mc.Get(anchorId)
}

func (s *cached) invalidCache(anchorId, userId string) {
	s.lc.Remove(levelKey{anchorId, userId})
	s.mc.Remove(anchorId)
}
