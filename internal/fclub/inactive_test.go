package fclub

import (
	"fmt"
	"math/rand/v2"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

func TestIsActive(t *testing.T) {
	tt := time.Date(2000, 1, 1, rand.IntN(24), rand.IntN(60), rand.IntN(60), 0, time.UTC)
	fmt.Println("start at", tt)
	tt = recordAt(ctz.China.String(), tt)
	fmt.Println("record at", tt)
	for d := 1; d < inactiveDays+2; d++ {
		at := time.Date(tt.Year(), tt.Month(), tt.Day()+d, 0, 0, 0, 0, time.UTC)
		fmt.Println(at, isActive(tt, at))
	}
}

func TestAdjIntimacy(t *testing.T) {
	tt := time.Date(2000, 1, 1, rand.Int<PERSON>(24), rand.Int<PERSON>(60), rand.IntN(60), 0, time.UTC)
	fmt.Println("start at", tt)
	tt = recordAt(ctz.China.String(), tt)
	fmt.Println("record at", tt)
	for d := 1; d < downgradeDays*2; d++ {
		at := time.Date(tt.Year(), tt.Month(), tt.Day()+d, 0, 0, 0, 0, time.UTC)
		fmt.Println(at, adjIntimacy(420, tt, at))
	}
}

func TestRecordAt(t *testing.T) {
	for i := -12; i <= 14; i++ {
		loc := ctz.Timezone(ctz.Zone(i))
		rat := recordAt(loc.String(), time.Now())
		fmt.Println(loc.String(), rat, userTz(rat))
	}
}

func TestSameRecord(t *testing.T) {
	for i := -12; i <= 14; i++ {
		loc := ctz.Timezone(ctz.Zone(i))
		rat := recordAt(loc.String(), time.Now())
		fmt.Println(loc.String(), rat, sameRecord(rat, time.Now()))
	}
}

func BenchmarkSameRecord(b *testing.B) {
	at := time.Now()
	rat := recordAt(ctz.China.String(), at)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		sameRecord(rat, at)
	}
	b.ReportAllocs()
}

func TestDowngradeDays(t *testing.T) {
	rat := time.Date(2025, 4, 1, 23, 59, 59, 0, ctz.Brazil).In(time.UTC)
	now := rat.AddDate(0, 0, downgradeDays).Add(time.Second)
	fmt.Println("now is", now)
	fmt.Println("scan before", downgradeRecord(now))
	fmt.Println("user record at", rat)
	assert.Equal(t, 1, ddDays(rat, now))
}
