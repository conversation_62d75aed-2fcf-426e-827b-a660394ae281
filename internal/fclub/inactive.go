package fclub

import (
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

const (
	inactiveDays  = 7
	downgradeDays = 14
)

var downgrades = []int{
	25, 25, 25, 50, 50, 75, 75, 75, 100, 100, 125, 125, 150, 150, 150, 400, 600, 600, 600, 1000,
}

func aDays(rat, at time.Time) int {
	tz := ctz.Timezone(userTz(rat))
	t1 := now.With(at.In(tz)).BeginningOfDay()
	t2 := now.With(rat.In(tz)).BeginningOfDay()
	return int(t1.Sub(t2).Hours() / 24)
}

func isActive(rat, at time.Time) bool {
	return aDays(rat, at) <= inactiveDays
}

func ddDays(rat, at time.Time) int {
	return aDays(rat, at) - downgradeDays
}

func adjIntimacy(curr int, rat, at time.Time) int {
	var adj int
	for i := 0; i < ddDays(rat, at); i++ {
		adj += downgrades[level(curr-adj)-1]
		if adj >= curr {
			return curr
		}
	}
	return adj
}

func recordAt(tz string, t time.Time) time.Time {
	return now.With(t.In(ctz.Timezone(tz))).EndOfDay().Truncate(time.Second).In(time.UTC)
}

func inactiveRecord(at time.Time) time.Time {
	return at.AddDate(0, 0, -inactiveDays).In(time.UTC)
}

func downgradeRecord(at time.Time) time.Time {
	return at.AddDate(0, 0, -downgradeDays).In(time.UTC)
}

func userTz(rat time.Time) string {
	var offset int
	if h := rat.In(time.UTC).Hour(); h > 8 {
		offset = 23 - h
	} else {
		offset = -h - 1
	}
	return ctz.Zone(offset)
}

func sameRecord(rat, at time.Time) bool {
	return rat.In(time.UTC).Equal(recordAt(userTz(rat), at))
}
