package ganopay

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
)

func (s *Manager) adjPrice(scene string, money float64, pp payermax.Payment) (float64, error) {
	fee, err := s.pm.FeeOf(pp.Cur, money, pp.Fee, payermax.Income)
	if err != nil {
		return 0, err
	}
	if s.ps.FeeFree(scene) {
		fee = 0
	}
	return pay.InPrice(money + fee), nil
}
