package ganopay

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"go.uber.org/zap"
)

var pmm = map[string]string{"bq101": "PIX"}

func (s *Manager) onPaySuccess(ctx context.Context, order *pay.Order) error {
	if order.Gateway != "ganopay" {
		return nil
	}
	org := pmm[order.Params.Get("o")]
	if org == "" {
		return nil
	}
	if err := s.act.Record(ctx, order.UserId, "payermax", org); err != nil {
		s.log.Warn("record action failed", zap.Error(err))
		return err
	}
	return nil
}
