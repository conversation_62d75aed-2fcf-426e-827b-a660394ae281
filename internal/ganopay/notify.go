package ganopay

import (
	"errors"
	"io"

	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

func (s *Manager) SetNotify(url string) {
	s.notifyUrl = url
}

type notify[T any] struct {
	Body T `json:"body"`
}

func (s *Manager) RecvNotify(ctx *gin.Context) error {
	switch ctx.Query("type") {
	case "pay":
		bs, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			return err
		}
		if err := s.sig1.Decode(&bs); err != nil {
			return err
		}
		var req notify[queryOrderResp]
		if err := sonic.Unmarshal(bs, &req); err != nil {
			return err
		}
		data := req.Body
		if _, err := s.updateOrder(ctx, data.OrderId, data.TradeId, data.Status); err != nil {
			if dbg.Ing() && errors.Is(err, pay.ErrOrderNotExists) {
				goto success
			}
			return err
		}
	}
success:
	ctx.String(200, "SUCCESS")
	return nil
}
