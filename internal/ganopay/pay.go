package ganopay

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"go.uber.org/zap"
)

func (s *Manager) Create(ctx context.Context, order *pay.Order) (*pay.Order, pay.Extras, error) {
	goods, err := s.ps.TakeGoods(ctx, order)
	if err != nil {
		return nil, nil, err
	}

	p := order.Params.Explode()
	org := p["o"]
	payment, has := paymentOf(order.Country, org)
	if !has {
		return nil, nil, errors.New("payment not found")
	}

	order.Price, err = s.adjPrice(order.Scene, order.Price, payment)
	if err != nil {
		return nil, nil, err
	}

	amount := decimal.NewFromFloat(order.Price).Mul(decimal.NewFromInt(100))

	resp, err := doRequest[createOrderResp](s.sig1, s.log, s.cli, "/gateway/api/commPay", makeRequest[createOrderReq](s.cfg, org, createOrderReq{
		OrderId:     order.TradeNo,
		OrderTime:   orderTime(order.CreatedAt),
		Currency:    order.Currency,
		Amount:      amount.String(),
		Goods:       goods.Name,
		CallbackUrl: s.cfg.CallbackUrl,
		NotifyUrl:   s.notifyOf("pay"),
	}))
	if err != nil {
		return nil, nil, err
	}

	if err := s.chk1.Submit(ctx, 60*time.Minute, &pendingOrder{TradeNo: order.TradeNo}); err != nil {
		s.log.Warn("submit order check task failed", zap.String("tradeNo", order.TradeNo), zap.Error(err))
	}

	order.OrderId = resp.TradeId
	order.Status = pay.OStatusBusy
	return order, pay.Extras{
		"redirectUrl": resp.PayUrl,
	}, nil
}

func (s *Manager) Query(ctx context.Context, tradeNo string) (*pay.Order, error) {
	order, err := s.ps.Take(ctx, tradeNo)
	if err != nil {
		return nil, err
	} else if order.Status.Final() {
		return order, nil
	}

	p := order.Params.Explode()
	org := p["o"]

	resp, err := doRequest[queryOrderResp](s.sig1, s.log, s.cli, "/gateway/api/queryPay", makeRequest(s.cfg, org, queryOrderReq{
		OrderId:   order.TradeNo,
		OrderTime: orderTime(order.CreatedAt),
		Currency:  order.Currency,
	}))
	if err != nil {
		return nil, err
	}

	if resp.Status == OrderUnknown {
		return order, nil
	}

	return s.updateOrder(ctx, tradeNo, resp.TradeId, resp.Status)
}

const (
	keyOrderMutex = "GANOPAY:MUTEX:%s" // tradeNo
)

func (s *Manager) updateOrder(ctx context.Context, tradeNo string, orderId string, status OrderStatus) (*pay.Order, error) {
	if l, err := s.dm.Lock(ctx, fmt.Sprintf(keyOrderMutex, tradeNo)); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}

	var (
		order *pay.Order
		err   error
	)

	switch status {
	case OrderSuccess:
		order, err = s.ps.MakePaid(ctx, tradeNo, pay.WithOrderId(orderId))
	case OrderFailure:
		order, err = s.ps.MakeFailed(ctx, tradeNo, pay.WithOrderId(orderId))
	}

	if err != nil {
		s.log.Warn("make status update failed", zap.String("tradeNo", tradeNo), zap.String("status", string(status)), zap.Error(err))
	} else {
		s.log.Debug("make status update success", zap.String("tradeNo", tradeNo), zap.String("status", string(status)))
	}

	return order, err
}
