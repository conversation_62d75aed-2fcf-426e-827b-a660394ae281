package ganopay

import (
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/act"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
)

func Provide(desc *conf.Setting, si sto.Instance, dm *redi.Mutex, ps *pay.Service, gws *pay.Gateways, pm *payermax.Manager, act act.Logger, qm *dq.Master, vnd log.Vendor) (*Manager, error) {
	payermax.InitLogo(si.Conf("default"), countryPayments)
	mgr, err := newManager(desc.Ganopay, dm, ps, pm, act, vnd.Scope("ganopay.mgr"))
	if err != nil {
		return nil, err
	}
	mgr.initCheck(qm)
	gws.Register("ganopay", mgr)
	return mgr, nil
}

func Invoke(evb ev.Bus, mgr *Manager, h *api.Host) {
	if h.Has() {
		mgr.SetNotify(h.URL("/ganopay/notify"))
	} else if dbg.Ing() {
		mgr.SetNotify("https://godzilla-api-test.sskjz.com/api/v1/ganopay/notify")
	}
	evb.Watch(pay.EvOrderPaid, "ganopay.process", ev.NewWatcher(mgr.onPaySuccess), ev.WithAsync())
	if env.Scheduler() {
		mgr.startCheck()
	}
}
