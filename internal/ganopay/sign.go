package ganopay

import (
	"bytes"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"net/url"
	"slices"
	"strings"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

type signer interface {
	Encode(bs *[]byte) error
	Decode(bs *[]byte) error
}

func newSign1(key string) signer {
	return &sign1{key: key}
}

type sign1 struct {
	key string
}

func (s1 *sign1) signOf(bs []byte) string {
	bm := gjson.GetBytes(bs, "body").Map()
	keys := lo.Keys(bm)
	slices.Sort(keys)
	qs := make([]string, 0, len(keys))
	for _, key := range keys {
		if val := bm[key].String(); val != "" {
			qs = append(qs, key+"="+val)
		}
	}
	qs = append(qs, "key="+s1.key)
	h := md5.Sum([]byte(strings.Join(qs, "&")))
	return hex.EncodeToString(h[:])
}

func (s1 *sign1) Encode(bs *[]byte) error {
	*bs, _ = sjson.SetBytes(*bs, "sign", s1.signOf(*bs))
	return nil
}

func (s1 *sign1) Decode(bs *[]byte) error {
	if strings.ToLower(gjson.GetBytes(*bs, "sign").Str) != s1.signOf(*bs) {
		if gjson.GetBytes(*bs, "head.respCode").Str != codeSuccess {
			return nil
		}
		return errors.New("invalid sign")
	}
	return nil
}

func newSign2(pubKey, priKey string) (signer, error) {
	s := &sign2{}
	{
		block, _ := pem.Decode([]byte(pubKey))
		if block == nil {
			return nil, errors.New("invalid public key")
		}
		pubKey, err := x509.ParsePKIXPublicKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		s.pubKey = pubKey.(*rsa.PublicKey)
	}
	{
		block, _ := pem.Decode([]byte(priKey))
		if block == nil {
			return nil, errors.New("invalid private key")
		}
		priKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		s.priKey = priKey.(*rsa.PrivateKey)
	}
	return s, nil
}

type sign2 struct {
	pubKey *rsa.PublicKey
	priKey *rsa.PrivateKey
}

func (s2 *sign2) Encode(bs *[]byte) error {
	partLen := s2.pubKey.N.BitLen()/8 - 11
	chunks := split([]byte(gjson.GetBytes(*bs, "body").Raw), partLen)
	buffer := bytes.NewBuffer(nil)
	for _, chunk := range chunks {
		bss, err := rsa.EncryptPKCS1v15(rand.Reader, s2.pubKey, chunk)
		if err != nil {
			return err
		}
		buffer.Write(bss)
	}
	*bs, _ = sjson.SetBytes(*bs, "body", url.QueryEscape(base64.StdEncoding.EncodeToString(buffer.Bytes())))
	return nil
}

func (s2 *sign2) Decode(bs *[]byte) error {
	bs1, _ := url.QueryUnescape(gjson.GetBytes(*bs, "body").Str)
	raw, _ := base64.StdEncoding.DecodeString(bs1)
	partLen := s2.pubKey.N.BitLen() / 8
	chunks := split(raw, partLen)
	buffer := bytes.NewBuffer(nil)
	for _, chunk := range chunks {
		bss, err := rsa.DecryptPKCS1v15(rand.Reader, s2.priKey, chunk)
		if err != nil {
			return err
		}
		buffer.Write(bss)
	}
	if bs2 := buffer.Bytes(); len(bs2) > 0 {
		*bs, _ = sjson.SetRawBytes(*bs, "body", bs2)
	}
	return nil
}

func split(buf []byte, lim int) [][]byte {
	var chunk []byte
	chunks := make([][]byte, 0, len(buf)/lim+1)
	for len(buf) >= lim {
		chunk, buf = buf[:lim], buf[lim:]
		chunks = append(chunks, chunk)
	}
	if len(buf) > 0 {
		chunks = append(chunks, buf[:])
	}
	return chunks
}
