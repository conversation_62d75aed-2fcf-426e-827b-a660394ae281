package ganopay

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

type orderTime time.Time

func (o orderTime) MarshalJSON() ([]byte, error) {
	return []byte(time.Time(o).In(ctz.Brazil).Format("20060102150405")), nil
}

func (o *orderTime) UnmarshalJSON(data []byte) error {
	data = data[len(`"`) : len(data)-len(`"`)]
	if len(data) == 0 {
		return nil
	}
	t, err := time.ParseInLocation("20060102150405", string(data), ctz.Brazil)
	if err == nil {
		*o = orderTime(t)
	}
	return err
}

func (o orderTime) String() string {
	return time.Time(o).String()
}
