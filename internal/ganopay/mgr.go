package ganopay

import (
	"context"
	"net/url"

	"github.com/go-resty/resty/v2"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/act"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

func newManager(cfg conf.Ganopay, dm *redi.Mutex, ps *pay.Service, pm *payermax.Manager, act act.Logger, log *zap.Logger) (*Manager, error) {
	sig, err := newSign2(cfg.PublicKey, cfg.PrivateKey)
	if err != nil {
		return nil, err
	}
	return &Manager{
		sig1: newSign1(cfg.<PERSON><PERSON><PERSON>),
		sig2: sig,
		cfg:  cfg,
		dm:   dm,
		ps:   ps,
		pm:   pm,
		act:  act,
		log:  log,
		cli:  resty.New().SetBaseURL(cfg.Endpoint),
	}, nil
}

type Manager struct {
	sig1 signer
	sig2 signer
	cfg  conf.Ganopay
	dm   *redi.Mutex
	ps   *pay.Service
	pm   *payermax.Manager
	act  act.Logger
	log  *zap.Logger
	cli  *resty.Client
	// task
	chk1 dq.Queue[*pendingOrder]
	// setting
	notifyUrl string
}

func (s *Manager) notifyOf(typ string) string {
	var notifyUrl string
	if s.notifyUrl != "" {
		u, _ := url.Parse(s.notifyUrl)
		q := u.Query()
		q.Add("type", typ)
		u.RawQuery = q.Encode()
		notifyUrl = u.String()
	}
	return notifyUrl
}

func (s *Manager) Methods(ctx context.Context, userId string, country pay.Country, scene, sku string) ([]payermax.Method, error) {
	payments := countryPayments[country]
	if len(payments) == 0 {
		return nil, nil
	}

	submit, err := s.ps.Forms(ctx, scene, sku, payments[0].Cur)
	if err != nil {
		return nil, err
	}

	out := make([]payermax.Method, 0, len(payments))
	for _, payment := range payments {
		price, err := s.adjPrice(scene, submit.Price, payment)
		if err != nil {
			return nil, err
		}
		if !validPrice(country, payment.Org, price) {
			continue
		}
		out = append(out, payermax.Method{
			Payment: payment,
			Price:   price,
		})
	}

	return out, nil
}
