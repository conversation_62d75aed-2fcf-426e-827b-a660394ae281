package ganopay

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

var countryPayments = map[pay.Country][]payermax.Payment{
	pay.BR: {
		{
			Name: "PIX",
			Logo: "{oss}/payermax/Rectangular_432x216/BR/PIX_BR.png",
			Type: "GANOPAY",
			Org:  "bq101",
			Cur:  pay.BRL,
			Fee:  payermax.Fee{Rate: 0.019, Lowest: 0.3, Tax: 0.0038},
		},
	},
}

func paymentOf(c pay.Country, org string) (payermax.Payment, bool) {
	return payermax.FindPayment(countryPayments, c, "GANOPAY", org)
}

var countryLimits = map[pay.Country]map[string][2]float64{
	pay.BR: {
		"bq101": {10, 50000},
	},
}

func validPrice(c pay.Country, org string, val float64) bool {
	if dbg.Ing() {
		return true
	}
	if l, has := countryLimits[c][org]; has {
		return val >= l[0] && val <= l[1]
	}
	return true
}
