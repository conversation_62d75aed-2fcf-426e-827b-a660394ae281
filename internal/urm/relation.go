package urm

type Relation uint32

const (
	RelationNone Relation = 0
	RelationTo   Relation = 1 << 0 // A block B
	RelationBy   Relation = 1 << 1 // A is blocked by B
)

func (r Relation) String() string {
	switch r {
	case RelationTo:
		return "to"
	case RelationBy:
		return "by"
	case RelationTo | RelationBy:
		return "to|by"
	default:
		return "none"
	}
}

func (r Relation) IsBlockTo() bool {
	return r&RelationTo > 0
}

func (r Relation) IsBlockBy() bool {
	return r&RelationBy > 0
}

func (r Relation) IsNone() bool {
	return r == RelationNone
}
