package urm

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var (
	ErrNoPermission = biz.NewError(biz.ErrNoPermission, "no permission")
	ErrGrantTooMany = biz.NewError(biz.ErrGrantTooMany, "grant too many")
)

const (
	UserGrantLimit = 120
)

func grantTooMany(_ GrantKind) error {
	return ErrGrantTooMany
}

type ListedGrants struct {
	Grants []Grant
	Cursor string
	Total  int64
}

func (m *Manager) listGrants(ctx context.Context, userId string, cursor string, kind GrantKind) (*ListedGrants, error) {
	c, err := db.ParseCursor[int64](cursor)
	if err != nil {
		return nil, fmt.Errorf("listGrants: parse cursor: %w", err)
	}

	var (
		f          = bson.M{"userId": userId, "kind": kind}
		collection = m.mc.Collection(GrantCollection)
	)

	n, err := collection.CountDocuments(ctx, f)
	if err != nil {
		return nil, fmt.Errorf("listGrants: count grants: %w", err)
	}

	if n == 0 {
		return &ListedGrants{}, nil
	}

	cur, err := collection.Find(ctx,
		f,
		options.Find().SetSkip(c.Offset).SetLimit(c.Limit).SetSort(bson.M{"createdAt": -1}))
	if err != nil {
		return nil, fmt.Errorf("listGrants: find grants: %w", err)
	}

	var grants []Grant
	if err := cur.All(ctx, &grants); err != nil {
		return nil, fmt.Errorf("listGrants: decode grants:  %w", err)
	}

	var ncs string
	if c.Offset+int64(len(grants)) < n {
		ncs = c.Skip(int64(len(grants))).MustEncode()
	}

	return &ListedGrants{Total: n, Cursor: ncs, Grants: grants}, nil
}

func (m *Manager) ListBlacklist(ctx context.Context, userId string, cursor string) (*ListedGrants, error) {
	return m.listGrants(ctx, userId, cursor, GrantKindBlacklist)
}

func (m *Manager) ListMuted(ctx context.Context, userId string, cursor string) (*ListedGrants, error) {
	return m.listGrants(ctx, userId, cursor, GrantKindMute)
}

func (m *Manager) AddList(ctx context.Context, k GrantKind, opUserId, userId, targetId string, sessionId string, at time.Time) error {
	logger := m.logger.With(
		zap.String("opUserId", opUserId),
		zap.String("sessionId", sessionId),
		zap.String("userId", userId),
		zap.String("targetId", targetId),
		zap.Any("kind", k),
	)

	if userId == targetId {
		return ErrNoPermission
	}

	if m.pg.Take(ctx, targetId).Valid() {
		return ErrNoPermission
	}

	if err := m.HasPermission(ctx, opUserId, userId); err != nil {
		logger.Error("add blacklist has permission", zap.Error(err))
		return fmt.Errorf("add blacklist: %w", err)
	}

	if err := m.addList(ctx, k, opUserId, userId, targetId, sessionId, at); err != nil {
		if !biz.Is(err) {
			logger.Error("add blacklist", zap.Error(err))
		}
		return fmt.Errorf("add blacklist: %w", err)
	}

	logger.Debug("add blacklist done")

	m.evb.Emit(ctx, evt.EvUserGrantUpdate, evt.UserGrantUpdate{
		Kind:     k.EvtKind(),
		OP:       evt.GrantOpTypeAdd,
		UserId:   userId,
		TargetId: targetId,
		At:       at,
	})

	return nil
}

func (m *Manager) addList(ctx context.Context, k GrantKind, opUserId, userId, targetId string, sessionId string, at time.Time) error {
	defer m.cleanUserCache(userId)

	{
		n, err := m.mc.Collection(GrantCollection).CountDocuments(ctx, bson.M{"userId": userId, "kind": k})
		if err != nil {
			return err
		}

		if n > UserGrantLimit {
			return grantTooMany(k)
		}
	}

	err := m.mc.TryTxn(ctx, func(ctx context.Context) error {
		grant := newGrant(sessionId, userId, targetId, k, opUserId, at)
		if _, err := m.mc.Collection(GrantCollection).InsertOne(ctx, grant); err != nil {
			return err
		}

		op := newOperation(opUserId, sessionId, userId, targetId, k, OpTypeAdd, grant.ID, at)
		if _, err := m.mc.Collection(OperationCollection).InsertOne(ctx, op); err != nil {
			return fmt.Errorf("add blacklist operation log: %w", err)
		}

		if k == GrantKindBlacklist {
			if err := m.taskQ.Submit(ctx, 0, &Task{
				Kind:     GrantKindBlacklist,
				OpUserId: opUserId,
				UserId:   userId,
				TargetId: targetId,
				At:       at,
			}); err != nil {
				return fmt.Errorf("submit task: %w", err)
			}
		}

		return nil
	})

	if err != nil && db.IsDuplicate(err) {
		m.logger.Info("add blacklist: duplicate", zap.String("userId", userId), zap.String("targetId", targetId), zap.Any("kind", k))
		return nil
	}

	return err
}

func (m *Manager) RemoveList(ctx context.Context, k GrantKind, opUserId, userId, targetId string) error {
	logger := m.logger.With(
		zap.String("opUserId", opUserId),
		zap.String("userId", userId),
		zap.String("targetId", targetId),
		zap.Any("kind", k),
	)

	if err := m.HasPermission(ctx, opUserId, userId); err != nil {
		logger.Error("remove blacklist has permission", zap.Error(err))
		return fmt.Errorf("remove blacklist: %w", err)
	}

	if err := m.removeList(ctx, k, opUserId, userId, targetId); err != nil {
		logger.Error("remove blacklist", zap.Error(err))
		return fmt.Errorf("remove blacklist: %w", err)
	}

	m.evb.Emit(ctx, evt.EvUserGrantUpdate, evt.UserGrantUpdate{
		Kind:     k.EvtKind(),
		OP:       evt.GrantOpTypeRemove,
		UserId:   userId,
		TargetId: targetId,
		At:       time.Now(),
	})

	return nil
}

func (m *Manager) removeList(ctx context.Context, k GrantKind, opUserId, userId, targetId string) error {
	var (
		deleted *Grant
		at      = time.Now()
	)

	defer m.cleanUserCache(userId)

	err := m.mc.TryTxn(ctx, func(ctx context.Context) error {
		if err := m.mc.Collection(GrantCollection).
			FindOneAndDelete(ctx, bson.M{"userId": userId, "targetId": targetId, "kind": k}).
			Decode(&deleted); err != nil {
			return err
		}

		op := newOperation(opUserId, deleted.SessionId, userId, targetId, k, OpTypeRemove, deleted.ID, at)
		if _, err := m.mc.Collection(OperationCollection).InsertOne(ctx, op); err != nil {
			return fmt.Errorf("remove blacklist operation log: %w", err)
		}

		if k == GrantKindBlacklist {
			if err := m.taskQ.Submit(ctx, 0, &Task{
				Kind:     GrantKindBlacklist,
				OpUserId: opUserId,
				UserId:   userId,
				TargetId: targetId,
				At:       at,
			}); err != nil {
				return fmt.Errorf("submit task: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			m.logger.Info("remove blacklist: not found", zap.String("userId", userId), zap.String("targetId", targetId), zap.String("kind", k.String()))
			return nil
		}

		return fmt.Errorf("remove blacklist: %w", err)
	}

	m.logger.Info("remove blacklist",
		zap.Any("deleted", deleted),
		zap.String("opUserId", opUserId),
		zap.String("userId", userId),
		zap.String("targetId", targetId),
		zap.Any("kind", k),
	)

	return nil
}

// HasPermission 如果有权限返回 nil, 否则返回错误
func (m *Manager) HasPermission(ctx context.Context, opUserId, userId string) error {
	if opUserId == userId {
		return nil
	}

	ua, err := m.User(ctx, userId)
	if err != nil {
		return fmt.Errorf("has permission: %w", err)
	}

	if slices.Contains(ua.Admins, opUserId) {
		return nil
	}

	return fmt.Errorf("has permission: %w", ErrNoPermission)
}
