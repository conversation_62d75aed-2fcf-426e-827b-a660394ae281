package urm

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/dq"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type Task struct {
	Kind     GrantKind
	OpUserId string
	UserId   string
	TargetId string
	At       time.Time
}

func (m *Manager) Register() {
	m.taskQ.Register(m.ProcessTask, dq.LogCost("urm.task"))
}

func (m *Manager) ProcessTask(ctx context.Context, tsk *Task) error {
	switch tsk.Kind {
	case GrantKindBlacklist:
		return m.processBlacklistTask(ctx, tsk)
	default:
		return nil
	}
}

func (m *Manager) processBlacklistTask(ctx context.Context, tsk *Task) error {
	m.logger.Debug("process blacklist task",
		zap.String("task", tsk.Kind.String()),
		zap.String("opUserId", tsk.OpUserId),
		zap.String("userId", tsk.UserId),
		zap.String("targetId", tsk.TargetId),
	)

	var (
		found bool
		grant Grant
		err   error
	)
	if err := m.mc.Collection(GrantCollection).FindOne(ctx,
		bson.M{
			"userId":   tsk.UserId,
			"targetId": tsk.TargetId,
			"kind":     tsk.Kind,
		}).Decode(&grant); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return fmt.Errorf("find grant: %w", err)
		}
	} else {
		found = true
	}

	if found {
		err = m.imm.AddBlackList(ctx, tsk.UserId, tsk.TargetId)
	} else {
		err = m.imm.RemoveBlackList(ctx, tsk.UserId, tsk.TargetId)
	}

	if err != nil {
		return fmt.Errorf("im: %w", err)
	}

	m.logger.Debug("process blacklist task",
		zap.String("task", tsk.Kind.String()),
		zap.String("opUserId", tsk.OpUserId),
		zap.String("userId", tsk.UserId),
		zap.String("targetId", tsk.TargetId),
		zap.Bool("found", found),
	)
	return nil
}
