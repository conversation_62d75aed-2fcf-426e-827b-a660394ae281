package urm

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	UserCollection      = "urm.users"
	GrantCollection     = "urm.grants"
	OperationCollection = "urm.operations"
)

type User struct {
	ID          string    `bson:"_id"`         // anchorId
	BannedWords []string  `bson:"bannedWords"` // 禁言词, 用于直播间聊天, 最长10个字
	Admins      []string  `bson:"admins"`      // 管理员列表上限30个
	CreatedAt   time.Time `bson:"createdAt"`
	UpdatedAt   time.Time `bson:"updatedAt"`
}

type GrantKind int

const (
	GrantKindNone      GrantKind = iota
	GrantKindBlacklist           // 黑名单
	GrantKindMute
)

func (k GrantKind) String() string {
	switch k {
	case GrantKindBlacklist:
		return "blacklist"
	case GrantKindMute:
		return "mute"
	default:
		return "none"
	}
}

func (k GrantKind) EvtKind() evt.GrantKind {
	switch k {
	case GrantKindBlacklist:
		return evt.GrantKindBlacklist
	case GrantKindMute:
		return evt.GrantKindMute
	default:
		return evt.GrantKindNone
	}
}

type Grant struct {
	ID        primitive.ObjectID `bson:"_id"`
	SessionId string             `bson:"sessionId"`
	UserId    string             `bson:"userId"`
	TargetId  string             `bson:"targetId"`
	Kind      GrantKind          `bson:"kind"`
	Operator  string             `bson:"operator"` // 操作人，主播或者管理员
	CreatedAt time.Time          `bson:"createdAt"`
}

func newGrant(sessionId, userId, targetId string, k GrantKind, operator string, at time.Time) Grant {
	return Grant{
		ID:        primitive.NewObjectIDFromTimestamp(at),
		SessionId: sessionId,
		UserId:    userId,
		TargetId:  targetId,
		Kind:      k,
		Operator:  operator,
		CreatedAt: at,
	}
}

type OpType int

const (
	OpTypeNone OpType = iota
	OpTypeAdd
	OpTypeRemove
)

type Operation struct {
	ID        primitive.ObjectID `bson:"_id"`
	SessionId string             `bson:"sessionId"`
	UserId    string             `bson:"userId"`
	TargetId  string             `bson:"targetId"`
	Kind      GrantKind          `bson:"kind"`
	OpType    OpType             `bson:"opType"`
	GrantId   primitive.ObjectID `bson:"grantId"`
	Operator  string             `bson:"operator"` // 操作人，主播或者管理员
	CreatedAt time.Time          `bson:"createdAt"`
}

func newOperation(opUserId, sessionId, userId, targetId string, k GrantKind, opt OpType, grantId primitive.ObjectID, at time.Time) Operation {
	return Operation{
		ID:        primitive.NewObjectIDFromTimestamp(at),
		SessionId: sessionId,
		UserId:    userId,
		TargetId:  targetId,
		Kind:      k,
		OpType:    opt,
		GrantId:   grantId,
		Operator:  opUserId,
		CreatedAt: at,
	}
}

var grantIndexers = []db.Indexer{
	{
		Name: "sessionId_userId_createdAt",
		Keys: bson.D{
			{Key: "sessionId", Value: 1},
			{Key: "userId", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},
	{
		Name: "userId_kind_createdAt",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "kind", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},
	{
		Name: "sessionId_createdAt",
		Keys: bson.D{
			{Key: "sessionId", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},

	{
		Name: "userId_targetId_kind",
		Uniq: lo.ToPtr(true),
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "targetId", Value: 1},
			{Key: "kind", Value: 1},
		},
	},

	{
		Name: "userId_kind",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "kind", Value: 1},
		},
	},
}

var operationIndexers = []db.Indexer{
	{
		Name: "userId",
		Keys: bson.D{
			{Key: "userId", Value: 1},
		},
	},
	{
		Name: "userId_kind_createdAt",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "kind", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},
	{
		Name: "userId_operator_createdAt",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "operator", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},
}
