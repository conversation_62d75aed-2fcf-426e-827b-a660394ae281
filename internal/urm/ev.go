package urm

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *Manager) onUserDeleted(ctx context.Context, deleted *user.Account) error {
	res, err := m.mc.Collection(GrantCollection).DeleteMany(ctx, bson.M{"$or": []bson.M{
		{"userId": deleted.UserId},
		{"targetId": deleted.UserId},
	}})

	if err != nil {
		m.logger.Error("user deleted: delete grants", zap.Error(err), zap.Any("deleted", deleted))
		return err
	}

	if res.DeletedCount > 0 {
		m.logger.Info("user deleted: delete grants",
			zap.Int64("deletedCount", res.DeletedCount),
			zap.Any("deleted", deleted),
			zap.Any("res", res),
		)
	}

	return nil
}
