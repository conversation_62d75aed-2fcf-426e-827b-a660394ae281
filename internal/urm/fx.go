package urm

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	mc *db.MongoClient,
	syn cc.Sync,
	evb ev.Bus,
	imm *im.Manager,
	pg patrol.Getter,
	dm *dq.Master,
	vnd log.Vendor,
) *Manager {

	mc.SyncSchema(UserCollection, 1)
	mc.SyncSchema(GrantCollection, 1, grantIndexers...)
	mc.SyncSchema(OperationCollection, 1, operationIndexers...)

	m := &Manager{
		mc:     mc,
		evb:    evb,
		imm:    imm,
		pg:     pg,
		taskQ:  dq.NewWith[*Task](dm, "urm.task"),
		logger: vnd.Scope("urm"),
	}

	m.userBlocks = cc.New[string, []string](4096,
		cc.LRU,
		cc.Expiration(time.Minute*15),
		cc.ExportStats("urm.user.block"),
		cc.WithSync(syn, "urm.user.blocks"),
		cc.LoaderFunc(func(userId string) ([]string, error) {
			return m.GetUserBlockList(context.TODO(), userId)
		}),
	)

	m.userMuted = cc.New[string, []string](2048,
		cc.LRU,
		cc.Expiration(time.Minute),
		cc.ExportStats("urm.user.muted"),
		cc.WithSync(syn, "urm.user.muted"),
		cc.LoaderFunc(func(userId string) ([]string, error) {
			return m.GetUserMutedList(context.TODO(), userId)
		}),
	)

	evb.Watch(user.EvUserDeleted, "urm.user.deleted", ev.NewWatcher(m.onUserDeleted), ev.WithAsync())

	return m
}

func InvokeTask(m *Manager) {
	m.Register()
}
