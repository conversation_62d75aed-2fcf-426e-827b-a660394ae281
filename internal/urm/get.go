package urm

import (
	"context"
	"slices"

	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UserBlockRelation returns the block relation between userId and targetUserId using cache
func (m *Manager) UserBlockRelation(ctx context.Context, userId, targetId string) (Relation, error) {
	blockTo, err := m.IsBlockTo(ctx, userId, targetId)
	if err != nil {
		return RelationNone, err
	}

	blockBy, err := m.IsBlockTo(ctx, targetId, userId)
	if err != nil {
		return RelationNone, err
	}

	var r Relation
	if blockTo {
		r |= RelationTo
	}

	if blockBy {
		r |= RelationBy
	}
	return r, nil
}

func (m *Manager) IsBlockTo(ctx context.Context, userId, targetId string) (bool, error) {
	lst, err := m.userBlocks.Get(userId)
	if err != nil {
		return false, err
	}

	return slices.Contains(lst, targetId), nil
}

func (m *Manager) GetUserBlockList(ctx context.Context, userId string) ([]string, error) {
	cur, err := m.mc.Collection(GrantCollection).Find(
		ctx,
		bson.M{"userId": userId, "kind": GrantKindBlacklist},
		options.Find().SetProjection(bson.M{"targetId": 1}),
	)

	if err != nil {
		return nil, err
	}

	var grants []Grant
	if err := cur.All(ctx, &grants); err != nil {
		return nil, err
	}

	return lo.Map(grants, func(g Grant, _ int) string {
		return g.TargetId
	}), nil
}

func (m *Manager) FilterBlockList(ctx context.Context, userId string, targets []string) ([]string, error) {
	lst, err := m.userBlocks.Get(userId)
	if err != nil {
		return nil, err
	}

	var out []string
	for _, target := range targets {
		if slices.Contains(lst, target) {
			continue
		}

		is, err := m.IsBlockTo(ctx, target, userId)
		if err != nil {
			return nil, err
		}

		if !is {
			out = append(out, target)
		}
	}

	return out, nil
}

// AnyDirBlockList 获取用户拉黑或者被拉黑的用户列表
func (m *Manager) AnyDirBlockList(ctx context.Context, userId string, offset, limit int64) ([]string, error) {
	filter := bson.M{
		"$or": []bson.M{
			{
				"userId": userId,
				"kind":   GrantKindBlacklist,
			},
			{
				"targetId": userId,
				"kind":     GrantKindBlacklist,
			},
		},
	}

	opts := options.Find().SetProjection(bson.M{"targetId": 1, "userId": 1}).SetSort(bson.M{"createdAt": -1})
	if offset > 0 {
		opts.SetSkip(offset)
	}

	if limit > 0 {
		opts.SetLimit(limit)
	}

	cur, err := m.mc.Collection(GrantCollection).Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var grants []Grant
	if err := cur.All(ctx, &grants); err != nil {
		return nil, err
	}

	var out []string
	for _, g := range grants {
		if g.UserId == userId {
			out = append(out, g.TargetId)
		} else {
			out = append(out, g.UserId)
		}
	}

	return lo.Uniq(out), nil
}

func (m *Manager) GetUserMutedList(ctx context.Context, userId string) ([]string, error) {
	cur, err := m.mc.Collection(GrantCollection).Find(
		ctx,
		bson.M{"userId": userId, "kind": GrantKindMute},
		options.Find().SetProjection(bson.M{"targetId": 1}),
	)

	if err != nil {
		return nil, err
	}

	var grants []Grant
	if err := cur.All(ctx, &grants); err != nil {
		return nil, err
	}

	return lo.Map(grants, func(g Grant, _ int) string {
		return g.TargetId
	}), nil
}

func (m *Manager) IsMuted(ctx context.Context, userId string, targetId string) (bool, error) {
	lst, err := m.userMuted.Get(userId)
	if err != nil {
		return false, err
	}

	return slices.Contains(lst, targetId), nil
}

func (m *Manager) UserMutedRelation(ctx context.Context, userId, targetId string) (Relation, error) {
	mutedTo, err := m.IsMuted(ctx, userId, targetId)
	if err != nil {
		return RelationNone, err
	}

	mutedBy, err := m.IsMuted(ctx, targetId, userId)
	if err != nil {
		return RelationNone, err
	}

	var r Relation
	if mutedTo {
		r |= RelationTo
	}

	if mutedBy {
		r |= RelationBy
	}
	return r, nil
}
