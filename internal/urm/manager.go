package urm

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type Manager struct {
	mc         *db.MongoClient
	imm        *im.Manager
	pg         patrol.Getter
	evb        ev.Bus
	userBlocks cc.Cache[string, []string]
	userMuted  cc.Cache[string, []string]
	taskQ      dq.Queue[*Task]
	logger     *zap.Logger
}

func (m *Manager) User(ctx context.Context, userId string) (*User, error) {
	var out User
	if err := m.mc.Collection(UserCollection).FindOne(ctx, bson.M{"_id": userId}).Decode(&out); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &out, nil
		}
		return nil, err
	}

	return &out, nil
}

func (m *Manager) SetBannedWords(ctx context.Context, opUserId, userId string, words []string, at time.Time) error {
	m.logger.Debug("set banned words", zap.String("userId", userId), zap.Strings("words", words))

	up := bson.M{
		"$set": bson.M{
			"bannedWord": words,
			"updatedAt":  at,
		},
		"$setOnInsert": bson.M{
			"_id":       userId,
			"createdAt": at,
		},
	}
	ur, err := m.mc.Collection(UserCollection).UpdateOne(ctx, bson.M{"_id": userId}, up, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}

	switch {
	case ur.ModifiedCount > 0:
		m.logger.Debug("set banned words done", zap.String("userId", userId), zap.Strings("words", words))
	case ur.UpsertedCount > 0:
		m.logger.Debug("set banned words new", zap.String("userId", userId), zap.Strings("words", words))
	default:
		m.logger.Error("set banned words skip", zap.String("userId", userId), zap.Strings("words", words))
		return fmt.Errorf("set banned words skip: %w", mongo.ErrNoDocuments)
	}

	return nil
}

func (m *Manager) AddManager(ctx context.Context, opUserId, userId string, manager string, at time.Time) error {
	m.logger.Debug("add manager", zap.String("userId", userId), zap.String("manager", manager))

	up := bson.M{
		"$addToSet": bson.M{
			"admin": manager,
		},
		"$setOnInsert": bson.M{
			"_id":       userId,
			"createdAt": at,
		},
	}
	ur, err := m.mc.Collection(UserCollection).UpdateOne(ctx, bson.M{"_id": userId}, up, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}

	switch {
	case ur.ModifiedCount > 0:
		m.logger.Debug("add manager done", zap.String("userId", userId), zap.String("manager", manager))
	case ur.UpsertedCount > 0:
		m.logger.Debug("add manager new", zap.String("userId", userId), zap.String("manager", manager))
	default:
		m.logger.Error("add manager skip", zap.String("userId", userId), zap.String("manager", manager))
		return fmt.Errorf("add manager skip: %w", mongo.ErrNoDocuments)
	}

	return nil
}

func (m *Manager) RemoveManager(ctx context.Context, opUserId, userId string, manager string, at time.Time) error {
	m.logger.Debug("remove manager", zap.String("userId", userId), zap.String("manager", manager))

	up := bson.M{
		"$pull": bson.M{
			"admin": manager,
		},
	}

	if _, err := m.mc.Collection(UserCollection).UpdateOne(ctx, bson.M{"_id": userId}, up); err != nil {
		return err
	}

	return nil
}
