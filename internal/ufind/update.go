package ufind

import (
	"context"

	"gitlab.sskjz.com/go/es"
	"gitlab.sskjz.com/go/es/index"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const (
	idxName = "osl-users"
)

var idxFields = map[string]index.Field{
	"showId":   index.Wildcard,
	"nickname": index.SimpleText,
}

func (s *Indexer) onUserCreate(ctx context.Context, acc *user.Account) error {
	return s.ec.Create(ctx, idxName, acc.UserId, map[string]any{
		"showId":   acc.ShowId,
		"nickname": acc.Nickname,
	})
}

func (s *Indexer) onUserUpdate(ctx context.Context, evd *user.EvUpdating) error {
	old, acc := evd.Old, evd.New

	if acc.Status.Deleted() {
		return nil
	}

	update := make(map[string]any)
	if old.ShowId != acc.ShowId {
		update["showId"] = acc.ShowId
	}
	if old.Nickname != acc.Nickname {
		update["nickname"] = acc.Nickname
	}

	if len(update) == 0 {
		return nil
	}

	return s.ec.Update(ctx, idxName, acc.UserId, update, es.WithUpsert())
}

func (s *Indexer) onUserDelete(ctx context.Context, acc *user.Account) error {
	return s.ec.Delete(ctx, idxName, acc.UserId)
}
