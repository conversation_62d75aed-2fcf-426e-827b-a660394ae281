package ufind

import (
	"cmp"
	"context"
	"slices"

	"github.com/samber/lo"
	"github.com/sourcegraph/conc/pool"
	"gitlab.sskjz.com/go/es/query"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
)

type sResult struct {
	priority int
	userIds  []string
}

func (s *Indexer) Search(ctx context.Context, userId string, keyword string, limit int) ([]string, error) {
	p := pool.NewWithResults[*sResult]().WithErrors().WithContext(ctx)

	p.Go(func(ctx context.Context) (*sResult, error) {
		userIds, err := s.searching(ctx, keyword, limit)
		if err != nil {
			return nil, err
		}
		return &sResult{priority: 1, userIds: userIds}, nil
	})

	p.Go(func(ctx context.Context) (*sResult, error) {
		_, items, err := s.fm.Followings(ctx, userId, keyword, "")
		if err != nil {
			return nil, err
		}
		return &sResult{priority: 0, userIds: lo.Map(items, func(f *follow.Follow, _ int) string { return f.Target })}, nil
	})

	rs, err := p.Wait()
	if err != nil {
		return nil, err
	}

	slices.SortFunc(rs, func(a, b *sResult) int {
		return cmp.Compare(a.priority, b.priority)
	})

	out := make([]string, 0, limit)
	for _, r := range rs {
		for _, id := range r.userIds {
			if slices.Contains(out, id) {
				continue
			}
			out = append(out, id)
			if len(out) >= limit {
				break
			}
		}
	}

	return out, nil
}

func (s *Indexer) searching(ctx context.Context, keyword string, limit int) ([]string, error) {
	resp, err := s.ec.Search(ctx, idxName,
		query.NoSource(), query.Limit(limit),
		query.ShouldContain("showId", keyword),
		query.ShouldMatch("nickname", keyword, query.WithFuzzy()),
	)
	if err != nil {
		return nil, err
	}
	return resp.DocIds(), nil
}
