package ufind

import (
	"gitlab.sskjz.com/go/es"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(ec *es.Client, fm *follow.Manager, vnd log.Vendor) *Indexer {
	ec.ApplyIndex(idxName, idxFields)
	return &Indexer{ec: ec, fm: fm, log: vnd.Scope("ufind.indexer")}
}

func Invoke(evb ev.Bus, idx *Indexer) {
	evb.Watch(user.EvUserCreated, "ufind.indexing", ev.<PERSON>(idx.onUserCreate), ev.WithAsync())
	evb.Watch(user.EvUserUpdated, "ufind.indexing", ev.<PERSON>(idx.onUserUpdate), ev.WithAsync())
	evb.Watch(user.EvUserDeleted, "ufind.indexing", ev.<PERSON>(idx.onUserDelete), ev.WithAsync())
}
