package face

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	ErrFaceNotFound = biz.NewError(biz.ErrBusiness, "face not found")
)

func (m *Manager) GetFaceUrl(ctx context.Context, userId string, scene FaceScene) (string, error) {
	var face Face

	err := m.dbmc.Collection(FaceCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
			"scene":  string(scene),
		},
		options.FindOne().SetSort(bson.M{"createdAt": -1}),
	).Decode(&face)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return "", ErrFaceNotFound
		}

		return "", err
	}

	return face.FaceUrl, nil
}

func (m *Manager) GetFaceList(ctx context.Context, userId string, scene FaceScene) ([]Face, error) {
	filter := bson.M{
		"userId": userId,
	}

	if scene != FaceSceneAll {
		filter["scene"] = string(scene)
	}

	cursor, err := m.dbmc.Collection(FaceCollectionName()).Find(
		ctx,
		filter,
		options.Find().SetSort(bson.M{"createdAt": -1}),
	)

	if err != nil {
		return nil, err
	}

	var faces []Face

	err = cursor.All(ctx, &faces)

	if err != nil {
		return nil, err
	}

	return faces, nil
}
