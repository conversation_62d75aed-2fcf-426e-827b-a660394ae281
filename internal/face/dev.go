package face

import (
	"context"

	"gitlab.sskjz.com/go/redi"
)

func devAllowedFace(ctx context.Context, rc *redi.Client, userId string) bool {
	return true
	// key := fmt.Sprintf("DEV:FACE:ALLOWED:%s", userId)
	// txp := rc.Pipeline()
	// cnt := txp.Incr(ctx, key)
	// txp.Expire(ctx, key, 5*time.Minute)
	// if _, err := txp.Exec(ctx); err != nil {
	// 	return false
	// }
	// return cnt.Val() > 3
}
