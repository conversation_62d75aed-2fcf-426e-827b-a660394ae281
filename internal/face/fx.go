package face

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dun"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	dbmc *db.MongoClient,
	df dun.Face,
	um *user.Manager,
	rc *redi.Client,
	dm *redi.Mutex,
	vnd log.Vendor,
) (*Manager, error) {
	createFaceCollectionIndexer(dbmc)
	createFaceRecordCollectionIndexer(dbmc)

	m := newManager(dbmc, df, um, rc, dm, vnd.Scope("face.mgr"))

	return m, nil
}
