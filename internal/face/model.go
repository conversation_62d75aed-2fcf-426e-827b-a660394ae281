package face

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FaceScene string

const (
	FaceSceneAll      FaceScene = ""
	FaceSceneLive     FaceScene = "live"
	FaceSceneWithdraw FaceScene = "withdraw"
)

// 人脸图
type Face struct {
	Id        primitive.ObjectID `bson:"_id" json:"-"`               // 主键
	UserId    string             `bson:"userId" json:"userId"`       // 主播ID
	Scene     string             `bson:"scene" json:"scene"`         // 场景
	FaceUrl   string             `bson:"faceUrl" json:"faceUrl"`     // 人脸地址
	CreatedAt time.Time          `bson:"createdAt" json:"createdAt"` // 创建时间
}

// 用户可能有多个
func FaceCollectionName() string {
	return "face"
}

func createFaceCollectionIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(FaceCollectionName(), 1,
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}},
	)
}

type FaceRecord struct {
	Id         primitive.ObjectID `bson:"_id"`        // 主键
	UserId     string             `bson:"userId"`     // 主播ID
	Scene      string             `bson:"scene"`      // 场景
	FaceUrl    string             `bson:"faceUrl"`    // 人脸地址
	State      string             `bson:"state"`      // 认证状态 success成功 fail失败
	Reason     string             `bson:"reason"`     // 失败原因
	MatchFaces []MatchFace        `bson:"matchFaces"` // 相似人脸
	CreatedAt  time.Time          `bson:"createdAt"`  // 创建时间
}

type MatchFace struct {
	Score   float64 `bson:"score"`
	Name    string  `bson:"name"`
	FaceId  string  `bson:"faceId"`
	FaceUrl string  `bson:"faceUrl"`
}

func FaceRecordCollectionName() string {
	return "face.record"
}

func createFaceRecordCollectionIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(FaceRecordCollectionName(), 1,
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}},
	)
}
