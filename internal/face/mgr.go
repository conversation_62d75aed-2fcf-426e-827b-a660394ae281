package face

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dun"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	dbmc *db.MongoClient
	df   dun.Face
	um   *user.Manager
	rc   *redi.Client
	dm   *redi.Mutex
	log  *zap.Logger
}

func newManager(
	dbmc *db.MongoClient,
	df dun.Face,
	um *user.Manager,
	rc *redi.Client,
	dm *redi.Mutex,
	log *zap.Logger,
) *Manager {
	m := &Manager{
		dbmc: dbmc,
		df:   df,
		um:   um,
		rc:   rc,
		dm:   dm,
		log:  log,
	}

	return m
}
