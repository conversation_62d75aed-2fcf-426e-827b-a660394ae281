package face

import (
	"context"

	"github.com/google/uuid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dun"
	"go.uber.org/zap"
)

func (m *Manager) Delete(ctx context.Context, userId string) error {
	dataId := uuid.NewString()

	logger := m.log.With(
		zap.String("userId", userId),
		zap.String("dataId", dataId),
	)

	err := m.df.Delete(ctx, userId, dun.WithDataId(dataId))

	if err != nil {
		logger.Error("人脸删除失败", zap.Error(err))

		return err
	}

	logger.Info("人脸删除成功")

	return nil
}
