package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/luckywheel"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/fops"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var luckywheelAwards = []int{
	500000, 240000, 120000, 80000, 60000, 40000, 20000, 10000, 5000, 2000,
}

var luckywheelAward = &cobra.Command{
	Use:   "luckywheel-award",
	Short: "send luckywheel award",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR, ctl.FUND, ctl.FOPS).Run(func(mc *db.MongoClient, fm *fops.Manager) error {
			var (
				ctx = context.TODO()
			)
			stage := luckywheel.PrevStage(time.Now())
			startAt, endAt := luckywheel.StageTime(stage)
			raw, err := db.DecodeAll[luckywheel.Chance](ctx)(mc.Collection("act.luckywheel.chance.v2").Find(ctx, bson.M{"stage": stage},
				options.Find().SetSort(bson.D{{"gain", -1}, {"gainAt", 1}}).SetLimit(10)),
			)
			if err != nil {
				return err
			}
			items := make(fops.OpLogs, 0, len(raw))
			for rank, c := range raw {
				award := fund.New(luckywheelAwards[rank])
				items = append(items, fops.OpLog{
					UserId: c.UserId,
					Type:   fund.JTypeRewards,
					Prop:   fund.PTypeDiamond,
					Amount: award,
					Notice: fmt.Sprintf("Foram adicionados %s Coins a sua conta devido a Recompensas de evento, verifique!", award.String()),
				})
			}
			return fm.Apply(ctx,
				"de865ddbe9c6484e84d545091fbc4665",
				fmt.Sprintf("幸运转盘周奖励%s~%s", startAt.Format("1.2"), endAt.Format("1.2")),
				items,
			)
		})
	},
}
