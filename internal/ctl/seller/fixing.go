package seller

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson"
)

var fixTradeTarget = &cobra.Command{
	Use:   "fix-trade-target",
	Short: "fix wrong trade target",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Run(func(mc *db.MongoClient, dc *db.Client) error {
			var (
				ctx    = context.TODO()
				gte, _ = time.Parse(time.RFC3339, "2025-04-07T12:03:16Z")
				lte, _ = time.Parse(time.RFC3339, "2025-04-08T12:56:19Z")
			)
			var trades []*seller.Trade
			if err := dc.Model(&seller.Trade{}).Where("type = ? AND created_at BETWEEN ? AND ?", seller.TradeOut, gte, lte).Find(&trades).Error; err != nil {
				return err
			}
			fmt.Println("got raw trades", len(trades))
			cursor, err := mc.Collection("fund.journal.202504").Find(ctx, bson.M{
				"prop": fund.PTypeTokens, "type": fund.JTypeRecharge, "createdAt": bson.M{"$gte": gte, "$lte": lte},
				"$expr": bson.M{"$eq": bson.A{"$userId", "$withUser"}},
			})
			if err != nil {
				return err
			}
			for cursor.Next(ctx) {
				var v *journal.Record
				if err := cursor.Decode(&v); err != nil {
					return err
				}
				if v.Amount.IsPositive() {
					continue
				}
				trade, ok := lo.Find(trades, func(st *seller.Trade) bool {
					return st.UserId == v.UserId &&
						int64(st.Amount) == v.Amount.Neg().IntPart() &&
						st.CreatedAt.Truncate(time.Second).Equal(v.CreatedAt.Truncate(time.Second))
				})
				if !ok {
					fmt.Println("skip missing data", v.Id)
					continue
				}
				if trade.Target == v.WithUser {
					fmt.Println("skip correct data", v.Id)
					continue
				}
				fmt.Println("found mistake target", v.Id, ":", v.WithUser, "->", trade.Target)
				if _, err := mc.Collection("fund.journal.202504").UpdateByID(ctx, v.Id, bson.M{"$set": bson.M{"withUser": trade.Target}}); err != nil {
					return err
				}
				fmt.Println("fix record", v.Id)
			}
			return nil
		})
	},
}
