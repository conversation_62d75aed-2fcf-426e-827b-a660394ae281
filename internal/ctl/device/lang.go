package device

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/ul"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"go.mongodb.org/mongo-driver/bson"
)

var fixNoLang = &cobra.Command{
	Use:   "fix-no-lang",
	Short: "fix device without lang",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Needs(cc.Provide, ul.Provide).Post(func(db *db.MongoClient, lm *ul.Manager) fx2.Defer {
			return func(ctx context.Context) error {
				cursor, err := db.Collection("user.devices").Find(ctx, bson.M{"lang": bson.M{"$exists": false}})
				if err != nil {
					return err
				}
				processed := make(map[string]struct{})
				return dbutil.Scanning2(cursor, func(dev *device.Device) error {
					if _, has := processed[dev.UserId]; has {
						return nil
					}
					processed[dev.UserId] = struct{}{}
					if lang, _ := lm.Lang(ctx, dev.UserId); lang != "" {
						resp, err := db.Collection("user.devices").UpdateMany(ctx,
							bson.M{"userId": dev.UserId, "lang": bson.M{"$exists": false}},
							bson.M{"$set": bson.M{"lang": lang}})
						if err != nil {
							return err
						}
						fmt.Printf("update device lang: %s -> %s (%d)\n", dev.UserId, lang, resp.ModifiedCount)
					}
					return nil
				})
			}
		}).Run()
	},
}
