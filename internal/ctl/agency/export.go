package agency

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/zap"
)

var exportCmd = &cobra.Command{
	Use:   "export",
	Short: "export",
	RunE: func(cmd *cobra.Command, args []string) error {
		fx2.New(cmd, args).With(ctl.USR).Needs(
			db.ProvideGORM,
			cron.Provide,
			unq.Provide,
			redi.Provide,
			agency.Provide,
		).Run(func(am *agency.Manager, si sto.Instance, ug user.Getter, log log.Vendor) error {
			list, err := am.GetAgencyApply(context.Background())
			if err != nil {
				log.Logger().Error("export err", zap.Error(err))
				return err
			}

			if len(list) == 0 {
				log.Logger().Info("export nil")
				return nil
			}

			sc := si.Conf("agency")

			var LogFile *os.File
			filename := fmt.Sprintf("公会申请列表_%s.csv", time.Now().Format("************"))
			LogFile, _ = os.Create(filename)
			LogFile.WriteString("\xEF\xBB\xBF")

			// 表头
			title := []string{"申请编号", "公会名称", "公会Logo", "会长ID", "会长昵称", "公会人数", "合作平台", "手机号", "whatsapp_id"}
			LogFile.WriteString(strings.Join(title, ",") + "\n")

			//for
			for _, apply := range list {
				chiefInfo, _ := ug.Account(context.Background(), apply.ChiefId)

				value := []string{
					fmt.Sprintf("%d", apply.ID),
					apply.Name,
					sc.ExternalURL(apply.ImageUrl),
					apply.ChiefId,
					chiefInfo.Nickname,
					fmt.Sprintf("%d", apply.MemberNum),
					`"` + apply.Platform + `"`,
					apply.Phone + "\t",
					apply.WhatsappId + "\t",
				}
				LogFile.WriteString(strings.Join(value, ",") + "\n")
			}

			log.Logger().Info("export success")

			return nil
		})

		return nil
	},
}
