package agency

import (
	"context"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/zap"
)

var verifyCmd = &cobra.Command{
	Use:   "verify",
	Short: "verify",
	RunE: func(cmd *cobra.Command, args []string) error {
		fx2.New(cmd, args).With(ctl.USR).Needs(
			db.ProvideGORM,
			cron.Provide,
			unq.Provide,
			redi.Provide,
			agency.Provide,
		).Run(func(am *agency.Manager, log log.Vendor) error {
			applyId, _ := cmd.Flags().GetInt("applyId")
			status, _ := cmd.Flags().GetInt("status")
			err := am.AgencyApplyVerify(context.Background(), "", applyId, agency.VerifyStatus(status))
			if err != nil {
				log.Logger().Error("verify err", zap.Error(err))
				return err
			}

			log.Logger().Info("verify success", zap.Int("applyId", applyId))

			return nil
		})

		return nil
	},
}

func verifyMerge() *cobra.Command {
	cmd := verifyCmd
	cmd.Flags().Int("applyId", 0, "")
	cmd.Flags().Int("status", 0, "")
	_ = cmd.MarkFlagRequired("user")
	_ = cmd.MarkFlagRequired("status")
	return cmd
}
