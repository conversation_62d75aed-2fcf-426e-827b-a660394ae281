package draw

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
)

func checkCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "check",
		Short: "check config",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs().Run(func(vnd log.Vendor) error {
				path := args[0]

				fmt.Printf("read pools from %s:\n", path)
				pools, err := readYamlPoos(path)
				if err != nil {
					return err
				}

				sim := cmd.Flag("s").Value.String() == "true"

				var sc *SimulateConfig
				if sim {
					c, _ := cmd.Flags().GetInt("c")
					t, _ := cmd.Flags().GetInt("t")
					sc = &SimulateConfig{
						Count: c,
						Total: t,
					}
				}

				// do some confirm
				fmt.Printf(ShowPools(pools, sc))
				return nil
			})

			return nil
		},
	}

	c.PersistentFlags().Bool("s", false, "simulate check")
	c.PersistentFlags().Int("c", 100, "simulate count")
	c.PersistentFlags().Int("t", 100000, "simulate times")

	return c
}
