package draw

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"gopkg.in/yaml.v2"
)

func listCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "list",
		Short: "list",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideGORM,
				cron.Provide,
				unq.Provide,
			).Run(func(dc *db.Client, log log.Vendor) error {
				pools, err := list(dc)
				if err != nil {
					return fmt.Errorf("list gift pool failed: %w", err)
				}

				fmt.Printf(ShowPools(pools, nil))
				output, err := cmd.Flags().GetString("o")
				if err != nil {
					return fmt.Errorf("get export failed: %w", err)
				}

				if output != "" {
					if err := dump(pools, output); err != nil {
						return fmt.Errorf("dump pool failed: %w", err)
					}
				}

				return nil
			})

			return nil
		},
	}

	c.PersistentFlags().String("o", "", "output file")

	return c
}

func list(dc *db.Client) ([]draw2.Pool, error) {
	var pool []draw2.Pool
	if err := dc.Find(&pool).Order("gift_id ASC").Find(&pool).Error; err != nil {
		return nil, fmt.Errorf("find gift pool failed: %w", err)
	}

	return pool, nil
}

func dump(pool []draw2.Pool, path string) error {
	bs, err := yaml.Marshal(&pool)
	if err != nil {
		return fmt.Errorf("marshal pool failed: %w", err)
	}

	if err := os.WriteFile(path, bs, 0666); err != nil {
		return fmt.Errorf("write pool failed: %w", err)
	}

	return nil
}
