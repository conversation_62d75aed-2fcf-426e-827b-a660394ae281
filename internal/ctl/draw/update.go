package draw

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func upCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "up",
		Short: "up pools by yaml config file",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideGORM,
				cron.Provide,
				unq.Provide,
			).Run(func(dc *db.Client, vnd log.Vendor) error {
				path := args[0]

				fmt.Printf("read pools from %s:\n", path)
				pools, err := readYamlPoos(path)
				if err != nil {
					return err
				}

				// do some confirm
				fmt.Printf(ShowPools(pools, nil))

				if !Confirm("confirm upsert pools? (y/n): ") {
					return nil
				}

				return upsertPools(dc, pools, vnd.Logger())
			})

			return nil
		},
	}

	return c
}

func readYamlPoos(path string) ([]draw2.Pool, error) {
	bs, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("read file failed: %w", err)
	}

	var pools []draw2.Pool
	if err := yaml.Unmarshal(bs, &pools); err != nil {
		return nil, fmt.Errorf("unmarshal yaml failed: %w", err)
	}
	return pools, nil
}

func upsertGiftPool(d *gorm.DB, p *draw2.Pool, at time.Time, logger *zap.Logger) error {
	p.UpdatedAt = at
	p.CreatedAt = at

	if err := d.Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{
			"gift_id",
			"total",
			"prizes",
			"enable",
			"updated_at",
		}),
	}).Create(&p).Error; err != nil {
		return fmt.Errorf("upsert gift pool failed: %w", err)
	}

	logger.Info("upsert gift pool",
		zap.Uint("pool", p.ID),
		zap.Int("gift", p.GiftId),
		zap.Int("total", p.Total),
		zap.Bool("enable", p.Enable),
		zap.Time("created_at", p.CreatedAt),
		zap.Time("updated_at", p.UpdatedAt),
		zap.Any("prizes", p.Prizes),
	)
	return nil
}

func upsertPools(dc *db.Client, pools []draw2.Pool, logger *zap.Logger) error {
	at := time.Now()

	if err := db.Transaction(context.TODO(), dc, func(ctx context.Context, d *gorm.DB) error {
		for _, p := range pools {
			if err := upsertGiftPool(d, &p, at, logger); err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return err
	}

	logger.Info("upsert gift pools success")
	return nil
}
