package draw

import (
	"os"
	"testing"

	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gopkg.in/yaml.v2"
)

func TestExportPools(t *testing.T) {
	giftIds := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18}

	var pools []draw2.Pool
	for _, id := range giftIds {
		p1 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  4830,
			Prizes: []draw2.Prize{
				{Mul: 500, Avg: 4, Random: 4},
				{Mul: 10, Avg: 20, Random: 10},
			},
		}
		p2 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  10300,
			Prizes: []draw2.Prize{
				{Mul: 1000, Avg: 1, Random: 0},
				{Mul: 500, Avg: 10, Random: 6},
				{Mul: 10, Avg: 20, Random: 25},
			},
		}

		p3 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  5830,
			Prizes: []draw2.Prize{
				{Mul: 500, Avg: 6, Random: 4},
				{Mul: 10, Avg: 20, Random: 5},
			},
		}

		p4 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  16530,
			Prizes: []draw2.Prize{
				{Mul: 1000, Avg: 1, Random: 0},
				{Mul: 500, Avg: 16, Random: 11},
				{Mul: 10, Avg: 10, Random: 5},
			},
		}

		p5 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  7350,
			Prizes: []draw2.Prize{
				{Mul: 1000, Avg: 1, Random: 0},
				{Mul: 500, Avg: 5, Random: 6},
				{Mul: 10, Avg: 15, Random: 20},
			},
		}
		p6 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  2930,
			Prizes: []draw2.Prize{
				{Mul: 500, Avg: 3, Random: 2},
				{Mul: 10, Avg: 6, Random: 4},
			},
		}

		p7 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  6330,
			Prizes: []draw2.Prize{
				{Mul: 1000, Avg: 1, Random: 0},
				{Mul: 500, Avg: 6, Random: 3},
				{Mul: 10, Avg: 12, Random: 3},
			},
		}
		p8 := draw2.Pool{
			GiftId: id,
			Enable: true,
			Total:  13030,
			Prizes: []draw2.Prize{
				{Mul: 1000, Avg: 1, Random: 0},
				{Mul: 500, Avg: 13, Random: 8},
				{Mul: 10, Avg: 10, Random: 10},
			},
		}

		pools = append(pools, p1, p2, p3, p4, p5, p6, p7, p8)
	}

	t.Log(ShowPools(pools, nil))

	bs, _ := yaml.Marshal(pools)
	// fmt.Printf("data: %s\n", string(bs))

	os.WriteFile("pools.yaml", bs, 0644)
}
