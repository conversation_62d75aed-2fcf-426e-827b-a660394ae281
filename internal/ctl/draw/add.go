package draw

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
)

/*
- id: 0
  giftId: 23
  total: 4830
  prizes:
  - mul: 500
    avg: 5
    random: 3
  - mul: 10
    avg: 20
    random: 10
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 10300
  prizes:
  - mul: 1000
    avg: 1
    random: 0
  - mul: 500
    avg: 11
    random: 5
  - mul: 10
    avg: 20
    random: 25
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 5830
  prizes:
  - mul: 500
    avg: 7
    random: 3
  - mul: 10
    avg: 20
    random: 5
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 16530
  prizes:
  - mul: 1000
    avg: 1
    random: 0
  - mul: 500
    avg: 17
    random: 10
  - mul: 10
    avg: 10
    random: 5
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 7350
  prizes:
  - mul: 1000
    avg: 1
    random: 0
  - mul: 500
    avg: 6
    random: 5
  - mul: 10
    avg: 15
    random: 20
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 2930
  prizes:
  - mul: 500
    avg: 4
    random: 1
  - mul: 10
    avg: 6
    random: 4
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 6330
  prizes:
  - mul: 1000
    avg: 1
    random: 0
  - mul: 500
    avg: 7
    random: 2
  - mul: 10
    avg: 12
    random: 3
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
- id: 0
  giftId: 23
  total: 13030
  prizes:
  - mul: 1000
    avg: 1
    random: 0
  - mul: 500
    avg: 14
    random: 7
  - mul: 10
    avg: 10
    random: 10
  enable: true
  createdAt: 2024-08-01T08:58:24.595Z
  updatedAt: 2024-08-02T06:12:17.481Z
*/

// poolTemplate 定义奖池模板的基本参数
type poolTemplate struct {
	total  int
	prizes []prizeTemplate
}

type prizeTemplate struct {
	mul    int
	avg    int
	random int
}

// 预定义的奖池模板
var poolTemplates = []poolTemplate{
	{
		total: 4830,
		prizes: []prizeTemplate{
			{mul: 500, avg: 5, random: 3},
			{mul: 10, avg: 20, random: 10},
		},
	},
	{
		total: 10300,
		prizes: []prizeTemplate{
			{mul: 1000, avg: 1, random: 0},
			{mul: 500, avg: 11, random: 5},
			{mul: 10, avg: 20, random: 25},
		},
	},
	{
		total: 5830,
		prizes: []prizeTemplate{
			{mul: 500, avg: 7, random: 3},
			{mul: 10, avg: 20, random: 5},
		},
	},
	{
		total: 16530,
		prizes: []prizeTemplate{
			{mul: 1000, avg: 1, random: 0},
			{mul: 500, avg: 17, random: 10},
			{mul: 10, avg: 10, random: 5},
		},
	},
	{
		total: 7350,
		prizes: []prizeTemplate{
			{mul: 1000, avg: 1, random: 0},
			{mul: 500, avg: 6, random: 5},
			{mul: 10, avg: 15, random: 20},
		},
	},
	{
		total: 2930,
		prizes: []prizeTemplate{
			{mul: 500, avg: 4, random: 1},
			{mul: 10, avg: 6, random: 4},
		},
	},
	{
		total: 6330,
		prizes: []prizeTemplate{
			{mul: 1000, avg: 1, random: 0},
			{mul: 500, avg: 7, random: 2},
			{mul: 10, avg: 12, random: 3},
		},
	},
	{
		total: 13030,
		prizes: []prizeTemplate{
			{mul: 1000, avg: 1, random: 0},
			{mul: 500, avg: 14, random: 7},
			{mul: 10, avg: 10, random: 10},
		},
	},
}

func makeDefPools(gid int) []draw2.Pool {
	pools := make([]draw2.Pool, 0, len(poolTemplates))

	for _, tmpl := range poolTemplates {
		pool := draw2.Pool{
			GiftId: gid,
			Total:  tmpl.total,
			Enable: true,
			Prizes: make([]draw2.Prize, 0, len(tmpl.prizes)),
		}

		for _, p := range tmpl.prizes {
			pool.Prizes = append(pool.Prizes, draw2.Prize{
				Mul:    p.mul,
				Avg:    p.avg,
				Random: p.random,
			})
		}

		pools = append(pools, pool)
	}

	return pools
}
func addCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "add",
		Short: "up pools by yaml config file",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideGORM,
				cron.Provide,
				unq.Provide,
			).Run(func(dc *db.Client, vnd log.Vendor) error {
				gids, _ := cmd.Flags().GetIntSlice("gid")

				var pools []draw2.Pool
				for _, gid := range gids {
					pools = append(pools, makeDefPools(gid)...)
				}

				fmt.Printf("make pools for gift %v:\n", gids)
				fmt.Printf("%s\n", ShowPools(pools, nil))

				if !Confirm("confirm upsert pools? (y/n): ") {
					return nil
				}

				return upsertPools(dc, pools, vnd.Logger())
			})

			return nil
		},
	}

	c.Flags().IntSlice("gid", nil, "gift id")

	return c
}
