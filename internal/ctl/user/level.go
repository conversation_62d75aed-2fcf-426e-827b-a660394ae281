package user

import (
	"context"
	"errors"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

var rebuildLevels = &cobra.Command{
	Use:   "rebuild-levels",
	Short: "rebuild levels for all users",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(level.Provide).Post(func(db *db.Client, lm *level.Manager, um *user.Manager) fx2.Defer {
			return func(ctx context.Context) error {
				return dbutil.Scanning(db.WithContext(ctx).Model(&level.Profile{}), func(lvp *level.Profile) error {
					if lvp.Points == 0 {
						return nil
					}
					lvInfo, err := lm.LevelInfo(ctx, lvp.UserId)
					if err != nil {
						return err
					}
					if err := um.Update(user.OmitFields(ctx, "updated_at"), lvp.UserId, user.SetLevel(lvInfo.Level)); err != nil {
						if !errors.Is(err, user.ErrNoUpdateValue) && !errors.Is(err, user.ErrAccountNotExists) {
							return err
						}
					}
					return nil
				})
			}
		}).Run()
	},
}

var checkLevels = &cobra.Command{
	Use:   "check-levels",
	Short: "recheck levels for all users",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Post(func(db *db.Client) fx2.Defer {
			return func(ctx context.Context) error {
				return dbutil.Scanning(db.WithContext(ctx).Model(&level.Profile{}), func(lvp *level.Profile) error {
					lvInfo := level.MakeInfo(lvp.Points)
					var acc user.Account
					if err := db.Where("user_id = ?", lvp.UserId).Select("level").Take(&acc).Error; err != nil {
						return err
					}
					if acc.Level != lvInfo.Level {
						log.Warn("level not match", zap.String("userId", lvp.UserId), zap.Int("expect", lvInfo.Level), zap.Int("actual", acc.Level))
					}
					return nil
				})
			}
		}).Run()
	},
}
