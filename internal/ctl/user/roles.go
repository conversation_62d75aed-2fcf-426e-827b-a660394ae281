package user

import (
	"context"
	"errors"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var rebuildRoles = &cobra.Command{
	Use:   "rebuild-roles",
	Short: "rebuild roles for all users",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR).Post(func(db *db.Client, um *user.Manager) fx2.Defer {
			return func(ctx context.Context) error {
				return dbutil.Scanning(db.WithContext(ctx).Model(&patrol.Patroller{}), func(pp *patrol.Patroller) error {
					if err := um.Update(user.OmitFields(ctx, "updated_at"), pp.UserId, user.SetRole(user.RolePatroller)); err != nil {
						if !errors.Is(err, user.ErrNoUpdateValue) {
							return err
						}
					}
					return nil
				})
			}
		}).Run()
	},
}
