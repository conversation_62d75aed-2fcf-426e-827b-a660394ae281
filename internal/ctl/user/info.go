package user

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func exportInfo() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "export-info",
		Short: "export user info",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR).Post(func(ug user.Getter) fx2.Defer {
				return func(ctx context.Context) error {
					ids := strings.Split(cmd.Flag("ids").Value.String(), ",")
					fmt.Println("id,showId,nickname")
					for _, id := range ids {
						var uac *user.Account
						if len(id) == 32 {
							uac, _ = ug.Account(ctx, id)
						} else if len(id) == 8 {
							uac, _ = ug.GetByShowId(ctx, id)
						} else {
							return fmt.Errorf("invalid user id: %s", id)
						}
						fmt.Printf("%s,%s,%s\n", id, uac.ShowId, uac.Nickname)
					}
					return nil
				}
			}).Run()
		},
	}

	cmd.PersistentFlags().String("ids", "", "user ids")
	_ = cmd.MarkPersistentFlagRequired("ids")

	return cmd
}
