package reindex

import (
	"bytes"
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
	"github.com/elastic/go-elasticsearch/v8/esutil"
	"github.com/elastic/go-elasticsearch/v8/typedapi/core/update"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/es"
	"go.uber.org/fx"
)

func newBulk(lc fx.Lifecycle, es *es.Client) *bulk {
	bi, _ := esutil.NewBulkIndexer(esutil.BulkIndexerConfig{
		Client: &elasticsearch.Client{
			BaseClient: elasticsearch.BaseClient{
				Transport: es.Transport,
			},
			API: esapi.New(es.Transport),
		},
	})
	lc.Append(fx.Hook{OnStop: func(ctx context.Context) error {
		if err := bi.Close(ctx); err != nil {
			return err
		}
		fmt.Printf("Bulk stats: %+v\n", bi.Stats())
		return nil
	}})
	return &bulk{bi: bi}
}

type bulk struct {
	bi esutil.BulkIndexer
}

func (s *bulk) update(ctx context.Context, idxName string, docId string, data any, upsert bool) error {
	req := &update.Request{}
	bs, _ := sonic.Marshal(data)
	req.Doc = bs
	if upsert {
		req.DocAsUpsert = lo.ToPtr(true)
	}
	bs, _ = sonic.Marshal(req)
	return s.bi.Add(ctx, esutil.BulkIndexerItem{
		Index:      idxName,
		Action:     "update",
		DocumentID: docId,
		Body:       bytes.NewReader(bs),
	})
}
