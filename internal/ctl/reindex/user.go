package reindex

import (
	"context"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/es"
)

var rebuildUsers = &cobra.Command{
	Use:   "users",
	Short: "reindex users",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Needs(es.Provide, newBulk).Post(func(db *db.Client, bi *bulk) fx2.Defer {
			return func(ctx context.Context) error {
				return makeUsers(ctx, db, bi)
			}
		}).Run()
	},
}

func makeUsers(ctx context.Context, db *db.Client, bi *bulk) error {
	return dbutil.Scanning(db.WithContext(ctx).Model(&user.Account{}), func(acc *user.Account) error {
		v := map[string]any{
			"showId":   acc.ShowId,
			"nickname": acc.Nickname,
		}
		if err := bi.update(ctx, "osl-users", acc.UserId, v, true); err != nil {
			return err
		}
		return nil
	})
}
