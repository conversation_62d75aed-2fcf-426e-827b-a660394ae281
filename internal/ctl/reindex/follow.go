package reindex

import (
	"context"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/es"
)

var rebuildFollows = &cobra.Command{
	Use:   "follows",
	Short: "reindex follows",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(es.Provide, newBulk).Post(func(db *db.Client, bi *bulk, ug user.Getter) fx2.Defer {
			return func(ctx context.Context) error {
				return makeFollows(ctx, db, bi, ug)
			}
		}).Run()
	},
}

func makeFollows(ctx context.Context, db *db.Client, bi *bulk, ug user.Getter) error {
	return dbutil.Scanning(db.WithContext(ctx).Model(&follow.Following{}), func(link *follow.Following) error {
		v := map[string]any{
			"userId": link.UserId,
			"target": link.Target,
			"remark": link.Remark,
			"duplex": link.Duplex,
			"chummy": link.Chummy,
			"focus":  link.Focus,
			"time":   link.CreatedAt,
		}
		if p1, _ := ug.Account(ctx, link.UserId); p1 != nil {
			v["p1Id"] = p1.ShowId
			v["p1Name"] = p1.Nickname
		}
		if p2, _ := ug.Account(ctx, link.Target); p2 != nil {
			v["p2Id"] = p2.ShowId
			v["p2Name"] = p2.Nickname
		}
		if err := bi.update(ctx, "osl-follows", link.UserId+link.Target, v, true); err != nil {
			return err
		}
		return nil
	})
}
