package data

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/blindbox"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func giftingInit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "gifting-init",
		Short: "init gifting data",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB).Run(func(mc *db.MongoClient) error {
				from, _ := time.Parse(time.RFC3339, cmd.Flag("from").Value.String())
				to, _ := time.Parse(time.RFC3339, cmd.Flag("to").Value.String())
				ctx := context.Background()
				cursor, err := mc.Collection(fmt.Sprintf("fund.journal.%s", to.Format("200601"))).Find(ctx, bson.M{
					"prop":      fund.PTypeDiamond,
					"type":      bson.M{"$in": bson.A{fund.JTypeSendGift, fund.JTypeLuckGift}},
					"createdAt": bson.M{"$gte": from, "$lt": to},
				})
				if err != nil {
					return err
				}
				return dbutil.Scanning2(cursor, func(rec *journal.Record) error {
					switch rec.Type {
					case fund.JTypeSendGift:
						sends := make(map[int]map[string]map[string][]int64)
						gifts := make(map[int]map[int]int)
						for _, log := range journal.History[journal.GiftLog](rec) {
							date := data.Id(log.Time)
							if !slices.Contains(blindbox.BoxIds, log.GiftId) {
								if _, ok := sends[date]; !ok {
									sends[date] = make(map[string]map[string][]int64)
								}
								if _, ok := sends[date][rec.UserId]; !ok {
									sends[date][rec.UserId] = make(map[string][]int64)
								}
								if _, ok := sends[date][rec.UserId][rec.WithUser]; !ok {
									sends[date][rec.UserId][rec.WithUser] = make([]int64, 2)
								}
								if log.GiftId < 10000 {
									sends[date][rec.UserId][rec.WithUser][0] -= log.Amount
								} else {
									sends[date][rec.UserId][rec.WithUser][1] -= log.Amount
								}
							}
							{
								if _, ok := gifts[date]; !ok {
									gifts[date] = make(map[int]int)
								}
								gifts[date][log.GiftId] += log.Count
							}
						}
						for date, pairs := range sends {
							for userId, receivers := range pairs {
								for anchorId, amount := range receivers {
									inc := make(bson.M)
									if amount[0] > 0 {
										inc["luckDiamonds"] = amount[0]
									}
									if amount[1] > 0 {
										inc["giftDiamonds"] = amount[1]
									}
									if _, err := mc.Collection("p.data.gifting.user").UpdateOne(
										ctx, bson.M{"date": date, "userId": userId, "anchorId": anchorId},
										bson.M{"$inc": inc},
										options.Update().SetUpsert(true),
									); err != nil {
										return err
									}
								}
							}
						}
						for date, pairs := range gifts {
							for giftId, count := range pairs {
								if _, err := mc.Collection("p.data.gifting.gift").UpdateOne(
									ctx, bson.M{"date": date, "giftId": giftId},
									bson.M{"$inc": bson.M{"count": count}},
									options.Update().SetUpsert(true),
								); err != nil {
									return err
								}
							}
						}
					case fund.JTypeLuckGift:
						gifts := make(map[int]map[int]int)
						for _, log := range journal.History[journal.DrawLog](rec) {
							date := data.Id(log.Time)
							{
								if _, ok := gifts[date]; !ok {
									gifts[date] = make(map[int]int)
								}
								gifts[date][log.GiftId] += log.Multi
							}
						}
						for date, pairs := range gifts {
							for giftId, gain := range pairs {
								if _, err := mc.Collection("p.data.gifting.gift").UpdateOne(
									ctx, bson.M{"date": date, "giftId": giftId},
									bson.M{"$inc": bson.M{"gain": gain}},
									options.Update().SetUpsert(true),
								); err != nil {
									return err
								}
							}
						}
					}
					return nil
				})
			})
		},
	}

	cmd.PersistentFlags().String("from", "", "from date as RFC3339")
	_ = cmd.MarkPersistentFlagRequired("from")

	cmd.PersistentFlags().String("to", "", "to date as RFC3339")
	_ = cmd.MarkPersistentFlagRequired("to")

	return cmd
}

func blindboxInit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "blindbox-init",
		Short: "init blindbox data",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB).Needs(gift.Provide).Run(func(mc *db.MongoClient, gm *gift.Manager) error {
				from, _ := time.Parse(time.RFC3339, cmd.Flag("from").Value.String())
				to, _ := time.Parse(time.RFC3339, cmd.Flag("to").Value.String())
				ctx := context.Background()
				cursor, err := mc.Collection("blindbox.records").Find(ctx, bson.M{
					"createdAt": bson.M{"$gte": from, "$lt": to},
				})
				if err != nil {
					return err
				}
				return dbutil.Scanning2(cursor, func(rec *blindbox.Record) error {
					if _, err := mc.Collection("p.data.gifting.user").UpdateOne(
						ctx, bson.M{
							"date":     data.Id(rec.CreatedAt),
							"userId":   rec.UserId,
							"anchorId": rec.AnchorId,
						},
						bson.M{"$inc": bson.M{
							"blindBoxSend": rec.BoxPrice,
							"blindBoxRecv": rec.GiftPrice,
						}},
						options.Update().SetUpsert(true),
					); err != nil {
						return err
					}
					return nil
				})
			})
		},
	}

	cmd.PersistentFlags().String("from", "", "from date as RFC3339")
	_ = cmd.MarkPersistentFlagRequired("from")

	cmd.PersistentFlags().String("to", "", "to date as RFC3339")
	_ = cmd.MarkPersistentFlagRequired("to")

	return cmd
}
