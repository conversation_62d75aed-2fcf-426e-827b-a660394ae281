package data

import (
	"context"
	"strconv"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func chargingInit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "charging-init",
		Short: "init charging data",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB).Run(func(db *db.Client, mc *db.MongoClient) error {
				from, _ := time.Parse(time.RFC3339, cmd.Flag("from").Value.String())
				to, _ := time.Parse(time.RFC3339, cmd.Flag("to").Value.String())
				ctx := context.Background()
				if err := dbutil.Scanning(db.Model(&pay.Order{}).Where("scene = ? AND status = ? AND updated_at BETWEEN ? AND ?", "recharge", pay.OStatusUsed, from, to), func(order *pay.Order) error {
					amount, _ := strconv.Atoi(order.SKU)
					_, err := mc.Collection("p.data.charging").UpdateOne(ctx, bson.M{
						"date":   data.Id(order.UpdatedAt),
						"userId": order.UserId,
					}, bson.M{"$inc": bson.M{"payOrder": amount}},
						options.Update().SetUpsert(true),
					)
					return err
				}); err != nil {
					return err
				}
				if err := dbutil.Scanning(db.Model(&seller.Trade{}).Where("type = ? AND created_at BETWEEN ? AND ?", "out", from, to), func(trade *seller.Trade) error {
					_, err := mc.Collection("p.data.charging").UpdateOne(ctx, bson.M{
						"date":   data.Id(trade.CreatedAt),
						"userId": trade.Target,
					}, bson.M{"$inc": bson.M{"sellerTrade": trade.Amount}},
						options.Update().SetUpsert(true),
					)
					return err
				}); err != nil {
					return err
				}
				return nil
			})
		},
	}

	cmd.PersistentFlags().String("from", "", "from date as RFC3339")
	_ = cmd.MarkPersistentFlagRequired("from")

	cmd.PersistentFlags().String("to", "", "to date as RFC3339")
	_ = cmd.MarkPersistentFlagRequired("to")

	return cmd
}
