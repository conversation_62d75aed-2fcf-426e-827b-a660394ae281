package pay

import (
	"context"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/log"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var fixThirdPay = &cobra.Command{
	Use:   "fix-thirdPay",
	Short: "fix thirdPay not in Brazil",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Needs(cc.Provide, payc.Provide).Run(func(db *db.MongoClient, pm *payc.Manager) error {
			var ctx = context.TODO()
			cursor, err := db.Collection("pay.stats").Find(ctx, bson.M{})
			if err != nil {
				return err
			}
			return dbutil.Scanning2(cursor, func(ps *payc.Stats) error {
				if ps.Count >= 2 && ps.Money.GreaterThanOrEqual(fund.New(2)) {
					if pp, err := pm.Policy(ctx, ps.Id); err == nil && pp.ThirdPay == payc.TPWait {
						_ = pm.SetThirdPay(ctx, ps.Id, payc.TPAllow)
						log.Info("set policy allow",
							zap.String("userId", ps.Id),
							zap.Int("count", ps.Count),
							zap.Int64("money", ps.Money.IntPart()),
						)
					}
				}
				return nil
			})
		})
	},
}
