package pay

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type jsonOrder struct {
	UserId    string          `json:"user_id"`
	Status    pay.OrderStatus `json:"status"`
	CreatedAt time.Time       `json:"-"`
	Time1     string          `json:"created_at"`
}

func checkOrders() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "check-orders",
		Short: "check pay orders",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR).Post(func(ug user.Getter) fx2.Defer {
				return func(ctx context.Context) error {
					bs, err := os.ReadFile(cmd.Flag("file").Value.String())
					if err != nil {
						return err
					}

					var orders []*jsonOrder
					if err := sonic.Unmarshal(bs, &orders); err != nil {
						return err
					}

					uOrders := make(map[string][]*jsonOrder)
					for _, o := range orders {
						uOrders[o.UserId] = append(uOrders[o.UserId], o)
					}

					fmt.Printf("KakoID,异常订单,待处理,已完成\n")

					for userId, orders2 := range uOrders {
						var abnormal int
						var orders3 []*jsonOrder
						var lastO *jsonOrder
						for _, o := range orders2 {
							o.CreatedAt, _ = time.ParseInLocation(time.DateTime, o.Time1, time.UTC)
							if lastO != nil && o.CreatedAt.Sub(lastO.CreatedAt) < 3*time.Second {
								abnormal++
								continue
							}
							lastO = o
							orders3 = append(orders3, o)
						}
						acc, err := ug.Account(ctx, userId)
						if err != nil {
							continue
						}
						fmt.Printf("%s,%d,%d,%d\n", acc.ShowId, abnormal,
							lo.CountBy(orders3, func(o *jsonOrder) bool { return o.Status == pay.OStatusInit }),
							lo.CountBy(orders3, func(o *jsonOrder) bool { return o.Status == pay.OStatusUsed }),
						)
					}

					return nil
				}
			}).Run()
		},
	}

	cmd.PersistentFlags().String("file", "", "json file path")
	_ = cmd.MarkPersistentFlagRequired("file")

	return cmd
}
