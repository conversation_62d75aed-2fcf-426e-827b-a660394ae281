package anchor

import (
	"context"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
)

func addFlagsCmd() *cobra.Command {
	isNewAnchorFlags.Flags().String("userId", "", "")
	_ = isNewAnchorFlags.MarkFlagRequired("userId")
	flagsCmd.AddCommand(isNewAnchorFlags)

	return flagsCmd
}

var flagsCmd = &cobra.Command{
	Use:   "flags",
	Short: "flags",
}

var isNewAnchorFlags = &cobra.Command{
	Use:   "isNewAnchorFlag",
	Short: "isNewAnchorFlag",
	RunE: func(cmd *cobra.Command, args []string) error {
		fx2.New(cmd, args).With(ctl.USR).With(ctl.DB).Needs(
			anchor.Provide,
		).Run(func(am *anchor.Manager, log log.Vendor) error {
			userId, _ := cmd.Flags().GetString("userId")
			am.SetNewAnchorFlag(context.Background(), userId)
			return nil
		})

		return nil
	},
}
