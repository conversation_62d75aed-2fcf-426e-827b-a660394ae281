package anchor

import (
	"context"
	"encoding/csv"
	"fmt"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"io"
	"os"
)

func addIpDeviceCmd() *cobra.Command {
	addIpDevice.Flags().String("file", "", "")
	_ = addIpDevice.MarkFlagRequired("file")
	evaluation.AddCommand(addIpDevice)

	return evaluation
}

var evaluation = &cobra.Command{
	Use:   "evaluation",
	Short: "evaluation",
}

var addIpDevice = &cobra.Command{
	Use:   "addIpDevice",
	Short: "addIpDevice",
	RunE: func(cmd *cobra.Command, args []string) error {
		fx2.New(cmd, args).With(ctl.LIVE).With(ctl.DB).Needs(
			anchor.Provide,
			cc.Provide,
			user.Provide,
		).Run(func(am *anchor.Manager, log log.Vendor) error {
			file, _ := cmd.Flags().GetString("file")
			fileOs, err := os.Open(file)
			if err != nil {
				panic(err)
			}
			defer fileOs.Close()

			reader := csv.NewReader(fileOs)

			for {
				record, err := reader.Read()
				if err == io.EOF {
					break
				}
				if err != nil {
					panic(err)
				}
				if len(record) == 3 && record[0] != "" && record[1] != "" && record[2] != "" {
					err := am.SetEvaluationIpDevice(context.Background(), record[0], record[1], record[2])
					fmt.Printf("success, %s\n", err)
				} else {
					fmt.Printf("err, %s\n", record)
				}
			}
			return nil
		})

		return nil
	},
}
