package im

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/zap"
)

func Command() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "im_init",
		Short: "im_init",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).With(ctl.USR).Needs(
				redi.Provide,
				db.ProvideGORM,
				db.ProvideMongo,
				im.Provide,
				cron.Provide,
				unq.Provide,
			).Run(func(db *db.Client, ug user.Getter, imm *im.Manager, vnd log.Vendor) error {
				// return imm.InitUpdateSystemAccount()
				// return rebuildImUser(context.Background(), db, ug, imm, vnd.Scope("im_init"))

				res, err := imm.BatchGetUser(context.Background(), []int64{**********})

				if err != nil {
					fmt.Println(err)
				} else {
					fmt.Println(res.Users)
				}

				return nil
			})

			return nil
		},
	}

	return cmd
}

func rebuildImUser(ctx context.Context, db *db.Client, ug user.Getter, imm *im.Manager, l *zap.Logger) error {
	err := dbutil.Scanning(db.WithContext(ctx).Model(&user.Account{}).Where("num_id=?", **********), func(acc *user.Account) error {
		acc2, err := ug.Account(ctx, acc.UserId)

		l.Debug("rebuildImUser", zap.Any("acc2", acc2), zap.Error(err))

		if err != nil {
			return err
		}

		err = imm.UpdateUser(ctx, acc2)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
