package fund

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type fProps map[fund.PropType]fund.Decimal

func makeProps(diamonds, fruits string) fProps {
	fp := make(fProps)
	if diamonds != "" {
		fp[fund.PTypeDiamond] = fund.New(diamonds)
	}
	if fruits != "" {
		fp[fund.PTypeFruits] = fund.New(fruits)
	}
	return fp
}

var jTypes = map[string]fund.JournalType{
	"recharge": fund.JTypeRecharge,
	"exchange": fund.JTypeExchange,
	"salary":   fund.JTypeSalary,
	"kickback": fund.JTypeKickback,
	"rewards":  fund.JTypeRewards,
	"others":   fund.JTypeOthers,
}

func jTypeOf(in string) fund.JournalType {
	if in != "" {
		if v, has := jTypes[in]; !has {
			panic(fmt.Sprintf("unknown journal type: %s", in))
		} else {
			return v
		}
	}
	return fund.JTypeOthers
}

func fundUpdate() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "update",
		Short: "update user balance",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR, ctl.FUND).Run(func(ug user.Getter, fm *fund.Manager) error {
				showIds := cmd.Flag("ids").Value.String()
				action := cmd.Flag("act").Value.String()
				jType := cmd.Flag("type").Value.String()
				remark := cmd.Flag("remark").Value.String()

				var (
					ctx     = context.TODO()
					operate func(*fund.Account, fProps) error
					opts    = []fund.PayOpt{fund.WithTime(time.Now()), fund.WithDetail(remark)}
				)

				if cmd.Flag("no-journal").Value.String() == "true" {
					ctx = journal.SkipRecord(ctx)
				}

				switch action {
				case "add":
					operate = func(fac *fund.Account, props fProps) error {
						var errs []error
						for prop, amount := range props {
							if err := fm.Income(ctx, fac.UserId, jTypeOf(jType), prop, amount, opts...); err != nil {
								errs = append(errs, err)
							}
						}
						return errors.Join(errs...)
					}
				case "cut":
					operate = func(fac *fund.Account, props fProps) error {
						var errs []error
						for prop, amount := range props {
							if err := fm.Expend(ctx, fac.UserId, jTypeOf(jType), prop, amount, opts...); err != nil {
								errs = append(errs, err)
							}
						}
						return errors.Join(errs...)
					}
				case "query":
					operate = func(fac *fund.Account, props fProps) error { return nil }
				default:
					return errors.New("unsupported action")
				}

				props := makeProps(
					cmd.Flag("diamonds").Value.String(),
					cmd.Flag("fruits").Value.String(),
				)

				if len(props) == 0 && action != "query" {
					return errors.New("no props")
				}

				for _, showId := range strings.Split(showIds, ",") {
					uac, err := ug.GetByShowId(ctx, showId)
					if err != nil {
						fmt.Println("get user failed", showId)
						continue
					}

					if action != "query" {
						if fac, err := fm.Take(ctx, uac.UserId); err != nil {
							fmt.Println("get balance failed", err)
							continue
						} else if err := operate(fac, props); err != nil {
							fmt.Println("update balance failed", err)
							continue
						}
					}

					fac, err := fm.Take(ctx, uac.UserId)
					if err != nil {
						fmt.Println("re get balance failed", err)
						continue
					}

					fmt.Println("user balance",
						showId,
						uac.Nickname,
						"diamonds", fac.BVal(fund.PTypeDiamond),
						"fruits", fac.BVal(fund.PTypeFruits),
					)
				}

				return nil
			})
		},
	}

	cmd.PersistentFlags().String("ids", "", "users showId")
	_ = cmd.MarkPersistentFlagRequired("ids")

	cmd.PersistentFlags().String("act", "", "act: add|cut|query")
	_ = cmd.MarkPersistentFlagRequired("act")

	cmd.PersistentFlags().String("type", "", "journal type")
	cmd.PersistentFlags().String("remark", "", "journal remark")

	cmd.PersistentFlags().String("diamonds", "", "diamonds")
	cmd.PersistentFlags().String("fruits", "", "fruits")

	cmd.PersistentFlags().Bool("no-journal", false, "skip journal record")

	return cmd
}
