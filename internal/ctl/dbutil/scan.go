package dbutil

import (
	"context"

	"github.com/schollz/progressbar/v3"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

func Scanning[T any](db *gorm.DB, process func(T) error) error {
	rows, err := db.Rows()
	if err != nil {
		return err
	}
	defer rows.Close()

	bar := progressbar.Default(-1)
	defer bar.Close()

	for rows.Next() {
		var v T
		if err := db.ScanRows(rows, &v); err != nil {
			return err
		}
		if err := process(v); err != nil {
			return err
		}
		_ = bar.Add(1)
	}

	return nil
}

func Scanning2[T any](cursor *mongo.Cursor, process func(T) error) error {
	bar := progressbar.Default(-1)
	defer bar.Close()

	ctx := context.TODO()
	for cursor.Next(ctx) {
		var v T
		if err := cursor.Decode(&v); err != nil {
			return err
		}
		if err := process(v); err != nil {
			return err
		}
		_ = bar.Add(1)
	}

	return nil
}
