package journal

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var (
	dirtyEndAt = time.Date(2024, 8, 19, 11, 0, 0, 0, time.UTC)
)

var checkRecords = &cobra.Command{
	Use:   "check-records",
	Short: "check journal records",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Post(func(db *db.MongoClient) fx2.Defer {
			return func(ctx context.Context) error {
				cursor, err := db.Collection(fmt.Sprintf("fund.journal.%s", time.Now().Format("200601"))).Find(ctx, bson.M{
					"prop": fund.PTypeDiamond,
					"type": bson.M{"$in": []fund.JournalType{
						fund.JTypeSendGift,
						fund.JTypeLuckGift,
					}},
				})
				if err != nil {
					return err
				}
				return dbutil.Scanning2(cursor, func(rec *journal.Record) error {
					if rec.CreatedAt.Before(dirtyEndAt) {
						return nil
					} else if time.Since(rec.CreatedAt) < time.Minute {
						return nil
					}
					history := journal.History[*journal.GiftLog](rec)
					if rec.Merged != len(history) {
						log.Info("merged not match", zap.String("id", rec.Id.Hex()), zap.Int("expected", rec.Merged), zap.Int("actual", len(history)))
					}
					amount := fund.New(0)
					for _, item := range history {
						fund.Add(&amount, fund.New(item.Amount))
					}
					if !rec.Amount.Equal(amount) {
						log.Info("amount not match", zap.String("id", rec.Id.Hex()), zap.String("expected", rec.Amount.String()), zap.String("actual", amount.String()))
					}
					return nil
				})
			}
		}).Run()
	},
}

var checkSummary = &cobra.Command{
	Use:   "check-summary",
	Short: "check journal summary",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB).Post(func(db *db.MongoClient) fx2.Defer {
			return func(ctx context.Context) error {
				cursor, err := db.Collection(fmt.Sprintf("fund.summary.%s", time.Now().Format("200601"))).Find(ctx, bson.M{})
				if err != nil {
					return err
				}
				type pk struct {
					userId string
					prop   fund.PropType
					typ    fund.JournalType
				}
				type retS struct {
					Incomes fund.Decimal
					Expends fund.Decimal
				}
				type retR struct {
					Amount fund.Decimal
				}
				processed := make(map[pk]struct{})
				return dbutil.Scanning2(cursor, func(ss *journal.Summary) error {
					k := pk{ss.UserId, ss.Prop, ss.Type}
					if _, has := processed[k]; has {
						return nil
					}
					defer func() {
						processed[k] = struct{}{}
					}()

					var s1 retS
					{
						a1, err := db.Collection(fmt.Sprintf("fund.summary.%s", ss.Time.Format("200601"))).Aggregate(ctx, bson.A{
							bson.M{"$match": bson.M{"userId": ss.UserId, "prop": ss.Prop, "type": ss.Type}},
							bson.M{"$project": bson.M{
								"incomes": bson.M{"$subtract": bson.A{"$incomes", bson.M{"$sum": "$removed.income"}}},
								"expends": bson.M{"$subtract": bson.A{"$expends", bson.M{"$sum": "$removed.expend"}}},
							}},
							bson.M{"$group": bson.M{"_id": nil,
								"incomes": bson.M{"$sum": "$incomes"},
								"expends": bson.M{"$sum": "$expends"},
							}},
						})
						if err != nil {
							return err
						}
						if !a1.Next(ctx) {
							return errors.New("not found")
						} else if err := a1.Decode(&s1); err != nil {
							return err
						}
					}

					if s1.Incomes.IsZero() && s1.Expends.IsZero() {
						return nil
					}

					var s2 retR
					{
						a2, err := db.Collection(fmt.Sprintf("fund.journal.%s", ss.Time.Format("200601"))).Aggregate(ctx, bson.A{
							bson.M{"$match": bson.M{"userId": ss.UserId, "prop": ss.Prop, "type": ss.Type, "deleted": nil}},
							bson.M{"$group": bson.M{"_id": nil,
								"amount": bson.M{"$sum": "$amount"},
							}},
						})
						if err != nil {
							return err
						}
						if !a2.Next(ctx) {
							return errors.New("not found")
						} else if err := a2.Decode(&s2); err != nil {
							return err
						}
					}

					if (ss.Type == fund.JTypeWithdraw || ss.Type == fund.JTypeGameplay) ||
						(ss.Type == fund.JTypeRecharge && ss.Prop == fund.PTypeTokens) {
						s1Amount := s1.Incomes.Sub(s1.Expends)
						if !s1Amount.Equal(s2.Amount) {
							log.Warn("final amount not match", zap.String("id", ss.Id.Hex()),
								zap.String("expected", s1Amount.String()),
								zap.String("actual", s2.Amount.String()),
							)
						}
						return nil
					}

					if !s1.Incomes.IsZero() {
						if !s1.Incomes.Equal(s2.Amount) {
							log.Warn("incomes not match", zap.String("id", ss.Id.Hex()),
								zap.String("expected", s1.Incomes.String()),
								zap.String("actual", s2.Amount.String()),
							)
						}
					}

					if !s1.Expends.IsZero() {
						if !s1.Expends.Equal(s2.Amount.Neg()) {
							log.Warn("expends not match", zap.String("id", ss.Id.Hex()),
								zap.String("expected", s1.Expends.String()),
								zap.String("actual", s2.Amount.String()),
							)
						}
					}

					return nil
				})
			}
		}).Run()
	},
}
