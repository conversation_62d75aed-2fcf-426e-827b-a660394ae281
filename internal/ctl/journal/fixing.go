package journal

import (
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/bytedance/sonic"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type record struct {
	Group     string           `bson:"group"`
	UserId    string           `bson:"userId"`
	Type      fund.JournalType `bson:"type"`
	Prop      fund.PropType    `bson:"prop"`
	CreatedAt time.Time        `bson:"createdAt"`
	Stash     bson.RawValue    `bson:"stash,omitempty"`
}

type mergeTask struct {
	At    time.Time     `json:"t"`
	User  string        `json:"u"`
	Prop  fund.PropType `json:"p"`
	Group string        `json:"g"`
}

func triggerMerge() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "trigger-merge",
		Short: "manual trigger merge task",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.MQ).Run(func(db *db.MongoClient, mq mq.Queue) error {
				objId, err := primitive.ObjectIDFromHex(cmd.Flag("record").Value.String())
				if err != nil {
					return err
				}
				var (
					col = fmt.Sprintf("fund.journal.%s", objId.Timestamp().Local().Format("200601"))
					ctx = cmd.Context()
					rec record
				)
				if err := db.Collection(col).FindOne(ctx, bson.M{"_id": objId}).Decode(&rec); err != nil {
					return err
				}
				switch rec.Type {
				case fund.JTypeSendGift:
					if len(rec.Stash.Value) <= 5 {
						return errors.New("no stash data found")
					}
					if lastAt := lastGiftTime(rec.Stash); lastAt.After(rec.CreatedAt) {
						if _, err := db.Collection(col).UpdateByID(ctx, objId, bson.M{"$set": bson.M{"createdAt": lastAt}}); err != nil {
							return err
						}
						rec.CreatedAt = lastAt
					}
				}
				bs, err := sonic.Marshal(&mergeTask{
					At:    rec.CreatedAt,
					User:  rec.UserId,
					Prop:  rec.Prop,
					Group: rec.Group,
				})
				if err != nil {
					return err
				}
				return mq.Publish(ctx, "osl-journal.merge.tasks", bs)
			})
		},
	}

	cmd.PersistentFlags().String("record", "", "journal record id")
	_ = cmd.MarkPersistentFlagRequired("record")

	return cmd
}

func lastGiftTime(raw bson.RawValue) time.Time {
	logs := make([]*journal.GiftLog, 0, 128)
	_ = raw.Unmarshal(&logs)
	if len(logs) == 0 {
		return time.Time{}
	}
	slices.SortFunc(logs, func(a, b *journal.GiftLog) int {
		return b.Time.Compare(a.Time)
	})
	return logs[0].Time
}
