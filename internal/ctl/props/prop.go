package props

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
)

func addProp() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "add-prop",
		Short: "add gift to props",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(gift.Provide, props.Provide).Run(func(gm *gift.Manager, pm *props.Manager) error {
				var (
					ctx     = context.TODO()
					giftIds = strings.Split(cmd.Flag("giftIds").Value.String(), ",")
				)
				for _, giftIdV := range giftIds {
					giftId, _ := strconv.Atoi(giftIdV)
					gft, err := gm.GiftById(giftId)
					if err != nil {
						return fmt.Errorf("get gift failed: %w", err)
					} else if gft.Discontinued(time.Now()) {
						return errors.New("gift discontinued")
					}
					if _, err := pm.TakeProp(ctx, propc.GPropId(gft.ID)); err == nil {
						continue
					}
					if err := pm.AddProp(ctx, &props.Prop{
						Key:     propc.GPropId(gft.ID),
						Name:    gft.Name,
						Class:   props.CGift,
						Diamond: gft.Diamond,
						IconUrl: gft.ImageUrl,
					}); err != nil {
						return err
					}
				}
				return nil
			})
		},
	}

	cmd.PersistentFlags().String("giftIds", "", "special gifts")
	_ = cmd.MarkPersistentFlagRequired("giftIds")

	return cmd
}
