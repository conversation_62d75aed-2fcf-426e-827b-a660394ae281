package props

import (
	"context"
	"errors"
	"time"

	"github.com/shopspring/decimal"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func addItem() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "add-item",
		Short: "add item to user",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(gift.Provide, props.Provide).Run(func(ug user.Getter, gm *gift.Manager, pm *props.Manager) error {
				var (
					ctx     = context.TODO()
					showId  = cmd.Flag("showId").Value.String()
					propId  = cmd.Flag("propId").Value.String()
					countV  = cmd.Flag("count").Value.String()
					expireV = cmd.Flag("expire").Value.String()
				)
				uac, err := ug.GetByShowId(ctx, showId)
				if err != nil {
					return err
				}
				if _, err := pm.TakeProp(ctx, propId); err != nil {
					return err
				}
				count, err := decimal.NewFromString(countV)
				if err != nil {
					return err
				}
				expire, err := time.ParseDuration(expireV)
				if err != nil {
					return err
				} else if expire < 24*time.Hour {
					return errors.New("expire must be at least 24 hours")
				}
				return pm.AddItem(ctx, time.Now(), uac.UserId, propId, int(count.IntPart()), props.WithExpire(int(expire/(24*time.Hour))))
			})
		},
	}

	cmd.PersistentFlags().String("showId", "", "showId")
	_ = cmd.MarkPersistentFlagRequired("showId")

	cmd.PersistentFlags().String("propId", "", "propId")
	_ = cmd.MarkPersistentFlagRequired("propId")

	cmd.PersistentFlags().String("count", "", "count")
	_ = cmd.MarkPersistentFlagRequired("count")

	cmd.PersistentFlags().String("expire", "168h", "expire")

	return cmd
}
