package props

import (
	"context"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
)

var initGifts = &cobra.Command{
	Use:   "init-gifts",
	Short: "import props from gifts",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(gift.Provide, props.Provide).Run(func(gm *gift.Manager, pm *props.Manager) error {
			var ctx = context.TODO()
			pages, err := gm.GiftPageList(ctx)
			if err != nil {
				return err
			}
			for _, page := range pages {
				for _, gft := range page.Gifts {
					if gft.Discontinued(time.Now()) {
						continue
					}
					if _, err := pm.TakeProp(ctx, propc.GPropId(gft.ID)); err == nil {
						continue
					}
					if err := pm.AddProp(ctx, &props.Prop{
						Key:     propc.GPropId(gft.ID),
						Name:    gft.Name,
						Class:   props.CGift,
						Diamond: gft.Diamond,
						IconUrl: gft.ImageUrl,
					}); err != nil {
						return err
					}
				}
			}
			return nil
		})
	},
}

func testGifts() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "test-gifts",
		Short: "send test gifts to user",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(gift.Provide, props.Provide).Run(func(gm *gift.Manager, pm *props.Manager) error {
				var (
					ctx    = context.TODO()
					userId = cmd.Flag("userId").Value.String()
				)
				pages, _ := gm.GiftPageList(ctx)
				for _, page := range pages {
					for _, gft := range page.Gifts {
						if gft.Discontinued(time.Now()) {
							continue
						}
						for i := 0; i < 100; i++ {
							giftId := gft.ID
							_ = pm.AddItem(ctx, time.Now(), userId, propc.GPropId(giftId), 1, props.WithExpire(rng.Range(1, 7)))
						}
					}
				}
				return nil
			})
		},
	}

	cmd.PersistentFlags().String("userId", "", "send to userId")
	_ = cmd.MarkPersistentFlagRequired("userId")

	return cmd
}
