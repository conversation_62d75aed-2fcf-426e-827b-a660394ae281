package live

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
)

func Command() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "live",
		Short: "live",
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Help()
		},
	}

	cmd.AddCommand(&cobra.Command{
		Use:   "remove",
		Short: "remove all data about live",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.USR).Needs(
				redi.Provide,
				db.ProvideGORM,
				db.ProvideMongo,
				cron.Provide,
				unq.Provide,
				live.Provide,
				ls.Provide,
				rsd.Provide,
				salary.Provide,
				task.Provide,
			).Run(func(
				um *user.Manager,
				lm *live.Manager,
				lsm *ls.Manager,
				sm *salary.Manager,
			) error {
				ctx := context.Background()
				userId := ""

				if userId != "" {
					// 人脸认证状态修改
					err := um.Update(ctx, userId, user.UnsetStatus(user.StatusVerified))

					if err != nil {
						fmt.Println(err)
					}

					// 删除直播间/删除直播场次/删除开播提醒/删除暂离记录
					err = lm.RemoveUserData(ctx, userId)

					if err != nil {
						fmt.Println(err)
					}

					// 删除天直播
					err = lsm.RemoveUserData(ctx, userId)

					if err != nil {
						fmt.Println(err)
					}

					// 删除薪资记录
					err = sm.RemoveUserData(ctx, userId)

					if err != nil {
						fmt.Println(err)
					}
				}

				return nil
			})
		},
	})

	return cmd
}
