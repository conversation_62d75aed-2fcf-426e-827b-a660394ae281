package dress

import (
	"context"
	"errors"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress/vehicle"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func addVehicle() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "add-vehicle",
		Short: "add vehicle to user",
		Run: func(cmd *cobra.Command, args []string) {
			fx2.New(cmd, args).With(ctl.DB, ctl.USR).Needs(dress.Provide).Run(func(ug user.Getter, dm *dress.Manager) error {
				var (
					ctx       = context.TODO()
					showId    = cmd.Flag("showId").Value.String()
					vehicleId = cmd.Flag("vehicle").Value.String()
					expireV   = cmd.Flag("expire").Value.String()
				)
				uac, err := ug.GetByShowId(ctx, showId)
				if err != nil {
					return err
				}
				if vehicle.Find(vehicleId) == nil {
					return errors.New("invalid vehicle")
				}
				expire, err := time.ParseDuration(expireV)
				if err != nil {
					return err
				}
				return dm.SetVehicle(ctx, uac.UserId, vehicleId, time.Now().Add(expire))
			})
		},
	}

	cmd.PersistentFlags().String("showId", "", "showId")
	_ = cmd.MarkPersistentFlagRequired("showId")

	cmd.PersistentFlags().String("vehicle", "", "vehicle")
	_ = cmd.MarkPersistentFlagRequired("vehicle")

	cmd.PersistentFlags().String("expire", "168h", "expire")

	return cmd
}
