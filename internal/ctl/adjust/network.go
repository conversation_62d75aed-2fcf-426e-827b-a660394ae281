package adjust

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	sls "github.com/aliyun/aliyun-log-go-sdk"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/adjust"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var fixNetworks = &cobra.Command{
	Use:   "fix-networks",
	Short: "fix organic networks",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR).Run(func(db *db.MongoClient, ug user.Getter) error {
			var (
				ctx  = context.TODO()
				kOrg = "Kako Organic"
			)
			cursor, err := db.Collection("adjust.profile").Find(ctx, bson.M{"network": bson.M{"$ne": kOrg}})
			if err != nil {
				return err
			}
			return dbutil.Scanning2(cursor, func(p *adjust.Profile) error {
				uac, err := ug.Account(ctx, p.Id)
				if err != nil {
					return err
				}
				if p.CreatedAt.Sub(uac.CreatedAt) > 24*time.Hour {
					_, _ = db.Collection("adjust.profile").UpdateByID(ctx, p.Id, bson.M{"$set": bson.M{"network": kOrg}})
					fmt.Printf("reassign network: %s: %s -> %s\n", p.Id, p.Network, kOrg)
				}
				return nil
			})
		})
	},
}

var fixNetworks2 = &cobra.Command{
	Use:   "fix-networks2",
	Short: "fix kako organic networks",
	Run: func(cmd *cobra.Command, args []string) {
		fx2.New(cmd, args).With(ctl.DB, ctl.USR).Run(func(db *db.MongoClient, ug user.Getter) error {
			var (
				ctx   = context.TODO()
				kOrg  = "Kako Organic"
				logAt = time.Date(2024, 11, 06, 15, 00, 00, 00, ctz.China)
			)

			slc := sls.CreateNormalInterfaceV2(
				"us-east-1-intranet.log.aliyuncs.com",
				sls.NewStaticCredentialsProvider("LTAI5tRvvEWQCV4xpn3JfYPP", "******************************", ""),
			)

			origNet := func(adId string, at time.Time) (string, error) {
				resp, err := slc.GetLogsV3("godzilla-live", "service", &sls.GetLogRequest{
					From:  at.Unix() - 900,
					To:    at.Unix() + 900,
					Query: fmt.Sprintf("path:/api/v1/adjust/notify and %s | select query from log limit 1", adId),
				})
				if err != nil {
					return "", err
				} else if len(resp.Logs) == 0 {
					return "", errors.New("no logs found")
				}
				q, _ := url.ParseQuery(resp.Logs[0]["query"])
				if q.Get("gps_adid") != adId {
					return "", errors.New("adId not match")
				}
				return q.Get("network_name"), nil
			}

			cursor, err := db.Collection("adjust.profile").Find(ctx, bson.M{"network": kOrg},
				options.Find().SetSort(bson.M{"createdAt": -1}).SetLimit(100),
			)
			if err != nil {
				return err
			}

			return dbutil.Scanning2(cursor, func(p *adjust.Profile) error {
				uac, err := ug.Account(ctx, p.Id)
				if err != nil {
					return err
				}
				if uac.CreatedAt.Before(logAt) {
					return nil
				}
				network, err := origNet(p.ADId, uac.CreatedAt)
				if err != nil {
					fmt.Printf("process failed: %s: %v\n", p.Id, err)
					return nil
				}
				_, _ = db.Collection("adjust.profile").UpdateByID(ctx, p.Id, bson.M{"$set": bson.M{"network": network}})
				fmt.Printf("reassign network: %s: %s -> %s\n", p.Id, p.Network, network)
				return nil
			})
		})
	},
}
