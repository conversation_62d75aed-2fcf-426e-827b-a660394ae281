package game

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/olekukonko/tablewriter"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
)

func userCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "user",
		Short: "user manager",
	}

	c.AddCommand(lookCmd())
	c.AddCommand(roleCmd())
	return c
}

func lookCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "look",
		Short: "look user",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideGORM,
				db.ProvideMongo,
				cron.Provide,
				unq.Provide,
				cc.Provide,
				ev.Provide,
				game.ProvideStore,
				user.Provide,
			).Run(func(mc *db.MongoClient, gus *game.Store, ug user.Getter, log log.Vendor) error {
				showId, _ := cmd.Flags().GetString("showId")
				if showId == "" {
					return fmt.Errorf("show id is required")
				}

				var rows [][]string

				dump := func(s string) error {
					if s == "" {
						return nil
					}
					var (
						acc *user.Account
						err error
					)
					acc, err = ug.GetByShowId(context.Background(), s)

					if err != nil {
						return fmt.Errorf("get account failed: %w", err)
					}

					user, err := gus.User(context.Background(), acc.UserId)
					if err != nil {
						return fmt.Errorf("get user failed: %w", err)
					}

					rows = append(rows, []string{acc.UserId, acc.ShowId, acc.Nickname, user.Role.String(), user.CreatedAt.In(ctz.China).Format(time.RFC3339), user.UpdatedAt.In(ctz.China).Format(time.RFC3339)})

					return nil
				}

				for _, id := range strings.Split(showId, ",") {
					if err := dump(id); err != nil {
						fmt.Printf("dump %s failed: %s\n", id, err)
					}
				}

				tb := tablewriter.NewWriter(os.Stdout)
				tb.SetHeader([]string{"userId", "showId", "nickname", "game.role", "createdAt", "updatedAt"})
				tb.AppendBulk(rows)
				tb.Render()
				return nil
			})

			return nil
		},
	}

	c.Flags().StringP("showId", "s", "", "show id")

	return c
}

func roleCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "role",
		Short: "role manager",
	}
	c.AddCommand(setRole())
	return c
}

func setRole() *cobra.Command {
	c := &cobra.Command{
		Use:   "set",
		Short: "set role",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideGORM,
				db.ProvideMongo,
				cron.Provide,
				unq.Provide,
				cc.Provide,
				ev.Provide,
				game.ProvideStore,
				user.Provide,
			).Run(func(mc *db.MongoClient, gus *game.Store, ug user.Getter, log log.Vendor) error {
				showId, _ := cmd.Flags().GetString("showId")
				if showId == "" {
					return fmt.Errorf("show id is required")
				}

				role, err := cmd.Flags().GetInt("role")
				if err != nil {
					return fmt.Errorf("role is required")
				}

				if !game.Role(role).Valid() {
					return fmt.Errorf("invalid role: %d", role)
				}

				var (
					at   = time.Now()
					rows [][]string
					set  = func(showid string, r game.Role) (*game.User, error) {
						acc, err := ug.GetByShowId(context.Background(), showid)
						if err != nil {
							return nil, fmt.Errorf("get account failed: %w", err)
						}

						user, err := gus.SetUserRole(context.Background(), acc.UserId, r, at)
						if err != nil {
							return nil, fmt.Errorf("set role failed: %w", err)
						}
						rows = append(rows, []string{acc.UserId, acc.ShowId, acc.Nickname, user.Role.String(), user.CreatedAt.In(ctz.China).Format(time.RFC3339), user.UpdatedAt.In(ctz.China).Format(time.RFC3339)})
						return user, nil
					}
				)

				for _, id := range strings.Split(showId, ",") {
					if _, err := set(id, game.Role(role)); err != nil {
						fmt.Printf("set %s failed: %s\n", id, err)
					}
				}

				tb := tablewriter.NewWriter(os.Stdout)
				tb.SetHeader([]string{"userId", "showId", "nickname", "game.role", "createdAt", "updatedAt"})
				tb.AppendBulk(rows)
				tb.Render()

				return nil
			})
			return nil
		},
	}
	c.Flags().Int("role", 0, "role")
	c.Flags().StringP("showId", "s", "", "show id")
	return c
}
