package game

import (
	"bytes"
	"context"
	"fmt"
	"os"

	"github.com/olekukonko/tablewriter"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gopkg.in/yaml.v2"
)

func listCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "list",
		Short: "list",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideMongo,
				cron.Provide,
				unq.Provide,
			).Run(func(mc *db.MongoClient, log log.Vendor) error {
				fmt.Printf("args: %v\n", args)
				items, err := list(mc)
				if err != nil {
					return fmt.Errorf("list items failed: %w", err)
				}

				fmt.Printf("%s\n", showGameListTable(items))

				var out string
				if len(args) > 0 {
					out = args[0]
				}

				if out != "" {
					if err := dump(items, out); err != nil {
						return fmt.Errorf("dump items failed: %w", err)
					}
				}

				return nil
			})

			return nil
		},
	}

	return c
}

func list(mc *db.MongoClient) ([]*game.ListItem, error) {
	cur, err := mc.Collection(game.Collection).Find(context.TODO(), bson.M{}, options.Find().SetSort(bson.M{"created_at": 1}))
	if err != nil {
		return nil, fmt.Errorf("find items failed: %w", err)
	}

	var items []*game.ListItem
	if err := cur.All(context.TODO(), &items); err != nil {
		return nil, fmt.Errorf("decode items failed: %w", err)
	}

	for _, itm := range items {
		itm.Data = nil
	}

	return items, nil
}

type DumpGameItem struct {
	Platform   string              `bson:"platform"`
	GameID     string              `bson:"game_id"`
	Show       bool                `bson:"show"`
	Name       string              `bson:"name"`
	Cover      string              `bson:"cover"` // 封面
	Icon       string              `bson:"icon"`
	RegionDesc map[string]string   `bson:"region_desc"`
	RegionTags map[string][]string `bson:"region_tags"`
	Sorts      map[string]int      `bson:"sorts"` // 在不同分类中的顺序，如果为<0或者key没有这个分类，则不显示
	Sort       int                 `bson:"sort"`
}

func dump(items []*game.ListItem, path string) error {
	itmes2 := make([]*DumpGameItem, 0, len(items))
	for _, item := range items {
		itmes2 = append(itmes2, &DumpGameItem{
			Platform:   item.Platform,
			GameID:     item.GameID,
			Show:       item.Show,
			Name:       item.Name,
			Cover:      item.Cover,
			Icon:       item.Icon,
			RegionDesc: item.RegionDesc,
			RegionTags: item.RegionTags,
			Sorts:      item.Sorts,
			Sort:       item.Sort,
		})
	}

	bs, err := yaml.Marshal(itmes2)
	if err != nil {
		return fmt.Errorf("marshal pool failed: %w", err)
	}

	if err := os.WriteFile(path, bs, 0666); err != nil {
		return fmt.Errorf("write pool failed: %w", err)
	}

	return nil
}

func showGameListTable(items []*game.ListItem) string {
	var bs bytes.Buffer
	tw := tablewriter.NewWriter(&bs)
	tw.SetHeader([]string{"ID", "Platform", "GameID", "PGID", "Show", "Name", "Descs", "Tags", "Tagss", "Sort", "Offline"})
	for _, item := range items {
		tw.Append([]string{
			item.ID.Hex(),
			item.Platform,
			item.GameID,
			item.PGID,
			fmt.Sprint(item.Show),
			item.Name,
			fmt.Sprint(item.RegionDesc),
			fmt.Sprint(item.Tags),
			fmt.Sprint(item.RegionTags),
			fmt.Sprint(item.Sorts),
			fmt.Sprint(item.Offline),
		})
	}
	tw.Render()
	return bs.String()
}
