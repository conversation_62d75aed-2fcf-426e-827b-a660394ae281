package game

import (
	"os"
	"testing"

	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gopkg.in/yaml.v2"
)

func TestConfigOutput(t *testing.T) {
	items := []*game.ListItem{
		{
			Platform: "baishun",
			GameID:   "1053",
			Name:     "magicslot",
			Show:     true,
			RegionName: map[string]string{
				"zh": "魔力slot",
				"pt": "Slot Mágico",
			},
			RegionDesc: map[string]string{
				"zh": "经典的slot游戏，画面精美",
				"pt": "Jogo de slot clássico com belos gráficos",
			},

			RegionTags: map[string][]string{
				"zh": {"最受欢迎"},
				"pt": {"Mais popular"},
			},
		},
		{
			Platform: "baishun",
			GameID:   "1041",
			Name:     "TeenPatti",
			Show:     true,
			RegionName: map[string]string{
				"zh": "欢乐三张",
				"pt": "TeenPatti pôquer",
			},
			RegionDesc: map[string]string{
				"zh": "经典扑克游戏，比比谁更大",
				"pt": "Jogo de pôquer clássico, quem é maior",
			},
			RegionTags: map[string][]string{
				"zh": {"多人比拼"},
				"pt": {"Competição multijogador"},
			},
		},
		{
			Platform: "baishun",
			GameID:   "1031",
			Name:     "CrashPro",
			Show:     true,
			RegionName: map[string]string{
				"zh": "激情火箭",
				"pt": "Foguete",
			},
			RegionDesc: map[string]string{
				"zh": "竞猜火箭能飞多高",
				"pt": "Apostando em quão alto um foguete pode voar",
			},
			RegionTags: map[string][]string{
				"zh": {"最多人中奖"},
				"pt": {"Mais vencedores"},
			},
		},
		{
			Platform: "baishun",
			GameID:   "1034",
			Name:     "fishingstarpro",
			Show:     true,
			RegionName: map[string]string{
				"zh": "深海捕鱼",
				"pt": "Pesca Oceanica",
			},
			RegionDesc: map[string]string{
				"zh": "捕捉珍稀鱼类，赢取丰厚奖励",
				"pt": "Pegue peixes raros e ganhe grandes recompensas",
			},
			RegionTags: map[string][]string{
				"zh": {"震撼的视觉效果"},
				"pt": {"Efeitos visuais incríveis"},
			},
		},

		{
			Platform: "baishun",
			GameID:   "1030",
			Name:     "lotterypro",
			Show:     true,
			RegionName: map[string]string{
				"zh": "欢乐骰子",
				"pt": "Dados Alegres",
			},
			RegionDesc: map[string]string{
				"zh": "五颗骰子，五颗骰子，7种押注选项",
				"pt": "Cinco dados com 7 opções de apostas",
			},
			RegionTags: map[string][]string{
				"zh": {"经典玩法"},
				"pt": {"Jogo clássico"},
			},
		},
		{
			Platform: "baishun",
			GameID:   "1048",
			Name:     "greedypro",
			Show:     true,
			RegionName: map[string]string{
				"zh": "贪嘴小象",
				"pt": "Elefante Glutão",
			},
			RegionDesc: map[string]string{
				"zh": "丰富的食物选项，猜猜小象会吃哪个",
				"pt": "Adivinhe qual comida o bebê elefante comerá",
			},
			RegionTags: map[string][]string{
				"zh": {"最多样的选项"},
				"pt": {"Mais diversas opções"},
			},
		},
		{
			Platform: "baishun",
			GameID:   "1043",
			Name:     "bountyracing",
			Show:     true,
			RegionName: map[string]string{
				"zh": "赏金赛车",
				"pt": "Carro Esportivo",
			},
			RegionDesc: map[string]string{
				"zh": "10种汽车品牌，选择你的爱车",
				"pt": "Escolha seu carro entre 10 marcas de automóveis",
			},
			RegionTags: map[string][]string{
				"zh": {"最高100倍奖励"},
				"pt": {"Recompensa de até 100 vezes"},
			},
		},
		{
			Platform: "baishun",
			GameID:   "1073",
			Name:     "DragonTiger",
			Show:     true,
			RegionName: map[string]string{
				"zh": "龙虎斗",
				"pt": "DragonTiger",
			},
			RegionDesc: map[string]string{
				"zh": "龙争虎斗，选择你的阵营",
				"pt": "Luta de Dragões e Tigres, escolha seu lado",
			},
			RegionTags: map[string][]string{
				"zh": {"规则简单"},
				"pt": {"As regras são simples"},
			},
		},
	}

	for i := range items {
		items[i].Sort = i * 10
	}

	bs, err := yaml.Marshal(items)
	if err != nil {
		t.Fatal(err)
	}

	os.WriteFile("/tmp/game.yaml", bs, 0666)
}
