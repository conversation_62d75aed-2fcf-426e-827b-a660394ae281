package game

import (
	"context"
	"strconv"
	"time"

	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dbutil"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var (
	d2usd = fund.New(10000)
)

func scanCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "scan",
		Short: "scan",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideMongo,
				db.ProvideGORM,
				cron.Provide,
				unq.Provide,
			).Run(func(mc *db.MongoClient, dc *db.Client, log log.Vendor) error {
				logger := log.Scope("game.recharge")
				apply := func(ctx context.Context, userId string, money fund.Decimal, scene string, at time.Time) {
					if _, err := mc.Collection(game.UserCollection).UpdateOne(
						ctx,
						bson.M{"_id": userId},
						bson.M{
							"$inc": bson.M{"recharged.money": money, "recharged.count": 1},
							"$set": bson.M{"updated_at": at},
							"$setOnInsert": bson.M{
								"_id":        userId,
								"role":       game.RoleNormal,
								"created_at": at,
							},
						},
						options.Update().SetUpsert(true),
					); err != nil {
						logger.Error("update user recharged failed", zap.Error(err), zap.String("userId", userId), zap.String("money", money.String()), zap.String("scene", scene))
					} else {
						logger.Info("update user recharged", zap.String("userId", userId), zap.String("money", money.String()), zap.String("scene", scene))
					}
				}

				upTime := time.Date(2025, 4, 22, 22, 0, 0, 0, ctz.China)

				dbutil.Scanning(dc.DB.Model(&pay.Order{}).Where("status=? and scene=?", 3, "recharge"), func(order *pay.Order) error {
					if order.CreatedAt.After(upTime) {
						return nil
					}

					diamonds, err := strconv.Atoi(order.SKU)
					if err != nil {
						return err
					}

					apply(
						context.Background(),
						order.UserId,
						fund.New(diamonds).Div(d2usd),
						"OnPaid",
						order.UpdatedAt,
					)

					return nil
				})

				dbutil.Scanning(dc.DB.Model(&seller.Trade{}).Where("type=?", "out"), func(trade *seller.Trade) error {
					if trade.CreatedAt.After(upTime) {
						return nil
					}

					apply(
						context.Background(),
						trade.Target,
						fund.New(trade.Amount).Div(d2usd),
						"OnSellerTransfer",
						trade.CreatedAt,
					)

					return nil
				})

				return nil
			})

			return nil
		},
	}

	return c
}
