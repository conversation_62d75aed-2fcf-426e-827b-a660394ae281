package game

import (
	"fmt"
	"strings"

	"github.com/olekukonko/tablewriter"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
)

func ShowPools(pools []draw2.Pool, doSim bool) string {
	var sb strings.Builder

	table := tablewriter.NewWriter(&sb)
	table.SetCaption(true, "pool list")
	table.SetHeader([]string{"id", "gift_id", "total", "prizes", "enable", "return_rate"})
	for _, p := range pools {
		var sb strings.Builder
		sb.WriteString("{")
		for _, p := range p.Prizes {
			sb.WriteString(fmt.Sprintf("%d", p.Mul))
			sb.WriteString(":")
			sb.WriteString(fmt.Sprintf("%d", p.Avg))
			sb.WriteString(",")
			sb.WriteString(fmt.Sprintf("%d", p.Random))
			sb.WriteString(",")
		}
		sb.WriteString("}")
		table.Append([]string{
			fmt.Sprintf("%d", p.ID),
			fmt.Sprintf("%d", p.GiftId),
			fmt.Sprintf("%d", p.Total),
			sb.String(),
			fmt.Sprintf("%v", p.Enable),
			p.CalcReturnRate().String(),
		})
	}

	table.Render()

	keys := lo.Map(pools, func(p draw2.Pool, _ int) int {
		return p.GiftId
	})

	keys = lo.Uniq(keys)

	pgs := lo.GroupBy(pools, func(p draw2.Pool) int {
		return p.GiftId
	})

	table = tablewriter.NewWriter(&sb)
	table.SetCaption(true, "pool group")
	table.SetHeader([]string{"gift_id", "return_rate"})

	for _, key := range keys {
		p := pgs[key]
		pg := draw2.NewPoolGroup(key, p)

		table.Append([]string{fmt.Sprintf("%d", key), pg.CalcReturnRate().String()})
	}
	table.Render()

	return sb.String()
}

func Confirm(msg string) bool {
	fmt.Print(msg)
	var confirm string
	if _, err := fmt.Scan(&confirm); err != nil {
		return false
	}
	return confirm == "y"
}
