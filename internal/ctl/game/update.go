package game

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
)

func upCmd() *cobra.Command {
	c := &cobra.Command{
		Use:   "up",
		Short: "up game list by yaml config file",
		RunE: func(cmd *cobra.Command, args []string) error {
			fx2.New(cmd, args).Needs(
				redi.Provide,
				db.ProvideMongo,
				cron.Provide,
				unq.Provide,
			).Run(func(mc *db.MongoClient, vnd log.Vendor) error {
				path := args[0]

				fmt.Printf("read games from %s:\n", path)
				items, err := readYaml(path)
				if err != nil {
					return err
				}

				// do some confirm
				fmt.Printf(showGameListTable(items))

				if !Confirm("confirm upsert games? (y/n): ") {
					return nil
				}

				return upsertGameItems(mc, items, vnd.Logger())
			})

			return nil
		},
	}

	return c
}

func readYaml(path string) ([]*game.ListItem, error) {
	bs, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("read file failed: %w", err)
	}

	var items []*game.ListItem
	if err := yaml.Unmarshal(bs, &items); err != nil {
		return nil, fmt.Errorf("unmarshal yaml failed: %w", err)
	}
	return items, nil
}

func updateGameItem(ctx context.Context, mc *db.MongoClient, item *game.ListItem, at time.Time) error {
	_, err := mc.Collection(game.Collection).UpdateOne(
		ctx,
		bson.M{"platform": item.Platform, "game_id": item.GameID},
		bson.M{
			"$set": bson.M{
				"name":        item.Name,
				"show":        item.Show,
				"desc":        item.Desc,
				"tags":        item.Tags,
				"sort":        item.Sort,
				"region_name": item.RegionName,
				"region_desc": item.RegionDesc,
				"region_tags": item.RegionTags,
				"sorts":       item.Sorts,
				"icon":        item.Icon,
				"cover":       item.Cover,
				"updated_at":  at,
			},
			"$setOnInsert": bson.M{
				"platform":   item.Platform,
				"game_id":    item.GameID,
				"pgid":       game.MakePGID(item.Platform, item.GameID),
				"offline":    false,
				"created_at": at,
			},
		},
		options.Update().SetUpsert(true),
	)
	return err
}

func upsertGameItems(mc *db.MongoClient, items []*game.ListItem, logger *zap.Logger) error {
	if err := db.SyncSchema(context.TODO(), logger, mc, db.FixedCollection(game.Collection), 1,
		db.Indexer{
			Name: "platform_game_id",
			Uniq: lo.ToPtr(true),
			Keys: bson.D{
				{Key: "platform", Value: 1},
				{Key: "game_id", Value: 1},
			},
		},
		db.Indexer{
			Name: "platform_game_id_str",
			Uniq: lo.ToPtr(true),
			Keys: bson.D{
				{Key: "pgid", Value: 1},
			},
		},
	); err != nil {
		return err
	}

	at := time.Now()

	if err := mc.TryTxn(context.TODO(), func(ctx context.Context) error {
		for _, itm := range items {
			if err := updateGameItem(ctx, mc, itm, at); err != nil {
				return fmt.Errorf("update game item failed: %w", err)
			} else {
				logger.Info("update game item success",
					zap.String("name", itm.Name),
					zap.String("platform", itm.Platform),
					zap.String("game_id", itm.GameID),
					zap.String("icon", itm.Icon),
					zap.Any("item", itm),
				)
			}
		}

		return nil
	}); err != nil {
		return err
	}

	logger.Info("upsert game list success")
	return nil
}
