package ctl

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/fops"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/co"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/dq"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/mq"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/fx"
)

var DB = fx.Module("db", fx.Provide(
	redi.Provide, unq.Provide,
	cron.Provide, db.ProvideGORM, db.ProvideMongo,
), fx.Invoke(
	cron.Invoke, db.ReleaseGORM, db.ReleaseMongo,
))

var MQ = fx.Module("mq", fx.Provide(
	mq.Provide,
), fx.Invoke(
	mq.Invoke,
))

var USR = fx.Module("usr", fx.Provide(
	ev.Provide, cc.Provide, user.Provide,
	sto.Provide, avatar.Provide,
), fx.Invoke(
	co.Invoke, avatar.Invoke,
))

var FUND = fx.Module("fund", fx.Provide(
	mq.Provide, dq.Provide, gift.Provide,
	fund.Provide, journal.Provide,
), fx.Invoke(
	journal.Invoke,
))

var FOPS = fx.Module("fops", fx.Provide(
	gid.Provide, im.Provide, order.Provide, fops.Provide,
))

var LIVE = fx.Module("live", fx.Provide(
	rsd.Provide, agency.Provide, live.Provide, ev.Provide,
))
