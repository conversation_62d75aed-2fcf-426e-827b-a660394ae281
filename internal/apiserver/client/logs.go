package client

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sts"
)

// @Tags 客户端
// @Summary 获取客户端日志上传配置
// @Description 获取客户端日志上传配置
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.STSToken}
// @Router /api/v1/client/logs/upload [get]
func (s *apis) logsUpload(ctx *api.Context, _ api.EmptyReq) (*types.STSToken, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	deviceId := app.DeviceId(ctx)
	if deviceId == "" {
		return nil, biz.NewError(biz.ErrInvalidParam, "missing deviceId")
	}
	resp, err := s.sm.AssumeRole("app_logs", uac.UserId, sts.WithPrefix("app_logs/"+uac.UserId+"/"+deviceId))
	if err != nil {
		return nil, err
	}
	return mixer.STSToken(resp), nil
}

type logsCollectResp struct {
	Status int `json:"status"` // 0 不需要处理 1 需要上报 2 上报完成
}

// @Tags 客户端
// @Summary 获取客户端日志收集标记
// @Description 获取客户端日志收集标记
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=logsCollectResp}
// @Router /api/v1/client/logs/collect [get]
func (s *apis) chkLogsCollect(ctx *api.Context, _ api.EmptyReq) (*logsCollectResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	status, _ := s.lm.GetCollectStatus(ctx, uac.UserId)
	return &logsCollectResp{Status: status}, nil
}

type logsCollectReq struct {
	Status int `json:"status"` // 2 上报完成
}

// @Tags 客户端
// @Summary 设置客户端日志收集标记
// @Description 设置客户端日志收集标记
// @Produce json
// @Security HeaderAuth
// @Param param body logsCollectReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/client/logs/collect [post]
func (s *apis) updLogsCollect(ctx *api.Context, req logsCollectReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	if req.Status == 2 {
		_ = s.lm.SetCollectStatus(ctx, uac.UserId, req.Status)
	}
	return &api.EmptyResp{}, nil
}
