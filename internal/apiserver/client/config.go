package client

import (
	"time"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/adjust"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

type config struct {
	CustomerService customerService `json:"customerService"`
	ProfileNavigate profileNavigate `json:"profileNavigate"` // deprecated
	LuckDraw        luckDrawSetting `json:"luckDraw"`
	Gift            Gift            `json:"gift"`
	ThirdChannel    thirdChannel    `json:"thirdChannel"`
	Features        Features        `json:"features"`
	Business        business        `json:"business"`
	Setting         map[string]any  `json:"setting"` // 客户端设置
}

type customerService struct {
	Channel string `json:"channel"`
	Contact string `json:"contact"`
}

type profileNavigate struct {
	MiniGame bool `json:"miniGame"` // 小游戏开关
}

type Features struct {
	MiniGame        bool `json:"miniGame"`        // 小游戏开关
	LiveGame        bool `json:"liveGame"`        // 直播游戏开关
	Payermax        bool `json:"payermax"`        // 四方支付开关
	Link            bool `json:"link"`            // 连麦开关
	LuckEffectClose int  `json:"luckEffectClose"` // 中奖特效：0：全开；1<<1：关闭低倍数；1<<2：关闭高倍数；1<<3：关闭公屏区；1<<1 | 1<<2 | 1<<3：全部关闭
	HomeVisiblePage int  `json:"homeVisiblePage"` // 主页样式：0：转盘；1：缩放；2：常规热门
	Preview         bool `json:"preview"`         // 房间外的预览开关
}

type luckDrawSetting struct {
	EffectThreshold    int `json:"effectThreshold"`    // 特效阈值
	EffectThresholdLv1 int `json:"effectThresholdLv1"` // 特效阈值lv1
	EffectThresholdLv2 int `json:"effectThresholdLv2"` // 特效阈值lv2
	EffectThresholdLv3 int `json:"effectThresholdLv3"` // 特效阈值lv3
}

type thirdChannel struct {
	Facebook bool `json:"facebook"`
}

type Gift struct {
	FastSendDuration int `json:"fastSendDuration"` // 连击时长 by seconds
}

type business struct {
	Tag int `json:"tag"` // 0：审核态；1：非审核态
}

// @Tags 客户端
// @Summary 获取客户端配置
// @Description 获取客户端配置
// @Produce json
// @Success 200 {object} codec.Response{data=config}
// @Router /api/v1/client/config [get]
func (s *apis) makeConfig(ctx *api.Context, _ api.EmptyReq) (config, error) {
	var c = config{
		CustomerService: customerService{
			Channel: "WhatsApp",
			Contact: "+55 ***********",
		},
		LuckDraw: luckDrawSetting{
			EffectThreshold:    500,
			EffectThresholdLv1: 100,
			EffectThresholdLv2: 500,
			EffectThresholdLv3: 1000,
		},
		Gift: Gift{
			FastSendDuration: 20, // 当前20s
		},
		ThirdChannel: thirdChannel{
			Facebook: false,
		},
		Features: Features{
			HomeVisiblePage: 2,
			Preview:         true,
		},
		Business: business{
			Tag: 1,
		},
		Setting: map[string]any{
			"homeAutoRefreshInterval": 30,
		},
	}

	if dbg.Ing() {
		c.Features.Payermax = true
	}

	if app.IsAppStore(ctx) {
		// TODO only for testflight
		c.Features.Payermax = true
	}

	if uac, err := ctx.User(); err == nil {
		{
			if err := s.dm.Touch(ctx, uac.UserId, app.DeviceId(ctx)); err != nil {
				s.log.Warn("touch device failed", zap.String("userId", uac.UserId), zap.Error(err))
			}

			if err := s.ulm.UpdateUserLang(ctx, uac.UserId, i3n.UnWarp(ctx)); err != nil {
				s.log.Error("update user lang failed", zap.String("userId", uac.UserId), zap.Error(err))
			}
		}

		s.dr.Inc(ctx, time.Now(), "activeUsers", 1, data.Distinct(uac.UserId))

		if policy, err := s.pp.Policy(ctx, uac.UserId); err == nil {
			if policy.ThirdPay == payc.TPAllow {
				c.Features.Payermax = true
			}
		}

		if c.Features.LiveGame = game.ReleaseGame(uac, app.DeviceType(ctx), app.Version(ctx), app.BuildId(ctx)); c.Features.LiveGame {
			c.Features.LiveGame = s.gus.OpenGame(ctx, uac)
		}
	} else {
		c.Features.LiveGame = false
	}

	adNetwork := adjust.Network(ctx.Query("adn"))
	if adNetwork == "" {
		adNetwork = s.adm.Network(ctx, app.DeviceId(ctx))
	}
	if !c.Features.Payermax && !adNetwork.Organic() {
		c.Features.Payermax = true
	}

	c.Features.MiniGame = c.Features.LiveGame
	c.Features.Link = link.Open()

	// 这个一定要放在最后面覆盖之前的所有配置
	if app.IsAppStore(ctx) && app.InReview(app.Version(ctx)) {
		c.Business.Tag = 0
		c.Features.MiniGame = false
		c.Features.LiveGame = false
		c.Features.LuckEffectClose = 1
		c.Features.HomeVisiblePage = 0
	}

	return c, nil
}
