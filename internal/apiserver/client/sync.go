package client

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type clientSyncReq struct {
	PushToken string `json:"pushToken"` // 客户端推送令牌
}

// @Tags 客户端
// @Summary 同步客户端信息
// @Description 同步客户端信息：推送令牌
// @Produce json
// @Security HeaderAuth
// @Param param body clientSyncReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/client/sync [post]
func (s *apis) clientSync(ctx *api.Context, req clientSyncReq) (*api.EmptyResp, error) {
	uac, _ := ctx.User()

	if did := app.DeviceId(ctx); did != "" {
		if req.PushToken != "" && uac != nil {
			if err := s.dm.SetPushToken(ctx, did, req.PushToken); err != nil {
				return nil, err
			}
		}
	}

	return &api.EmptyResp{}, nil
}
