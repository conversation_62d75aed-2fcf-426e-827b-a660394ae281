package client

import (
	"errors"
	"slices"

	"gitlab.sskjz.com/overseas/live/osl/internal/push"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type pushOptionPayload struct {
	Channels map[string]bool `json:"channels"` // 频道开关
}

// @Tags 客户端
// @Summary 获取通知设置
// @Description 获取通知设置
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=pushOptionPayload}
// @Router /api/v1/client/push/option [get]
func (s *apis) getPushOption(ctx *api.Context, _ api.EmptyReq) (*pushOptionPayload, error) {
	dev, err := s.dm.Take(ctx, app.DeviceId(ctx))
	if err != nil {
		return nil, err
	}
	channels := make(map[string]bool)
	for _, key := range push.Channels {
		channels[key] = !slices.Contains(dev.PushIgnore, key)
	}
	return &pushOptionPayload{Channels: channels}, nil
}

// @Tags 客户端
// @Summary 写入通知设置
// @Description 写入通知设置（只传修改的即可）
// @Produce json
// @Security HeaderAuth
// @Param param body pushOptionPayload true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/client/push/option [post]
func (s *apis) setPushOption(ctx *api.Context, req pushOptionPayload) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	dev, err := s.dm.Take(ctx, app.DeviceId(ctx))
	if err != nil {
		return nil, err
	}
	if dev.UserId != uac.UserId {
		return nil, errors.New("permission denied")
	}
	if err := s.dm.SetPushIgnore(ctx, app.DeviceId(ctx), req.Channels); err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, nil
}
