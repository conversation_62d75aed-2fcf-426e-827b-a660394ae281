package client

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/adjust"
	"gitlab.sskjz.com/overseas/live/osl/internal/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/client/clogs"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/internal/ul"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/acl"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sts"
	"go.uber.org/zap"
)

func API(r *api.Router, mu *redi.Mutex, sm *sts.Mgr, pp payc.Getter, dm *device.Manager, am *acl.Manager, adm *adjust.Manager, mgr *client.Manager, lm *clogs.Manager, ulm *ul.Manager, gm *game.Manager, gus *game.Store, dr data.Recorder, vnd log.Vendor) {
	s := &apis{mu: mu, sm: sm, pp: pp, dm: dm, am: am, adm: adm, mgr: mgr, lm: lm, ulm: ulm, gm: gm, gus: gus, dr: dr, log: vnd.Scope("client.api")}
	{
		r.GET("/client/status", api.Generic(s.getStatus))
		r.GET("/client/version", api.Generic(s.chkVersion))
	}
	tr := r.TryAuth()
	{
		tr.GET("/client/config", api.Generic(s.makeConfig))
		tr.POST("/client/sync", api.Generic(s.clientSync))
	}
	ar := r.WithAuth()
	{
		ar.GET("/client/logs/upload", api.Generic(s.logsUpload))
		ar.GET("/client/logs/collect", api.Generic(s.chkLogsCollect))
		ar.POST("/client/logs/collect", api.Generic(s.updLogsCollect))
		ar.GET("/client/push/option", api.Generic(s.getPushOption))
		ar.POST("/client/push/option", api.Generic(s.setPushOption))
	}
}

type apis struct {
	rc  *redi.Client
	mu  *redi.Mutex
	sm  *sts.Mgr
	pp  payc.Getter
	dm  *device.Manager
	am  *acl.Manager
	adm *adjust.Manager
	mgr *client.Manager
	lm  *clogs.Manager
	ulm *ul.Manager
	gm  *game.Manager
	gus *game.Store
	dr  data.Recorder
	log *zap.Logger
}
