package client

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"go.uber.org/zap"
)

type versionResp struct {
	Version       string `json:"version"` // 可能为空
	BuildId       string `json:"buildId"` // Ios 特有
	ForceUpdate   bool   `json:"forceUpdate"`
	DownloadUrl   string `json:"downloadUrl"`
	MD5           string `json:"md5"`
	Title         string `json:"title"`
	UpdateContent string `json:"updateContent"`
	SilentUpdate  bool   `json:"silentUpdate"`
}

// @Tags 客户端
// @Summary 检查版本升级
// @Description 检查版本升级, 错误： 18001 - 没有更新版本（最新版本被禁用或者不存在已经发布的版本）
// @Produce json
// @Success 200 {object} codec.Response{data=versionResp}
// @Router /api/v1/client/version [get]
func (s *apis) chkVersion(ctx *api.Context, _ api.EmptyReq) (*versionResp, error) {
	var (
		devType = app.DeviceType(ctx)
		channel = app.ChannelId(ctx)
	)

	s.log.Debug("check version", zap.String("deviceType", devType), zap.String("channel", channel))

	if devType == "" || channel == "" {
		return nil, biz.NewError(biz.ErrInvalidParam, "deviceType or channel is empty")
	}

	v, err := s.mgr.LatestVersion2(ctx, devType, channel)
	if err != nil {
		return nil, err
	}

	if v == nil || v.Disabled {
		return nil, biz.NewError(biz.ErrVersionNotFound, "It's the latest version")
	}

	lang := i18n.PreferLang(ctx, "en")
	return &versionResp{
		Version:       v.Version,
		BuildId:       v.BuildId,
		ForceUpdate:   v.ForceUpdate,
		DownloadUrl:   v.DownloadUrl,
		MD5:           v.MD5,
		Title:         v.Title.Local(lang),
		UpdateContent: v.UpdateContent.Local(lang),
		SilentUpdate:  v.SilentUpdate,
	}, nil
}
