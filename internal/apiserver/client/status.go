package client

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/geoip"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type statusResp struct {
	Forbidden bool `json:"forbidden"` // 是否禁止访问
}

// @Tags 客户端
// @Summary 获取客户端状态
// @Description 获取客户端状态
// @Produce json
// @Success 200 {object} codec.Response{data=statusResp}
// @Router /api/v1/client/status [get]
func (s *apis) getStatus(ctx *api.Context, _ api.EmptyReq) (*statusResp, error) {
	var forbidden bool
	if !dbg.Ing() {
		if geoip.Client(ctx).Country == "CN" {
			forbidden = true
			if s.am.InWhitelist2(ctx, app.DeviceId(ctx), ctx.ClientIP()) {
				forbidden = false
			} else {
				_ = s.am.LogForbid(ctx)
			}
		}
		if !forbidden {
			if dev, err := s.dm.Take(ctx, app.DeviceId(ctx)); err == nil && dev.Status.Blocked() {
				forbidden = true
			}
		}
	}
	return &statusResp{
		Forbidden: forbidden,
	}, nil
}
