package sticker

import (
	"fmt"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/sticker"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

func Invoke(r *api.Router, sm *sticker.Manager, reviewm *review.Manager, vnd log.Vendor) {
	s := &apis{
		sm:      sm,
		reviewm: reviewm,
		logger:  vnd.Scope("api.sticker"),
	}

	ar := r.With<PERSON>uth()
	{
		// 贴纸列表
		ar.GET("/sticker/list", api.Generic(s.list))
		// 设置贴纸
		ar.POST("/sticker/set", api.Generic(s.set))
	}
}

type apis struct {
	sm      *sticker.Manager
	reviewm *review.Manager
	logger  *zap.Logger
}

type listResp struct {
	Text []Sticker `json:"text"` // 文字贴纸
	Img  []Sticker `json:"img"`  // 图片贴纸
}

type Sticker struct {
	Url       string            `json:"url"`
	NinePatch *StickerNinePatch `json:"ninePatch,omitempty"`
}

type StickerNinePatch struct {
	XDiv    []int `json:"xDiv"`    // 横向可拉伸区域，必须为2个Int，分别表示从左到右第一个拉伸点的像素位置和第二个拉伸点的像素位置
	YDiv    []int `json:"yDiv"`    // 纵向可拉伸区域，如上，从上到下。目前业务上应该为0和图片的高度
	Padding []int `json:"padding"` //内容区域，左上右下的内容padding值，目前业务左右应该是配置的，上下应该是0
}

// @Tags 贴纸
// @Summary 贴纸列表
// @Description 贴纸列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=listResp}
// @Router /api/v1/sticker/list [get]
func (a *apis) list(ctx *api.Context, _ api.EmptyReq) (*listResp, error) {
	textList := make([]Sticker, 0)
	imgList := make([]Sticker, 0)

	host := "https://godzilla-live-oss.kako.live"

	conf := [][]int{
		{31, 55, 0, 32, 21, 0, 8, 2},
		{25, 55, 0, 22, 25, 2, 8, 0},
		{24, 53, 0, 22, 24, 0, 8, 0},
		{25, 56, 0, 22, 25, 2, 8, 0},
		{28, 57, 0, 21, 28, 0, 8, 1},
		{35, 58, 0, 27, 25, 0, 8, 3},
		{26, 56, 0, 24, 26, 0, 8, 0},
		{28, 61, 0, 24, 28, 1, 8, 0},
		{30, 61, 0, 25, 30, 3, 8, 0},
	}

	factor := 3

	// 后台表中读取
	for i := 1; i <= 9; i++ {
		if len(conf) < i {
			continue
		}

		v := conf[i-1]

		np := &StickerNinePatch{
			XDiv:    []int{v[0] * factor, v[1] * factor},
			YDiv:    []int{v[2] * factor, v[3] * factor},
			Padding: []int{v[4] * factor, v[5] * factor, v[6] * factor, v[7] * factor}, // 文字位置
		}

		textList = append(textList, Sticker{
			Url:       fmt.Sprintf("%s/sticker/sticker_text_250313%d_3x.png", host, i),
			NinePatch: np,
		})
	}

	lng := i3n.UnWarp(ctx)

	if !lo.Contains([]string{"en", "pt"}, lng) {
		lng = "en"
	}

	for i := 1; i <= 7; i++ {
		imgList = append(imgList, Sticker{
			Url: fmt.Sprintf("%s/sticker/sticker_img_250306%d_3x_%s.png", host, i, lng),
		})
	}

	return &listResp{
		Text: textList,
		Img:  imgList,
	}, nil
}

type setReq struct {
	Content string `json:"content" binding:"max=2000"` // 贴纸内容
}

// @Tags 贴纸
// @Summary 主播设置贴纸
// @Description 主播设置贴纸 错误码：20002，包含敏感词
// @Produce json
// @Security HeaderAuth
// @Param param body setReq true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/sticker/set [post]
func (a *apis) set(c *api.Context, req setReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	content := req.Content

	if content != "" {
		words := a.reviewm.SensitiveWords(content)

		if len(words) > 0 {
			return nil, biz.NewError(biz.ErrSensitiveContent, "sensitive words detected")
		}
	}

	if err := a.sm.Set(c, uac.UserId, content); err != nil {
		return nil, err
	}

	return nil, nil
}
