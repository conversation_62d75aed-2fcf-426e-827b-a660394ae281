package mixer

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
)

func AnchorLiveSession(v *live.Session) *types.AnchorLiveSession {
	return &types.AnchorLiveSession{
		SessionId: v.Id.Hex(),
		Title:     v.Title,
		Cover:     v.Cover,
		StartTime: v.StartTime.Unix(),
		EndTime:   v.EndTime.Unix(),
		AnchorLive: types.AnchorLive{
			AudCount:        v.AudCount,
			GiftUserCount:   v.GiftUserCount,
			NewFanCount:     v.<PERSON>FanCount,
			Duration:        v.Duration / 60,
			ValidDuration:   v.ValidDuration() / 60,
			LuckDiamond:     v.<PERSON>,
			GiftDiamond:     v.GiftDiamond,
			TotalGiftIncome: v.LuckIncome.Add(v.GiftIncome).Add(v.FClubIncome).IntPart(),
		},
	}
}
