package mixer

import (
	"strings"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sts"
)

func newStsTokenOpts(opts []stsTokenOpt) *stsTokenOpts {
	o := new(stsTokenOpts)
	for _, opt := range opts {
		opt(o)
	}
	return o
}

type stsTokenOpts struct {
	imageSuffix string
}

type stsTokenOpt func(*stsTokenOpts)

func ImageSuffix(val string) stsTokenOpt {
	return func(s *stsTokenOpts) {
		s.imageSuffix = val
	}
}

func STSToken(ar *sts.AssumeRoleResponse, opts ...stsTokenOpt) *types.STSToken {
	token := &types.STSToken{
		Endpoint:        ar.Storage.EndpointURL(),
		Bucket:          ar.Storage.Bucket,
		AccessKeyId:     ar.AccessKeyId,
		AccessKeySecret: ar.AccessKeySecret,
		SecurityToken:   ar.SecurityToken,
		Expiration:      ar.Expiration.Unix(),
		ServerTime:      time.Now().Unix(),
		Prefix:          addSuffix(ar.Prefix, "/"),
		Pattern:         ar.Storage.ExternalURL(ar.Prefix, "$key$"),
	}
	opt := newStsTokenOpts(opts)
	if opt.imageSuffix != "" {
		token.Suffix.Image = addPrefix(opt.imageSuffix, "/")
	}
	return token
}

func addPrefix(s, prefix string) string {
	if strings.HasPrefix(s, prefix) {
		return s
	}
	return prefix + s
}

func addSuffix(s, suffix string) string {
	if strings.HasSuffix(s, suffix) {
		return s
	}
	return s + suffix
}
