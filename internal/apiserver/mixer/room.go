package mixer

import (
	"context"
	"fmt"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Room(ctx context.Context, userId string, ug user.Getter, fg follow.Getter, sm *rsd.Stats, v *live.Room) (*types.Room, error) {
	anchor, err := ug.Account(ctx, v.UserId)
	if err != nil {
		return nil, err
	}

	var followed bool

	if userId != "" {
		followed, _ = fg.Following(ctx, userId, v.UserId)
	}

	ext := v.Ext
	showAgr := false

	if userId == v.UserId {
		// 主播态
		// 未开播状态&&结束时间早于开始时间，表示未开播过
		if v.Status == live.LiveStatusUnLive && v.EndTime.Before(v.StartTime) {
			showAgr = true
		}
	} else {
		// 用户态
		// 隐藏设备ID
		// TODO 这里会引发 concurrent map writes
		//delete(ext, live.RoomExtDeviceId)
	}

	// 分辨率
	resolution := "360P"

	if app.IsAppStore(api.Unwrap(ctx)) {
		resolution = "1080P"
	} else {
		if v.Grade >= 3 {
			resolution = "720P"
		}
	}

	var endTime int64

	if !v.EndTime.IsZero() {
		endTime = v.EndTime.Unix()
	}

	tr := &types.Room{
		User:       UserWithExt(ctx, anchor),
		Followed:   followed,
		RoomId:     v.Id.Hex(),
		SessionId:  v.SessionId.Hex(),
		Title:      v.Title,
		Cover:      v.Cover,
		Stream:     v.Stream,
		Like:       NoErr(sm.GetSessionLike2(ctx, v.SessionId.Hex())),
		LabelUrl:   v.Label.Url(ctx),
		Ext:        ext,
		Status:     v.Status,
		Mode:       v.Mode,
		ShowAgr:    showAgr,
		StartTime:  v.StartTime.Unix(),
		EndTime:    endTime,
		Paused:     v.Pause.Paused(),
		ForLucky:   v.IsLuckyRoom(),
		ForGame:    v.IsGameRoom(),
		Resolution: resolution,
		Sticker:    v.Sticker,
	}

	if tr.Title == "" {
		tr.Title = fmt.Sprintf("%s %s", anchor.Nickname, i3n.T(ctx, "is live streaming"))
	}

	if tr.Cover == "" {
		tr.Cover = anchor.Avatar
	}

	return tr, nil
}

func RoomV2(ctx context.Context, userId string, ug user.Getter, fg follow.Getter, sm *rsd.Stats, v *live.Room, lm *live.Manager) (*types.Room, error) {
	tr, err := Room(ctx, userId, ug, fg, sm, v)
	if err != nil {
		return nil, err
	}

	if userId == "" {
		return tr, nil
	}

	// 直播中不需要预约信息
	if v.IsLiving() {
		return tr, nil
	}

	// 是否预约了下一场直播
	appointment, err := lm.HasAppointment(ctx, userId, v.UserId)

	if err == nil {
		tr.Appointment = appointment
	}

	return tr, nil
}

func RoomUser(ctx context.Context, anchorId, userId string) (*types.RoomUser, error) {
	var ret types.RoomUser

	if m := hasMixer(ctx); m != nil {
		if level, _ := m.fc.ALevel(ctx, anchorId, userId); level > 0 {
			ret.FansLevel = level
		}
	}

	return &ret, nil
}

func RoomMe(ctx context.Context, anchorId, userId string, fc fclub.Getter) (*types.RoomMe, error) {
	var fansclub *types.RoomFans

	if userId != "" && userId != anchorId {
		if lv, err := fc.Info(ctx, anchorId, userId); err == nil && lv.Valid() {
			fansclub = &types.RoomFans{
				Level:    lv.Level,
				Inactive: !lv.Active,
			}
		}
	}

	return &types.RoomMe{
		Fansclub: fansclub,
	}, nil
}
