package mixer

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
)

var (
	ErrNoMixerFound = errors.New("no mixer found")
)

func UserWallet(ctx context.Context, userId string) (*types.UserWallet, error) {
	if w, err := GlobalWallet(ctx, userId); err != nil {
		return nil, err
	} else {
		return &w.UserWallet, nil
	}
}

func AnchorWallet(ctx context.Context, userId string) (*types.AnchorWallet, error) {
	if w, err := GlobalWallet(ctx, userId); err != nil {
		return nil, err
	} else {
		return &w.AnchorWallet, nil
	}
}

func GlobalWallet(ctx context.Context, userId string) (*types.GlobalWallet, error) {
	m := hasMixer(ctx)
	if m == nil {
		return nil, ErrNoMixerFound
	}
	fac, err := m.fg.Take(ctx, userId)
	if err != nil {
		return nil, err
	}
	return &types.GlobalWallet{
		UserWallet:   types.UserWallet{Diamond: fac.BVal(fund.PTypeDiamond).IntPart()},
		AnchorWallet: types.AnchorWallet{Fruit: fac.BVal(fund.PTypeFruits).IntPart()},
	}, nil
}
