package mixer

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var (
	validBirthday = time.Date(1900, 1, 1, 0, 0, 0, 0, time.Local)
)

func User(ctx context.Context, acc *user.Account) *types.User {
	var u = types.User{
		UserId:   acc.UserId,
		NumId:    acc.NumId,
		Nickname: acc.Nickname,
		Avatar:   acc.Avatar,
		Level:    acc.Level,
		Role:     acc.Roles,
	}
	if acc.Status.Deleted() {
		maskUser(&u)
		return &u
	}
	if m := hasMixer(ctx); m != nil {
		if patrol.Has(u.Role) && m.pg.Take(ctx, acc.UserId).Hidden() {
			u.Role = u.Role.Unset(user.RolePatroller)
		}
		if seller.Has(u.Role) && m.sg.Take(ctx, acc.UserId).Hidden() {
			u.Role = u.Role.Unset(user.RoleSeller)
		}

		if is, err := m.lm.IsLiving(ctx, acc.UserId); err == nil {
			u.IsLiving = is
			if is {
				u.LStatus |= protocol.LStatusLive
				if sess, _ := m.pkm.UserCurSession2(ctx, acc.UserId); sess != nil {
					u.LStatus |= pk.MakeStatus(sess.Status)
				}
			}
		}

		if u.LStatus&protocol.LStatusPK == 0 {
			if li, _ := m.lkm.UserLinkInfo2(ctx, acc.UserId); li != nil {
				u.LStatus |= protocol.LStatusLink
			}
		}

		{
			u.AvatarBorder = m.dsm.AvatarBorder(ctx, acc.UserId)
		}
	}
	return &u
}

func UserExt(ctx context.Context, acc *user.Account) *types.UserExt {
	var u = types.UserExt{
		ShowId:    acc.ShowId,
		Signature: acc.Signature,
		Gender:    acc.Gender,
		School:    acc.School,
	}
	if acc.Status.Deleted() {
		maskUserExt(&u)
		return &u
	}
	if acc.Birthday.After(validBirthday) {
		u.Age = int(time.Since(acc.Birthday).Hours()) / (24 * 365)
	}
	if m := hasMixer(ctx); m != nil {
		u.Area = m.rg.Take(ctx, acc.Region)
	}
	return &u
}

func UserWithExt(ctx context.Context, acc *user.Account) *types.UserWithExt {
	return &types.UserWithExt{
		User:    *User(ctx, acc),
		UserExt: *UserExt(ctx, acc),
	}
}

func UserSocial(stats *follow.Stats) *types.UserSocial {
	var u = types.UserSocial{
		Following: stats.Following,
		Followers: stats.Followers,
	}
	return &u
}

func UserPrivate(acc *user.Account, stats *follow.Stats) *types.UserPrivate {
	var u = types.UserPrivate{
		Verified: acc.Status.Verified(),
		Friends:  stats.Friends,
		Region:   acc.Region,
	}
	if acc.Birthday.After(validBirthday) {
		u.Birthday = acc.Birthday.Format("********")
	}
	return &u
}

func UserMoment(stats *moment.Stats, isSelf, isFriend bool) *types.UserMoment {
	var moments int
	if isSelf {
		moments = stats.MomentsVisibleAll + stats.MomentsVisibleFriend + stats.MomentsVisibleSelf
	} else if isFriend {
		moments = stats.MomentsVisibleAll + stats.MomentsVisibleFriend
	} else {
		moments = stats.MomentsVisibleAll
	}
	var u = types.UserMoment{
		Moments:   moments,
		RecvLikes: stats.RecvLikes,
	}
	return &u
}

func FollowInfo(ctx context.Context, userId string) *types.UserSocial {
	var f types.UserSocial
	if m := hasMixer(ctx); m != nil {
		stats := NoErr(m.fi.Stats(ctx, userId))
		if stats != nil {
			f = *UserSocial(stats)
		}
	}
	return &f
}

func FollowState(ctx context.Context, from, to string) *types.FollowState {
	var f types.FollowState
	if m := hasMixer(ctx); m != nil {
		f.Followed = NoErr(m.fi.Following(ctx, from, to))
		f.Follower = NoErr(m.fi.Following(ctx, to, from))
	}
	return &f
}

func UserGrant(ctx context.Context, from, to string) *types.UserGrant {
	var ug types.UserGrant
	if m := hasMixer(ctx); m != nil {
		if st, err := m.urm.UserBlockRelation(ctx, from, to); err == nil {
			ug.BlockStatus = uint32(st)
		}

		if mtd, err := m.urm.IsMuted(ctx, from, to); err == nil {
			ug.Muted = mtd
		}

	}
	return &ug
}
