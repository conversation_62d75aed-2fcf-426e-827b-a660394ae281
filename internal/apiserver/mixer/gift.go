package mixer

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
)

func Gift(gift *gift.Gift) *types.Gift {
	return &types.Gift{
		Id:            gift.ID,
		Name:          gift.Name,
		Diamond:       gift.Diamond,
		Display:       gift.Display,
		Combo:         gift.Combo,
		Group:         gift.Group,
		QuickSend:     gift.QuickSend,
		ForFansclub:   gift.ForFansclub,
		HonorLevel:    gift.HonorLevel,
		FansclubLevel: gift.FansclubLevel,
		EffectId:      gift.EffectId,
		ImageUrl:      gift.ImageUrl,
		GifUrl:        gift.GifUrl,
		LabelUrl:      gift.LabelUrl,
		Describe:      gift.Describe,
		GroupInfo:     gift.GroupInfo,
	}
}

func LiteGift(gift *gift.Gift) *types.LiteGift {
	return &types.LiteGift{
		Id:       gift.ID,
		Name:     gift.Name,
		Diamond:  gift.Diamond,
		ImageUrl: gift.ImageUrl,
	}
}
