package appstore

import (
	"gitlab.sskjz.com/go/iap/appstore"
)

var products = map[string]map[string]string{
	"live.kako.global": {
		"kako_global_standard_7000":    "7000",
		"kako_global_standard_35000":   "35000",
		"kako_global_standard_70000":   "70000",
		"kako_global_standard_350000":  "350000",
		"kako_global_standard_700000":  "700000",
		"kako_global_standard_3500000": "3500000",
	},
	"live.kako.oversea": {
		"kako_oversea_standard_7000":    "7000",
		"kako_oversea_standard_35000":   "35000",
		"kako_oversea_standard_70000":   "70000",
		"kako_oversea_standard_350000":  "350000",
		"kako_oversea_standard_700000":  "700000",
		"kako_oversea_standard_3500000": "3500000",
	},
}

func findProduct(bundleId string, inApps []appstore.InApp, sku string) (appstore.InApp, bool) {
	for _, inApp := range inApps {
		if products[bundleId][inApp.ProductID] == sku {
			return inApp, true
		}
	}
	return appstore.InApp{}, false
}
