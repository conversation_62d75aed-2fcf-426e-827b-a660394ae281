package appstore

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/iap/appstore"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/sb"
)

func newIns(ps *pay.Service, sc *sb.Checker, log *zap.Logger) *IAP {
	return &IAP{
		ps:  ps,
		sc:  sc,
		cli: appstore.New(),
		log: log,
	}
}

type IAP struct {
	ps  *pay.Service
	sc  *sb.Checker
	cli *appstore.Client
	log *zap.Logger
}

func (s *IAP) Create(ctx context.Context, order *pay.Order) (*pay.Order, pay.Extras, error) {
	order.OrderId = order.TradeNo
	return order, nil, nil
}

func (s *IAP) Verify(ctx context.Context, tradeNo, receipt string) (*pay.Order, error) {
	resp := &appstore.IAPResponse{}
	if err := s.cli.Verify(ctx, appstore.IAPRequest{
		ReceiptData:            receipt,
		ExcludeOldTransactions: true,
	}, resp); err != nil {
		return nil, err
	}

	order, err := s.ps.Take(ctx, tradeNo)
	if err != nil {
		if errors.Is(err, pay.ErrOrderNotExists) {
			s.log.Info("receipt without order", zap.String("tradeNo", tradeNo), zap.Any("receipt", resp))
		}
		return nil, err
	}

	s.log.Debug("verify result", zap.String("userId", order.UserId), zap.String("tradeNo", order.TradeNo), zap.Any("receipt", resp))

	if resp.Status != 0 {
		return nil, errors.New("verify failed")
	}

	inApps := resp.Receipt.InApp
	if len(inApps) == 0 {
		return nil, errors.New("invalid receipt")
	}

	payInfo, has := findProduct(resp.Receipt.BundleID, inApps, order.SKU)
	if !has {
		return nil, errors.New("invalid product id")
	}

	options := []pay.Option{pay.WithOrderId(payInfo.TransactionID)}
	if resp.Environment == appstore.Sandbox {
		if !s.sc.In(order.UserId) {
			return nil, sb.ErrNotSandboxUser
		}
		options = append(options, pay.WithRemark("sandbox"))
	}

	return s.ps.MakePaid(ctx, order.TradeNo, options...)
}

func (s *IAP) Refund(ctx context.Context, orderId string) (*pay.Order, error) {
	order, err := s.ps.Take2(ctx, gwId, orderId)
	if err != nil {
		return nil, err
	}
	return s.ps.MakeRefund(ctx, order.TradeNo)
}
