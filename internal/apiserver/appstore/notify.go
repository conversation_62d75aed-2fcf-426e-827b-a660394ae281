package appstore

import (
	"github.com/golang-jwt/jwt/v4"
	"gitlab.sskjz.com/go/iap/appstore"
	storeAPI "gitlab.sskjz.com/go/iap/appstore/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

type rtdnBody struct {
	Payload string `json:"signedPayload"`
}

func (s *apis) notify(ctx *api.Context, req rtdnBody) error {
	notify, trans, err := parseNotify(req.Payload)
	if err != nil {
		return err
	}

	switch notify.NotificationType {
	case appstore.NotificationTypeV2Refund:
		if order, err := s.iap.Refund(ctx, trans.TransactionID); err != nil {
			s.iap.log.Warn("refund failed in notify", zap.Any("notify", notify), zap.Error(err))
		} else {
			s.iap.log.Info("refund success in notify", zap.Any("notify", notify), zap.Any("order", order))
		}
	}

	s.iap.log.Info("received notify", zap.Any("notify", notify), zap.Any("transaction", trans))
	return nil
}

type SubscriptionNotificationV2DecodedPayload struct {
	appstore.SubscriptionNotificationV2DecodedPayload
}

func (s SubscriptionNotificationV2DecodedPayload) Valid() error { return nil }

var storeCli = storeAPI.NewStoreClient(&storeAPI.StoreConfig{})

func parseNotify(payload string) (*SubscriptionNotificationV2DecodedPayload, *storeAPI.JWSTransaction, error) {
	var out SubscriptionNotificationV2DecodedPayload
	_, _, err := (&jwt.Parser{}).ParseUnverified(payload, &out)
	if err != nil {
		return nil, nil, err
	}
	ts, err := storeCli.ParseSignedTransactions([]string{string(out.Data.SignedTransactionInfo)})
	if err != nil {
		return nil, nil, err
	}
	out.Data.SignedTransactionInfo = ""
	return &out, ts[0], nil
}
