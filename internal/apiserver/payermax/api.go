package payermax

import (
	"slices"
	"strconv"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/binance"
	"gitlab.sskjz.com/overseas/live/osl/internal/ganopay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/usolve"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/internal/paypal"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"go.uber.org/zap"
)

func Provide(ps *pay.Service, pm *payermax.Manager, gm *ganopay.Manager, bm *binance.Manager, pp *paypal.Manager, um *usolve.Manager, vnd log.Vendor) *API {
	return &API{ps: ps, pm: pm, gm: gm, bm: bm, pp: pp, um: um, log: vnd.Scope("api.payermax")}
}

func Invoke(r *api.Router, s *API, si sto.Instance) {
	initLogo(si.Conf("default"))
	ar := r.WithAuth()
	{
		ar.GET("/payermax/country", api.Generic(s.Country))
		ar.GET("/payermax/methods", api.Generic(s.Methods))
		ar.POST("/payermax/order", api.Generic(s.CreateOrder))
		ar.POST("/payermax/query", api.Generic(s.QueryOrder))
	}
	{
		r.POST("/payermax/notify", api.Custom(s.pm.RecvNotify))
		r.POST("/ganopay/notify", api.Custom(s.gm.RecvNotify))
		r.POST("/binance/notify", api.Custom(s.bm.RecvNotify))
		r.POST("/paypal/notify", api.Custom(s.pp.RecvNotify))
		r.POST("/usolve/notify", api.Custom(s.um.RecvNotify))
	}
}

type API struct {
	ps  *pay.Service
	pm  *payermax.Manager
	gm  *ganopay.Manager
	bm  *binance.Manager
	pp  *paypal.Manager
	um  *usolve.Manager
	log *zap.Logger
}

type CountryRes struct {
	Id pay.Country `json:"id"` // 国家ID
	countryRes
	Default bool `json:"default"` // 是否为默认
}

type CountryResponse struct {
	List []*CountryRes `json:"list"`
}

// @Tags 支付
// @Summary 获取支付国家列表
// @Description 获取payermax可用的国家列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=CountryResponse}
// @Router /api/v1/payermax/country [get]
func (s *API) Country(ctx *api.Context, _ api.EmptyReq) (*CountryResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ids := lo.Keys(countries)
	slices.Sort(ids)

	out := make([]*CountryRes, 0, len(countries))
	for _, id := range ids {
		out = append(out, &CountryRes{
			Id:         id,
			countryRes: *countries[id],
			Default:    uac.Country == string(id),
		})
	}

	if !slices.ContainsFunc(out, func(c *CountryRes) bool { return c.Default }) {
		out[0].Default = true
	}

	return &CountryResponse{List: out}, nil
}

type Method struct {
	Id       string       `json:"id"`       // 支付方式ID，创建订单时需要
	Name     string       `json:"name"`     // 收单机构名称
	Logo     string       `json:"logo"`     // 收单机构logo
	Currency pay.Currency `json:"currency"` // 标价币种
	Price    float64      `json:"price"`
}

type MethodsRequest struct {
	Country pay.Country `form:"country"` // 国家
	Scene   string      `form:"scene"`   // 支付场景
	SKU     string      `form:"sku"`     // 商品SKU
}

type MethodsResponse struct {
	List []*Method `json:"list"`
}

// @Tags 支付
// @Summary 获取支付机构列表
// @Description 获取payermax可用的收单机构列表
// @Produce json
// @Security HeaderAuth
// @Param param query MethodsRequest true "请求参数"
// @Success 200 {object} codec.Response{data=MethodsResponse}
// @Router /api/v1/payermax/methods [get]
func (s *API) Methods(ctx *api.Context, req MethodsRequest) (*MethodsResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	methods, err := s.pm.Methods(ctx, uac.UserId, req.Country, req.Scene, req.SKU)
	if err != nil {
		return nil, err
	}

	if err := s.adjMethods(ctx, uac.UserId, req.Country, req.Scene, req.SKU, &methods); err != nil {
		return nil, err
	}

	out := make([]*Method, 0, len(methods))
	for _, m := range methods {
		out = append(out, &Method{
			Id:       m.Payment.Id(),
			Name:     m.Payment.Name,
			Logo:     m.Payment.Logo,
			Currency: m.Payment.Cur,
			Price:    m.Price,
		})
	}

	return &MethodsResponse{List: out}, nil
}

type CreateOrderRequest struct {
	Method   string       `json:"method" binding:"required"`
	Currency pay.Currency `json:"currency" binding:"required,iso4217"`
	Scene    string       `json:"scene" binding:"required"`
	SKU      string       `json:"sku" binding:"required"`
}

type CreateOrderResponse struct {
	TradeNo     string          `json:"tradeNo"`     // 内部交易号
	Status      pay.OrderStatus `json:"status"`      // 订单状态
	RedirectUrl string          `json:"redirectUrl"` // 支付地址
	External    bool            `json:"external"`    // 跳转外部
}

// @Tags 支付
// @Summary 创建订单
// @Description 创建订单
// @Produce json
// @Security HeaderAuth
// @Param param body CreateOrderRequest true "请求参数"
// @Success 200 {object} codec.Response{data=CreateOrderResponse}
// @Router /api/v1/payermax/order [post]
func (s *API) CreateOrder(ctx *api.Context, req CreateOrderRequest) (*CreateOrderResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	gateway := "payermax"
	switch pay.Params(req.Method).Get("t") {
	case "GANOPAY":
		gateway = "ganopay"
	case "BINANCE":
		gateway = "binance"
	case "PAYPAL":
		gateway = "paypal"
	case "USOLVE":
		gateway = "usolve"
	}

	order, extras, err := s.ps.Create(ctx, gateway, uac.UserId, req.Scene, req.SKU, req.Currency, pay.WithParams(req.Method))
	if err != nil {
		return nil, err
	}

	return &CreateOrderResponse{
		TradeNo:     order.TradeNo,
		Status:      order.Status,
		RedirectUrl: extras["redirectUrl"],
		External:    mixer.NoErr(strconv.ParseBool(extras["external"])),
	}, nil
}

type QueryOrderRequest struct {
	TradeNo string `json:"tradeNo" binding:"required"` // 内部交易号
}

type QueryOrderResponse struct {
	Status pay.OrderStatus `json:"status"` // 订单状态
}

// @Tags 支付
// @Summary 查询订单
// @Description 查询订单
// @Produce json
// @Security HeaderAuth
// @Param param body QueryOrderRequest true "请求参数"
// @Success 200 {object} codec.Response{data=QueryOrderResponse}
// @Router /api/v1/payermax/query [post]
func (s *API) QueryOrder(ctx *api.Context, req QueryOrderRequest) (*QueryOrderResponse, error) {
	order, err := s.ps.Take(ctx, req.TradeNo)
	if err != nil {
		return nil, err
	} else if order.Status.Final() {
		return &QueryOrderResponse{Status: order.Status}, nil
	}

	switch order.Gateway {
	case "payermax":
		order, err = s.pm.Query(ctx, req.TradeNo)
	case "ganopay":
		order, err = s.gm.Query(ctx, req.TradeNo)
	case "binance":
		order, err = s.bm.Query(ctx, req.TradeNo)
	case "paypal":
		order, err = s.pp.Query(ctx, req.TradeNo)
	case "usolve":
		order, err = s.um.Query(ctx, req.TradeNo)
	}
	if err != nil {
		return nil, err
	}

	return &QueryOrderResponse{Status: order.Status}, nil
}
