package payermax

import (
	"context"
	"slices"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type provider func(ctx context.Context, userId string, country pay.Country, scene, sku string) ([]payermax.Method, error)

func (s *API) adjMethods(ctx *api.Context, userId string, country pay.Country, scene, sku string, raw *[]payermax.Method) error {
	*raw = lo.Filter(*raw, func(m payermax.Method, _ int) bool {
		if m.Payment.Type == payermax.Apple && !app.IsIOS(ctx) {
			return false
		}
		return true
	})

	appendNew := func(call provider) {
		methods, _ := call(ctx, userId, country, scene, sku)
		*raw = append(*raw, methods...)
	}

	replaceSame := func(call provider) {
		methods, _ := call(ctx, userId, country, scene, sku)
		for _, method := range methods {
			if i := slices.IndexFunc(*raw, func(p payermax.Method) bool {
				return p.Payment.Name == method.Payment.Name
			}); i >= 0 {
				(*raw)[i] = method
			}
		}
	}

	if app.IsWeb(ctx) {
		appendNew(s.bm.Methods)
	}

	if app.IsWeb(ctx) && scene == "recharge" {
		appendNew(s.pp.Methods)
	}

	if dbg.Ing() && rng.Prob(100, 50) {
		replaceSame(s.gm.Methods)
	}

	if scene == "recharge" && slices.Contains([]string{"10000", "15000", "50000", "100000"}, sku) {
		replaceSame(s.um.Methods)
	}

	if scene == "bscharge" {
		replaceSame(s.um.Methods)
	}

	return nil
}
