package payermax

import (
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
)

type countryRes struct {
	Name string `json:"name"`
	Icon string `json:"icon"`
}

var (
	countries = map[pay.Country]*countryRes{
		pay.BR: {
			Name: "Brazil",
			Icon: "{oss}/payermax/country/br.png",
		},
		pay.US: {
			Name: "United States",
			Icon: "{oss}/payermax/country/us.png",
		},
	}
)

func initLogo(sc sto.Conf) {
	domain := sc.ExternalURL()
	for code, country := range countries {
		countries[code].Icon = strings.Replace(country.Icon, "{oss}", domain, 1)
	}
}
