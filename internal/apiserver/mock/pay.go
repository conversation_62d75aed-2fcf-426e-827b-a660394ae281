package mock

import (
	"context"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

func Pay(r *api.Router, ps *pay.Service, gg gid.Generator, gws *pay.Gateways, vnd log.Vendor) {
	if !dbg.Ing() {
		return
	}
	h := &mockPayAPI{logger: vnd.Scope("api.pay.mock"), ps: ps, id: gg.New("mock-order")}
	ar := r.WithAuth()
	{
		ar.POST("/pay/mock/order", api.Generic(h.MakeOrder))
	}
	gws.Register("mock", h)
}

type mockPayAPI struct {
	logger *zap.Logger
	ps     *pay.Service
	id     gid.Generator
}

func (s *mockPayAPI) Create(ctx context.Context, order *pay.Order) (*pay.Order, pay.Extras, error) {
	next, err := s.id.Next()
	if err != nil {
		return nil, nil, err
	}
	order.OrderId = next
	return order, nil, nil
}

type MakeOrderRequest struct {
	Scene string `json:"scene"`
	SKU   string `json:"sku"`
}

type MakeOrderResponse struct {
	TradeNo string          `json:"tradeNo"` // 内部交易号
	Status  pay.OrderStatus `json:"status"`  // 订单状态
}

func (s *mockPayAPI) MakeOrder(ctx *api.Context, req MakeOrderRequest) (*MakeOrderResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	order, _, err := s.ps.Create(ctx, "mock", uac.UserId, req.Scene, req.SKU, pay.USD)
	if err != nil {
		return nil, err
	}

	if order.Status == pay.OStatusInit {
		order, err = s.ps.MakePaid(ctx, order.TradeNo)
		if err != nil {
			return nil, err
		}
	}

	return &MakeOrderResponse{
		TradeNo: order.TradeNo,
		Status:  order.Status,
	}, nil
}
