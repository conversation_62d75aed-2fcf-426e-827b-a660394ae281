package mock

import (
	"errors"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func Login(r *api.Router, jwt *auth.JWT, um *user.Manager, ud ulink.Delegate, vnd log.Vendor) {
	if !dbg.Ing() {
		return
	}
	h := &mockLoginAPI{logger: vnd.Scope("api.login.mock"), jwt: jwt, um: um, ud: ud}
	r.GET("/login/mock", api.Request(h.<PERSON>ck))
}

type mockLoginAPI struct {
	logger *zap.Logger
	jwt    *auth.JWT
	um     *user.Manager
	ud     ulink.Delegate
}

type loginRequest struct {
	Id   string `form:"id"`   // Id
	Name string `form:"name"` // 昵称
}

// @Tags 登录
// @Summary Mock登录
// @Description Mock登录
// @Produce json
// @Param param query loginRequest true "请求参数"
// @Success 200 {object} codec.Response{data=auth.Payload}
// @Router /api/v1/login/mock [get]
func (h *mockLoginAPI) Mock(ctx *api.Context, req loginRequest) error {
	appId := "mock"
	openId := req.Id

	var account *user.Account

	if exists, err := h.ud.Take(ctx, appId, openId); err != nil {
		if !errors.Is(err, user.ErrAccountNotExists) {
			return err
		}
		if created, err := h.ud.Create(
			ctx, &ulink.Connect{AppId: appId, OpenId: openId},
			&user.Account{
				Nickname: req.Name,
				Gender:   rng.Range(1, 2),
			},
			user.AllowRandNN(),
		); err != nil {
			return err
		} else {
			account = created
		}
	} else {
		if err := h.um.Update(ctx, exists.UserId, user.UpAccount(&user.Account{Nickname: req.Name})); err != nil {
			if !errors.Is(err, user.ErrNoUpdateValue) {
				h.logger.Info("update account failed", zap.Error(err))
			}
		}
		account = exists
	}

	h.logger.Debug("assign login", zap.String("userId", account.UserId), zap.String("nickname", account.Nickname))
	return h.jwt.AssignLogin(ctx.Context, account)
}
