package security

import (
	"net/url"
	"slices"
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

var (
	ErrQrCodeNotSupport = biz.NewError(biz.ErrQrCodeNotSupport, "qr value not support")
)

var (
	supportSchemes = []string{"https", "http"}
	securityDomain = []string{"*.kako.live", "*.sskjz.com"}
)

func API(r *api.Router) {
	s := &apis{}
	r.TryAuth().POST("/security/check", api.Generic(s.check))
}

type apis struct {
}

type checkReq struct {
	Url string `json:"url"`
}

type checkResp struct {
	Url string `json:"url"`
}

// @Tags 安全
// @Summary 校验url地址
// @Description 需要登录态的页面会附上用户的token
// @Produce json
// @Param param body checkReq true "请求参数"
// @Success 200 {object} codec.Response{data=checkResp}
// @Router /api/v1/security/check [post]
func (s *apis) check(ctx *api.Context, req *checkReq) (*checkResp, error) {
	u, err := url.Parse(req.Url)
	if err != nil {
		return nil, ErrQrCodeNotSupport
	}

	if !slices.Contains(supportSchemes, u.Scheme) {
		return nil, ErrQrCodeNotSupport
	}

	for _, domain := range securityDomain {
		if domain[0] == '*' {
			if !strings.HasSuffix(u.Host, domain[1:]) {
				continue
			}
		} else if u.Host != domain {
			continue
		}
		q := u.Query()
		if _, err := ctx.User(); err == nil {
			q.Set("token", strings.TrimPrefix(ctx.GetHeader("Authorization"), "Bearer "))
			q.Del("needLogin")
		} else if q.Has("needLogin") {
			return nil, err
		}
		{
			// TODO temp code
			if strings.HasSuffix(u.Path, "new_anchor_activity.html") {
				q.Set("deviceId", app.DeviceId(ctx))
			}
		}
		u.RawQuery = q.Encode()
		break
	}

	return &checkResp{Url: u.String()}, nil
}
