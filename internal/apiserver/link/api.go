package link

import (
	"context"
	"errors"
	"slices"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/online"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var ErrUserInPK = biz.NewError(biz.ErrPKing, "In PK, the link function cannot be used")

type Api struct {
	mlm *link.Manager
	om  *online.Manager
	lm  *live.Manager
	pkm *pk.Manager
	sm  *rsd.Stats
	ug  user.Getter
	pg  patrol.Getter
}

func (a *Api) PKingError(ctx context.Context, userId string) error {
	if in, err := a.pkm.IsPKing(ctx, userId); err != nil {
		return err
	} else if in {
		return ErrUserInPK
	}

	return nil
}

type StreamerStatus struct {
	Allow      bool         `json:"allow"`      // 是否允许连线
	Requesters []ScoreUser  `json:"requesters"` // 连线请求列表, 最大20个
	Audiences  []InviteUser `json:"audiences"`  // 观众列表, 最大20个
}

// @tags 连线
// @summary 观众连线状态（主播端）
// @description 观众连线状态（主播端）
// @produce json
// @success 200 {object} StreamerStatus
// @router /api/v1/link/streamer/status [get]
func (a *Api) StreamerStatus(ctx *api.Context, _ api.EmptyReq) (*StreamerStatus, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	rr, err := a.om.RoomTopUser(ctx, ri.Id.Hex(), link.MaxLinkCount+link.MaxInviteCount+link.MaxRequestCount+10)
	if err != nil {
		return nil, err
	}

	cfg, err := a.mlm.GetUserConfig(uac.UserId)
	if err != nil {
		return nil, err
	}

	var (
		sid = ri.SessionId.Hex()
		out = &StreamerStatus{
			Allow:     cfg.Allow,
			Audiences: make([]InviteUser, 0, len(rr)),
		}
	)

	{
		rr, err := a.mlm.Requesters(ctx, sid, 20)
		if err != nil {
			return nil, err
		}

		out.Requesters = make([]ScoreUser, 0, len(rr))
		for _, userId := range rr {
			acc, err := a.ug.Account(ctx, userId)
			if err != nil {
				continue
			}

			iu := ScoreUser{
				User: User{
					UserWithExt: *mixer.UserWithExt(ctx, acc),
					FollowState: *mixer.FollowState(ctx, uac.UserId, userId),
				},
			}

			if c, _ := a.sm.SessionUserContrib(ctx, sid, userId); c != nil {
				iu.Score = int64(c.Score)
			}

			out.Requesters = append(out.Requesters, iu)
		}
	}

	invited, err := a.mlm.Invitees(ctx, sid, link.MaxInviteCount+1)
	if err != nil {
		return nil, err
	}

	linked, err := a.mlm.Linked(ctx, sid)
	if err != nil {
		return nil, err
	}

	for _, r := range rr {
		if r.UserId == uac.UserId {
			continue
		}

		if slices.ContainsFunc(out.Requesters, func(micUser ScoreUser) bool {
			return micUser.User.UserId == r.UserId
		}) {
			continue
		}

		if slices.ContainsFunc(linked, func(u *link.Linked) bool {
			return u.UserId == r.UserId
		}) {
			continue
		}

		acc, err := a.ug.Account(ctx, r.UserId)
		if err != nil {
			continue
		} else if patrol.Has(acc.Roles) {
			continue
		}

		iu := InviteUser{
			ScoreUser: ScoreUser{
				User: User{
					UserWithExt: *mixer.UserWithExt(ctx, acc),
					FollowState: *mixer.FollowState(ctx, uac.UserId, r.UserId),
				},
				Score: r.Score,
			},
			Invited: slices.Contains(invited, r.UserId),
		}

		out.Audiences = append(out.Audiences, iu)
	}

	if len(out.Audiences) > 100 {
		out.Audiences = out.Audiences[:100]
	}

	if !link.Open() {
		out.Audiences = []InviteUser{}
		out.Requesters = []ScoreUser{}
	}

	return out, nil
}

type UpdateUserConfigRequest struct {
	Allow bool `json:"allow"` // 是否允许观众连线
}

// @tags 连线
// @summary 更新用户连线配置
// @description 更新用户连线配置
// @accept json
// @produce json
// @param req body UpdateUserConfigRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/streamer/config [post]
func (a *Api) UpdateUserConfig(ctx *api.Context, req UpdateUserConfigRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := a.mlm.SetUserConfig(ctx, uac.UserId, req.Allow); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type InviteRequest struct {
	UserId string `json:"userId" binding:"required"` // 用户ID
}

// @tags 连线
// @summary 邀请观众连线
// @description 邀请观众连线
// @accept json
// @produce json
// @param req body InviteRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/streamer/invite [post]
func (a *Api) Invite(ctx *api.Context, req InviteRequest) (*api.EmptyResp, error) {
	if err := upgrade(ctx); err != nil {
		return nil, err
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := a.PKingError(ctx, uac.UserId); err != nil {
		return nil, err
	}

	if uac.UserId == req.UserId {
		return nil, biz.NewError(biz.ErrUserInviteSelf, "user can't invite self")
	}

	if a.pg.Take(ctx, req.UserId).Valid() {
		return nil, biz.NewError(biz.ErrNoPermission, "no permission")
	}

	if err := a.mlm.Invite(ctx, uac.UserId, req.UserId); err != nil {
		if errors.Is(err, link.ErrLinkInvited) || errors.Is(err, link.ErrUserLinked) {
			return &api.EmptyResp{}, nil
		}
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type StreamerAcceptRequest struct {
	UserId string `json:"userId" binding:"required"` // 用户ID
}

// @tags 连线
// @summary 接受用户连线
// @description 接受用户连线
// @accept json
// @produce json
// @param req body StreamerAcceptRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/streamer/accept [post]
func (a *Api) StreamerAccept(ctx *api.Context, req StreamerAcceptRequest) (*api.EmptyResp, error) {
	if err := upgrade(ctx); err != nil {
		return nil, err
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := a.PKingError(ctx, uac.UserId); err != nil {
		return nil, err
	}

	if err := a.mlm.Accept(ctx, link.ModeRequest, uac.UserId, req.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type StreamerAckRequest struct {
	Token string `json:"token" binding:"required"` // 通知ID
}

// @tags 连线
// @summary 主播确认连线
// @description 主播确认连线
// @accept json
// @produce json
// @param req body StreamerAckRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/streamer/ack [post]
func (a *Api) StreamerAck(ctx *api.Context, req StreamerAckRequest) (*api.EmptyResp, error) {
	if err := upgrade(ctx); err != nil {
		return nil, err
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	t, err := link.DecodeToken(req.Token)
	if err != nil {
		return nil, err
	}

	if t.To != uac.UserId || t.SessionId != ri.SessionId.Hex() {
		return nil, biz.NewError(biz.ErrLinkInvalidToken, "invalid token")
	}

	if err := a.PKingError(ctx, uac.UserId); err != nil {
		return nil, err
	}

	if err := a.mlm.Ready(ctx, req.Token, app.DeviceId(ctx)); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type UserAcceptRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
}

// @tags 连线
// @summary 接受主播邀请
// @description 接受主播邀请
// @accept json
// @produce json
// @param req body UserAcceptRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/accept [post]
func (a *Api) UserAccept(ctx *api.Context, req UserAcceptRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.PKingError(ctx, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.mlm.Accept(ctx, link.ModeInvite, ri.UserId, uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type UserRejectRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
}

// @tags 连线
// @summary 拒绝主播邀请
// @description 拒绝主播邀请
// @accept json
// @produce json
// @param req body UserRejectRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/reject [post]
func (a *Api) UserReject(ctx *api.Context, req UserRejectRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.mlm.UserReject(ctx, ri.SessionId.Hex(), uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type MuteRequest struct {
	UserId string `json:"userId" binding:"required"` // 用户ID
	Mute   bool   `json:"mute"`                      // 是否禁音
}

// @tags 连线
// @summary 😈禁音/解除禁音，同步给客户端
// @description 禁音/解除禁音
// @accept json
// @produce json
// @param req body MuteRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/streamer/mute [post]
func (a *Api) StreamerMute(ctx *api.Context, req MuteRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	if err := a.mlm.StreamerMute(ctx, ri.SessionId.Hex(), req.UserId, req.Mute); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type UnlinkRequest struct {
	UserId string `json:"userId" binding:"required"` // 用户ID
}

// @tags 连线
// @summary 结束和连麦者的连线
// @description 结束和连麦者的连线
// @accept json
// @produce json
// @param req body UnlinkRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/streamer/unlink [post]
func (a *Api) StreamerUnlink(ctx *api.Context, req UnlinkRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	if err := a.mlm.Unlink(ctx, ri.SessionId.Hex(), req.UserId, false); err != nil {
		if !errors.Is(err, link.ErrNotLinked) {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}

type MyStatusResponse struct {
	Requested  bool                            `json:"requested"`  // 是否已申请连线
	User       ScoreUserT[types.UserWithExt]   `json:"user"`       // 用户信息
	Requesters []ScoreUserT[types.UserWithExt] `json:"requesters"` // 申请连线的用户列表
}

type UserStatusRequest struct {
	RoomId string `json:"roomId" form:"roomId" binding:"required"` // 房间ID
}

// @tags 连线
// @summary 观众自己的连线状态
// @description 观众自己的连线状态
// @accept json
// @produce json
// @param req body UserStatusRequest true "请求参数"
// @success 200 {object} MyStatusResponse
// @router /api/v1/link/user/status [get]
func (a *Api) UserStatus(ctx *api.Context, req UserStatusRequest) (*MyStatusResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	sid := ri.SessionId.Hex()

	requesters, err := a.mlm.Requesters(ctx, sid, 20)
	if err != nil {
		return nil, err
	}

	out := &MyStatusResponse{
		Requested: slices.Contains(requesters, uac.UserId),
		User: ScoreUserT[types.UserWithExt]{
			User: *mixer.UserWithExt(ctx, uac),
		},
		Requesters: make([]ScoreUserT[types.UserWithExt], 0, len(requesters)),
	}

	if score, err := a.sm.SessionUserContrib(ctx, sid, uac.UserId); err == nil {
		out.User.Score = int64(score.Score)
	}

	for _, userId := range requesters {
		if userId == uac.UserId {
			continue
		}

		acc, err := a.ug.Account(ctx, userId)
		if err != nil {
			continue
		}

		su := ScoreUserT[types.UserWithExt]{
			User: *mixer.UserWithExt(ctx, acc),
		}

		if c, _ := a.sm.SessionUserContrib(ctx, sid, userId); c != nil {
			su.Score = int64(c.Score)
		}

		out.Requesters = append(out.Requesters, su)
	}

	return out, nil
}

type ApplyRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
}

// @tags 连线
// @summary 申请连线
// @description 申请连线
// @accept json
// @produce json
// @param req body ApplyRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/request [post]
func (a *Api) Apply(ctx *api.Context, req ApplyRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if a.pg.Take(ctx, uac.UserId).Valid() {
		return nil, biz.NewError(biz.ErrNoPermission, "no permission")
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if uac.UserId == ri.UserId {
		return nil, biz.NewError(biz.ErrUserInviteSelf, "user can't request self")
	}

	cfg, err := a.mlm.GetUserConfig(ri.UserId)
	if err != nil {
		return nil, err
	}

	if !cfg.Allow {
		return nil, biz.NewError(biz.ErrLinkNotAllow, "The host has not activated the voice chat")
	}

	if err := a.PKingError(ctx, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.mlm.Request(ctx, ri.SessionId.Hex(), uac.UserId); err != nil {
		if errors.Is(err, link.ErrLinkRequested) || errors.Is(err, link.ErrUserLinked) {
			return &api.EmptyResp{}, nil
		}
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type CancelRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
}

// @tags 连线
// @summary 取消连线
// @description 取消连线
// @accept json
// @produce json
// @param req body CancelRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/cancel [post]

func (a *Api) Cancel(ctx *api.Context, req CancelRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.mlm.Cancel(ctx, ri.SessionId.Hex(), uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type UserMuteRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
	Mute   bool   `json:"mute"`                      // 是否禁音
}

// @tags 连线
// @summary 用户禁音
// @description 用户禁音
// @accept json
// @produce json
// @param req body UserMuteRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/mute [post]
func (a *Api) UserMute(ctx *api.Context, req UserMuteRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.mlm.UserMute(ctx, ri.SessionId.Hex(), uac.UserId, req.Mute); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type UserUnlinkRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
}

// @tags 连线
// @summary 结束连线
// @description 结束连线
// @accept json
// @produce json
// @param req body UserUnlinkRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/unlink [post]
func (a *Api) UserUnlink(ctx *api.Context, req UserUnlinkRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.mlm.Unlink(ctx, ri.SessionId.Hex(), uac.UserId, true); err != nil {
		if !errors.Is(err, link.ErrNotLinked) {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}

type UserAckRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
	Token  string `json:"token" binding:"required"`  // 通知ID
}

// @tags 连线
// @summary 用户确认
// @description 用户确认
// @accept json
// @produce json
// @param req body UserAckRequest true "请求参数"
// @success 200 {object} api.EmptyResp
// @router /api/v1/link/user/ack [post]
func (a *Api) UserAck(ctx *api.Context, req UserAckRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	t, err := link.DecodeToken(req.Token)
	if err != nil {
		return nil, err
	}

	if t.To != uac.UserId || t.SessionId != ri.SessionId.Hex() {
		return nil, biz.NewError(biz.ErrLinkInvalidToken, "invalid token")
	}

	if err := a.PKingError(ctx, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.mlm.Ready(ctx, req.Token, app.DeviceId(ctx)); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type ListUserScoredRequest struct {
	RoomId string `form:"roomId" binding:"required"` // 房间ID
	UserId string `form:"userId" binding:"required"` // 目标用户ID
}

type ListUserScoredItem struct {
	User  types.User `json:"user"`
	Rank  int64      `json:"rank"`  // 排名, -1 表示未上榜(客户端显示 - )
	Score int64      `json:"score"` // 贡献值
}

type ListUserScoredResponse struct {
	List []*ListUserScoredItem `json:"list"`
	Mine *ListUserScoredItem   `json:"mine"`
}

// @tags 连线
// @summary 上麦用户昵称的贡献榜
// @description 上麦用户昵称的贡献榜
// @accept json
// @produce json
// @param req body ListUserScoredRequest true "请求参数"
// @success 200 {object} ListUserScoredResponse
// @router /api/v1/link/user/scored [get]
func (a *Api) ListUserScored(ctx *api.Context, req ListUserScoredRequest) (*ListUserScoredResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	const limit = 20

	ranks, err := a.mlm.ListUserScored(ctx, ri.SessionId.Hex(), req.UserId, limit)
	if err != nil {
		return nil, err
	}

	out := &ListUserScoredResponse{
		List: make([]*ListUserScoredItem, 0, len(ranks)),
	}

	for _, rank := range ranks {
		acc, err := a.ug.Account(ctx, rank.UserId)
		if err != nil {
			continue
		}

		itm := &ListUserScoredItem{
			User:  *mixer.User(ctx, acc),
			Rank:  rank.Rank,
			Score: rank.Score,
		}

		if rank.UserId == uac.UserId {
			out.Mine = itm
		}

		out.List = append(out.List, itm)
	}

	if out.Mine != nil {
		return out, nil
	}

	unscored := func() *ListUserScoredItem {
		return &ListUserScoredItem{
			User:  *mixer.User(ctx, uac),
			Rank:  -1,
			Score: 0,
		}
	}

	if len(out.List) < limit {
		out.Mine = unscored()
		return out, nil
	}

	rank, err := a.mlm.UserScoreToTarget(ctx, ri.SessionId.Hex(), uac.UserId)
	if err != nil {
		return nil, err
	}

	if rank == nil {
		out.Mine = unscored()
	} else {
		out.Mine = &ListUserScoredItem{
			User:  *mixer.User(ctx, uac),
			Rank:  rank.Rank,
			Score: rank.Score,
		}
	}

	return out, nil
}
