package link

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/online"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Invoke(r *api.Router,
	mlm *link.Manager,
	om *online.Manager,
	lm *live.Manager,
	pkm *pk.Manager,
	sm *rsd.Stats,
	ug user.Getter,
	pg patrol.Getter,
) {
	apis := &Api{
		mlm: mlm,
		om:  om,
		lm:  lm,
		pkm: pkm,
		sm:  sm,
		ug:  ug,
		pg:  pg,
	}

	ar := r.With<PERSON>()
	{
		steamer := ar.Group("/link/streamer")
		steamer.GET("/status", api.Generic(apis.StreamerStatus))
		steamer.POST("/config", api.Generic(apis.UpdateUserConfig))
		steamer.POST("/invite", api.Generic(apis.Invite))
		steamer.POST("/accept", api.Generic(apis.StreamerAccept))
		steamer.POST("/mute", api.Generic(apis.StreamerMute))
		steamer.POST("/unlink", api.Generic(apis.StreamerUnlink))
		steamer.POST("/ack", api.Generic(apis.StreamerAck))

		user := ar.Group("/link/user")
		user.POST("/accept", api.Generic(apis.UserAccept))
		user.POST("/reject", api.Generic(apis.UserReject))
		user.GET("/status", api.Generic(apis.UserStatus))
		user.POST("/request", api.Generic(apis.Apply))
		user.POST("/cancel", api.Generic(apis.Cancel))
		user.POST("/mute", api.Generic(apis.UserMute))
		user.POST("/unlink", api.Generic(apis.UserUnlink))
		user.POST("/ack", api.Generic(apis.UserAck))
		user.GET("/scored", api.Generic(apis.ListUserScored))
	}
}
