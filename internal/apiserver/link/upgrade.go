package link

import (
	"github.com/hashicorp/go-version"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func linkOpen(devType string, cv *version.Version, bid int64) bool {
	switch devType {
	case app.Android:
		return cv.GreaterThanOrEqual(version.Must(version.NewVersion("1.9.0-0")))
	case app.IOS:
		return cv.Equal(app.V100) && bid >= 52
	}

	return false
}

func upgrade(ctx *api.Context) error {
	var (
		devType = app.DeviceType(ctx)
		cv      = app.Version(ctx)
		bid     = app.MustBuildId(ctx)
	)

	if !linkOpen(devType, cv, bid) {
		return biz.Legacy("please upgrade to the latest version")
	}

	return nil
}
