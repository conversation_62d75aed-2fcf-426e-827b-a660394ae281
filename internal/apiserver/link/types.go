package link

import "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"

type PagingListRequest struct {
	Cursor string `json:"cursor"` // 分页游标
}

type User struct {
	types.UserWithExt
	types.FollowState
}

type ScoreUser = ScoreUserT[User]

type ScoreUserT[T any] struct {
	User  T     `json:"user"`  // 用户信息
	Score int64 `json:"score"` // 贡献值
}

type InviteUser struct {
	ScoreUser
	Invited bool `json:"invited"` // 是否已邀请
}

type UserListResponse[T any] struct {
	Cursor string `json:"cursor"` // 分页游标
	List   []T    `json:"list"`   // 申请连线用户列表
}
