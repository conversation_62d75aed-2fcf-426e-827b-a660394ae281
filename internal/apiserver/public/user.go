package public

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type UserInfoRequest struct {
	ShowId string `form:"showId"` // 用户自定义ID
}

type UserInfoResponse struct {
	UserId    string `json:"userId"`    // 用户ID
	ShowId    string `json:"showId"`    // 用户自定义ID
	Nickname  string `json:"nickname"`  // 用户昵称
	Avatar    string `json:"avatar"`    // 用户头像
	Level     int    `json:"level"`     // 观众等级
	Signature string `json:"signature"` // 个性签名
	BirthDays int    `json:"birthDays"` // 加入天数
	types.UserSocial
}

// @Tags 公开接口
// @Summary 获取用户信息
// @Description 获取用户信息
// @Produce json
// @Param param query UserInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=UserInfoResponse}
// @Router /api/v1/public/user/info [get]
func (s *apis) UserInfo(ctx *api.Context, req UserInfoRequest) (*UserInfoResponse, error) {
	uac, err := s.ug.GetByShowId(ctx, req.ShowId)
	if err != nil {
		return nil, err
	}
	return &UserInfoResponse{
		UserId:     uac.UserId,
		ShowId:     uac.ShowId,
		Nickname:   uac.Nickname,
		Avatar:     uac.Avatar,
		Level:      uac.Level,
		Signature:  uac.Signature,
		BirthDays:  int(time.Since(uac.CreatedAt).Hours() / 24),
		UserSocial: *mixer.FollowInfo(ctx, uac.UserId),
	}, nil
}
