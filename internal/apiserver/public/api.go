package public

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/payermax"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/recharge"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlimit"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func API(
	r *api.Router, rl *rlimit.Handler,
	ug user.Getter, rc *recharge.API, pm *payermax.API,
) {
	s := &apis{ug: ug, rc: rc, pm: pm}
	rr := r.With(rl.Middleware())
	{
		// user
		rr.GET("/public/user/info", api.Generic(s.UserInfo))
		// recharge
		rr.GET("/public/recharge/diamond", api.Generic(s.RechargeDiamond))
		// payermax
		rr.GET("/public/payermax/country", api.Generic(s.PayermaxCountry))
		rr.GET("/public/payermax/methods", api.Generic(s.PayermaxMethods))
		rr.POST("/public/payermax/order", api.Generic(s.PayermaxOrder))
		rr.POST("/public/payermax/query", api.Generic(s.PayermaxQuery))
	}
}

type apis struct {
	ug user.Getter
	rc *recharge.API
	pm *payermax.API
}
