package public

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type PayermaxCountryRequest struct {
	UserId string `form:"userId"` // 指定用户Id
	api.EmptyReq
}

// @Tags 公开接口
// @Summary 获取支付国家列表
// @Description 获取payermax可用的国家列表
// @Produce json
// @Success 200 {object} codec.Response{data=payermax.CountryResponse}
// @Router /api/v1/public/payermax/country [get]
func (s *apis) PayermaxCountry(ctx *api.Context, req PayermaxCountryRequest) (*payermax.CountryResponse, error) {
	return proxy(ctx, s.ug, req.UserId, req.EmptyReq, s.pm.Country)
}

type PayermaxMethodsRequest struct {
	UserId string `form:"userId"` // 指定用户Id
	payermax.MethodsRequest
}

// @Tags 公开接口
// @Summary 获取支付机构列表
// @Description 获取payermax可用的收单机构列表
// @Produce json
// @Param param query PayermaxMethodsRequest true "请求参数"
// @Success 200 {object} codec.Response{data=payermax.MethodsResponse}
// @Router /api/v1/public/payermax/methods [get]
func (s *apis) PayermaxMethods(ctx *api.Context, req PayermaxMethodsRequest) (*payermax.MethodsResponse, error) {
	return proxy(ctx, s.ug, req.UserId, req.MethodsRequest, s.pm.Methods)
}

type PayermaxCreateOrderRequest struct {
	UserId string `json:"userId" binding:"required"` // 指定用户Id
	payermax.CreateOrderRequest
}

// @Tags 公开接口
// @Summary 创建用户订单
// @Description 创建用户订单
// @Produce json
// @Param param body PayermaxCreateOrderRequest true "请求参数"
// @Success 200 {object} codec.Response{data=payermax.CreateOrderResponse}
// @Router /api/v1/public/payermax/order [post]
func (s *apis) PayermaxOrder(ctx *api.Context, req PayermaxCreateOrderRequest) (*payermax.CreateOrderResponse, error) {
	return proxy(ctx, s.ug, req.UserId, req.CreateOrderRequest, s.pm.CreateOrder)
}

type PayermaxQueryOrderRequest struct {
	UserId string `json:"userId" binding:"required"` // 指定用户Id
	payermax.QueryOrderRequest
}

// @Tags 公开接口
// @Summary 查询用户订单
// @Description 查询用户订单
// @Produce json
// @Param param body PayermaxQueryOrderRequest true "请求参数"
// @Success 200 {object} codec.Response{data=payermax.QueryOrderResponse}
// @Router /api/v1/public/payermax/query [post]
func (s *apis) PayermaxQuery(ctx *api.Context, req PayermaxQueryOrderRequest) (*payermax.QueryOrderResponse, error) {
	return proxy(ctx, s.ug, req.UserId, req.QueryOrderRequest, s.pm.QueryOrder)
}
