package public

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func proxy[REQ, RESP any](ctx *api.Context, ug user.Getter, asUser string, req REQ, api func(*api.Context, REQ) (RESP, error)) (RESP, error) {
	var resp RESP
	uac, err := ug.Account(ctx, asUser)
	if err != nil {
		return resp, err
	}
	ctx.Set(auth.ContextUser, uac)
	return api(ctx, req)
}
