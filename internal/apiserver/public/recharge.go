package public

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/recharge"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type RechargeConfigRequest struct {
	UserId string `form:"userId"` // 指定用户Id
	recharge.ConfigRequest
}

// @Tags 公开接口
// @Summary 获取钻石充值配置
// @Description 获取钻石充值配置
// @Produce json
// @Param param query RechargeConfigRequest true "请求参数"
// @Success 200 {object} codec.Response{data=recharge.ConfigResponse}
// @Router /api/v1/public/recharge/diamond [get]
func (s *apis) RechargeDiamond(ctx *api.Context, req RechargeConfigRequest) (*recharge.ConfigResponse, error) {
	if req.UserId == "" {
		return s.rc.GetConfig(ctx, "", req.Scene)
	}
	return proxy(ctx, s.ug, req.UserId, req.ConfigRequest, s.rc.Diamond)
}
