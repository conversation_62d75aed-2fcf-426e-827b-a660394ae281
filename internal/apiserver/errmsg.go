package apiserver

import (
	"context"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

func debugErrMsg(ctx context.Context, err error, code int, msg string) (int, string) {
	if msg == "" {
		msg = err.Error()
	}
	return i18nErrMsg(ctx, err, code, msg)
}

func i18nErrMsg(ctx context.Context, err error, code int, msg string) (int, string) {
	if code != biz.ErrSystem && msg != "" {
		msg = i3n.T(ctx, msg)
	}
	return code, msg
}
