package cert

import (
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/adm"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Invoke(r *api.Router, ug user.Getter, sg seller.Getter, adm *adm.Manager) {
	s := &apis{ug: ug, sg: sg, adm: adm}
	ar := r.WithAuth()
	{
		ar.GET("/certificate/view", api.Generic(s.view))
	}
}

type apis struct {
	ug  user.Getter
	sg  seller.Getter
	adm *adm.Manager
}

type certReq struct {
	UserId string    `form:"userId"`
	Role   user.Role `form:"role"` // 查看身份认证
}

type certResp struct {
	User *types.UserWithExt `json:"user"` // 用户信息（客户端重新校验下身份）
	Desc string             `json:"desc"` // 描述信息（身份不存在时为空）
	Time int64              `json:"time"` // 认证时间（为0时需隐藏）
}

// @Tags 身份认证
// @Summary 身份认证
// @Description 获取用户的身份认证信息
// @Produce json
// @Security HeaderAuth
// @Param param query certReq true "请求参数"
// @Success 200 {object} codec.Response{data=certResp}
// @Router /api/v1/certificate/view [get]
func (s *apis) view(ctx *api.Context, req certReq) (*certResp, error) {
	acc, err := s.ug.Account(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	var (
		desc string
		time int64
	)

	switch {
	case req.Role.Has(user.RoleSeller):
		if sp := s.sg.Take(ctx, req.UserId); sp.Valid() {
			desc = i3n.T(ctx, "cert desc of seller")
			time = sp.CreatedAt.Unix()
		}
	case req.Role.Has(user.RoleADM):
		if aa, _ := s.adm.GetAdmInfo(ctx, req.UserId); aa != nil {
			desc = i3n.T(ctx, "cert desc of adm")
			time = aa.CreatedAt.Unix()
		}
	case req.Role.Has(user.RoleOfficial):
		desc = i3n.T(ctx, "cert desc of official")
	}

	return &certResp{
		User: mixer.UserWithExt(ctx, acc),
		Desc: desc,
		Time: time,
	}, nil
}
