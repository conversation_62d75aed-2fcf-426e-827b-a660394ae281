package handler

import (
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/banner"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
	"gitlab.sskjz.com/overseas/live/osl/internal/shortener"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlimit"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func InvokeEntranceHandler(
	g *gin.Engine,
	r *api.Router,
	vnd log.Vendor,
	bm *banner.Manager,
	lm *live.Manager,
	mm *moment.Manager,
	ug user.Getter,
	sm *shortener.Manager,
	rl *rlimit.Handler,
	desc *conf.Setting,
) *Entrance {
	h := NewEntrance(vnd.Scope("api.entrance"), bm, lm, mm, ug, sm, rl, desc.Share)

	ar := r.WithAuth()
	{
		ar.GET("/entrance/banner", api.Generic(h.Banner))
		ar.GET("/entrance/share", api.Generic(h.Share))
	}

	// 解析分享文本
	r.GET("/entrance/share/parse", api.Generic(h.ShareParse))

	r.With(rl.Middleware())
	{
		// 短链接生成
		r.POST("/entrance/shorten", api.Generic(h.Shorten))
	}

	// 短链接重定向
	g.GET("/:shortId", api.Request(h.ShareRedirect))

	return h
}

type Entrance struct {
	logger *zap.Logger
	bm     *banner.Manager
	lm     *live.Manager
	mm     *moment.Manager
	ug     user.Getter
	sm     *shortener.Manager
	rl     *rlimit.Handler
	share  conf.Share
}

func NewEntrance(
	logger *zap.Logger,
	bm *banner.Manager,
	lm *live.Manager,
	mm *moment.Manager,
	ug user.Getter,
	sm *shortener.Manager,
	rl *rlimit.Handler,
	share conf.Share,
) *Entrance {
	return &Entrance{
		logger: logger,
		bm:     bm,
		lm:     lm,
		mm:     mm,
		ug:     ug,
		sm:     sm,
		rl:     rl,
		share:  share,
	}
}

// @Tags 入口
// @Summary 生成短链接
// @Description 生成短链接
// @Produce json
// @Security HeaderAuth
// @Param param body types.EntranceShortenRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.EntranceShortenResponse}
// @Router /api/v1/entrance/shorten [post]
func (h *Entrance) Shorten(ctx *api.Context, req types.EntranceShortenRequest) (*types.EntranceShortenResponse, error) {
	longUrl := req.LongUrl

	u, err := url.ParseRequestURI(longUrl)

	if err != nil || u.Scheme == "" || u.Host == "" {
		return nil, biz.NewError(biz.ErrInvalidParam, "longUrl invalid")
	}

	shortUrl, err := h.sm.ShortenUrl(ctx, longUrl)

	if err != nil {
		return nil, err
	}

	return &types.EntranceShortenResponse{
		ShortUrl: shortUrl,
	}, nil
}

// @Tags 入口
// @Summary ✅各场景banner列表
// @Description 各场景所有banner列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.EntranceBannerRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.EntranceBannerResponse}
// @Router /api/v1/entrance/banner [get]
func (h *Entrance) Banner(ctx *api.Context, req types.EntranceBannerRequest) (*types.EntranceBannerResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	scene := req.Scene

	if scene == "" {
		scene = banner.SceneLiveRoom
	}

	logger := h.logger.With(
		zap.String("scene", scene),
	)

	res, err := h.bm.List(ctx, app.DeviceType(ctx), scene, uac.UserId)

	if err != nil {
		logger.Error("获取banner失败", zap.Error(err))

		return nil, err
	}

	list := make([]types.Banner, 0, len(res))

	for _, v := range res {
		list = append(list, types.Banner{
			Title:    v.Title,
			ImageUrl: v.ImageUrl,
			LinkUrl:  v.LinkUrl,
		})
	}

	return &types.EntranceBannerResponse{
		List: list,
	}, nil
}

// @Tags 入口
// @Summary 分享
// @Description 分享
// @Produce json
// @Security HeaderAuth
// @Param param query types.EntranceShareRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.EntranceShareResponse}
// @Router /api/v1/entrance/share [get]
func (h *Entrance) Share(ctx *api.Context, req types.EntranceShareRequest) (*types.EntranceShareResponse, error) {
	userId := ""
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	var title string
	var shareUrl string
	var thumb string

	// 传入的参数都自动拼接到分享链接上
	uv := ctx.Request.URL.Query()
	// 追加的参数
	uv.Set("fuid", userId)

	switch req.Type {
	case "live":
		roomId := req.Id

		room, err := h.lm.Room(roomId)

		if err != nil {
			return nil, biz.NewError(biz.ErrInvalidParam, "room not found")
		}

		acc, err := h.ug.Account(ctx, room.UserId)

		if err != nil {
			return nil, biz.NewError(biz.ErrInvalidParam, "user not found")
		}

		// template := "我正在#KakoLive观看【%s】的精彩直播，来和我一起支持Ta吧。复制链接打开【Kako Live】，直接观看直播！"
		template := "Estou assistindo uma ótima transmissão ao vivo de [%s] no #KakoLive, venham apoiar comigo! Copie o link para abrir o [Kako Live] e assistir diretamente à transmissão ao vivo!"

		title = fmt.Sprintf(
			template,
			acc.Nickname,
		)
		shareUrl = fmt.Sprintf("%s/app/gzl_live.html", h.share.Host)
		thumb = acc.Avatar
	case "moment":
		id, err := strconv.ParseInt(req.Id, 10, 64)

		if err != nil {
			return nil, biz.NewError(biz.ErrInvalidParam, "moment id invalid")
		}

		moment, err := h.mm.MomentGet(ctx, uint(id), userId)

		if err != nil {
			return nil, biz.NewError(biz.ErrInvalidParam, "moment not found")
		}

		acc, err := h.ug.Account(ctx, moment.Author.UserId)

		if err != nil {
			return nil, biz.NewError(biz.ErrInvalidParam, "user not found")
		}

		// template := "复制打开【Kako Live】，看看【%s】的作品%s"
		template := "Copie e abra [Kako Live] para ver a publicação de [%s]!%s"

		title = fmt.Sprintf(
			template,
			acc.Nickname,
			strings.TrimSpace(moment.Title),
		)
		shareUrl = fmt.Sprintf("%s/app/gzl_feed.html", h.share.Host)
		thumb = acc.Avatar
	default:
		return nil, biz.NewError(biz.ErrInvalidParam, "type invalid")
	}

	shareUrl = fmt.Sprintf("%s?%s", shareUrl, uv.Encode())

	shortUrl, err := h.sm.ShortenUrl(ctx, shareUrl)

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "shorten url failed")
	}

	return &types.EntranceShareResponse{
		Title: fmt.Sprintf("%s %s", title, shortUrl),
		Url:   "",
		Thumb: thumb,
	}, nil
}

// @Tags 入口
// @Summary 解析分享文本
// @Description 解析分享文本
// @Produce json
// @Param param query types.EntranceShareParseRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.EntranceShareParseResponse}
// @Router /api/v1/entrance/share/parse [get]
func (h *Entrance) ShareParse(ctx *api.Context, req types.EntranceShareParseRequest) (*types.EntranceShareParseResponse, error) {
	text := req.Text

	re := regexp.MustCompile(`(https?://[^\s]+)`)

	urls := re.FindAllString(text, -1)

	if len(urls) == 0 {
		return nil, biz.NewError(biz.ErrInvalidParam, "no url found")
	}

	ud, err := url.Parse(urls[0])

	if err != nil {
		return nil, biz.NewError(biz.ErrInvalidParam, "url parse failed")
	}

	paths := strings.Split(ud.Path, "/")

	shortId := paths[len(paths)-1]

	long, err := h.sm.ResolveShortUrl(ctx, shortId)

	if err != nil {
		return nil, biz.NewError(biz.ErrInvalidParam, "short url resolve failed")
	}

	lu, err := url.Parse(long)

	if err != nil {
		return nil, biz.NewError(biz.ErrInvalidParam, "url parse failed")
	}

	uv := lu.Query()

	id := uv.Get("id")
	typ := uv.Get("type")

	return &types.EntranceShareParseResponse{
		Url:  long,
		Id:   id,
		Type: typ,
	}, nil
}

// 短链接重定向
func (h *Entrance) ShareRedirect(ctx *api.Context, _ api.EmptyReq) error {
	shortId := ctx.Param("shortId")

	longUrl, err := h.sm.ResolveShortUrl(ctx, shortId)

	if err != nil {
		return err
	}

	ctx.Redirect(302, longUrl)

	return nil
}
