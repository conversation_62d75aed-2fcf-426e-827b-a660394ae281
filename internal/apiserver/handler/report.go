package handler

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	"gitlab.sskjz.com/overseas/live/osl/internal/report"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
	"time"
)

type Report struct {
	rm     *report.Manager
	urmm   *urm.Manager
	logger *zap.Logger
}

func NewReport(rm *report.Manager, urmm *urm.Manager, logger *zap.Logger) *Report {
	return &Report{
		rm:     rm,
		urmm:   urmm,
		logger: logger,
	}
}

func InvokeReportHandler(
	rm *report.Manager,
	urmm *urm.Manager,
	us *upload.Scenes,
	r *api.Router,
	vnd log.Vendor,
) *Report {
	a := NewReport(rm, urmm, vnd.Scope("api.report"))

	ar := r.WithAuth()
	{
		// 获取举报信息
		ar.GET("/report/type", api.Generic(a.ReportType))

		// 举报提交
		ar.POST("/report/commit", api.Generic(a.ReportCommit))
	}

	us.Add("report", us.Classic("report", "1000x0"))

	return a
}

// @Tags 举报
// @Summary 举报信息获取
// @Description 举报信息获取
// @Produce json
// @Security HeaderAuth
// @Param param query types.ReportTypeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ReportTypeResponse}
// @Router /api/v1/report/type [get]
func (r *Report) ReportType(ctx *api.Context, req types.ReportTypeRequest) (*types.ReportTypeResponse, error) {
	return &types.ReportTypeResponse{
		List: r.rm.ReportInfo(ctx, req.ReportTarget),
	}, nil
}

// @Tags 举报
// @Summary 提交举报
// @Description 提交举报
// @Produce json
// @Security HeaderAuth
// @Param param body types.ReportCommitRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ReportCommitResponse}
// @Router /api/v1/report/commit [post]
func (r *Report) ReportCommit(ctx *api.Context, req types.ReportCommitRequest) (*types.ReportCommitResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 不能举报自己
	if req.ReportTargetUserId == uac.UserId {
		return nil, biz.NewError(biz.ErrNoPermission, "no permission")
	}

	if err := r.rm.ReportCommit(ctx, &report.ReportData{
		UserId:             uac.UserId,
		ReportTarget:       req.ReportTarget,
		ReportTargetId:     req.ReportTargetId,
		ReportTargetUserId: req.ReportTargetUserId,
		ReportTypeId:       req.ReportTypeId,
		Desc:               req.Desc,
		Images:             req.Images,
	}); err != nil {
		return nil, err
	}

	if req.IsBlockUser {
		if blocked, err := r.urmm.IsBlockTo(ctx, uac.UserId, req.ReportTargetUserId); err == nil && !blocked {
			r.urmm.AddList(
				ctx,
				urm.GrantKindBlacklist,
				uac.UserId, uac.UserId,
				req.ReportTargetUserId,
				"",
				time.Now())
		}
	}

	return &types.ReportCommitResponse{}, nil
}
