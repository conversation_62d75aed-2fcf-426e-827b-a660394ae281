package handler

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/open"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"go.uber.org/zap"
)

// 开放接口
func InvokeOpenHandler(
	r *api.Router,
	aj *auth.JWT,
	om *open.Manager,
	fom *order.Manager,
	vnd log.Vendor,
) *Open {
	h := NewOpen(aj, om, fom, vnd.Scope("api.open"))

	r.POST("/open/session", api.Generic(h.Session))
	r.POST("/open/diamond/balance", api.Generic(h.DiamondBalance))
	r.POST("/open/diamond/update", api.Generic(h.DiamondUpdate))
	r.POST("/open/order/query", api.Generic(h.OrderQuery))

	return h
}

type Open struct {
	aj     *auth.JWT
	om     *open.Manager
	fom    *order.Manager
	logger *zap.Logger
}

func NewOpen(
	aj *auth.JWT,
	om *open.Manager,
	fom *order.Manager,
	logger *zap.Logger,
) *Open {
	return &Open{
		aj:     aj,
		om:     om,
		fom:    fom,
		logger: logger,
	}
}

// @Tags 开放接口
// @Summary 用户信息
// @Description 用户信息
// @Produce json
// @Security HeaderAuth
// @Param param body types.OpenSessionRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.OpenSessionResponse}
// @Router /api/v1/open/session [post]
func (h *Open) Session(ctx *api.Context, req types.OpenSessionRequest) (*types.OpenSessionResponse, error) {
	appId := req.AppId
	token := req.Token

	_, err := h.om.GetApp(appId)

	if err != nil {
		return nil, err
	}

	acc, err := h.aj.ParseToken(ctx, token)

	if err != nil {
		return nil, err
	}

	return &types.OpenSessionResponse{
		OpenId:   acc.UserId,
		Nickname: acc.Nickname,
	}, nil
}

// @Tags 开放接口
// @Summary 查询余额
// @Description 查询余额
// @Produce json
// @Security HeaderAuth
// @Param param body types.OpenDiamondBalanceRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.OpenDiamondBalanceResponse}
// @Router /api/v1/open/diamond/balance [post]
func (h *Open) DiamondBalance(ctx *api.Context, req types.OpenDiamondBalanceRequest) (*types.OpenDiamondBalanceResponse, error) {
	appId := req.AppId
	openId := req.OpenId

	_, err := h.om.GetApp(appId)

	if err != nil {
		return nil, err
	}

	b, err := h.fom.Balance(ctx, openId, fund.PTypeDiamond)

	if err != nil {
		return nil, err
	}

	return &types.OpenDiamondBalanceResponse{
		Diamond: b.IntPart(),
	}, nil
}

// @Tags 开放接口
// @Summary 增减币
// @Description 增减币
// @Produce json
// @Security HeaderAuth
// @Param param body types.OpenDiamondUpdateRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.OpenDiamondUpdateResponse}
// @Router /api/v1/open/diamond/update [post]
func (h *Open) DiamondUpdate(ctx *api.Context, req types.OpenDiamondUpdateRequest) (*types.OpenDiamondUpdateResponse, error) {
	appId := req.AppId
	openId := req.OpenId
	diamond := req.Diamond
	operate := req.Operate
	orderId := req.OrderId

	_, err := h.om.GetApp(appId)

	if err != nil {
		return nil, err
	}

	logger := h.logger.With(
		zap.String("appId", appId),
		zap.String("openId", openId),
		zap.Int64("diamond", diamond),
		zap.String("operate", operate),
		zap.String("orderId", orderId),
	)

	switch operate {
	case "add":
		err := h.fom.Income(ctx, appId, orderId, openId, fund.JTypeGameplay, fund.PTypeDiamond, diamond)

		if err != nil {
			logger.Error("开放接口增减币", zap.Error(err))

			return nil, err
		}
	case "subtract":
		err := h.fom.Expend(ctx, appId, orderId, openId, fund.JTypeGameplay, fund.PTypeDiamond, diamond)

		if err != nil {
			logger.Error("开放接口增减币", zap.Error(err))

			return nil, err
		}

	default:
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid operate")
	}

	b, err := h.fom.Balance(ctx, openId, fund.PTypeDiamond)

	if err != nil {
		logger.Error("开放接口增减币", zap.Error(err))

		return nil, err
	}

	logger.Info(
		"开放接口增减币",
		zap.Int64("balance", b.IntPart()),
	)

	return &types.OpenDiamondUpdateResponse{
		Status:  int(order.StatusSuccess),
		Diamond: b.IntPart(),
	}, nil
}

// @Tags 开放接口
// @Summary 查询订单
// @Description 查询订单
// @Produce json
// @Security HeaderAuth
// @Param param body types.OpenOrderQueryRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.OpenOrderQueryResponse}
// @Router /api/v1/open/order/query [post]
func (h *Open) OrderQuery(ctx *api.Context, req types.OpenOrderQueryRequest) (*types.OpenOrderQueryResponse, error) {
	appId := req.AppId
	orderId := req.OrderId

	order, err := h.fom.Query(ctx, appId, orderId)

	if err != nil {
		return nil, err
	}

	return &types.OpenOrderQueryResponse{
		Status: int(order.Status),
	}, nil
}
