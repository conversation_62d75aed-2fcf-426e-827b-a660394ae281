package handler

import (
	"context"

	"github.com/tidwall/gjson"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/warning"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/logging"
)

func initLiveWarningLog(wm *warning.Manager) {
	logging.Register("/live/warning/warn", logging.Handler{
		Desc:    "巡管操作：直播警告",
		Tags:    logging.Tags("patrol"),
		PreSave: logging.GuessTargetId,
		Render: logging.Renders(logging.GuessTargetView, func(ctx context.Context, log *logging.Log, r logging.Resolver) logging.View {
			out := make(logging.View, 0)
			if opt := wm.WarnOption(ctx, gjson.GetBytes(log.Raw, "id").String()); opt != nil {
				out.Join(logging.KV("原因", opt.Reason))
			}
			return out
		}),
	})
	logging.Register("/live/warning/ban", logging.Handler{
		Desc:    "巡管操作：中断直播",
		Tags:    logging.Tags("patrol"),
		PreSave: logging.GuessTargetId,
		Render: logging.Renders(logging.GuessTargetView, func(ctx context.Context, log *logging.Log, r logging.Resolver) logging.View {
			out := make(logging.View, 0)
			out.Join(logging.KV("封禁时长", gjson.GetBytes(log.Raw, "seconds").String()+"秒"))
			return out
		}),
	})
}
