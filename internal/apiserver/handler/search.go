package handler

import (
	"context"
	"slices"
	"sort"
	"strings"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/im/limiter"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/search"
	"gitlab.sskjz.com/overseas/live/osl/internal/ufind"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 搜索
func InvokeSearchHandler(
	r *api.Router,
	ug user.Getter,
	fg follow.Getter,
	lm *live.Manager,
	us *ufind.Indexer,
	sm *rsd.Stats,
	srm *search.Manager,
	ilm *limiter.Manager,
	urm *urm.Manager,
	vnd log.Vendor,
) *Search {
	h := &Search{
		ug:  ug,
		fg:  fg,
		lm:  lm,
		us:  us,
		sm:  sm,
		srm: srm,
		ilm: ilm,
		urm: urm,
		log: vnd.Scope("api.search"),
	}

	ar := r.WithAuth()
	{
		ar.GET("/search/general", api.Generic(h.General))
		ar.GET("/search/suggest/live", api.Generic(h.SuggestLive))
		ar.POST("/search/click", api.Generic(h.Click))
		ar.GET("/search/users", api.Generic(h.Users))
	}

	return h
}

type Search struct {
	ug  user.Getter
	fg  follow.Getter
	lm  *live.Manager
	us  *ufind.Indexer
	sm  *rsd.Stats
	srm *search.Manager
	ilm *limiter.Manager
	urm *urm.Manager
	log *zap.Logger
}

func NewSearch(ug user.Getter, fg follow.Getter, lm *live.Manager, us *ufind.Indexer, sm *rsd.Stats, srm *search.Manager, ilm *limiter.Manager, log *zap.Logger) *Search {
	return &Search{
		ug:  ug,
		fg:  fg,
		lm:  lm,
		us:  us,
		sm:  sm,
		srm: srm,
		ilm: ilm,
		log: log,
	}
}

// @Tags 搜索
// @Summary ✅综合查询
// @Description 综合查询。暂不支持分页，最多返回100条数据
// @Produce json
// @Security HeaderAuth
// @Param param query types.SearchGeneralRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.SearchGeneralResponse}
// @Router /api/v1/search/general [get]
func (h *Search) General(ctx *api.Context, req types.SearchGeneralRequest) (*types.SearchGeneralResponse, error) {
	res := &types.SearchGeneralResponse{
		List: make([]types.SearchItem, 0),
	}

	keyword := req.Keyword

	if keyword == "" {
		return res, nil
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userIds, err := h.us.Search(ctx, uac.UserId, keyword, 10)
	if err != nil {
		return nil, err
	}

	h.log.Info("search general", zap.String("keyword", keyword), zap.Strings("userIds", userIds))

	for _, userId := range userIds {
		room, err := h.lm.RoomByUserId2(ctx, userId)
		if err != nil {
			// !日志
			continue
		}

		tr, err := mixer.Room(ctx, uac.UserId, h.ug, h.fg, h.sm, room)

		if err != nil {
			continue
		}

		item := types.SearchItem{
			Type: 1,
			User: &types.SearchItemUser{
				UserWithExt: *tr.User,
				UserSocial:  *mixer.FollowInfo(ctx, userId),
				FollowState: *mixer.FollowState(ctx, uac.UserId, userId),
				Room:        tr,
			},
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

// @Tags 搜索
// @Summary ✅猜你想搜
// @Description 猜你想搜
// @Produce json
// @Security HeaderAuth
// @Param param query types.SearchSuggestLiveRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.SearchSuggestLiveResponse}
// @Router /api/v1/search/suggest/live [get]
func (h *Search) SuggestLive(ctx *api.Context, req types.SearchSuggestLiveRequest) (*types.SearchSuggestLiveResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	res := &types.SearchSuggestLiveResponse{
		List: make([]types.Room, 0),
	}

	eids := strings.Split(req.Eid, ",")
	count := 4

	// 所有正在直播的直播间
	rooms, err := h.lm.LivingRoom2(ctx)

	if err != nil {
		return nil, err
	}

	type roomScore struct {
		Room  *live.Room
		Score int64
	}

	tmp := make([]roomScore, 0)

	for _, room := range rooms {
		if lo.Contains(eids, room.Id.Hex()) {
			continue
		}

		tmp = append(tmp, roomScore{
			Room:  room,
			Score: h.srm.GetClickCount(ctx, room.UserId),
		})
	}

	// 按点击数降序
	sort.SliceStable(tmp, func(i, j int) bool {
		return tmp[i].Score > tmp[j].Score
	})

	// 取前count个
	for _, v := range tmp {
		if len(res.List) >= count {
			break
		}

		r, err := mixer.Room(ctx, uac.UserId, h.ug, h.fg, h.sm, v.Room)

		if err != nil {
			continue
		}

		res.List = append(res.List, *r)
	}

	return res, nil
}

// @Tags 搜索
// @Summary ✅上报点击搜索结果
// @Description 搜索列表展示给用户后，用户点击进入某个直播间，则需要调用此接口上报。（产品确认：猜你想搜是根据用户此处的点击行为返回结果）
// @Produce json
// @Security HeaderAuth
// @Param param body types.SearchClickRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/search/click [post]
func (h *Search) Click(ctx *api.Context, req types.SearchClickRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	h.srm.Click(ctx, uac.UserId, req.UserId)

	return &api.EmptyResp{}, nil
}

// @Tags 搜索
// @Summary ✅搜索用户
// @Description 搜索用户
// @Produce json
// @Security HeaderAuth
// @Param param query types.SearchUsersRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.SearchUsersResponse}
// @Router /api/v1/search/users [get]
func (h *Search) Users(ctx *api.Context, req types.SearchUsersRequest) (*types.SearchUsersResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var userIds []string
	if req.Keyword != "" {
		userIds, err = h.us.Search(ctx, uac.UserId, req.Keyword, 10)
		if err != nil {
			return nil, err
		}
	} else {
		switch req.Scene {
		case 0: // 0不限制
			userIds, err = h.fg.FriendIds(ctx, uac.UserId)
			if err != nil {
				return nil, err
			}
		case 1, 2: // 1动态 2直播间
			userIds, err = h.ilm.LastSentUsers(ctx, uac.UserId, 10)
			if err != nil {
				return nil, err
			}
			userIds = h.cleanBlockedUser(ctx, uac.UserId, userIds)
		case 3: // 3私信好友
			userIds, err = h.ilm.LastSentUsers(ctx, uac.UserId, 20)
			if err != nil {
				return nil, err
			}
			userIds = h.cleanBlockedUser(ctx, uac.UserId, userIds)
			for _, userId := range mixer.NoErr(h.fg.FriendIds(ctx, uac.UserId)) {
				if !slices.Contains(userIds, userId) {
					userIds = append(userIds, userId)
				}
			}
			for _, userId := range mixer.NoErr(h.fg.TargetIds(ctx, uac.UserId)) {
				if !slices.Contains(userIds, userId) {
					userIds = append(userIds, userId)
				}
			}
			if len(userIds) > 100 {
				userIds = userIds[:100]
			}
		}
	}

	var resp types.SearchUsersResponse
	for _, userId := range userIds {
		if acc, err := h.ug.Account(ctx, userId); err == nil {
			resp.List = append(resp.List, mixer.User(ctx, acc))
		}
	}

	return &resp, nil
}

func (h *Search) cleanBlockedUser(ctx context.Context, userId string, targetIds []string) []string {
	for i := 0; i < len(targetIds); i++ {
		targetId := targetIds[i]
		if r, _ := h.urm.UserBlockRelation(ctx, userId, targetId); !r.IsNone() {
			targetIds = slices.Delete(targetIds, i, i+1)
			i--
		}
	}
	return targetIds
}
