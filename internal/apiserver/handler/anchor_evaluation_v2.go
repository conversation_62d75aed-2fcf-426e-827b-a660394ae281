package handler

import (
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// @Tags 主播
// @Summary ✅新主播考核V2状态
// @Description 新主播考核V2状态
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.AnchorEvaluationV2StatusResponse}
// @Router /api/v1/anchor/evaluation/v2/status [get]
func (h *Anchor) EvaluationV2Status(ctx *api.Context, req api.EmptyReq) (*types.AnchorEvaluationV2StatusResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	e, err := h.am.GetEvaluation(ctx, userId)

	if err != nil {
		if err != anchor.ErrEvaluationNotFound {
			return nil, err
		}
	}

	// 默认值
	if e == nil {
		e = &anchor.AnchorEvaluation{}
	}

	phase := "document"

	if e.Phase != "" {
		phase = e.Phase
	}

	var document *types.AnchorEvaluationV2Document
	var fund *types.AnchorEvaluationV2Fund
	var support *types.AnchorEvaluationV2Support

	switch phase {
	case "document":
		sc := h.si.Conf("evaluation")

		var screenshotUrl, videoUrl, attachmentUrl string

		if e.Screenshot != "" {
			screenshotUrl = sc.ExternalURL(e.Screenshot)
		}

		if e.Video != "" {
			videoUrl = sc.ExternalURL(e.Video)
		}

		if e.Attachment != "" {
			attachmentUrl = sc.ExternalURL(e.Attachment)
		}

		var needFund bool
		isOld := e.IsOldRecord()

		if !isOld && e.Status == anchor.AnchorEvaluationStatusNotAllow {
			needFund = true
		}

		document = &types.AnchorEvaluationV2Document{
			Status:        e.Status,
			Screenshot:    e.Screenshot,
			ScreenShotUrl: screenshotUrl,
			Video:         e.Video,
			VideoUrl:      videoUrl,
			Attachment:    e.Attachment,
			AttachmentUrl: attachmentUrl,
			Reason:        e.Reason,
			NeedFund:      needFund,
			IsOld:         isOld,
		}
	case "fund":
		res, err := h.am.StatusFund(ctx, uac.UserId)

		if err != nil {
			return nil, err
		}

		fund = &types.AnchorEvaluationV2Fund{
			Status:    res.Status,
			RemainDay: res.RemainDay,
			Diamond:   res.LuckDiamond,
			Target:    res.Target,
		}
	case "support":
		res, err := h.am.StatusSupport(ctx, uac.UserId)

		if err != nil {
			return nil, err
		}

		list := make([]types.AnchorEvaluationV2SupportDay, 0, len(res.List))

		for _, v := range res.List {
			list = append(list, types.AnchorEvaluationV2SupportDay{
				Day:      v.Day,
				Date:     v.Date.In(ctz.Brazil).Format(time.DateTime),
				IsToday:  v.IsToday,
				Paused:   v.Paused,
				Duration: v.Duration,
				Diamond:  v.LuckDiamond,
				Status:   v.Status,
			})
		}

		support = &types.AnchorEvaluationV2Support{
			Status: res.Status,
			Day:    res.Day,
			List:   list,
		}
	}

	return &types.AnchorEvaluationV2StatusResponse{
		IsQuality: lo.Must(h.am.IsQualityRoom(ctx, uac.UserId)),
		Phase:     phase,
		Document:  document,
		Fund:      fund,
		Support:   support,
	}, nil
}

// @Tags 主播
// @Summary ✅新主播考核V2申请
// @Description 新主播考核V2申请
// @Produce json
// @Security HeaderAuth
// @Param param body types.AnchorEvaluationV2ApplyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/anchor/evaluation/v2/apply [post]
func (h *Anchor) EvaluationV2Apply(ctx *api.Context, req types.AnchorEvaluationV2ApplyRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	screenshot := req.Screenshot
	video := req.Video
	attachment := req.Attachment

	logger := h.logger.With(
		zap.String("userId", userId),
		zap.String("screenshot", screenshot),
		zap.String("video", video),
		zap.String("attachment", attachment),
	)

	l, err := h.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:EVALUATION:APPLY:%s", userId))

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "Please try again later")
	}
	defer l.MustUnlock()

	e, err := h.am.GetEvaluation(ctx, userId)

	if err != nil {
		if err != anchor.ErrEvaluationNotFound {
			return nil, err
		}
	} else {
		if e.Status != anchor.AnchorEvaluationStatusReject {
			logger.Debug("新主播考核申请失败", zap.Int("status", e.Status))

			return nil, biz.NewError(biz.ErrBusiness, "Invalid status")
		}
	}

	err = h.am.ApplyEvaluation(ctx, userId, ctx.ClientIP(), app.DeviceId(ctx), screenshot, video, attachment)

	if err != nil {
		logger.Error("新主播考核申请失败", zap.Error(err))

		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags 主播
// @Summary ✅新主播考核V2流水考核操作
// @Description 新主播考核V2流水考核操作
// @Produce json
// @Security HeaderAuth
// @Param param body types.AnchorEvaluationV2FundRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/anchor/evaluation/v2/fund/operate [post]
func (h *Anchor) EvaluationV2FundOperate(ctx *api.Context, req types.AnchorEvaluationV2FundRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	err = h.am.StartFund(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags 主播
// @Summary ✅新主播考核V2政策扶持操作
// @Description 新主播考核V2政策扶持操作 14009当天不满3小时，确认是否开启 14010当天不满2小时，无法开启
// @Produce json
// @Security HeaderAuth
// @Param param body types.AnchorEvaluationV2SupportRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/anchor/evaluation/v2/support/operate [post]
func (h *Anchor) EvaluationV2SupportOperate(ctx *api.Context, req types.AnchorEvaluationV2SupportRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	nt := time.Now().In(ctz.Brazil)
	et := now.New(nt).EndOfDay()

	duration := et.Sub(nt)

	if duration < anchor.DurationSupportStartDeny {
		return nil, biz.NewError(biz.ErrAnchorSupportStartDeny, "Today is less than 2 hours")
	}

	if duration < anchor.DurationSupportStartConfirm {
		if !req.Force {
			return nil, biz.NewError(biz.ErrAnchorSupportStartConfirm, "Today is less than 3 hours")
		}
	}

	// 优质房间不支持开启新主播扶持
	if lo.Must(h.am.IsQualityRoom(ctx, uac.UserId)) {
		return nil, biz.NewError(biz.ErrAnchorSupportStartDeny, "Can not start support, please contact customer service")
	}

	err = h.am.StartSupport(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags 主播
// @Summary ✅新主播考核V2政策扶持奖励领取
// @Description 新主播考核V2政策扶持奖励领取
// @Produce json
// @Security HeaderAuth
// @Param param body types.AnchorEvaluationV2SupportAwardReceiveRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/anchor/evaluation/v2/support/award/receive [post]
func (h *Anchor) EvaluationV2SupportAwardReceive(ctx *api.Context, req types.AnchorEvaluationV2SupportAwardReceiveRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	date, err := time.ParseInLocation(time.DateTime, req.Date, ctz.Brazil)

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "Invalid date")
	}

	err = h.am.ReceiveSupportAward(ctx, uac.UserId, date)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, biz.NewError(biz.ErrBusiness, "Invalid date")
		}

		return nil, err
	}

	return &api.EmptyResp{}, nil
}
