package handler

import (
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/jstream"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 主播
func InvokeAnchorHandler(
	r *api.Router,
	ug user.Getter,
	lm *live.Manager,
	lsm *ls.Manager,
	tm *task.Manager,
	sm *salary.Manager,
	js *jstream.API,
	jm *journal.Manager,
	am *anchor.Manager,
	dm *redi.Mutex,
	si sto.Instance,
	us *upload.Scenes,
	vnd log.Vendor,
) *Anchor {
	h := NewAnchor(ug, lm, lsm, tm, sm, js, jm, am, dm, si, vnd.Scope("api.anchor"))

	ar := r.WithAuth()
	{
		// 直播数据汇总
		ar.GET("/anchor/live", api.Generic(h.Live))
		// 直播数据场次列表
		ar.GET("/anchor/session/list", api.Generic(h.SessionList))
		// 直播场次数据详情
		ar.GET("/anchor/session/detail", api.Generic(h.SessionDetail))
		// 主播任务
		ar.GET("/anchor/task/list", api.Generic(h.TaskList))
		// 主播任务数据
		ar.GET("/anchor/task/overview", api.Generic(h.TaskOverview))
		// 主播账单
		ar.GET("/anchor/bill/history", api.Generic(h.BillHistory))
		// 账单详情
		ar.GET("/anchor/bill/detail", api.Generic(h.BillDetail))
		// 主播考核状态
		ar.GET("/anchor/evaluation/status", api.Generic(h.EvaluationStatus))
		// 主播考核申请
		ar.POST("/anchor/evaluation/apply", api.Generic(h.EvaluationApply))
		// 主播考核V2状态
		ar.GET("/anchor/evaluation/v2/status", api.Generic(h.EvaluationV2Status))
		// 主播考核V2申请
		ar.POST("/anchor/evaluation/v2/apply", api.Generic(h.EvaluationV2Apply))
		// 主播考核V2流水考核操作
		ar.POST("/anchor/evaluation/v2/fund/operate", api.Generic(h.EvaluationV2FundOperate))
		// 主播考核V2扶持操作
		ar.POST("/anchor/evaluation/v2/support/operate", api.Generic(h.EvaluationV2SupportOperate))
		// 主播考核V2扶持奖励领取
		ar.POST("/anchor/evaluation/v2/support/award/receive", api.Generic(h.EvaluationV2SupportAwardReceive))
	}

	// 直播时长查询
	r.GET("/anchor/live/duration", api.Generic(h.LiveDuration))

	{
		us.Add("evaluation", us.Classic("evaluation", "1000x0"))
	}

	return h
}

type Anchor struct {
	ug     user.Getter
	lm     *live.Manager
	lsm    *ls.Manager
	tm     *task.Manager
	sm     *salary.Manager
	js     *jstream.API
	jm     *journal.Manager
	am     *anchor.Manager
	dm     *redi.Mutex
	si     sto.Instance
	logger *zap.Logger
}

func NewAnchor(
	ug user.Getter,
	lm *live.Manager,
	lsm *ls.Manager,
	tm *task.Manager,
	sm *salary.Manager,
	js *jstream.API,
	jm *journal.Manager,
	am *anchor.Manager,
	dm *redi.Mutex,
	si sto.Instance,
	logger *zap.Logger,
) *Anchor {
	return &Anchor{
		ug:     ug,
		lm:     lm,
		lsm:    lsm,
		tm:     tm,
		sm:     sm,
		js:     js,
		jm:     jm,
		am:     am,
		dm:     dm,
		si:     si,
		logger: logger,
	}
}

// @Tags 主播
// @Summary ✅主播中心数据概览
// @Description 主播中心数据概览
// @Produce json
// @Security HeaderAuth
// @Param param query types.AnchorLiveRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorLiveResponse}
// @Router /api/v1/anchor/live [get]
func (h *Anchor) Live(ctx *api.Context, req types.AnchorLiveRequest) (*types.AnchorLiveResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	acc, err := h.ug.Account(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	anchorId := uac.UserId

	// 直播间信息
	room, err := h.lm.RoomByUserId(ctx, anchorId)

	if err != nil {
		return nil, err
	}

	cc := ctz.Parse(room.CTZ)

	nt := now.New(cc.In(time.Now()))
	period := req.Period
	startTime := cc.In(time.Unix(req.StartTime, 0))
	endTime := cc.In(time.Unix(req.EndTime, 0))

	// TODO 暂时关闭今日预估，避免生成的错误的时区数据
	// 预估今日收入
	// h.sm.EstimateIncome(ctx, anchorId, cc, nt.BeginningOfDay())

	summary, err := h.lsm.GetLiveSummaryInPeriod(ctx, anchorId, ls.PeriodType(period), startTime, endTime, nt)

	if err != nil {
		return nil, err
	}

	return &types.AnchorLiveResponse{
		UserWithExt: *mixer.UserWithExt(ctx, acc),
		Live: &types.AnchorLiveOverview{
			Period:   period,
			ValidDay: summary.ValidDay,
			AnchorLive: types.AnchorLive{
				AudCount:      summary.AudCount,
				GiftUserCount: summary.GiftUserCount,
				NewFanCount:   summary.NewFanCount,
				Duration:      summary.Duration / 60, // 天维度汇总数据都是有效时长
				ValidDuration: summary.Duration / 60, // 天维度汇总数据都是有效时长
				LuckDiamond:   summary.LuckDiamond,
				GiftDiamond:   summary.GiftDiamond,
				Salary:        summary.Salary,
				Income:        fund.New(summary.Salary).Add(summary.LuckIncome).Add(summary.GiftIncome).IntPart(),
			},
		},
	}, nil
}

// @Tags 主播
// @Summary ✅主播数据中心场次列表
// @Description 主播数据中心场次列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AnchorSessionListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorSessionListResponse}
// @Router /api/v1/anchor/session/list [get]
func (h *Anchor) SessionList(ctx *api.Context, req types.AnchorSessionListRequest) (*types.AnchorSessionListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	res, cursor, err := h.lm.GetSessionList(ctx, uac.UserId, req.Cursor, time.Time{}, time.Time{})

	if err != nil {
		return nil, err
	}

	list := make([]types.AnchorLiveSession, 0, len(res))

	for _, v := range res {
		list = append(list, *mixer.AnchorLiveSession(&v))
	}

	return &types.AnchorSessionListResponse{
		Cursor: cursor,
		List:   list,
	}, nil
}

// @Tags 主播
// @Summary ✅主播数据中心场次详情
// @Description 主播数据中心场次详情
// @Produce json
// @Security HeaderAuth
// @Param param query types.AnchorSessionDetailRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorSessionDetailResponse}
// @Router /api/v1/anchor/session/detail [get]
func (h *Anchor) SessionDetail(ctx *api.Context, req types.AnchorSessionDetailRequest) (*types.AnchorSessionDetailResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	v, err := h.lm.Session2(req.SessionId)

	if err != nil {
		return nil, err
	}

	if v.UserId != uac.UserId {
		return nil, errors.New("invalid session")
	}

	return &types.AnchorSessionDetailResponse{
		AnchorLiveSession: *mixer.AnchorLiveSession(v),
	}, nil
}

// @Tags 主播
// @Summary ✅主播任务列表
// @Description 主播任务列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AnchorTaskListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorTaskListResponse}
// @Router /api/v1/anchor/task/list [get]
func (h *Anchor) TaskList(ctx *api.Context, req types.AnchorTaskListRequest) (*types.AnchorTaskListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := uac.UserId

	room, err := h.lm.RoomByUserId2(ctx, anchorId)

	if err != nil {
		return nil, err
	}

	nn := now.New(ctz.Parse(room.CTZ).In(time.Now()))

	list, err := h.tm.AnchorTaskList(ctx, anchorId, nn.Time)

	if err != nil {
		return nil, err
	}

	var giftDiamond int64

	summary, err := h.lsm.GetLiveSummaryInPeriod(
		ctx,
		anchorId,
		ls.PeriodType("today"),
		nn.BeginningOfDay(),
		nn.EndOfDay(),
		nn,
	)

	if err == nil {
		giftDiamond = summary.GiftDiamond
	}

	return &types.AnchorTaskListResponse{
		Now:         nn.Unix(),
		NowFormat:   nn.Format(time.RFC3339),
		List:        list,
		GiftDiamond: giftDiamond,
	}, nil
}

// @Tags 主播
// @Summary ✅主播任务数据总览
// @Description 主播任务数据总览
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.AnchorTaskOverviewResponse}
// @Router /api/v1/anchor/task/overview [get]
func (h *Anchor) TaskOverview(ctx *api.Context, _ api.EmptyReq) (*types.AnchorTaskOverviewResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := uac.UserId

	room, err := h.lm.RoomByUserId2(ctx, anchorId)

	if err != nil {
		return nil, err
	}

	nn := now.New(ctz.Parse(room.CTZ).In(time.Now()))

	list, err := h.tm.AnchorTaskList(ctx, anchorId, nn.Time)

	if err != nil {
		return nil, err
	}

	var duration int64
	var luckDiamond int64

	for _, v := range list {
		switch v.Key {
		case task.AnchorTaskKeyDuration:
			duration = v.Current
		case task.AnchorTaskKeyLuckDiamond:
			luckDiamond = v.Current
		}
	}

	var giftDiamond int64

	summary, err := h.lsm.GetLiveSummaryInPeriod(
		ctx,
		anchorId,
		ls.PeriodType("today"),
		nn.BeginningOfDay(),
		nn.EndOfDay(),
		nn,
	)

	if err == nil {
		giftDiamond = summary.GiftDiamond
	}

	return &types.AnchorTaskOverviewResponse{
		Now:         nn.Unix(),
		NowFormat:   nn.Format(time.RFC3339),
		Duration:    duration,
		LuckDiamond: luckDiamond,
		GiftDiamond: giftDiamond,
	}, nil
}

// @Tags 主播
// @Summary 主播账单
// @Description 主播账单
// @Produce json
// @Security HeaderAuth
// @Param param query types.AnchorBillHistoryRequest true "请求参数"
// @Success 200 {object} codec.Response{data=jstream.Response}
// @Router /api/v1/anchor/bill/history [get]
func (h *Anchor) BillHistory(ctx *api.Context, req types.AnchorBillHistoryRequest) (*jstream.Response, error) {
	rr := jstream.Request{Prop: fund.PTypeFruits, EndAt: req.EndAt, Cursor: req.Cursor}
	if req.Type != 0 {
		rr.Type = req.Type
	} else {
		rr.Types = []fund.JournalType{
			fund.JTypeSendGift,
			fund.JTypeSalary,
			fund.JTypeKickback,
			fund.JTypeRewards,
			fund.JTypeOthers,
		}
	}

	jr, err := h.js.Journal(ctx, rr)
	if err != nil {
		return nil, err
	}

	adj := make(map[time.Time]int64)
	sss := make(map[string]*jstream.Record)

	for i := 0; i < len(jr.List); i++ {
		r := jr.List[i].Raw
		if r.Type != fund.JTypeSendGift {
			continue
		}
		k := r.GKey()
		if ss, _ := h.lm.Session2(k); ss != nil && ss.EndTime.IsZero() {
			// 正在直播的不显示
			adj[r.CreatedAt] -= r.Amount.IntPart()
			jr.List = slices.Delete(jr.List, i, i+1)
			i--
		}
		break
	}

	for i := 0; i < len(jr.List); i++ {
		r := jr.List[i].Raw
		if r.Type != fund.JTypeSendGift {
			continue
		}
		k := r.GKey()
		if v, has := sss[k]; has {
			// 前面的已经有场次数据了，合并过去
			amount := r.Amount.IntPart()
			v.Amount += amount
			adj[v.Raw.CreatedAt] += amount
			adj[r.CreatedAt] -= amount
			jr.List = slices.Delete(jr.List, i, i+1)
			i--
		} else {
			sss[k] = jr.List[i]
		}
	}

	for i := 0; i < len(jr.Aggs); i++ {
		agg := jr.Aggs[i]
		for t, v := range adj {
			if journal.SameMonth(agg.Raw.Time, t) {
				agg.Incomes += v
			}
		}
		if len(adj) > 0 && agg.Incomes == 0 {
			if !slices.ContainsFunc(jr.List, func(v *jstream.Record) bool {
				return journal.SameMonth(agg.Raw.Time, v.Raw.CreatedAt)
			}) {
				jr.Aggs = slices.Delete(jr.Aggs, i, i+1)
				i--
			}
		}
	}

	return jr, nil
}

// @Tags 主播
// @Summary ✅账单详情
// @Description 账单详情
// @Produce json
// @Security HeaderAuth
// @Param param query types.AnchorBillDetailRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorBillDetailResponse}
// @Router /api/v1/anchor/bill/detail [get]
func (h *Anchor) BillDetail(ctx *api.Context, req types.AnchorBillDetailRequest) (*types.AnchorBillDetailResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	jr, err := h.jm.Detail(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	var bSession *salary.BillSession
	var bSalary *salary.BillSalary

	var rType string
	switch jr.Type {
	case fund.JTypeSendGift:
		bSession, err = h.sm.GetBillSession(ctx, userId, jr.GKey())

		if err != nil {
			return nil, err
		}
		rType = "gift"
		bSession.Time = jr.CreatedAt.Unix()
	case fund.JTypeSalary:
		bSalary, err = h.sm.GetBillSalary(ctx, userId, strings.TrimPrefix(jr.Trade, "ANCHOR"))

		if err != nil {
			return nil, err
		}
		rType = "salary"
		bSalary.Time = jr.CreatedAt.Unix()
	default:
		return nil, errors.New("invalid type")
	}

	return &types.AnchorBillDetailResponse{
		Id:      req.Id,
		Type:    rType,
		Session: bSession,
		Salary:  bSalary,
	}, nil
}

// @Tags 主播
// @Summary ✅直播时长查询
// @Description 账单详情
// @Produce json
// @Param param query types.AnchorLiveDurationRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorLiveDurationResponse}
// @Router /api/v1/anchor/live/duration [get]
func (h *Anchor) LiveDuration(ctx *api.Context, req types.AnchorLiveDurationRequest) (*types.AnchorLiveDurationResponse, error) {
	showId := req.ShowId
	day := req.Day // 2024-01-01

	acc, err := h.ug.GetByShowId(ctx, showId)

	if err != nil {
		return nil, err
	}

	userId := acc.UserId

	room, err := h.lm.RoomByUserId2(ctx, userId)

	if err != nil {
		return nil, err
	}

	cc := ctz.Parse(room.CTZ)
	// 默认当前时间的日期
	dayTime := now.New(cc.In(time.Now())).BeginningOfDay()

	// day日期转time
	if day != "" {
		res, err := time.ParseInLocation("2006-01-02", day, cc.Timezone)
		if err != nil {
			return nil, biz.NewError(biz.ErrBusiness, "invalid day")
		}

		dayTime = now.New(res).BeginningOfDay()
	}

	lsd, err := h.lsm.GetLiveSummaryDay(
		ctx,
		userId,
		dayTime,
	)

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, fmt.Sprintf("get live summary day failed: %v", err))
	}

	h.logger.Info(
		"get live summary day",
		zap.Any("userId", userId),
		zap.String("showId", showId),
		zap.Time("dayTime", dayTime),
		zap.String("day", day),
		zap.Any("lsd", lsd),
	)

	return &types.AnchorLiveDurationResponse{
		UserId:   userId,
		Day:      dayTime.Format("2006-01-02"),
		Nickname: acc.Nickname,
		Duration: lsd.Duration / 60,
	}, nil
}

// @Tags 主播
// @Summary ✅新主播考核状态
// @Description 新主播考核状态
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.AnchorEvaluationStatusResponse}
// @Router /api/v1/anchor/evaluation/status [get]
func (h *Anchor) EvaluationStatus(ctx *api.Context, _ api.EmptyReq) (*types.AnchorEvaluationStatusResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	var status int
	var screenshot, screenshotUrl, video, videoUrl string
	var reason string

	e, err := h.am.GetEvaluation(ctx, userId)

	if err != nil {
		if err != anchor.ErrEvaluationNotFound {
			return nil, err
		}
	} else {
		sc := h.si.Conf("evaluation")

		status = e.Status
		screenshot = e.Screenshot
		screenshotUrl = sc.ExternalURL(e.Screenshot)
		video = e.Video
		videoUrl = sc.ExternalURL(e.Video)
		reason = e.Reason
	}

	return &types.AnchorEvaluationStatusResponse{
		Status:        status,
		Screenshot:    screenshot,
		ScreenShotUrl: screenshotUrl,
		Video:         video,
		VideoUrl:      videoUrl,
		Reason:        reason,
	}, nil
}

// @Tags 主播
// @Summary ✅新主播考核申请
// @Description 新主播考核申请
// @Produce json
// @Security HeaderAuth
// @Param param body types.AnchorEvaluationApplyRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/anchor/evaluation/apply [post]
func (h *Anchor) EvaluationApply(ctx *api.Context, req types.AnchorEvaluationApplyRequest) (*api.EmptyReq, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if anchor.IsV2(time.Now()) {
		return nil, biz.NewError(biz.ErrBusiness, "Please try again later")
	}

	userId := uac.UserId
	screenshot := req.Screenshot
	video := req.Video

	logger := h.logger.With(
		zap.String("userId", userId),
		zap.String("screenshot", screenshot),
		zap.String("video", video),
	)

	l, err := h.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:EVALUATION:APPLY:%s", userId))

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "Please try again later")
	}
	defer l.MustUnlock()

	e, err := h.am.GetEvaluation(ctx, userId)

	if err != nil {
		if err != anchor.ErrEvaluationNotFound {
			return nil, err
		}
	} else {
		if e.Status != anchor.AnchorEvaluationStatusReject {
			logger.Debug("新主播考核申请失败", zap.Int("status", e.Status))

			return nil, biz.NewError(biz.ErrBusiness, "Invalid status")
		}
	}

	err = h.am.ApplyEvaluation(ctx, userId, ctx.ClientIP(), app.DeviceId(ctx), screenshot, video, "")

	if err != nil {
		logger.Error("新主播考核申请失败", zap.Error(err))

		return nil, err
	}

	return &api.EmptyReq{}, nil
}
