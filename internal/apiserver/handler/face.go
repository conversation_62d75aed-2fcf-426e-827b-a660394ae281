package handler

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/face"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

// 人脸检测
func InvokeFaceHandler(
	r *api.Router,
	fm *face.Manager,
	vnd log.Vendor,
) *Face {
	h := NewFace(fm, vnd.Scope("api.face"))

	ar := r.WithAuth()
	{
		ar.POST("/face/check", api.Generic(h.Check))
	}

	return h
}

type Face struct {
	fm     *face.Manager
	logger *zap.Logger
}

func NewFace(
	fm *face.Manager,
	logger *zap.Logger,
) *Face {
	return &Face{
		fm:     fm,
		logger: logger,
	}
}

// @Tags 人脸
// @Summary ✅检测
// @Description 检测，并返回检测凭证
// @Produce json
// @Security HeaderAuth
// @Param param body types.FaceCheckRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FaceCheckResponse}
// @Router /api/v1/face/check [post]
func (h *Face) Check(ctx *api.Context, req types.FaceCheckRequest) (*types.FaceCheckResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	scene := req.Scene

	ticket, err := h.fm.Check(ctx, uac.UserId, req.Token, scene)

	if err != nil {
		return nil, err
	}

	return &types.FaceCheckResponse{
		Ticket: ticket,
	}, nil
}
