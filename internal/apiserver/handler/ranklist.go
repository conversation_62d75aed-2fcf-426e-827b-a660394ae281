package handler

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/ranklist"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 榜单
func InvokeRanklistHandler(
	r *api.Router, hc *cache.Handler,
	rlm *ranklist.Manager,
	ug user.Getter,
	vnd log.Vendor,
	lg *live.Manager,
	rm *rocket.Manager,
) *Ranklist {
	h := NewRanklist(rlm, ug, vnd.Scope("api.ranklist"), lg, rm)

	ar := r.WithAuth()
	{
		// 小时榜入口信息
		ar.GET("/ranklist/hour_entrance", api.Generic(h.HourEntrance))
		// 小时榜入口信息V2，
		ar.GET("/ranklist/entrance", hc.Middleware(cache.WithExpire(time.Millisecond*500)), api.Generic(h.Entrance))
		// 小时榜详情
		ar.GET("/ranklist/hour_detail", api.Generic(h.HourDetail))
		// 人气榜详情
		ar.GET("/ranklist/popularity", api.Generic(h.Popularity))
		// 房间内流水榜入口信息
		//ar.GET("/ranklist/gift_entrance", api.Generic(h.GiftEntrance))
		// 房间内流水榜
		ar.GET("/ranklist/room/user", api.Generic(h.UserGiftRankInRoom))
		// 首页主播榜
		ar.GET("/ranklist/homepage/anchor", api.Generic(h.Anchor))
		// 首页用户榜
		ar.GET("/ranklist/homepage/user", api.Generic(h.User))
	}

	return h
}

type Ranklist struct {
	rlm    *ranklist.Manager
	ug     user.Getter
	logger *zap.Logger
	lg     *live.Manager
	rm     *rocket.Manager
}

func NewRanklist(
	rlm *ranklist.Manager,
	ug user.Getter,
	logger *zap.Logger,
	lg *live.Manager,
	rm *rocket.Manager,
) *Ranklist {
	return &Ranklist{
		rlm:    rlm,
		ug:     ug,
		logger: logger,
		lg:     lg,
		rm:     rm,
	}
}

// @Tags 排行榜
// @Summary 小时榜入口信息
// @Description 小时榜入口信息
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistHourEntranceRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistHourEntranceResponse}
// @Router /api/v1/ranklist/hour_entrance [get]
func (h *Ranklist) HourEntrance(ctx *api.Context, req types.RanklistHourEntranceRequest) (*types.RanklistHourEntranceResponse, error) {
	res, err := h.rlm.HourEntrance(ctx, req.AnchorId)

	if err != nil {
		return nil, err
	}

	return &types.RanklistHourEntranceResponse{
		HourEntrance: *res,
	}, nil
}

// @Tags 排行榜
// @Summary 小时榜入口信息V2
// @Description 小时榜入口信息V2，客户端本地化处理，新版本使用
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistEntranceRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistEntranceResponse}
// @Router /api/v1/ranklist/entrance [get]
func (h *Ranklist) Entrance(ctx *api.Context, req types.RanklistEntranceRequest) (*types.RanklistEntranceResponse, error) {
	res, err := h.rlm.Entrance(ctx, req.AnchorId)
	if err != nil {
		return nil, err
	}

	return &types.RanklistEntranceResponse{
		Entrance: *res,
	}, nil
}

// @Tags 排行榜
// @Summary 小时榜详情
// @Description 小时榜详情
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistHourDetailRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistHourDetailResponse}
// @Router /api/v1/ranklist/hour_detail [get]
func (h *Ranklist) HourDetail(ctx *api.Context, req types.RanklistHourDetailRequest) (*types.RanklistHourDetailResponse, error) {
	res, err := h.rlm.HourDetail(ctx.Context)
	if err != nil {
		return nil, err
	}

	var owner types.RanklistHourDetailRankInfo
	ranks := make([]types.RanklistHourDetailRankInfo, 0, 100)
	for _, v := range res.Users {
		rank := types.RanklistHourDetailRankInfo{
			Index: v.Rank,
			User:  *mixer.User(ctx, v.User),
			Tags:  nil,
			Score: v.Score,
		}

		ranks = append(ranks, rank)

		if v.User.UserId == req.AnchorId {
			owner = rank
		}
	}

	// 主播不在top100
	if owner.Index == 0 {
		// 获取主播信息
		acc, err := h.ug.Account(ctx, req.AnchorId)
		if err != nil {
			return nil, fmt.Errorf("HourDetail: get owner user failed: %w", err)
		}

		// 获取主播排名
		var (
			anchorRank  int64
			anchorScore int64
		)
		anchorRank, anchorScore = h.rlm.GetMyHourRankAndScore(ctx, req.AnchorId, time.Now())
		owner = types.RanklistHourDetailRankInfo{
			Index: int(anchorRank),
			User:  *mixer.User(ctx, acc),
			Score: anchorScore,
			Tags:  nil,
		}
	}

	// 获取用户最佳排名

	// 获取上小时前十
	advanceRes, err := h.rlm.AdvanceInfo(ctx.Context)
	if err != nil {
		return nil, err
	}
	advanceRanks := make([]types.RanklistHourDetailRankInfo, 0, 10)
	for _, v := range advanceRes.Users {
		rank := types.RanklistHourDetailRankInfo{
			Index: v.Rank,
			Score: v.Score,
			User:  *mixer.User(ctx, v.User),
			Tags:  nil,
		}

		advanceRanks = append(advanceRanks, rank)
	}

	return &types.RanklistHourDetailResponse{
		PeriodRemainSeconds: res.PeriodRemainSeconds,
		OwnerRank:           owner,
		Ranks:               ranks,
		AdvanceInfo:         advanceRanks,
	}, nil
}

// @Tags 排行榜
// @Summary 人气榜详情
// @Description 人气榜详情
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistPopularityRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistPopularityResponse}
// @Router /api/v1/ranklist/popularity [get]
func (h *Ranklist) Popularity(ctx *api.Context, req types.RanklistPopularityRequest) (*types.RanklistPopularityResponse, error) {
	res, err := h.rlm.PopularityDetail(ctx)
	if err != nil {
		return nil, err
	}

	var owner types.RanklistPopularityDetailRankInfo
	ranks := make([]types.RanklistPopularityDetailRankInfo, 0, 100)
	for k, v := range res.Users {
		rank := types.RanklistPopularityDetailRankInfo{
			Index:          k + 1,
			Score:          v.Score,
			ContributorNum: v.ContributorNum,
			User:           *mixer.User(ctx, v.User),
		}

		ranks = append(ranks, rank)

		if v.User.UserId == req.AnchorId {
			owner = rank
		}
	}

	// 主播不在top100
	if owner.Index == 0 {
		// 获取主播信息
		acc, err := h.ug.Account(ctx, req.AnchorId)
		if err != nil {
			return nil, fmt.Errorf("Popularity: get owner user failed: %w", err)
		}

		// 获取排名
		owner = types.RanklistPopularityDetailRankInfo{
			Index:          h.rlm.PopularityContributorRank(ctx, acc.UserId),
			Score:          h.rlm.PopularityContributorScore(ctx, acc.UserId),
			ContributorNum: h.rlm.PopularityContributorNum(ctx, acc.UserId),
			User:           *mixer.User(ctx, acc),
		}
	}

	return &types.RanklistPopularityResponse{
		PeriodRemainSeconds: res.PeriodRemainSeconds,
		OwnerRank:           owner,
		Ranks:               ranks,
	}, nil
}

// @Tags 排行榜
// @Summary 直播间礼物流水榜入口
// @Description 直播间礼物流水榜入口
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistGiftEntranceRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistGiftEntranceResponse}
// @Router /api/v1/ranklist/gift_entrance [get]
func (h *Ranklist) GiftEntrance(ctx *api.Context, req types.RanklistGiftEntranceRequest) (*types.RanklistGiftEntranceResponse, error) {
	dailyScore, weeklyScore, err := h.rlm.GetAnchorGiftRankScore(ctx, req.AnchorId)

	if err != nil {
		return nil, err
	}

	return &types.RanklistGiftEntranceResponse{
		DailyScore:  dailyScore,
		WeeklyScore: weeklyScore,
	}, nil
}

// @Tags 排行榜
// @Summary 直播间礼物流水榜
// @Description 直播间礼物流水榜
// @Produce json
// @Security HeaderAuth
// @Param param query types.UserGiftRankInRoomRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveAudienceResponse}
// @Router /api/v1/ranklist/room/user [get]
func (h *Ranklist) UserGiftRankInRoom(ctx *api.Context, req types.UserGiftRankInRoomRequest) (*types.LiveAudienceResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	r, err := h.lg.Room2(req.Id)
	if err != nil {
		h.logger.Error("UserGiftRankInRoom: get room failed", zap.Error(err), zap.String("roomId", req.Id))
		return nil, errors.New("get room rank failed")
	}

	if r.IsGameRoom() {
		roomUser, err := mixer.RoomUser(ctx, r.UserId, uac.UserId)
		if err != nil {
			return nil, err
		}

		mime := &types.AudienceRank{
			Audience: &types.Audience{
				User:      *mixer.User(ctx, uac),
				RoomUser:  *roomUser,
				Score:     0, // 榜单分
				ScoreDesc: "",
				Rank:      0,
				RankDesc:  "-",
			},
		}

		return &types.LiveAudienceResponse{
			List: make([]types.Audience, 0),
			Mine: mime,
		}, nil
	}

	userRank, err := h.rlm.GetUserRankInRoom(ctx, r.UserId, uac.UserId, req.Type, r.IsLuckyRoom())
	if err != nil {
		return nil, err
	}

	const (
		maxAudienceSize = 200
	)
	distanceFunc := func(l, r int64) int64 {
		if l != r {
			return l - r
		}

		return 1
	}

	res, err := h.rlm.UserGiftRankInRoom(ctx, r.UserId, req.Type, 0, maxAudienceSize, r.IsLuckyRoom())
	if err != nil {
		h.logger.Error("UserGiftRankInRoom: UserGiftRankInRoom failed", zap.Error(err), zap.String("roomId", req.Id))
		return nil, err
	}

	var (
		audiences   = make([]types.Audience, 0, len(res.Ranklist))
		distance    = int64(0)
		showContrib = userRank.Rank <= maxAudienceSize && userRank.Score > 0
	)
	// 火箭头像框
	rocketUser, _ := h.rm.GetRoomAvatarBorderUserList(ctx, r.SessionId.Hex())

	for i, v := range res.Ranklist {
		roomUser, err := mixer.RoomUser(ctx, r.UserId, v.User.UserId)
		if err != nil {
			return nil, err
		}

		aud := types.Audience{
			Rank:     v.Rank,
			User:     *mixer.User(ctx, v.User),
			RoomUser: *roomUser,
			Score:    v.Score,
		}

		if lo.Contains(rocketUser, v.User.UserId) {
			aud.User.AvatarBorder = rocket.AvatarBorder
		}

		if v.Score == 0 {
			aud.RankDesc = "-"
		} else {
			aud.RankDesc = strconv.Itoa(int(v.Rank))
		}

		aud.ScoreDesc = i18n.NumberString(ctx, float64(v.Score))

		audiences = append(audiences, aud)

		if v.User.UserId == uac.UserId && i > 0 && showContrib {
			distance = distanceFunc(res.Ranklist[i-1].Score, v.Score)
		}
	}

	if distance == 0 && userRank.Rank > 1 && showContrib {
		h.logger.Debug("UserGiftRankInRoom: get distance to the previous",
			zap.String("roomId", req.Id),
			zap.String("userId", uac.UserId),
			zap.Any("ranked", userRank),
		)
		upper, err := h.rlm.RoomRankUserScore(ctx, r.UserId, userRank.Rank-1, req.Type, r.IsLuckyRoom())
		if err != nil {
			return nil, err
		}

		if upper != nil {
			distance = distanceFunc(upper.Score, userRank.Score)
		}
	}

	roomUser, err := mixer.RoomUser(ctx, r.UserId, uac.UserId)
	if err != nil {
		return nil, err
	}

	mime := &types.AudienceRank{
		Audience: &types.Audience{
			User:     *mixer.User(ctx, uac),
			RoomUser: *roomUser,
			Score:    userRank.Score, // 榜单分
			Rank:     userRank.Rank,
		},
	}

	if lo.Contains(rocketUser, uac.UserId) {
		mime.User.AvatarBorder = rocket.AvatarBorder
	}

	if showContrib {
		mime.ScoreDesc = i18n.NumberString(ctx, float64(userRank.Score))
		mime.RankDesc = strconv.Itoa(int(userRank.Rank))
		if userRank.Rank > 1 {
			mime.Mark = i3n.T(ctx, "Distance to the previous %d", distance)
		}
	} else {
		mime.ScoreDesc = ""
		mime.RankDesc = "-"
	}

	return &types.LiveAudienceResponse{
		List: audiences,
		Mine: mime,
	}, nil
}

// @Tags 排行榜
// @Summary 首页主播榜
// @Description 首页主播榜
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistAnchorRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistAnchorResponse}
// @Router /api/v1/ranklist/homepage/anchor [get]
func (h *Ranklist) Anchor(ctx *api.Context, req types.RanklistAnchorRequest) (*types.RanklistAnchorResponse, error) {
	res, err := h.rlm.AnchorRank(ctx, req.Type)
	if err != nil {
		return nil, err
	}

	ranks := make([]types.RanklistHomepageRankInfo, 0, 100)
	for k, v := range res.Ranklist {
		rank := types.RanklistHomepageRankInfo{
			Index: k + 1,
			User:  *mixer.User(ctx, v.User),
			Score: v.Score,
		}

		ranks = append(ranks, rank)

	}

	return &types.RanklistAnchorResponse{
		Ranks: ranks,
	}, nil
}

// @Tags 排行榜
// @Summary 首页用户榜
// @Description 首页用户榜
// @Produce json
// @Security HeaderAuth
// @Param param query types.RanklistUserRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.RanklistUserResponse}
// @Router /api/v1/ranklist/homepage/user [get]
func (h *Ranklist) User(ctx *api.Context, req types.RanklistUserRequest) (*types.RanklistUserResponse, error) {
	res, err := h.rlm.UserRank(ctx, req.Type)
	if err != nil {
		return nil, err
	}

	ranks := make([]types.RanklistHomepageRankInfo, 0, 100)
	for k, v := range res.Ranklist {
		rank := types.RanklistHomepageRankInfo{
			Index: k + 1,
			User:  *mixer.User(ctx, v.User),
			Score: v.Score,
		}

		ranks = append(ranks, rank)

	}

	return &types.RanklistUserResponse{
		Ranks: ranks,
	}, nil
}
