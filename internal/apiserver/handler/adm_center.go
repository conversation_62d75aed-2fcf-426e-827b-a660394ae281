package handler

import (
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/adm"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ADM中心
func InvokeAdmCenterHandler(
	r *api.Router,
	admm *adm.Manager,
	am *agency.Manager,
	ug user.Getter,
	lsm *ls.Manager,
	anm *anchor.Manager,
	sm *seller.Manager,
	sto sto.Instance,
	vnd log.Vendor,
) *AdmCenter {
	h := NewAdmCenter(admm, am, ug, lsm, anm, sm, sto, sto.Conf("adm"), vnd.Scope("api.adm_center"))

	ar := r.WithAuth()
	{
		// ADM基础信息
		ar.GET("/adm/center/info", api.Generic(h.Info))
		// 数据总览
		ar.GET("/adm/center/overview", api.Generic(h.Overview))
		// 旗下公会列表
		ar.GET("/adm/center/agency/list", api.Generic(h.AgencyList))
		// ADM邀请信息获取
		ar.GET("/adm/center/invite/info", api.Generic(h.InviteInfo))
	}

	return h
}

type AdmCenter struct {
	admm   *adm.Manager
	am     *agency.Manager
	ug     user.Getter
	lsm    *ls.Manager
	anm    *anchor.Manager
	sm     *seller.Manager
	sto    sto.Instance
	stoc   sto.Conf
	logger *zap.Logger
}

func NewAdmCenter(
	admm *adm.Manager,
	am *agency.Manager,
	ug user.Getter,
	lsm *ls.Manager,
	anm *anchor.Manager,
	sm *seller.Manager,
	sto sto.Instance,
	stoc sto.Conf,
	logger *zap.Logger,
) *AdmCenter {
	return &AdmCenter{
		admm:   admm,
		am:     am,
		ug:     ug,
		lsm:    lsm,
		anm:    anm,
		sm:     sm,
		sto:    sto,
		stoc:   stoc,
		logger: logger,
	}
}

// @Tags ADM中心
// @Summary 基础信息
// @Description 基础信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.AdmCenterInfoResponse}
// @Router /api/v1/adm/center/info [get]
func (h *AdmCenter) Info(ctx *api.Context, req api.EmptyReq) (*types.AdmCenterInfoResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	var (
		days        int
		inviteCode  string
		trialPeriod int64
	)

	aa, err := h.admm.GetAdmInfo(ctx, userId)

	if err == nil {
		switch aa.Status {
		case adm.StatusTrial:
			trialPeriod = aa.TrialPeriodAt.Unix()
		case adm.StatusApproved:
			startDay := now.New(aa.CreatedAt.In(ctz.Brazil)).BeginningOfDay()
			today := now.New(time.Now().In(ctz.Brazil)).BeginningOfDay()

			days = int(today.Sub(startDay).Hours()/24) + 1
		}

		inviteCode = aa.AgencyInviteCode
	}

	return &types.AdmCenterInfoResponse{
		User:        mixer.User(ctx, uac),
		InviteCode:  inviteCode,
		InviteUrl:   fmt.Sprintf(adm.InviteUrl, inviteCode),
		Status:      aa.Status,
		TrialPeriod: trialPeriod,
		Days:        days,
	}, nil
}

// @Tags ADM中心
// @Summary 数据总览
// @Description 数据总览
// @Produce json
// @Security HeaderAuth
// @Param param query types.AdmCenterOverviewRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AdmCenterOverviewResponse}
// @Router /api/v1/adm/center/overview [get]
func (h *AdmCenter) Overview(ctx *api.Context, req types.AdmCenterOverviewRequest) (*types.AdmCenterOverviewResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var resp types.AdmCenterOverviewResponse

	if seller.Has(uac.Roles) {
		balance, err := h.sm.Balance(ctx, uac.UserId)
		if err == nil {
			resp.Seller = &types.SellerCenterView{
				Balance: balance.IntPart(),
			}
		}
	}

	userId := uac.UserId
	period := req.Period
	customMonth := req.Month

	_, err = h.admm.GetAdmInfo(ctx, userId)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &resp, nil
		}
		return nil, err
	}

	month := h.month(period, customMonth)

	lng := i3n.UnWarp(ctx)

	if !lo.Contains([]string{"pt", "en", "zh"}, lng) {
		lng = "pt"
	}

	// ratioDescUrl := h.stoc.ExternalURL(fmt.Sprintf("/sys/adm_ratio_%s.png", lng))
	// 可以直接使用线上图片资源
	ratioDescUrl := fmt.Sprintf("https://godzilla-live-oss.kako.live/sys/adm_ratio_%s_v2.png", lng)

	overview, err := h.lsm.GetAdmMonthOverview(ctx, userId, month)

	if err != nil {
		return nil, err
	}

	estimatedRatioPercentage := overview.EstimatedRatio.Mul(decimal.NewFromInt(100)).StringFixed(0) + "%"

	resp.AdmCenterOverview = types.AdmCenterOverview{
		AgencyCount:            overview.AgencyCount,
		AnchorIncome:           overview.AnchorIncome.IntPart(),
		EligibleAgencyCount:    overview.EligibleAgencyCount,
		NewEligibleAgencyCount: overview.NewEligibleAgencyCount,
		IneligibleAgencyCount:  overview.IneligibleAgencyCount,
		EstimatedRatio:         estimatedRatioPercentage,
		EstimatedIncome:        overview.EstimatedIncome.IntPart(),
		RatioDescUrl:           ratioDescUrl,
	}

	return &resp, nil
}

// @Tags ADM中心
// @Summary 旗下公会列表
// @Description 旗下公会列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AdmCenterAgencyListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AdmCenterAgencyListResponse}
// @Router /api/v1/adm/center/agency/list [get]
func (h *AdmCenter) AgencyList(ctx *api.Context, req types.AdmCenterAgencyListRequest) (*types.AdmCenterAgencyListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	period := req.Period
	customMonth := req.Month
	page := req.Page
	pageSize := req.PageSize
	eligibleType := req.Filter

	if pageSize <= 0 || pageSize > 100 {
		pageSize = 30
	}

	_, err = h.admm.GetAdmInfo(ctx, userId)

	if err != nil {
		return nil, err
	}

	month := h.month(period, customMonth)

	res, err := h.lsm.GetAdmMonthAgencyList(ctx, userId, month, eligibleType, page, pageSize)

	if err != nil {
		return nil, err
	}

	sc := h.sto.Conf("agency")

	list := make([]types.AdmCenterAgency, 0)

	for _, v := range res {
		agencyId := v.AgencyId

		aa, err := h.am.GetAgencyById(ctx, agencyId)

		if err != nil {
			h.logger.Error("ADM中心", zap.Error(err))

			continue
		}

		aaa, err := h.admm.GetAdmAgencyByAgencyIdIgnoreStatus(ctx, agencyId)

		if err != nil {
			h.logger.Error("ADM中心", zap.Error(err))

			continue
		}

		list = append(list, types.AdmCenterAgency{
			Name:        aa.Name,
			ImageUrl:    sc.ExternalURL(aa.ImageUrl),
			LuckDiamond: v.LuckDiamond,
			JoinTime:    aaa.CreatedAt.Unix(),
			Eligible:    v.Eligible,
		})
	}

	return &types.AdmCenterAgencyListResponse{
		Page:     page,
		PageSize: pageSize,
		List:     list,
	}, nil
}

func (h *AdmCenter) month(period string, customMonth string) string {
	var monthTime time.Time

	switch period {
	case "cm":
		monthTime = now.New(time.Now().In(ctz.Brazil)).BeginningOfMonth()
	case "lm":
		monthTime = now.New(time.Now().In(ctz.Brazil).AddDate(0, -1, 0)).BeginningOfMonth()
	case "custom":
		return customMonth
	}

	return monthTime.Format("200601")
}

// @Tags ADM中心
// @Summary 获取adm邀请链接
// @Description 获取adm邀请链接
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.AdmCenterInviteInfoResponse}
// @Router /api/v1/adm/center/invite/info [get]
func (h *AdmCenter) InviteInfo(ctx *api.Context, req api.EmptyReq) (*types.AdmCenterInviteInfoResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	admInfo, err := h.admm.GetAdmInfo(ctx, userId)
	if err != nil {
		return nil, err
	}

	return &types.AdmCenterInviteInfoResponse{
		InviteCode: admInfo.AgencyInviteCode,
		InviteUrl:  fmt.Sprintf(adm.InviteUrl, admInfo.AgencyInviteCode),
	}, nil
}
