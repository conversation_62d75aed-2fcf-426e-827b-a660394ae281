package handler

import (
	"strings"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/mute"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func InvokeChatHandler(
	r *api.Router,
	im *interact.Manager,
	lm *live.Manager,
	um *urm.Manager,
	mm *mute.Manager,
	ug user.Getter,
	vnd log.Vendor,
) *Chat {
	h := &Chat{
		im:     im,
		lm:     lm,
		um:     um,
		mm:     mm,
		ug:     ug,
		logger: vnd.Scope("chat.api"),
	}

	ar := r.WithAuth()
	{
		ar.POST("/chat/send", api.Generic(h.SendChatMessage))
		ar.GET("/chat/history", api.Generic(h.History))
	}

	return h
}

type Chat struct {
	im     *interact.Manager
	lm     *live.Manager
	um     *urm.Manager
	mm     *mute.Manager
	ug     user.Getter
	logger *zap.Logger
}

// @Tags 聊天
// @Summary 发送聊天消息
// @Description 发送聊天消息,错误码： 11003-用户被主播禁言
// @Produce json
// @Security HeaderAuth
// @Param param body types.SendChatMessageRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.SendChatMessageResponse}
// @Router /api/v1/chat/send [post]
func (c *Chat) SendChatMessage(ctx *api.Context, req *types.SendChatMessageRequest) (*types.SendChatMessageResponse, error) {
	if strings.TrimSpace(req.Content) == "" && req.Sticker == "" {
		return nil, biz.NewError(biz.ErrInvalidParam, "content or sticker is required")
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if ok, err := c.mm.IsMuteUser(ctx, uac.UserId); err != nil {
		c.logger.Error("send chat: get user mute status failed", zap.String("userId", uac.UserId), zap.String("roomId", req.RoomId), zap.Error(err))
		return nil, err
	} else if ok {
		return nil, biz.NewError(biz.ErrBusiness, "You have been muted")
	}

	ri, err := c.lm.Room2(req.RoomId)
	if err != nil {
		c.logger.Error("send chat: get room info failed", zap.String("roomId", req.RoomId), zap.Error(err))
		return nil, err
	}

	ok, err := c.um.IsMuted(ctx, ri.UserId, uac.UserId)
	if err != nil {
		c.logger.Error("send chat: get user mute status failed", zap.String("roomId", req.RoomId), zap.Error(err))
		return nil, err
	}

	if ok {
		return nil, biz.NewError(biz.ErrChatUserMuted, "You have been muted by the anchor")
	}

	if err := c.im.Chat(ctx, req.RoomId, uac.UserId, app.DeviceId(ctx), req.Content, req.Sticker, req.Quotes); err != nil {
		return nil, err
	}

	return &types.SendChatMessageResponse{}, nil
}

// @Tags 聊天
// @Summary 获取聊天历史
// @Description 获取聊天历史
// @Produce json
// @Security HeaderAuth
// @Param roomId query string true "房间ID"
// @Success 200 {object} codec.Response{data=types.ChatHistoryResponse}
// @Router /api/v1/chat/history [get]
func (c *Chat) History(ctx *api.Context, req *types.ChatHistoryRequest) (*types.ChatHistoryResponse, error) {
	if strings.ToLower(app.DeviceType(ctx)) == "android" {
		if app.Version(ctx).LessThanOrEqual(app.V112) {
			return &types.ChatHistoryResponse{List: []types.HistoryChat{}}, nil
		}
	}

	history, err := c.im.GetChatHistory(ctx, req.RoomId)
	if err != nil {
		return nil, err
	}

	if acceptSticker := app.IsAndroid(ctx) && app.Version(ctx).GreaterThan(app.V11120); !acceptSticker {
		history = lo.Filter(history, func(h interact.HistoryChat, _ int) bool { return h.Sticker == "" })
	}

	out := make([]types.HistoryChat, 0, len(history))
LOOP:
	for _, h := range history {
		acc, err := c.ug.Account(ctx, h.UserId)
		if err != nil {
			continue
		}

		var quotes []types.User
		for _, quote := range h.Quotes {
			quoteAcc, err := c.ug.Account(ctx, quote)
			if err != nil {
				continue LOOP
			}
			quotes = append(quotes, *mixer.User(ctx, quoteAcc))
		}

		out = append(out, types.HistoryChat{
			Id:      h.Id,
			User:    *mixer.UserWithExt(ctx, acc),
			Content: h.Content,
			Sticker: h.Sticker,
			Quotes:  quotes,
			At:      h.At.UnixMilli(),
		})
	}

	return &types.ChatHistoryResponse{List: out}, nil
}
