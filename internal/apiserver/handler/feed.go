package handler

import (
	"context"
	"gitlab.sskjz.com/overseas/live/osl/internal/asset"
	"gitlab.sskjz.com/overseas/live/osl/internal/feed"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/recommender/recmixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/banner"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/recommender"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

// 首页feed流
// 一期tab：
// 推荐（仅直播内容）
// 关注（仅直播内容）
// 热门（仅直播内容）
func InvokeFeedHandler(
	r *api.Router,
	dbmc *db.MongoClient,
	ug user.Getter,
	fg follow.Getter,
	vnd log.Vendor,
	recMixer *recmixer.Manager,
	lm *live.Manager,
	bm *banner.Manager,
	sm *rsd.Stats,
	fm *feed.Manager,
	am *asset.Manager,
	urm *urm.Manager,
	hc *cache.Handler,
	rm *rocket.Manager,
) *Feed {
	h := NewFeed(dbmc, ug, fg, vnd.Scope("api.feed"), recMixer, lm, bm, sm, fm, am, urm, rm)

	tr := r.TryAuth()
	{
		// 推荐
		tr.GET("/feed/list", api.Generic(h.List))
		// 关注-正在直播直播间-顶部
		tr.GET("/feed/follow", api.Generic(h.Follow))
		// 关注-正在直播直播间-列表
		tr.GET("/feed/follow/live", api.Generic(h.FollowLive))
		// 热门tab
		tr.GET("/feed/hot", api.Generic(h.Hot))
		// 热门火箭位
		tr.GET("/feed/rocket", hc.Middleware(cache.WithExpire(time.Second*5)), api.Generic(h.Rocket))
		// 热门直播
		tr.GET("/feed/popular", api.Generic(h.Popular))
	}

	return h
}

type Feed struct {
	dbmc     *db.MongoClient
	ug       user.Getter
	fg       follow.Getter
	logger   *zap.Logger
	recMixer *recmixer.Manager
	lm       *live.Manager
	bm       *banner.Manager
	sm       *rsd.Stats
	fm       *feed.Manager
	am       *asset.Manager
	urm      *urm.Manager
	rm       *rocket.Manager
}

func NewFeed(
	dbmc *db.MongoClient,
	ug user.Getter,
	fg follow.Getter,
	logger *zap.Logger,
	recMixer *recmixer.Manager,
	lm *live.Manager,
	bm *banner.Manager,
	sm *rsd.Stats,
	fm *feed.Manager,
	am *asset.Manager,
	urm *urm.Manager,
	rm *rocket.Manager,
) *Feed {
	return &Feed{
		dbmc:     dbmc,
		ug:       ug,
		fg:       fg,
		logger:   logger,
		recMixer: recMixer,
		lm:       lm,
		bm:       bm,
		sm:       sm,
		fm:       fm,
		am:       am,
		urm:      urm,
		rm:       rm,
	}
}

// @Tags Feed
// @Summary ✅推荐列表
// @Description 推荐列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.FeedListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FeedListResponse}
// @Router /api/v1/feed/list [get]
func (h *Feed) List(ctx *api.Context, req types.FeedListRequest) (*types.FeedListResponse, error) {
	userId := ""
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	page := req.Page

	if page <= 0 {
		page = 1
	}

	roomIds, err := h.lm.GetPopular(ctx, 20, page)

	if err != nil {
		h.logger.Error("获取推荐列表错误", zap.Error(err))
		return nil, err
	}

	list, err := h.makeRooms(ctx, userId, roomIds, nil)

	if err != nil {
		return nil, err
	}

	return &types.FeedListResponse{
		List: list,
	}, nil
}

// @Tags Feed
// @Summary ✅关注主播列表
// @Description 关注主播列表，按与主播的互动亲密度排序
// @Produce json
// @Security HeaderAuth
// @Param param query types.FeedFollowRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FeedFollowResponse}
// @Router /api/v1/feed/follow [get]
func (h *Feed) Follow(ctx *api.Context, req types.FeedFollowRequest) (*types.FeedFollowResponse, error) {
	res := &types.FeedFollowResponse{
		List:   make([]types.Room, 0),
		Cursor: 0,
	}

	uac, err := ctx.User()
	if err != nil {
		return res, nil
	}

	userId := uac.UserId

	if userId == "" {
		return res, nil
	}

	cursor := req.Cursor

	fls, err := h.fm.FollowingLive(ctx, feed.FollowByIntimacy, userId, cursor)

	if err != nil {
		return nil, err
	}

	res.Cursor = fls.Cursor

	for _, v := range fls.List {
		r, err := mixer.Room(ctx, userId, h.ug, h.fg, h.sm, &v.Room)

		if err != nil {
			continue
		}

		res.List = append(res.List, *r)
	}

	return res, nil
}

// @Tags Feed
// @Summary ✅关注主播直播间列表
// @Description 关注主播直播间列表，按直播间热度排序
// @Produce json
// @Security HeaderAuth
// @Param param query types.FeedFollowLiveRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FeedFollowLiveResponse}
// @Router /api/v1/feed/follow/live [get]
func (h *Feed) FollowLive(ctx *api.Context, req types.FeedFollowLiveRequest) (*types.FeedFollowLiveResponse, error) {
	res := &types.FeedFollowLiveResponse{
		List:   make([]types.Room, 0),
		Cursor: 0,
	}

	uac, err := ctx.User()
	if err != nil {
		return res, nil
	}

	userId := uac.UserId

	if userId == "" {
		return res, nil
	}

	cursor := req.Cursor

	fls, err := h.fm.FollowingLive(ctx, feed.FollowByLike, userId, cursor)

	if err != nil {
		return nil, err
	}

	res.Cursor = fls.Cursor

	for _, v := range fls.List {
		r, err := mixer.Room(ctx, userId, h.ug, h.fg, h.sm, &v.Room)

		if err != nil {
			continue
		}

		res.List = append(res.List, *r)
	}

	return res, nil
}

// @Tags Feed
// @Summary ✅热门页签
// @Description 热门页签
// @Produce json
// @Security HeaderAuth
// @Param param query types.FeedHotRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FeedHotResponse}
// @Router /api/v1/feed/hot [get]
func (h *Feed) Hot(ctx *api.Context, req types.FeedHotRequest) (*types.FeedHotResponse, error) {
	userId := ""
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	categories := []string{recommender.CategoryLiveAll, recommender.CategoryLiveNewbie}

	rec := make([]types.Room, 0)
	latest := make([]types.Room, 0)

	const (
		page      = 1
		hotNum    = 4
		newbieNum = 10
		num       = 20
	)

	logger := h.logger.With(
		zap.String("userId", userId),
	)

	excludeIds := make([]string, 0)

	for _, category := range categories {
		switch category {
		case recommender.CategoryLiveAll:
			popularRooms, err := h.lm.GetPopularRooms(ctx, num, page)

			if err != nil {
				continue
			}

			list, err := h.makeRooms(ctx, userId, nil, popularRooms)

			if err != nil {
				continue
			}

			logger.Debug(
				"热门页签",
				zap.String("category", category),
				zap.Int("count", len(popularRooms)),
				zap.Int("curr", len(list)),
			)

			for _, v := range list {
				if len(rec) >= hotNum {
					break
				}

				excludeIds = append(excludeIds, v.RoomId)

				rec = append(rec, v)
			}

			for i := range rec {
				if labelUrl, err := h.am.GetLabelUrl(ctx, asset.LabelHotTopKey(i+1)); err == nil {
					rec[i].LabelUrl = labelUrl
				}
			}
		case recommender.CategoryLiveNewbie:
			newAnchorRooms := make([]live.Room, 0)

			// 获取新人直播间
			res2, err := h.lm.GetRoomListByFlag(ctx, live.RoomFlagNewAnchor, page, num)

			if err != nil {
				h.logger.Error("获取热门页签新人直播间错误", zap.Error(err))
				continue
			}

			// !测试环境补充新人数据
			if dbg.Ing() && len(res2) == 0 {
				res2, _ = h.lm.GetPopularRooms(ctx, num, page)
			}

			newAnchorRooms = append(newAnchorRooms, res2...)

			list, err := h.makeRooms(ctx, userId, nil, newAnchorRooms)

			if err != nil {
				continue
			}

			for _, v := range list {
				// 过滤掉已经在热门直播间中的直播间
				if lo.Contains(excludeIds, v.RoomId) {
					continue
				}

				if len(latest) >= newbieNum {
					break
				}

				latest = append(latest, v)
			}

			for i := range latest {
				if labelUrl, err := h.am.GetLabelUrl(ctx, asset.LabelTopKey(i+1)); err == nil {
					latest[i].LabelUrl = labelUrl
				}
			}

			if luckyroom.Open() && luckyroom.Accept(app.DeviceType(ctx), app.Version(ctx), app.BuildId(ctx)) {
				// 第一个位置放幸运礼物体验直播间
				res, err := h.lm.GetRoomListByFlag(ctx, live.RoomFlagLucky, 1, 1)

				if err == nil && len(res) > 0 {
					sr, err := mixer.Room(ctx, userId, h.ug, h.fg, h.sm, &res[0])

					if err == nil {
						if labelUrl, err := h.am.GetLabelUrl(ctx, asset.LabelEvento); err == nil {
							sr.LabelUrl = labelUrl
						}
						// 在latest的头部插入
						latest = append([]types.Room{*sr}, latest...)
					}
				}
			}
		}
	}

	res, _ := h.bm.List(ctx, app.DeviceType(ctx), banner.SceneHomeHot, userId)

	banners := make([]types.Banner, 0)

	for _, v := range res {
		banners = append(banners, types.Banner{
			Title:    v.Title,
			ImageUrl: v.ImageUrl,
			LinkUrl:  v.LinkUrl,
		})
	}

	// 获取火箭位直播间
	var rocketRooms = make([]types.FeedRocketItem, 0)
	rocketRoomList, err := h.rm.GetRocketList(ctx)
	if err == nil {
		for _, v := range rocketRoomList {
			room, err := h.makeRooms(ctx, userId, []string{v.RoomId}, nil)
			if err != nil || len(room) == 0 {
				continue
			}
			if h.rm.IsSender(ctx, room[0].SessionId, room[0].User.UserId) {
				room[0].User.AvatarBorder = rocket.AvatarBorder
			}
			userInfo, err := h.ug.Account(ctx, v.SenderUserId)
			if err != nil {
				continue
			}
			rocketRooms = append(rocketRooms, types.FeedRocketItem{
				Room:       room[0],
				SenderUser: mixer.UserWithExt(ctx, userInfo),
			})
		}
	} else {
		h.logger.Error("获取热门页签火箭位直播间错误", zap.Error(err))
	}

	return &types.FeedHotResponse{
		Banner: banners,
		Rec:    rec,
		Latest: latest,
		Rocket: rocketRooms,
	}, nil
}

// @Tags Feed
// @Summary ✅热门直播
// @Description 热门直播
// @Produce json
// @Security HeaderAuth
// @Param param query types.FeedPopularRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FeedPopularResponse}
// @Router /api/v1/feed/popular [get]
func (h *Feed) Popular(ctx *api.Context, req types.FeedPopularRequest) (*types.FeedPopularResponse, error) {
	userId := ""
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	page := req.Page
	eid := req.Eid

	if page <= 0 {
		page = 1
	}

	roomIds, err := h.lm.GetPopular(ctx, 20, page)

	if err != nil {
		h.logger.Error("GetHot", zap.Error(err))
		return nil, err
	}

	// eid过滤
	eids := strings.Split(eid, ",")

	var ids []string

	if len(eids) > 0 {
		for _, v := range roomIds {
			v := strings.Trim(v, " ")
			if !lo.Contains(eids, v) {
				ids = append(ids, v)
			}
		}
	} else {
		ids = roomIds
	}

	list, err := h.makeRooms(ctx, userId, ids, nil)

	if err != nil {
		return nil, err
	}

	return &types.FeedPopularResponse{
		List: list,
	}, nil
}

func (h *Feed) makeRooms(ctx context.Context, userId string, roomIds []string, rooms []live.Room) ([]types.Room, error) {
	logger := h.logger.With(
		zap.String("userId", userId),
	)

	res := make([]types.Room, 0)

	lrs := make([]live.Room, 0)
	anchorIds := make([]string, 0)

	for _, v := range rooms {
		anchorIds = append(anchorIds, v.UserId)

		lrs = append(lrs, v)
	}

	for _, roomId := range roomIds {
		v, err := h.lm.Room2(roomId)

		if err != nil {
			continue
		}

		anchorIds = append(anchorIds, v.UserId)

		lrs = append(lrs, *v)
	}

	shouldShowAnchorIds, err := h.urm.FilterBlockList(ctx, userId, anchorIds)

	if err != nil {
		logger.Error("Feed直播间", zap.Error(err))

		// 如果获取失败，直接展示
		shouldShowAnchorIds = anchorIds
	}

	h.logger.Debug(
		"Feed直播间",
		zap.String("userId", userId),
		zap.Int("count", len(anchorIds)),
		zap.Int("curr", len(shouldShowAnchorIds)),
	)

	for _, v := range lrs {
		if !v.IsLiving() {
			continue
		}

		if v.Pause.Paused() {
			continue
		}

		if !lo.Contains(shouldShowAnchorIds, v.UserId) {
			continue
		}

		// 默认列表里不显示幸运礼物体验直播间和游戏直播间
		if v.IsLuckyRoom() || v.IsGameRoom() {
			continue
		}

		room, err := mixer.Room(ctx, userId, h.ug, h.fg, h.sm, &v)

		if err != nil {
			continue
		}

		res = append(res, *room)
	}

	return res, nil
}

// @Tags Feed
// @Summary ✅热门火箭位
// @Description 热门火箭位
// @Produce json
// @Security HeaderAuth
// @Param param query types.FeedRocketRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.FeedRocketResponse}
// @Router /api/v1/feed/rocket [get]
func (h *Feed) Rocket(ctx *api.Context, req types.FeedRocketRequest) (*types.FeedRocketResponse, error) {
	userId := ""
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	list, err := h.rm.GetRocketList(ctx)
	if err != nil {
		return nil, err
	}

	var res = make([]types.FeedRocketItem, 0)
	for _, v := range list {
		room, err := h.makeRooms(ctx, userId, []string{v.RoomId}, nil)
		if err != nil || len(room) == 0 {
			continue
		}

		if h.rm.IsSender(ctx, room[0].SessionId, room[0].User.UserId) {
			room[0].User.AvatarBorder = rocket.AvatarBorder
		}

		userInfo, err := h.ug.Account(ctx, v.SenderUserId)
		if err != nil {
			continue
		}
		res = append(res, types.FeedRocketItem{
			Room:       room[0],
			SenderUser: mixer.UserWithExt(ctx, userInfo),
		})
	}

	return &types.FeedRocketResponse{
		List: res,
	}, nil
}
