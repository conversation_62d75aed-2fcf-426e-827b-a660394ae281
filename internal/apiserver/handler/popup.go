package handler

import (
	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/popup"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

// InvokePopupHandler 弹窗
func InvokePopupHandler(
	pm *popup.Manager,
	r *api.Router,
	vnd log.Vendor,
) *Popup {
	h := NewPopup(pm, vnd.Scope("api.popup"))

	ar := r.TryAuth()
	{
		// 弹窗列表
		ar.GET("/popup/homepage", api.Generic(h.PopupHomepage))
	}

	return h
}

type Popup struct {
	logger *zap.Logger
	pm     *popup.Manager
}

func NewPopup(pm *popup.Manager, logger *zap.Logger) *Popup {
	return &Popup{
		pm:     pm,
		logger: logger,
	}
}

// @Tags 弹窗接口
// @Summary 首页弹窗
// @Description 首页弹窗列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.PopupListResponse}
// @Router /api/v1/popup/homepage [get]
func (h *Popup) PopupHomepage(ctx *api.Context, _ api.EmptyReq) (*types.PopupListResponse, error) {
	var userId string
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}
	var res []types.PopupItem
	list := h.pm.PopupList(ctx, popup.SceneHomepage, userId)
	for _, v := range list {
		b, _ := sonic.Marshal(v.Content)

		res = append(res, types.PopupItem{
			Id:       v.Id,
			Interval: v.Interval,
			Type:     int(v.Type),
			Content:  string(b),
		})
	}
	return &types.PopupListResponse{
		Popups: res,
	}, nil
}
