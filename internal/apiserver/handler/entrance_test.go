package handler

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

func TestXxx(t *testing.T) {
	text := "我正在#%s Live观看【%s】的精彩直播，来和我一起支持Ta吧。复制链接打开【%s】，直接观看直播！http://192.168.31.152:8080/1S3ZjMAVZN1k"
	re := regexp.MustCompile(`(https?://[^\s]+)`)

	urls := re.FindAllString(text, -1)

	u := urls[0]

	// 解析url
	ud, err := url.Parse(u)

	paths := strings.Split(ud.Path, "/")

	fmt.Println(paths[len(paths)-1], err)

}

func TestXxx2(t *testing.T) {
	now.WeekStartDay = time.Monday

	ctzIn := ctz.New(ctz.BR, ctz.Brazil.String())

	fmt.Println(ctzIn)

	cc := ctz.Parse(ctzIn)
	nn := now.New(cc.In(time.Now()))

	fmt.Println(nn.Weekday(), nn.WeekStartDay, nn.BeginningOfWeek().Format("2006-01-02 15:04:05"), nn.BeginningOfDay().Format("2006-01-02 15:04:05"))
}
