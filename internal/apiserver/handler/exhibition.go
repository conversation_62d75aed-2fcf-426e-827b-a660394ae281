package handler

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/exhibition"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func InvokeExhibitionHandler(r *api.Router, ug user.Getter, gm *gift.Manager, em *exhibition.Manager) {
	s := &Exhibition{ug: ug, gm: gm, em: em}
	ar := r.With<PERSON>uth()
	{
		ar.GET("/gift/exhibition/home", api.Generic(s.Home))
		ar.GET("/gift/exhibition/detail", api.Generic(s.Detail))
		ar.GET("/gift/exhibition/medal", api.Generic(s.Medal))
	}
}

type Exhibition struct {
	ug user.Getter
	gm *gift.Manager
	em *exhibition.Manager
}

// @Tags 礼物展馆
// @Summary 概览信息
// @Description 礼物展馆主界面
// @Produce json
// @Security HeaderAuth
// @Param param query types.ExhibitionHomeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ExhibitionHomeResponse}
// @Router /api/v1/gift/exhibition/home [get]
func (s *Exhibition) Home(ctx *api.Context, req types.ExhibitionHomeRequest) (*types.ExhibitionHomeResponse, error) {
	acc, err := s.ug.Account(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	items, err := s.em.Items(ctx, acc.UserId, time.Now())
	if err != nil {
		return nil, err
	}

	resp := types.ExhibitionHomeResponse{Nickname: acc.Nickname}

	var lighted, unlighted []*exhibition.Item
	for _, item := range items {
		if item.Received >= item.Threshold {
			lighted = append(lighted, item)
		} else {
			unlighted = append(unlighted, item)
		}
	}

	if items, err := s.exhibitionItems(lighted); err != nil {
		return nil, err
	} else {
		resp.Lighted = items
	}

	if items, err := s.exhibitionItems(unlighted); err != nil {
		return nil, err
	} else {
		resp.Unlighted = items
	}

	return &resp, nil
}

func (s *Exhibition) exhibitionItems(in []*exhibition.Item) ([]*types.ExhibitionItem, error) {
	out := make([]*types.ExhibitionItem, 0, len(in))
	for _, raw := range in {
		gInfo, err := s.gm.GiftById(raw.GiftId)
		if err != nil {
			return nil, err
		}
		item := &types.ExhibitionItem{
			Gift:      mixer.LiteGift(gInfo),
			Lighted:   raw.Received >= raw.Threshold,
			Threshold: raw.Threshold,
			Received:  raw.Received,
		}
		if raw.Task != nil {
			item.Task = &types.ExhibitionTask{
				StageId:   raw.Task.StageId,
				Finished:  raw.Task.Received >= raw.Task.Threshold,
				Threshold: raw.Task.Threshold,
				Received:  raw.Task.Received,
				Rewards:   raw.Task.Rewards,
				Ceiling:   raw.Task.Ceiling,
			}
		}
		out = append(out, item)
	}
	return out, nil
}

// @Tags 礼物展馆
// @Summary 礼物的详细信息
// @Description 礼物展馆榜单界面
// @Produce json
// @Security HeaderAuth
// @Param param query types.ExhibitionDetailRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ExhibitionDetailResponse}
// @Router /api/v1/gift/exhibition/detail [get]
func (s *Exhibition) Detail(ctx *api.Context, req types.ExhibitionDetailRequest) (*types.ExhibitionDetailResponse, error) {
	return &types.ExhibitionDetailResponse{}, nil
}

// @Tags 礼物展馆
// @Summary 集星铭牌信息
// @Description 礼物展馆铭牌界面
// @Produce json
// @Security HeaderAuth
// @Param param query types.ExhibitionMedalRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ExhibitionMedalResponse}
// @Router /api/v1/gift/exhibition/medal [get]
func (s *Exhibition) Medal(ctx *api.Context, req types.ExhibitionMedalRequest) (*types.ExhibitionMedalResponse, error) {
	return &types.ExhibitionMedalResponse{}, nil
}
