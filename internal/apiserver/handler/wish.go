package handler

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func InvokeWishHandler(
	r *api.Router,
) *Wish {
	h := NewWish()

	ar := r.<PERSON>()
	{
		ar.GET("/wish/list", api.Generic(h.List))
	}

	return h
}

type Wish struct {
}

func NewWish() *Wish {
	return &Wish{}
}

// @Tags 心愿
// @Summary 心愿礼物
// @Description 心愿礼物
// @Produce json
// @Security HeaderAuth
// @Param param query types.WishListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.WishListResponse}
// @Router /api/v1/wish/list [get]
func (h *Wish) List(ctx *api.Context, req types.WishListRequest) (*types.WishListResponse, error) {
	return &types.WishListResponse{}, nil
}
