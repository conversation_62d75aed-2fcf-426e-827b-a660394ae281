package handler

import (
	"time"

	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction/sredpacket"

	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type LivePollRequest struct {
	RoomId       string `form:"roomId" binding:"required"`
	RoomWidgetId string `form:"roomWidgetId"`
}

type LivePollResponse struct {
	ServerMs   int64                 `json:"serverMs"`   // 服务器时间戳，毫秒级时间戳
	RedPacket  *sredpacket.RedPacket `json:"redpacket"`  // 红包业务数据
	RedPacket2 *sredpacket.RedPacket `json:"redpacket2"` // 红包业务数据
	RoomWidget map[string]string     `json:"roomWidget"` // 直播间挂件数据
}

// @Tags 直播间
// @Summary 直播间轮训
// @Description 直播间轮训
// @Accept json
// @Produce json
// @Param roomId query string true "房间ID"
// @Success 200 {object} codec.Response{data=LivePollResponse}
// @Router /api/v1/live/poll [get]
func (h *Live) Poll(ctx *api.Context, req LivePollRequest) (*LivePollResponse, error) {
	ri, err := h.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	var (
		sid = ri.SessionId.Hex()
		at  = time.Now()
		out = LivePollResponse{
			ServerMs:  at.UnixMilli(),
			RedPacket: &sredpacket.RedPacket{List: []*sredpacket.PacketItem{}},
		}
	)

	if rp, err := h.rh.Poll(ctx, req.RoomId, sid, at); err != nil {
		h.logger.Debug("red packet poll fail", zap.Error(err))
	} else {
		out.RedPacket2 = rp
	}

	// 直播间挂件数据
	if len(req.RoomWidgetId) > 0 {
		uac, _ := ctx.User()
		if rw, err := h.rw.Poll(ctx, req.RoomWidgetId, ri.UserId, uac); err != nil {
			h.logger.Error("room widget poll fail", zap.Error(err))
		} else {
			out.RoomWidget = map[string]string{
				req.RoomWidgetId: rw,
			}
		}
	}

	return &out, nil
}
