package handler

import (
	"testing"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

func TestNow(t *testing.T) {
	nt := time.Now()

	cc := ctz.Parse("BR:America/Manaus")

	currentTzTime := now.New(cc.In(nt)).Format("2006-01-02 15:04:05")

	needTzTime := now.New(nt.In(ctz.Brazil)).Format("2006-01-02 15:04:05")

	println(currentTzTime)
	println(needTzTime)
	println(currentTzTime == needTzTime)
}
