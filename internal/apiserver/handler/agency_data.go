package handler

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 公会数据
func InvokeAgencyDataHandler(
	r *api.Router,
	am *agency.Manager,
	ug user.Getter,
	lsm *ls.Manager,
	anm *anchor.Manager,
	vnd log.Vendor,
) *AgencyData {
	h := NewAgencyData(am, ug, lsm, anm, vnd.Scope("api.agency_data"))

	ar := r.WithAuth()
	{
		// 数据总览
		ar.GET("/agency/data/overview", api.Generic(h.Overview))
		// 日期列表
		ar.GET("/agency/data/day/list", api.Generic(h.DayList))
		// 日期详细
		ar.GET("/agency/data/day/detail", api.Generic(h.DayDetail))
		// 主播列表
		ar.GET("/agency/data/anchor/list", api.Generic(h.AnchorList))
		// 主播详细
		ar.GET("/agency/data/anchor/detail", api.Generic(h.AnchorDetail))
	}

	return h
}

type AgencyData struct {
	am     *agency.Manager
	ug     user.Getter
	lsm    *ls.Manager
	anm    *anchor.Manager
	logger *zap.Logger
}

func NewAgencyData(
	am *agency.Manager,
	ug user.Getter,
	lsm *ls.Manager,
	anm *anchor.Manager,
	logger *zap.Logger,
) *AgencyData {
	return &AgencyData{
		am:     am,
		ug:     ug,
		lsm:    lsm,
		anm:    anm,
		logger: logger,
	}
}

// @Tags 公会数据
// @Summary 数据总览
// @Description 数据总览
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyDataOverviewRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyDataOverviewResponse}
// @Router /api/v1/agency/data/overview [get]
func (h *AgencyData) Overview(ctx *api.Context, req types.AgencyDataOverviewRequest) (*types.AgencyDataOverviewResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	agencyUserId := uac.UserId
	period := req.Period

	agency, err := h.am.MyOwnAgency(ctx, agencyUserId)

	if err != nil {
		return nil, err
	}

	startTime, endTime, err := ls.FormatTimeRange(
		period,
		ctz.New(app.Country(ctx), app.Timezone(ctx).String()),
		time.Unix(req.StartTime, 0),
		time.Unix(req.EndTime, 0),
	)

	if err != nil {
		return nil, err
	}

	h.logger.Info(
		"Overview",
		zap.String("period", period),
		zap.Time("startTime", startTime),
		zap.Time("endTime", endTime),
		zap.Any("agency", agency),
	)

	ad, err := h.lsm.GetAgencyOverview(ctx, int64(agency.ID), startTime, endTime)

	if err != nil {
		return nil, err
	}

	return &types.AgencyDataOverviewResponse{
		AgencyDataOverview: types.AgencyDataOverview{
			AnchorAmount:     ad.LuckDiamond, // TODO 客户端显示幸运礼物流水时读取了此字段，改成返回幸运礼物流水（原AnchorAmount）
			LuckDiamond:      ad.LuckDiamond,
			AnchorIncome:     ad.AnchorIncome,
			AgencyIncome:     ad.AgencyIncome,
			LiveAnchorCount:  ad.LiveAnchorCount,
			ValidAnchorCount: ad.ValidAnchorCount,
			NewAnchorCount:   ad.NewAnchorCount,
		},
	}, nil
}

// @Tags 公会数据
// @Summary 日期列表
// @Description 日期列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyDataDayListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyDataDayListResponse}
// @Router /api/v1/agency/data/day/list [get]
func (h *AgencyData) DayList(ctx *api.Context, req types.AgencyDataDayListRequest) (*types.AgencyDataDayListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	agencyUserId := uac.UserId
	cursor := req.Cursor
	agg := req.Agg
	anchorId := req.AnchorId

	var user *types.UserWithExt
	var isNewAnchor bool

	if anchorId != "" {
		acc, err := h.ug.Account(ctx, anchorId)

		if err != nil {
			return nil, err
		}
		user = mixer.UserWithExt(ctx, acc)

		flags, err := h.anm.GetAnchorFlags(anchorId)

		if err == nil {
			isNewAnchor = flags.IsNewAnchorFlag()
		}
	}

	agency, err := h.am.MyOwnAgency(ctx, agencyUserId)

	if err != nil {
		return nil, err
	}

	ctzIn := ctz.New(app.Country(ctx), app.Timezone(ctx).String())
	cc := ctz.Parse(ctzIn)

	startTime, endTime, err := ls.FormatTimeRange(
		"custom",
		ctzIn,
		time.Unix(req.StartTime, 0),
		time.Unix(req.EndTime, 0),
	)

	if err != nil {
		return nil, err
	}

	var cursorTime time.Time

	if cursor == "" {
		cursorTime = now.New(cc.In(time.Now())).BeginningOfDay().AddDate(0, 0, 1)
	} else {
		timestamp, err := strconv.ParseInt(cursor, 10, 64)

		if err != nil {
			return nil, err
		}

		cursorTime = cc.In(time.Unix(timestamp, 0))
	}

	aggStartTime := startTime
	aggEndTime := endTime

	h.logger.Info(
		"DayList",
		zap.Time("startTime", startTime),
		zap.Time("endTime", endTime),
		zap.Time("cursorTime", cursorTime),
		zap.Any("agency", agency),
		zap.String("cursor", cursor),
		zap.Any("agg", agg),
		zap.String("anchorId", anchorId),
	)

	// 分页范围
	if cursorTime.Before(endTime) {
		endTime = cursorTime
	}

	// 分页
	pageSize := 0
	list := make([]types.AgencyDataDay, 0)
	aggs := make([]types.Summary, 0)
	m := make(map[string]int)

	for endTime.After(startTime) {
		if pageSize >= 20 {
			break
		}

		ad, err := h.lsm.AgencyDay(
			ctx,
			int64(agency.ID),
			anchorId,
			endTime,
		)

		if err == nil && ad.HasData() {
			pageSize++
			list = append(list, types.AgencyDataDay{
				AnchorAmount:     ad.AnchorAmount,
				LuckDiamond:      ad.LuckDiamond,
				GiftDiamond:      ad.GiftDiamond,
				AnchorIncome:     ad.AnchorIncome,
				AgencyIncome:     ad.AgencyIncome,
				LiveAnchorCount:  ad.LiveAnchorCount,
				ValidAnchorCount: ad.ValidAnchorCount,
				NewAnchorCount:   ad.NewAnchorCount,
				ValidDuration:    ad.ValidDuration,
				Time:             ad.Time,
			})

			// 对应是否已经查询过总值
			key := now.New(endTime).BeginningOfDay().Format("200601")
			switch agg {
			case 0:
				aggStartTime = now.New(endTime).BeginningOfMonth()
				aggEndTime = now.New(endTime).EndOfMonth()
			case 3:
				key = "total"
			default:
				return nil, biz.NewError(biz.ErrInvalidParam, "Invalid Agg")
			}

			if _, ok := m[key]; !ok {
				ad, err := h.lsm.AgencyPeriod(
					ctx,
					int64(agency.ID),
					anchorId,
					aggStartTime,
					aggEndTime,
				)

				if err == nil {
					aggs = append(aggs, types.Summary{
						Time:          ad.Time,
						Incomes:       ad.AgencyIncome,
						Amount:        ad.AnchorAmount,
						LuckDiamond:   ad.LuckDiamond,
						GiftDiamond:   ad.GiftDiamond,
						AnchorIncome:  ad.AnchorIncome,
						AgencyIncome:  ad.AgencyIncome,
						LiveDayCount:  ad.LiveDayCount,
						ValidDayCount: ad.ValidDayCount,
						ValidDuration: ad.ValidDuration,
					})

					m[key] = 1
				}
			}
		}

		endTime = endTime.AddDate(0, 0, -1)

		cursor = strconv.FormatInt(now.New(endTime).BeginningOfDay().Unix(), 10)
	}

	return &types.AgencyDataDayListResponse{
		Cursor:      cursor,
		Aggs:        aggs,
		List:        list,
		User:        user,
		IsNewAnchor: isNewAnchor,
	}, nil
}

// @Tags 公会数据
// @Summary 日期详细
// @Description 日期详细
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyDataDayDetailRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyDataDayDetailResponse}
// @Router /api/v1/agency/data/day/detail [get]
func (h *AgencyData) DayDetail(ctx *api.Context, req types.AgencyDataDayDetailRequest) (*types.AgencyDataDayDetailResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	agencyUserId := uac.UserId

	agency, err := h.am.MyOwnAgency(ctx, agencyUserId)

	if err != nil {
		return nil, err
	}

	ctzIn := ctz.New(app.Country(ctx), app.Timezone(ctx).String())
	cc := ctz.Parse(ctzIn)

	startTime, endTime, err := ls.FormatTimeRange(
		"custom",
		ctzIn,
		time.Unix(req.StartTime, 0),
		time.Unix(req.EndTime, 0),
	)

	if err != nil {
		return nil, err
	}

	ad, err := h.lsm.AgencyPeriod(
		ctx,
		int64(agency.ID),
		"",
		startTime,
		endTime,
	)

	if err != nil {
		return nil, err
	}

	return &types.AgencyDataDayDetailResponse{
		AgencyDataDay: types.AgencyDataDay{
			AnchorAmount:     ad.AnchorAmount,
			LuckDiamond:      ad.LuckDiamond,
			GiftDiamond:      ad.GiftDiamond,
			AnchorIncome:     ad.AnchorIncome,
			AgencyIncome:     ad.AgencyIncome,
			LiveAnchorCount:  ad.LiveAnchorCount,
			ValidAnchorCount: ad.ValidAnchorCount,
			NewAnchorCount:   ad.NewAnchorCount,
			ValidDuration:    ad.ValidDuration,
			Time:             cc.In(startTime).Unix(),
		},
	}, nil
}

// @Tags 公会数据
// @Summary 主播列表
// @Description 主播列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyDataAnchorListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyDataAnchorListResponse}
// @Router /api/v1/agency/data/anchor/list [get]
func (h *AgencyData) AnchorList(ctx *api.Context, req types.AgencyDataAnchorListRequest) (*types.AgencyDataAnchorListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	aggs := make([]types.Summary, 0)
	list := make([]types.AgencyDataAnchor, 0)

	agencyUserId := uac.UserId
	cursor := req.Cursor
	agg := req.Agg

	agency, err := h.am.MyOwnAgency(ctx, agencyUserId)

	if err != nil {
		return nil, err
	}

	ctzIn := ctz.New(app.Country(ctx), app.Timezone(ctx).String())
	cc := ctz.Parse(ctzIn)

	startTime, endTime, err := ls.FormatTimeRange(
		"custom",
		ctzIn,
		time.Unix(req.StartTime, 0),
		time.Unix(req.EndTime, 0),
	)

	if err != nil {
		return nil, err
	}

	var maxTotalDiamond int64

	var anchorIds []string

	// 0按月 1按周 2按天 3总计
	switch agg {
	case 0:
		var lastStartTime time.Time
		var lastEndTime time.Time

		cs := strings.Split(cursor, "|")

		if len(cs) == 3 {
			lastStartTimestamp, err := strconv.ParseInt(cs[0], 10, 64)
			if err != nil {
				return nil, err
			}

			lastEndTimestamp, err := strconv.ParseInt(cs[1], 10, 64)
			if err != nil {
				return nil, err
			}

			maxTotalDiamond, err = strconv.ParseInt(cs[2], 10, 64)
			if err != nil {
				return nil, err
			}

			lastStartTime = cc.In(time.Unix(lastStartTimestamp, 0))
			lastEndTime = cc.In(time.Unix(lastEndTimestamp, 0))
		} else {
			lastStartTime = now.New(endTime).BeginningOfMonth()
			lastEndTime = now.New(endTime).EndOfMonth()
		}

		var pageSize int64

		for {
			if pageSize >= 20 || lastStartTime.Before(startTime) {
				break
			}

			anchorIds, err = h.lsm.AgencyAnchorList(
				ctx,
				int64(agency.ID),
				lastStartTime,
				lastEndTime,
				maxTotalDiamond,
			)

			if err != nil {
				return nil, err
			}

			if len(anchorIds) == 0 {
				lastStartTime = now.New(lastStartTime.AddDate(0, 0, -1)).BeginningOfMonth()
				lastEndTime = now.New(lastStartTime).EndOfMonth()
				continue
			}

			ad, err := h.lsm.AgencyPeriod(
				ctx,
				int64(agency.ID),
				"",
				lastStartTime,
				lastEndTime,
			)

			if err == nil {
				aggs = append(aggs, types.Summary{
					Time:          ad.Time,
					Incomes:       ad.AgencyIncome,
					Amount:        ad.AnchorAmount,
					LuckDiamond:   ad.LuckDiamond,
					GiftDiamond:   ad.GiftDiamond,
					AnchorIncome:  ad.AnchorIncome,
					AgencyIncome:  ad.AgencyIncome,
					ValidDuration: ad.ValidDuration,
				})
			}

			if len(anchorIds) > 0 {
				startTimeDay := now.New(lastStartTime).BeginningOfDay().Unix()
				for _, anchorId := range anchorIds {
					if pageSize >= 20 {
						break
					}

					acc, err := h.ug.Account(ctx, anchorId)

					if err != nil {
						h.logger.Error("Account", zap.Error(err))
						continue
					}

					var isNewAnchor bool

					user := mixer.UserWithExt(ctx, acc)

					flags, err := h.anm.GetAnchorFlags(anchorId)

					if err == nil {
						isNewAnchor = flags.IsNewAnchorFlag()
					}

					ad, err := h.lsm.AgencyPeriod(
						ctx,
						int64(agency.ID),
						anchorId,
						lastStartTime,
						lastEndTime,
					)

					if err == nil && ad.HasData() {
						list = append(list, types.AgencyDataAnchor{
							User:          user,
							IsNewAnchor:   isNewAnchor,
							AnchorAmount:  ad.LuckDiamond, // !改成为幸运礼物流水值
							LuckDiamond:   ad.LuckDiamond,
							GiftDiamond:   ad.GiftDiamond,
							AnchorIncome:  ad.AnchorIncome,
							AgencyIncome:  ad.AgencyIncome,
							LiveDayCount:  ad.LiveDayCount, // 只有有主播ID时才会查询开播天和有效天
							ValidDayCount: ad.ValidDayCount,
							ValidDuration: ad.ValidDuration,
							Time:          startTimeDay,
						})

						maxTotalDiamond = ad.AnchorAmount

						// 避免和默认值混淆
						if maxTotalDiamond == 0 {
							maxTotalDiamond = -1
						}

						pageSize++
					}
				}

				cursor = fmt.Sprintf("%d|%d|%d", lastStartTime.Unix(), lastEndTime.Unix(), maxTotalDiamond)
			}
		}
	case 3:
		cs := strings.Split(cursor, "|")

		if len(cs) == 2 {
			maxTotalDiamond, err = strconv.ParseInt(cs[1], 10, 64)

			if err != nil {
				return nil, err
			}
		}

		anchorIds, err = h.lsm.AgencyAnchorList(
			ctx,
			int64(agency.ID),
			startTime,
			endTime,
			maxTotalDiamond,
		)

		if err != nil {
			return nil, err
		}

		ad, err := h.lsm.AgencyPeriod(
			ctx,
			int64(agency.ID),
			"",
			startTime,
			endTime,
		)

		if err == nil {
			aggs = append(aggs, types.Summary{
				Time:          ad.Time,
				Incomes:       ad.AgencyIncome,
				Amount:        ad.AnchorAmount,
				LuckDiamond:   ad.LuckDiamond,
				GiftDiamond:   ad.GiftDiamond,
				AnchorIncome:  ad.AnchorIncome,
				AgencyIncome:  ad.AgencyIncome,
				ValidDuration: ad.ValidDuration,
			})
		}

		if len(anchorIds) > 0 {
			startTimeDay := now.New(startTime).BeginningOfDay().Unix()
			for _, anchorId := range anchorIds {
				acc, err := h.ug.Account(ctx, anchorId)

				if err != nil {
					h.logger.Error("Account", zap.Error(err))
					continue
				}

				var isNewAnchor bool

				user := mixer.UserWithExt(ctx, acc)

				flags, err := h.anm.GetAnchorFlags(anchorId)

				if err == nil {
					isNewAnchor = flags.IsNewAnchorFlag()
				}

				ad, err := h.lsm.AgencyPeriod(
					ctx,
					int64(agency.ID),
					anchorId,
					startTime,
					endTime,
				)

				if err == nil && ad.HasData() {
					list = append(list, types.AgencyDataAnchor{
						User:          user,
						IsNewAnchor:   isNewAnchor,
						AnchorAmount:  ad.LuckDiamond, // !改成为幸运礼物流水值
						LuckDiamond:   ad.LuckDiamond,
						GiftDiamond:   ad.GiftDiamond,
						AnchorIncome:  ad.AnchorIncome,
						AgencyIncome:  ad.AgencyIncome,
						LiveDayCount:  0,
						ValidDayCount: 0,
						ValidDuration: ad.ValidDuration,
						Time:          startTimeDay,
					})

					maxTotalDiamond = ad.AnchorAmount

					// 避免和默认值混淆
					if maxTotalDiamond == 0 {
						maxTotalDiamond = -1
					}
				}
			}

			cursor = fmt.Sprintf("total|%d", maxTotalDiamond)
		}
	default:
		return nil, biz.NewError(biz.ErrInvalidParam, "Invalid Agg")
	}

	h.logger.Info(
		"AnchorList",
		zap.String("cursor", cursor),
		zap.Any("agg", agg),
		zap.String("agencyUserId", agencyUserId),
		zap.Any("anchorIds", anchorIds),
	)

	return &types.AgencyDataAnchorListResponse{
		Cursor: cursor,
		Aggs:   aggs,
		List:   list,
	}, nil
}

// @Tags 公会数据
// @Summary 主播详细
// @Description 主播详细
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyDataAnchorDetailRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyDataAnchorDetailResponse}
// @Router /api/v1/agency/data/anchor/detail [get]
func (h *AgencyData) AnchorDetail(ctx *api.Context, req types.AgencyDataAnchorDetailRequest) (*types.AgencyDataAnchorDetailResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	agencyUserId := uac.UserId
	anchorId := req.AnchorId

	agency, err := h.am.MyOwnAgency(ctx, agencyUserId)

	if err != nil {
		return nil, err
	}

	ctzIn := ctz.New(app.Country(ctx), app.Timezone(ctx).String())
	cc := ctz.Parse(ctzIn)

	startTime, endTime, err := ls.FormatTimeRange(
		"custom",
		ctzIn,
		time.Unix(req.StartTime, 0),
		time.Unix(req.EndTime, 0),
	)

	if err != nil {
		return nil, err
	}

	var user *types.UserWithExt
	var isNewAnchor bool

	if anchorId != "" {
		acc, err := h.ug.Account(ctx, anchorId)

		if err != nil {
			return nil, err
		}
		user = mixer.UserWithExt(ctx, acc)

		flags, err := h.anm.GetAnchorFlags(anchorId)

		if err == nil {
			isNewAnchor = flags.IsNewAnchorFlag()
		}
	}

	ad, err := h.lsm.AgencyPeriod(
		ctx,
		int64(agency.ID),
		anchorId,
		startTime,
		endTime,
	)

	if err != nil {
		return nil, err
	}

	return &types.AgencyDataAnchorDetailResponse{
		AgencyDataAnchor: types.AgencyDataAnchor{
			User:          user,
			IsNewAnchor:   isNewAnchor,
			AnchorAmount:  ad.AnchorAmount,
			LuckDiamond:   ad.LuckDiamond,
			GiftDiamond:   ad.GiftDiamond,
			AnchorIncome:  ad.AnchorIncome,
			AgencyIncome:  ad.AgencyIncome,
			LiveDayCount:  ad.LiveDayCount,
			ValidDayCount: ad.ValidDayCount,
			ValidDuration: ad.ValidDuration,
			Time:          cc.In(startTime).Unix(),
		},
	}, nil
}
