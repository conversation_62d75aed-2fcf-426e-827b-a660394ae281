package handler

import (
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/in"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
	"go.uber.org/zap"
)

// InvokeInHandler 互动消息（interactive news）
func InvokeInHandler(
	inm *in.Manager,
	r *api.Router,
	vnd log.Vendor,
) *In {
	h := NewIn(inm, vnd.Scope("api.in"))

	ar := r.WithAuth()
	{
		// 互动消息预览
		ar.GET("/in/view", api.Generic(h.InteractiveView))
		// 互动消息列表
		ar.GET("/in/list", api.Generic(h.InteractiveList))
		// 互动消息未读数忽略
		ar.POST("/in/unread/ignore", api.Custom(h.InteractiveUnreadIgnore))
	}

	return h
}

type In struct {
	inm *in.Manager
	// fg     follow.Getter
	// ug     user.Getter
	// us     *ufind.Indexer
	logger *zap.Logger
}

func NewIn(inm *in.Manager, logger *zap.Logger) *In {
	return &In{
		inm:    inm,
		logger: logger,
	}
}

// @Tags 消息
// @Summary 互动消息概览
// @Description 互动消息概览，包括最后一条消息提示内容，最后一条消息时间，新消息数量
// @Produce json
// @Security HeaderAuth
// @Param param query types.InViewRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.InViewResponse}
// @Router /api/v1/in/view [get]
func (h *In) InteractiveView(ctx *api.Context, req types.InViewRequest) (*types.InViewResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		lastMessage string
		lastTime    int64
	)
	res, newCount, err := h.inm.GetInteractiveView(ctx, uac.UserId)
	if err != nil || res.Id == 0 {
		return nil, biz.NewError(biz.ErrNoInteractiveView, "no interactive view")
	}

	if res != nil {
		switch res.Type {
		case in.MessageInteractiveTypeMomentLike:
			lastMessage = i3n.T(ctx, "%s liked your post", res.FromUser.Nickname)
		case in.MessageInteractiveTypeCommentLike:
			lastMessage = i3n.T(ctx, "%s liked your comment", res.FromUser.Nickname)
		case in.MessageInteractiveTypeMomentComment:
			lastMessage = i3n.T(ctx, "%s commented on your post", res.FromUser.Nickname)
		case in.MessageInteractiveTypeCommentReply:
			lastMessage = i3n.T(ctx, "%s replied to your comment", res.FromUser.Nickname)
		case in.MessageInteractiveTypeAt:
			lastMessage = i3n.T(ctx, "%s mentioned you", res.FromUser.Nickname)
		}
		lastTime = res.CreateTime
	}

	return &types.InViewResponse{
		LastMessage: lastMessage,
		LastTime:    lastTime,
		NewCount:    newCount,
	}, nil
}

// @Tags 消息
// @Summary 互动未读消息数清空
// @Description 互动未读消息数清空
// @Produce json
// @Security HeaderAuth
// @Param param query types.InUnreadIgnoreRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.InUnreadIgnoreResponse}
// @Router /api/v1/in/unread/ignore [POST]
func (h *In) InteractiveUnreadIgnore(ctx *gin.Context) error {
	uac, err := auth.User(ctx)
	if err != nil {
		return err
	}
	h.inm.UnreadIgnore(ctx, uac.UserId)
	codec.MakeResp(ctx, 0, "", nil)
	return nil
}

// @Tags 消息
// @Summary 互动消息
// @Description 互动消息列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.InListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.InListResponse}
// @Router /api/v1/in/list [get]
func (h *In) InteractiveList(ctx *api.Context, req types.InListRequest) (*types.InListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if req.Count <= 0 || req.Count >= 50 {
		req.Count = 10
	}

	res, err := h.inm.GetInteractiveList(ctx, uac.UserId, req.Type, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	list := make([]types.InMessageItem, 0, len(res))
	for _, v := range res {
		i := types.InMessageItem{
			Type: v.Type,
			Mid:  v.Mid,
			FromUser: &types.InUser{
				UserWithExt: *mixer.UserWithExt(ctx, v.FromUser),
				FollowState: *mixer.FollowState(ctx, uac.UserId, v.FromUser.UserId),
			},
			Thumbnail:  v.Thumbnail,
			MomentText: v.MomentText,
			CreateTime: v.CreateTime,
		}

		if v.CommentInfo != nil {
			i.CommentInfo = &types.CommentInfo{
				Mid:       v.CommentInfo.Mid,
				ParentId:  v.CommentInfo.ParentId,
				TargetId:  v.CommentInfo.TargetId,
				CId:       v.CommentInfo.CId,
				Text:      v.CommentInfo.Text,
				TextExtra: v.CommentInfo.TextExtra,
				IsLike:    v.CommentInfo.IsLike,
				FromUser:  mixer.User(ctx, v.CommentInfo.FromUser),
			}
		}

		list = append(list, i)
	}

	return &types.InListResponse{
		List:   list,
		Cursor: req.Cursor + 20,
	}, nil
}
