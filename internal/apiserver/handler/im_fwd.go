package handler

import (
	"bytes"
	"io"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/r3labs/sse/v2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func envIsTest() bool {
	hostname, _ := os.Hostname()
	return hostname == "osl-app-t01"
}

func envIsLocal() bool {
	hostname, _ := os.Hostname()
	return hostname == "dev152"
}

var (
	sseServer *sse.Server
)

func initSSEServer(r *api.Router) {
	sseServer = sse.New()
	r.GET("/localTest/sse/endpoint", func(c *gin.Context) {
		sseServer.ServeHTTP(c.Writer, c.Request)
	})
	sseServer.CreateStream("imCallback")
}

func initSSEClient() {
	client := sse.NewClient("https://godzilla-api-test.sskjz.com/api/v1/localTest/sse/endpoint")
	go client.Subscribe("imCallback", func(msg *sse.Event) {
		http.Post("http://127.0.0.1:8080/api/v1/im/callback", "application/json", bytes.NewReader(msg.Data))
	})
}

func forwardImCallback(c *gin.Context) {
	defer c.Next()
	if sseServer != nil {
		var bodyBytes []byte
		if c.Request.Body != nil {
			bodyBytes, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		}
		go sseServer.Publish("imCallback", &sse.Event{
			Data: bodyBytes,
		})
	}
}
