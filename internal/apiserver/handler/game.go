package handler

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 游戏中心
func InvokeGameHandler(
	r *api.Router,
	ug user.Getter,
	vnd log.Vendor,
) *Game {
	h := NewGame(ug, vnd.Scope("api.game"))

	ar := r.WithAuth()
	{
		ar.GET("/game/list", api.Generic(h.List))
	}

	return h
}

type Game struct {
	ug     user.Getter
	logger *zap.Logger
}

func NewGame(
	ug user.Getter,
	logger *zap.Logger,
) *Game {
	return &Game{
		ug:     ug,
		logger: logger,
	}
}

// @Tags 游戏中心
// @Summary ✅游戏列表
// @Description 游戏列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.GameListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.GameListResponse}
// @Router /api/v1/game/list [get]
func (h *Game) List(ctx *api.Context, req types.GameListRequest) (*types.GameListResponse, error) {
	return &types.GameListResponse{
		List: []types.Game{
			{
				Name:    "Parque dos piratas",
				Subhead: "",
				Icon:    "https://godzilla-live-oss.kako.live/banner/laba-icon.png",
				Image:   "https://godzilla-live-oss.kako.live/banner/laba.png",
				Count:   5610,
				Url:     "http://laba.kako.live/login/kako/laba/Default.aspx",
			},
		},
	}, nil
}
