package handler

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/history"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

// 历史
func InvokeHistoryHandler(
	r *api.Router,
	ev ev.Bus,
	hm *history.Manager,
	lm *live.Manager,
	ug user.Getter,
	fg follow.Getter,
	sm *rsd.Stats,
) *History {
	h := NewHistory(ev, hm, lm, ug, fg, sm)

	ar := r.WithAuth()
	{
		ar.POST("/history/write", api.Generic(h.Write))
		ar.POST("/history/delete", api.Generic(h.Delete))
		ar.GET("/history/live/list", api.Generic(h.LiveList))
	}

	return h
}

type History struct {
	ev ev.Bus
	hm *history.Manager
	lm *live.Manager
	ug user.Getter
	fg follow.Getter
	sm *rsd.Stats
}

func NewHistory(
	ev ev.Bus,
	hm *history.Manager,
	lm *live.Manager,
	ug user.Getter,
	fg follow.Getter,
	sm *rsd.Stats,
) *History {
	return &History{
		ev: ev,
		hm: hm,
		lm: lm,
		ug: ug,
		fg: fg,
		sm: sm,
	}
}

// @Tags 历史
// @Summary ✅写入观看历史记录
// @Description 直播观看历史在离开直播间时调用/需在直播间内看了10秒以上
// @Produce json
// @Security HeaderAuth
// @Param param body types.HistoryWriteRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.HistoryWriteResponse}
// @Router /api/v1/history/write [post]
func (h *History) Write(ctx *api.Context, req types.HistoryWriteRequest) (*types.HistoryWriteResponse, error) {
	uac, err := ctx.User()

	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	if err = h.hm.Write(ctx, userId, req.ArtId, req.ArtType); err != nil {
		return nil, err
	}

	switch req.ArtType {
	case history.TypeLive:
		roomId := req.ArtId

		h.ev.Emit(ctx, evt.LiveWatch, &evt.WatchLive{
			UserId: userId,
			RoomId: roomId,
		})
	case history.TypeMoment:
		//momentId := req.ArtId
		//
		//h.ev.Emit(ctx, evt.MomentWatch, &evt.WatchMoment{
		//	UserId:   userId,
		//	MomentId: momentId,
		//})
	}

	return nil, nil
}

// @Tags 历史
// @Summary ✅删除观看历史记录
// @Description 删除观看历史记录
// @Produce json
// @Security HeaderAuth
// @Param param body types.HistoryDeleteRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.HistoryWriteResponse}
// @Router /api/v1/history/delete [post]
func (h *History) Delete(ctx *api.Context, req types.HistoryDeleteRequest) (*types.HistoryWriteResponse, error) {
	uac, err := ctx.User()

	if err != nil {
		return nil, err
	}

	if err := h.hm.Delete(ctx, uac.UserId, req.ArtIds, req.ArtType, req.Clear); err != nil {
		return nil, err
	}

	return &types.HistoryWriteResponse{}, nil
}

// @Tags 历史
// @Summary ✅获取直播观看记录
// @Description 获取直播观看记录
// @Produce json
// @Security HeaderAuth
// @Param param query types.HistoryLiveListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.HistoryLiveListResponse}
// @Router /api/v1/history/live/list [get]
func (h *History) LiveList(ctx *api.Context, req types.HistoryLiveListRequest) (*types.HistoryLiveListResponse, error) {
	uac, err := ctx.User()

	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	list := make([]types.HistoryLive, 0)

	records, err := h.hm.Live(ctx, userId, req.Page)

	if err != nil {
		return nil, err
	}

	for _, v := range records {
		rm, err := h.lm.Room2(v.ArtId)

		if err != nil {
			continue
		}

		room, err := mixer.Room(ctx, userId, h.ug, h.fg, h.sm, rm)

		if err != nil {
			continue
		}

		list = append(list, types.HistoryLive{
			Room:         room,
			UserRelation: mixer.FollowState(ctx, userId, room.User.UserId),
			At:           v.At.Unix(),
		})
	}

	return &types.HistoryLiveListResponse{
		List: list,
	}, nil
}
