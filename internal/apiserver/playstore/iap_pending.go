package playstore

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
)

func (s *IAP) markPending(ctx context.Context, userId, productId string, pat time.Time, receipt string) (*pay.Order, error) {
	order, err := s.findLatest(ctx, userId, productId, pat)
	if err != nil {
		return nil, err
	}

	order, err = s.ps.MakeBusy(ctx, order.TradeNo, pay.WithOrderId(keyReceipt(receipt)))
	if err != nil {
		return nil, err
	}

	_ = s.unlinkOrder(ctx, userId, order.TradeNo)

	return order, nil
}

func (s *IAP) pending(ctx context.Context, receipt string) (*pay.Order, error) {
	order, err := s.ps.Take2(ctx, gwId, keyReceipt(receipt))
	if err != nil {
		if errors.Is(err, pay.ErrOrderNotExists) {
			return nil, nil
		}
		return nil, err
	}
	return order, nil
}
