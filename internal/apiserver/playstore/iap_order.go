package playstore

import (
	"cmp"
	"context"
	"fmt"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"golang.org/x/exp/maps"
)

const (
	keyOrders = "PLAYSTORE:ORDERS:%s" // userId (key=tradeNo,val=productId)
	ttlOrders = 24 * time.Hour
)

func (s *IAP) linkOrder(ctx context.Context, userId, productId, tradeNo string) error {
	key := fmt.Sprintf(keyOrders, userId)
	txp := s.rc.Pipeline()
	txp.HSet(ctx, key, tradeNo, productId)
	txp.Expire(ctx, key, ttlOrders)
	if _, err := txp.Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (s *IAP) unlinkOrder(ctx context.Context, userId, tradeNo string) error {
	return s.rc.HDel(ctx, fmt.Sprintf(keyOrders, userId), tradeNo).Err()
}

func (s *IAP) findLatest(ctx context.Context, userId, productId string, pat time.Time) (*pay.Order, error) {
	return s.findOrder(ctx, userId, productId, pat, latest, func() error {
		return fmt.Errorf("no matching order %s", pat.Format(time.DateTime))
	})
}

func (s *IAP) findOldest(ctx context.Context, userId, productId string, pat time.Time) (*pay.Order, error) {
	return s.findOrder(ctx, userId, productId, pat, oldest, func() error {
		return fmt.Errorf("no nearest order %s", pat.Format(time.DateTime))
	})
}

func latest(a, b string) int { return cmp.Compare(b, a) }
func oldest(a, b string) int { return cmp.Compare(a, b) }

func (s *IAP) findOrder(ctx context.Context, userId, productId string, pat time.Time,
	sort func(a, b string) int, error func() error,
) (*pay.Order, error) {
	orders, err := s.rc.HGetAll(ctx, fmt.Sprintf(keyOrders, userId)).Result()
	if err != nil {
		return nil, err
	}
	tradeNos := maps.Keys(orders)
	slices.SortFunc(tradeNos, sort)
	for _, tradeNo := range tradeNos {
		if orders[tradeNo] == productId {
			if order, err := s.ps.Take(ctx, tradeNo); err == nil && order.Status.Init() {
				if pat.IsZero() || pat.Year() < 2000 {
					return order, nil
				}
				if order.CreatedAt.Before(pat) {
					return order, nil
				}
			}
		}
	}
	return nil, error()
}
