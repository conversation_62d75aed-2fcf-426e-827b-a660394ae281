package playstore

import (
	"encoding/base64"

	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/go/iap/playstore"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

type rtdnMsg struct {
	Data string `json:"data"`
}

type rtdnBody struct {
	Msg rtdnMsg `json:"message"`
}

func (s *apis) notify(ctx *api.Context, req rtdnBody) error {
	bs, _ := base64.StdEncoding.DecodeString(req.Msg.Data)
	var notify playstore.DeveloperNotification
	_ = sonic.Unmarshal(bs, &notify)

	otp := notify.OneTimeProductNotification
	if otp.NotificationType == playstore.OneTimeProductNotificationTypePurchased {
		if order, err := s.iap.Verify(ctx, "", notify.PackageName, otp.SKU, otp.PurchaseToken, false); err != nil {
			s.iap.log.Warn("verify failed in notify", zap.Any("notify", notify), zap.Error(err))
		} else {
			s.iap.log.Info("verify success in notify", zap.Any("notify", notify), zap.Any("order", order))
		}
		return nil
	}

	if otp.NotificationType == playstore.OneTimeProductNotificationTypeCanceled {
		if order, err := s.iap.Cancel(ctx, notify.PackageName, otp.SKU, otp.PurchaseToken); err != nil {
			s.iap.log.Warn("cancel failed in notify", zap.Any("notify", notify), zap.Error(err))
		} else {
			s.iap.log.Info("cancel success in notify", zap.Any("notify", notify), zap.Any("order", order))
		}
		return nil
	}

	vp := notify.VoidedPurchaseNotification
	if vp.OrderId != "" {
		if order, err := s.iap.Refund(ctx, vp.OrderId); err != nil {
			s.iap.log.Warn("refund failed in notify", zap.Any("notify", notify), zap.Error(err))
		} else {
			s.iap.log.Info("refund success in notify", zap.Any("notify", notify), zap.Any("order", order))
		}
		return nil
	}

	s.iap.log.Info("received notify", zap.Any("notify", notify))
	return nil
}
