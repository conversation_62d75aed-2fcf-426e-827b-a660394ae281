package playstore

import (
	"cmp"
	"context"
	"slices"
	"testing"

	"github.com/davecgh/go-spew/spew"
	"gitlab.sskjz.com/go/iap/playstore"
	"golang.org/x/exp/maps"
)

func TestVerify(t *testing.T) {
	cli, err := playstore.New(json<PERSON>ey)
	if err != nil {
		t.Fatal(err)
	}
	resp, _ := cli.VerifyProduct(context.TODO(),
		"live.kako.global",
		"kako_global_standard_7000",
		"bnmhpbjgiilfelhhfaebpjhd.AO-J1Oy7GJ_3GCqO2QahcHkiN0op97S0oFKDLmSBKIoRxxj1dUk8PvM2DcgwJ8ZsAjfi6QqRrN0UaONSIZZm0D7wSNKj1nXGJg",
	)
	spew.Dump(resp)
}

func TestFindOrder(t *testing.T) {
	orders := map[string]string{
		"20240819144031004048": "kako_global_standard_7000",
		"20240817190643838657": "kako_global_standard_7000",
		"20240819161253236629": "kako_global_standard_7000",
	}
	tradeNos := maps.Keys(orders)
	slices.SortFunc(tradeNos, func(a, b string) int { return cmp.Compare(b, a) })
	spew.Dump(tradeNos)
}
