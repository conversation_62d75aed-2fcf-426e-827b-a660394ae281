package playstore

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/dq"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

const (
	chkDelay = time.Hour
)

type ackTask struct {
	UserId      string `json:"userId"`
	Purchased   bool   `json:"purchased"`
	PackageName string `json:"packageName"`
	ProductId   string `json:"productId"`
	Receipt     string `json:"receipt"`
}

func (s *IAP) initAck(qm *dq.Master) {
	s.ack = dq.NewWith[*ackTask](qm, "playstore.purchases")
	if env.APIServer() {
		s.ack.Register(s.doAcknowledge, dq.Channel("acknowledge"),
			dq.LogCost("playstore.purchases.ack", 0.01, 0.1, 0.5, 1, 3, 5),
			dq.Concurrency(32),
		)
	}
}

func (s *IAP) delayChk(ctx context.Context, userId, packageName, productId, receipt string) {
	task := &ackTask{
		UserId:      userId,
		PackageName: packageName,
		ProductId:   productId,
		Receipt:     receipt,
	}
	if err := s.ack.Submit(ctx, chkDelay, task); err != nil {
		s.log.Warn("submit chk task failed", zap.Any("task", task), zap.Error(err))
	}
}

func (s *IAP) delayAck(ctx context.Context, packageName, productId, receipt string) {
	task := &ackTask{
		Purchased:   true,
		PackageName: packageName,
		ProductId:   productId,
		Receipt:     receipt,
	}
	if err := s.ack.Submit(ctx, 0, task); err != nil {
		s.log.Warn("submit ack task failed", zap.Any("task", task), zap.Error(err))
	}
}

func (s *IAP) doAcknowledge(ctx context.Context, task *ackTask) error {
	if task.Purchased {
		if err := s.cli.ConsumeProduct(ctx, task.PackageName, task.ProductId, task.Receipt); err != nil {
			s.log.Warn("acknowledge failed", zap.Any("task", task), zap.Error(err))
			if isHttpErr(err, 400) {
				return nil
			}
			return err
		} else {
			s.log.Info("acknowledge success", zap.Any("task", task))
		}
		return nil
	}
	if _, err := s.Verify(ctx, task.UserId, task.PackageName, task.ProductId, task.Receipt, false); err != nil {
		return err
	} else {
		s.log.Info("verify success in async", zap.Any("task", task))
	}
	return nil
}
