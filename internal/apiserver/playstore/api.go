package playstore

import (
	"cmp"
	"errors"
	"slices"
	"strconv"

	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/sb"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

const gwId = "playstore"

func Invoke(r *api.Router, rc *redi.Client, dm *redi.Mutex, dq *dq.Master, ps *pay.Service, pp payc.Getter, gws *pay.Gateways, sc *sb.Checker, vnd log.Vendor) error {
	iap, err := newIns(rc, dm, ps, sc, vnd.Scope("playstore"))
	if err != nil {
		return err
	}
	iap.initAck(dq)
	gws.Register(gwId, iap)
	s := &apis{ps: ps, pp: pp, iap: iap}
	{
		r.GET("/playstore/config", api.Generic(s.config))
		r.POST("/playstore/notify", api.Request(s.notify))
	}
	ar := r.WithAuth()
	{
		ar.POST("/playstore/order", api.Generic(s.createOrder))
		ar.POST("/playstore/verify", api.Generic(s.verifyOrder))
		ar.POST("/playstore/sync", api.Generic(s.syncOrders))
	}
	return nil
}

type apis struct {
	ps  *pay.Service
	pp  payc.Getter
	iap *IAP
}

type productItem struct {
	Id  string `json:"id"`  // 产品ID
	SKU string `json:"sku"` // 对应充值SKU
}

type configResp struct {
	Products []productItem `json:"products"` // 内购产品列表
}

// @Tags 谷歌内购
// @Summary 获取配置
// @Description 获取配置
// @Produce json
// @Security HeaderAuth
// @Param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=configResp}
// @Router /api/v1/playstore/config [get]
func (s *apis) config(ctx *api.Context, _ api.EmptyReq) (*configResp, error) {
	items := make([]productItem, 0, len(products))
	for id, sku := range products {
		items = append(items, productItem{Id: id, SKU: sku})
	}
	slices.SortFunc(items, func(a, b productItem) int {
		aa, _ := strconv.Atoi(a.SKU)
		bb, _ := strconv.Atoi(b.SKU)
		return cmp.Compare(aa, bb)
	})
	return &configResp{Products: items}, nil
}

type createOrderReq struct {
	Scene     string `json:"scene" binding:"required"`
	ProductId string `json:"productId" binding:"required"`
}

type createOrderResp struct {
	TradeNo string          `json:"tradeNo"` // 内部交易号
	Status  pay.OrderStatus `json:"status"`  // 订单状态
}

// @Tags 谷歌内购
// @Summary 创建订单
// @Description 创建订单
// @Produce json
// @Security HeaderAuth
// @Param param body createOrderReq true "请求参数"
// @Success 200 {object} codec.Response{data=createOrderResp}
// @Router /api/v1/playstore/order [post]
func (s *apis) createOrder(ctx *api.Context, req createOrderReq) (*createOrderResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	sku, has := products[req.ProductId]
	if !has {
		return nil, errors.New("invalid product id")
	}

	if policy, err := s.pp.Policy(ctx, uac.UserId); err != nil {
		return nil, err
	} else if policy.IAP == payc.IAPForbid {
		return nil, sb.ErrNotSandboxUser
	}

	order, _, err := s.ps.Create(ctx, gwId, uac.UserId, req.Scene, sku, pay.USD)
	if err != nil {
		return nil, err
	}

	if err := s.iap.linkOrder(ctx, order.UserId, req.ProductId, order.TradeNo); err != nil {
		return nil, err
	}

	return &createOrderResp{
		TradeNo: order.TradeNo,
		Status:  order.Status,
	}, nil
}

type verifyOrderReq struct {
	ProductId string `json:"productId" binding:"required"`
	Receipt   string `json:"receipt" binding:"required"`
}

type verifyOrderResp struct {
	Status pay.OrderStatus `json:"status"` // 订单状态
}

// @Tags 谷歌内购
// @Summary 验证支付凭证
// @Description 验证支付凭证
// @Produce json
// @Param param body verifyOrderReq true "请求参数"
// @Success 200 {object} codec.Response{data=verifyOrderResp}
// @Router /api/v1/playstore/verify [post]
func (s *apis) verifyOrder(ctx *api.Context, req verifyOrderReq) (*verifyOrderResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	order, err := s.iap.Verify(ctx, uac.UserId, app.BundleId(ctx), req.ProductId, req.Receipt, true)
	if err != nil {
		return nil, err
	}

	return &verifyOrderResp{Status: order.Status}, nil
}

type syncOrdersReq struct {
	Purchases []verifyOrderReq `json:"purchases"`
}

type syncOrdersResp struct {
}

// @Tags 谷歌内购
// @Summary 同步历史订单
// @Description 同步历史订单
// @Produce json
// @Param param body syncOrdersReq true "请求参数"
// @Success 200 {object} codec.Response{data=syncOrdersResp}
// @Router /api/v1/playstore/sync [post]
func (s *apis) syncOrders(ctx *api.Context, req syncOrdersReq) (*syncOrdersResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	for _, req := range req.Purchases {
		if _, has := products[req.ProductId]; has {
			if _, err := s.iap.Verify(ctx, uac.UserId, app.BundleId(ctx), req.ProductId, req.Receipt, true); err != nil {
				s.iap.log.Info("verify failed in sync", zap.String("userId", uac.UserId), zap.Any("purchase", req), zap.Error(err))
			} else {
				s.iap.log.Info("verify success in sync", zap.String("userId", uac.UserId), zap.Any("purchase", req))
			}
		}
	}
	return &syncOrdersResp{}, nil
}
