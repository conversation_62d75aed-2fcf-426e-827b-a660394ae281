package playstore

import (
	"context"
	_ "embed"
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/iap/playstore"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/sb"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"go.uber.org/zap"
	"google.golang.org/api/androidpublisher/v3"
)

//go:embed key.json
var jsonKey []byte

func newIns(rc *redi.Client, dm *redi.Mutex, ps *pay.Service, sc *sb.Checker, log *zap.Logger) (*IAP, error) {
	var cli *playstore.Client
	if !dbg.Ing() {
		var err error
		cli, err = playstore.New(jsonKey)
		if err != nil {
			return nil, err
		}
	}
	iap := &IAP{
		rc:  rc,
		dm:  dm,
		ps:  ps,
		sc:  sc,
		cli: cli,
		log: log,
	}
	if iap.cli == nil {
		go func() {
			var err error
			iap.cli, err = playstore.New(jsonKey)
			if err != nil {
				log.Warn("init playstore client failed", zap.Error(err))
			}
		}()
	}
	return iap, nil
}

type IAP struct {
	rc  *redi.Client
	dm  *redi.Mutex
	ps  *pay.Service
	sc  *sb.Checker
	cli *playstore.Client
	ack dq.Queue[*ackTask]
	log *zap.Logger
}

func (s *IAP) Create(ctx context.Context, order *pay.Order) (*pay.Order, pay.Extras, error) {
	order.OrderId = order.TradeNo
	return order, nil, nil
}

const (
	keyMutex = "PLAYSTORE:VERIFY:%s" // md5(receipt)
)

func (s *IAP) Verify(ctx context.Context, userId, packageName, productId, receipt string, cached bool) (*pay.Order, error) {
	if l, err := s.dm.Lock(ctx, fmt.Sprintf(keyMutex, keyReceipt(receipt))); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}

	if delayed, err := s.delaying(ctx, receipt); err != nil {
		return nil, err
	} else if delayed != "" {
		if cached {
			return &pay.Order{Status: pay.OStatusBusy}, nil
		}
		if userId == "" {
			userId = delayed
		}
	}

	if tradeNo, err := s.resolved(ctx, receipt); err != nil {
		return nil, err
	} else if tradeNo != "" {
		return s.ps.Take(ctx, tradeNo)
	}

	order, err := s.pending(ctx, receipt)
	if err != nil {
		return nil, err
	}

	if order != nil && cached {
		return order, nil
	}

	resp, order2, err := s.verifyOrder(ctx, packageName, productId, receipt)
	if err != nil {
		return nil, err
	} else if order2 != nil {
		return order2, nil
	}

	if userId == "" {
		userId = resp.ObfuscatedExternalAccountId
	}

	if pUserId := resp.ObfuscatedExternalAccountId; pUserId != "" && pUserId != userId {
		s.log.Warn("verify for different user", zap.String("userId", userId), zap.String("pUserId", pUserId))
		userId = pUserId
	}

	s.log.Debug("verify result", zap.String("userId", userId), zap.String("productId", productId), zap.Any("receipt", resp))

	if userId == "" {
		return nil, errors.New("empty user id")
	}

	if resp.PurchaseState != 0 {
		if resp.PurchaseState == 1 {
			return s.cancel(ctx, packageName, productId, receipt, resp)
		}
		if resp.PurchaseState == 2 {
			if order == nil {
				// TODO fix for legacy order
				if _, err := s.findLatest(ctx, userId, productId, time.UnixMilli(resp.PurchaseTimeMillis)); err != nil {
					s.log.Info("find latest order failed for pending", zap.Error(err))
					return &pay.Order{Status: pay.OStatusBusy}, nil
				}
			}
			s.markDelaying(ctx, userId, packageName, productId, receipt)
			if order != nil {
				return order, nil
			}
			return s.markPending(ctx, userId, productId, time.UnixMilli(resp.PurchaseTimeMillis), receipt)
		}
		return nil, errors.New("invalid purchase state")
	}

	if resp.ConsumptionState == 1 {
		return nil, errors.New("invalid consumption state")
	}

	if order == nil {
		order, err = s.findLatest(ctx, userId, productId, time.UnixMilli(resp.PurchaseTimeMillis))
		if err != nil {
			return nil, err
		}
	}

	options := []pay.Option{pay.WithOrderId(resp.OrderId)}
	if resp.PurchaseType != nil && *resp.PurchaseType == 0 {
		if !s.sc.In(order.UserId) {
			s.delayAck(ctx, packageName, productId, receipt)
			return nil, sb.ErrNotSandboxUser
		}
		options = append(options, pay.WithRemark("sandbox"))
	}

	order, err = s.ps.MakePaid(ctx, order.TradeNo, options...)
	if err != nil {
		return nil, err
	}

	_ = s.unlinkOrder(ctx, userId, order.TradeNo)
	s.markResolved(ctx, packageName, productId, receipt, order.TradeNo)

	return order, nil
}

func (s *IAP) verifyOrder(ctx context.Context, packageName, productId, receipt string) (*androidpublisher.ProductPurchase, *pay.Order, error) {
	resp, err := s.cli.VerifyProduct(ctx, packageName, productId, receipt)
	if err != nil {
		return nil, nil, err
	}
	if order, err := s.ps.Take2(ctx, gwId, resp.OrderId); err == nil {
		return resp, order, nil
	}
	return resp, nil, nil
}

func (s *IAP) Cancel(ctx context.Context, packageName, productId, receipt string) (*pay.Order, error) {
	if l, err := s.dm.Lock(ctx, fmt.Sprintf(keyMutex, keyReceipt(receipt))); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}
	return s.cancel(ctx, packageName, productId, receipt, nil)
}

func (s *IAP) cancel(ctx context.Context, packageName, productId, receipt string,
	resp *androidpublisher.ProductPurchase,
) (*pay.Order, error) {
	if resp == nil {
		resp2, order, err := s.verifyOrder(ctx, packageName, productId, receipt)
		if err != nil {
			return nil, err
		} else if order != nil {
			return order, nil
		} else {
			resp = resp2
		}
	}

	userId := resp.ObfuscatedExternalAccountId
	if userId == "" {
		return nil, errors.New("empty user id")
	}

	order, err := s.pending(ctx, receipt)
	if err != nil {
		return nil, err
	}

	if order == nil {
		order, err = s.findOldest(ctx, userId, productId, time.UnixMilli(resp.PurchaseTimeMillis))
		if err != nil {
			return nil, err
		}
	}

	order, err = s.ps.MakeFailed(ctx, order.TradeNo, pay.WithOrderId(resp.OrderId))
	if err != nil {
		return nil, err
	}

	_ = s.unlinkOrder(ctx, userId, order.TradeNo)

	return order, nil
}

func (s *IAP) Refund(ctx context.Context, orderId string) (*pay.Order, error) {
	order, err := s.ps.Take2(ctx, gwId, orderId)
	if err != nil {
		return nil, err
	}
	return s.ps.MakeRefund(ctx, order.TradeNo)
}
