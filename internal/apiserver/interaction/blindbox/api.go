package blindbox

import (
	"context"
	"errors"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/blindbox"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Invoke(r *api.Router, hm *mux.Locker, ug user.Getter, gm *gift.Manager, blm *blindbox.Manager) {
	s := &apis{ug: ug, gm: gm, blm: blm}
	ar := r.WithAuth(hm.Middleware(mux.WithPOST)).Group("/blindbox")
	{
		ar.POST("/send", api.Generic(s.send))
		ar.GET("/info", api.Generic(s.info))
		ar.GET("/history", api.Generic(s.history))
	}
}

type apis struct {
	ug  user.Getter
	gm  *gift.Manager
	blm *blindbox.Manager
}

type sendReq struct {
	RoomId string `json:"roomId" binding:"required"` // 房间Id
	GiftId int    `json:"giftId" binding:"required"` // 盲盒礼物id
}

type sendResp struct {
	Wallet    *types.UserWallet `json:"wallet,omitempty"`    // 钱包信息
	LevelInfo *types.LevelInfo  `json:"levelInfo,omitempty"` // 等级信息
}

// @Tags 直播间内互动功能
// @Summary 赠送盲盒
// @Description 赠送盲盒
// @Produce json
// @Security HeaderAuth
// @Param param body sendReq true "请求参数"
// @Success 200 {object} codec.Response{data=sendResp}
// @Router /api/v1/blindbox/send [post]
func (s *apis) send(ctx *api.Context, req sendReq) (*sendResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	sr, err := s.blm.Send(context.TODO(), rlog.RequestId(ctx), req.RoomId, uac.UserId, req.GiftId, time.Now())
	if err != nil {
		return nil, err
	}

	var resp sendResp

	if sr.Wallet != nil {
		resp.Wallet = &types.UserWallet{
			Diamond: sr.Wallet.BVal(fund.PTypeDiamond).IntPart(),
		}
	}

	if sr.Level != nil {
		resp.LevelInfo = mixer.LevelInfo(sr.Level)
	}

	return &resp, nil
}

type infoReq struct {
	GiftId int `form:"giftId"` // 盲盒礼物id
}

type poolGift struct {
	Prob  float64         `json:"prob"`  // 概率
	Multi float64         `json:"multi"` // 倍数
	Gift  *types.LiteGift `json:"gift"`  // 礼物
}

type infoResp struct {
	Gift *types.LiteGift `json:"gift"` // 盲盒礼物
	Pool []poolGift      `json:"pool"` // 奖池礼物
}

// @Tags 直播间内互动功能
// @Summary 盲盒信息
// @Description 盲盒信息
// @Produce json
// @Security HeaderAuth
// @Param param query infoReq true "请求参数"
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/blindbox/info [get]
func (s *apis) info(ctx *api.Context, req infoReq) (*infoResp, error) {
	poolGifts := blindbox.PoolGifts[req.GiftId]
	if len(poolGifts) == 0 {
		return nil, errors.New("unknown gift id")
	}

	giftProbs := lo.Associate(blindbox.PrizeGifts[req.GiftId], func(r *rng.Ratio[int]) (int, float64) {
		return r.Data, r.Rate
	})

	var resp infoResp

	if gft, err := s.gm.GiftById(req.GiftId); err != nil {
		return nil, err
	} else {
		resp.Gift = mixer.LiteGift(gft)
	}

	for _, giftId := range poolGifts {
		if gft, err := s.gm.GiftById(giftId); err != nil {
			return nil, err
		} else {
			resp.Pool = append(resp.Pool, poolGift{
				Prob:  giftProbs[giftId],
				Multi: float64(gft.Diamond) / float64(resp.Gift.Diamond),
				Gift:  mixer.LiteGift(gft),
			})
		}
	}

	return &resp, nil
}

type historyReq struct {
	GiftId int    `form:"giftId"` // 盲盒礼物id
	Cursor string `form:"cursor"` // 分页游标
}

type sendRecord struct {
	Time   int64           `json:"time"`   // 时间：unix秒
	Anchor *types.User     `json:"anchor"` // 主播信息
	Gift   *types.LiteGift `json:"gift"`   // 开出礼物
}

type historyResp struct {
	Cursor string        `json:"cursor"` // 分页游标：用于下次请求
	List   []*sendRecord `json:"list"`   // 列表
}

// @Tags 直播间内互动功能
// @Summary 盲盒赠送记录
// @Description 盲盒赠送记录
// @Produce json
// @Security HeaderAuth
// @Param param query historyReq true "请求参数"
// @Success 200 {object} codec.Response{data=historyResp}
// @Router /api/v1/blindbox/history [get]
func (s *apis) history(ctx *api.Context, req historyReq) (*historyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	cursor, records, err := s.blm.History(ctx, uac.UserId, req.GiftId, req.Cursor)
	if err != nil {
		return nil, err
	}

	return &historyResp{
		Cursor: cursor,
		List: lo.Map(records, func(rec *blindbox.Record, _ int) *sendRecord {
			return &sendRecord{
				Time:   rec.CreatedAt.Unix(),
				Anchor: mixer.User(ctx, mixer.NoErr(s.ug.Account(ctx, rec.AnchorId))),
				Gift:   mixer.LiteGift(mixer.NoErr(s.gm.GiftById(rec.GiftId))),
			}
		}),
	}, nil
}
