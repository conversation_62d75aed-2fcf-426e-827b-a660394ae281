package rocket

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
)

func Invoke(r *api.Router, hm *mux.Locker, gm *gift.Manager, im *interact.Manager, lm *live.Manager, ev ev.Bus) {
	s := &apis{gm: gm, im: im, lm: lm, ev: ev}
	ar := r.With<PERSON>uth(hm.Middleware(mux.WithPOST)).Group("/interaction/rocket")
	{
		ar.POST("/send", api.Generic(s.send))
	}
}

type apis struct {
	gm *gift.Manager
	im *interact.Manager
	lm *live.Manager
	ev ev.Bus
}
