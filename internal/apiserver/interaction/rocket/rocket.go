package rocket

import (
	"context"
	"crypto/rand"
	"math/big"
	"strconv"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/foolsday"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
)

type sendReq struct {
	RoomId string `json:"roomId" binding:"required"` // 房间Id
}

type sendResp struct {
	Wallet    *types.UserWallet `json:"wallet,omitempty"`    // 钱包信息
	LevelInfo *types.LevelInfo  `json:"levelInfo,omitempty"` // 等级信息
	LuckDraw  *types.LuckDraw   `json:"luckDraw,omitempty"`  // 中奖信息
}

// @Tags 直播间内互动功能
// @Summary 赠送火箭
// @Description 赠送火箭
// @Produce json
// @Security HeaderAuth
// @Param param body sendReq true "请求参数"
// @Success 200 {object} codec.Response{data=sendResp}
// @Router /api/v1/interaction/rocket/send [post]
func (s *apis) send(ctx *api.Context, req sendReq) (*sendResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	roomInfo, err := s.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if roomInfo.IsGameRoom() || roomInfo.IsSystemRoom() || roomInfo.IsLuckyRoom() {
		return nil, biz.NewError(biz.ErrRoomNotAllow, "room not allow")
	}

	var rocketGift = rocket.GiftId
	//在活动期间内按概率发送破碎火箭
	if foolsday.IsOpen() {
		num, _ := rand.Int(rand.Reader, big.NewInt(99))
		if num.Int64() > 50 {
			rocketGift = rocket.FoolsDayGiftId
		}
	}

	gft, err := s.gm.GiftById(rocketGift)
	if err != nil {
		return nil, err
	}

	comboId := strconv.Itoa(int(time.Now().UnixNano()))
	sr, err := s.im.SendGift(context.TODO(), rlog.RequestId(ctx), req.RoomId, uac.UserId, comboId, rocketGift, 1, nil, time.Now())
	if err != nil {
		return nil, err
	}

	var resp sendResp
	if sr.Wallet != nil {
		resp.Wallet = &types.UserWallet{
			Diamond: sr.Wallet.BVal(fund.PTypeDiamond).IntPart(),
		}
	}
	if sr.Level != nil {
		resp.LevelInfo = mixer.LevelInfo(sr.Level)
	}
	if !sr.Pows.Empty() {
		resp.LuckDraw = &types.LuckDraw{
			GiftId:    rocketGift,
			GiftCount: 1,
			Prizes:    sr.Pows.Protocol(gft.Diamond),
		}
	}

	// 火箭发送
	s.ev.Emit(ctx, evt.RocketSend, &evt.RocketSendData{
		UserId:   uac.UserId,
		AnchorId: roomInfo.UserId,
		RoomId:   req.RoomId,
		Session:  roomInfo.SessionId.Hex(),
		GiftId:   rocketGift,
	})

	return &resp, nil
}
