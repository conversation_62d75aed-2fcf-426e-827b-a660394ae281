package interaction

import (
	"strconv"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox_collect"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/blindbox"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Invoke(r *api.Router, gm *gift.Manager) {
	a := &apis{
		gm: gm,
	}
	ar := r.WithAuth().Group("/interaction")
	{
		ar.GET("/list", api.Generic(a.list))
	}
}

type apis struct {
	gm *gift.Manager
}

type listReq struct {
	UserId string `json:"userId" form:"userId"` // 主播userId
}

type listResp struct {
	List []interaction `json:"list"`
}

type interaction struct {
	Key            string `json:"key"`            // 功能标识
	Name           string `json:"name"`           // 功能名称
	Desc           string `json:"desc"`           // 描述
	ImageUrl       string `json:"imageUrl"`       // 图片资源地址
	LabelUrl       string `json:"labelUrl"`       // 角标资源地址
	Display        bool   `json:"display"`        // 是否显示
	Diamond        int    `json:"diamond"`        // 价值钻石
	Headline       string `json:"headline"`       // 主标题
	Subhead        string `json:"subhead"`        // 副标题
	LinkUrl        string `json:"linkUrl"`        // 跳转链接
	BackgroundIcon string `json:"backgroundIcon"` // 详情icon
	BackgroundUrl  string `json:"backgroundUrl"`  // 背景图片
	BorderColor    string `json:"borderColor"`
	// features
	BlindBoxId    *int  `json:"blindBoxId,omitempty"`    // 盲盒礼物id
	BlindBoxGifts []int `json:"blindBoxGifts,omitempty"` // 盲盒可开出礼物id
}

// @Tags 直播间内互动功能
// @Summary 功能列表
// @Description 功能列表
// @Produce json
// @Security HeaderAuth
// @Param param body listReq true "请求参数"
// @Success 200 {object} codec.Response{data=listResp}
// @Router /api/v1/interaction/list [get]
func (s *apis) list(ctx *api.Context, req listReq) (*listResp, error) {
	rocketGift, err := s.gm.GiftById(rocket.GiftId)
	if err != nil {
		return nil, err
	}

	resp := &listResp{List: []interaction{
		{
			Key:      "rocket",
			Name:     i3n.T(ctx, "Rocket"),
			ImageUrl: "https://godzilla-live-oss.kako.live/gift/exclusive_huojian.png",
			LabelUrl: "https://godzilla-live-oss.kako.live/label/interaction_rocket_top.png",
			Diamond:  rocketGift.Diamond,
			Display:  true,
			Headline: i3n.T(ctx, "Help the host climb into the top 1"),
			Subhead:  i3n.T(ctx, "The host was in the top 1 and all live rooms will be notified"),
			LinkUrl: lo.Ternary(
				dbg.Ing(),
				"https://www-test.kako.live/app_inner/rocket_introduce.html?app_notitle=true&needLogin&lng=pt",
				"https://www.kako.live/app_inner/rocket_introduce.html?app_notitle=true&needLogin&lng=pt",
			),
			BackgroundIcon: lo.Ternary(
				dbg.Ing(),
				"https://godzilla-live-oss-test.sskjz.com/live_interaction/rocket/gift_panel_header_icon.png",
				"https://godzilla-live-oss.kako.live/live_interaction/rocket/gift_panel_header_icon.png",
			),
			BackgroundUrl: lo.Ternary(
				dbg.Ing(),
				"https://godzilla-live-oss-test.sskjz.com/live_interaction/rocket/gift_panel_header_bg.png",
				"https://godzilla-live-oss.kako.live/live_interaction/rocket/gift_panel_header_bg.png"),
			BorderColor: "#ffffff99",
		},
		{
			Key:      "redpacket2",
			Name:     i3n.T(ctx, "Hong bao"),
			Desc:     i3n.T(ctx, "Popularity"),
			ImageUrl: "https://godzilla-live-oss.kako.live/live_interaction/redpacket/icon.png",
			LabelUrl: "https://godzilla-live-oss.kako.live/live_interaction/redpacket/lab.png",
			Diamond:  0,
			Display:  true,
			Headline: i3n.T(ctx, "Send Hong bao to increase interaction"),
			Subhead:  i3n.T(ctx, "Others can receive their Hong bao"),
			LinkUrl: lo.Ternary(
				dbg.Ing(),
				"https://www-test.kako.live/app_inner/hongbao_introduce.html?app_notitle=true&lng=pt",
				"https://www.kako.live/app_inner/hongbao_introduce.html?app_notitle=true&lng=pt",
			),
			BackgroundIcon: "https://godzilla-live-oss.kako.live/live_interaction/redpacket/icon2.png",
			BackgroundUrl:  "https://godzilla-live-oss.kako.live/live_interaction/redpacket/bg.png",
			BorderColor:    "#ffffff99",
		},
	}}

	for _, boxId := range blindbox.BoxIds {
		if (boxId == 10087 || boxId == 10088) && !blindbox_collect.IsOpen(time.Now()) {
			continue
		}
		blindGift, err := s.gm.GiftById(boxId)
		if err != nil {
			continue
		}
		resp.List = append(resp.List, interaction{
			Key:      "blindBox",
			Name:     blindGift.Name,
			ImageUrl: blindGift.ImageUrl,
			LabelUrl: blindGift.LabelUrl,
			Diamond:  blindGift.Diamond,
			Display:  true,
			Headline: i3n.T(ctx, "Possible wins"),
			LinkUrl: lo.Ternary(
				dbg.Ing(),
				"https://www-test.kako.live/app_inner/blind_box_introduce.html?app_notitle=true&needLogin&lng=pt&giftId="+strconv.Itoa(boxId),
				"https://www.kako.live/app_inner/blind_box_introduce.html?app_notitle=true&needLogin&lng=pt&giftId="+strconv.Itoa(boxId),
			),
			BackgroundIcon: lo.Ternary(app.DeviceType(ctx) == "android",
				"https://godzilla-live-oss.kako.live/live_interaction/blindbox/icon_android_v2.png",
				"",
			),
			BackgroundUrl: lo.Ternary(app.DeviceType(ctx) == "android",
				"https://godzilla-live-oss.kako.live/live_interaction/blindbox/bg_android.png",
				"https://godzilla-live-oss.kako.live/live_interaction/blindbox/bg.png",
			),
			BorderColor:   "#ffffff99",
			BlindBoxId:    lo.ToPtr(boxId),
			BlindBoxGifts: blindbox.PoolGifts[boxId],
		})
	}

	if !s.showV170Stuff(ctx) {
		resp.List = lo.Filter(resp.List, func(item interaction, _ int) bool {
			return item.Key != "blindBox" && item.Key != "redpacket"
		})
	}

	if !s.showV180Stuff(ctx) {
		resp.List = lo.Filter(resp.List, func(item interaction, _ int) bool {
			return item.Key != "redpacket2"
		})
	}

	if dbg.Ing() {
		resp.List = append(resp.List, interaction{
			Key:      "fake",
			Name:     "BUG扣钱",
			ImageUrl: "https://godzilla-live-oss-test.sskjz.com/payermax/Circular_144x144/Global/UnionPay_C_A.png",
			LabelUrl: "https://godzilla-live-oss-test.sskjz.com/label/interaction_rocket_top.png",
			Diamond:  1,
			Display:  true,
		})
	}

	return resp, nil
}

func (s *apis) showV170Stuff(ctx *api.Context) (show bool) {
	var (
		devType = app.DeviceType(ctx)
		cv      = app.Version(ctx)
		buildId = app.BuildId(ctx)
	)

	switch devType {
	case "android":
		show = cv.GreaterThanOrEqual(app.V170)
	case "ios":
		if nBuildId, err := strconv.Atoi(buildId); err == nil {
			show = cv.Equal(app.V100) && nBuildId >= 42
		}
	default:
	}

	return show
}

func (s *apis) showV180Stuff(ctx *api.Context) (show bool) {
	var (
		devType = app.DeviceType(ctx)
		cv      = app.Version(ctx)
		buildId = app.BuildId(ctx)
	)

	switch devType {
	case "android":
		show = cv.GreaterThanOrEqual(app.V180)
	case "ios":
		if nBuildId, err := strconv.Atoi(buildId); err == nil {
			show = cv.Equal(app.V100) && nBuildId >= 47
		}
	default:
	}

	return show
}
