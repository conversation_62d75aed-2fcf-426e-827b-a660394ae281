package sredpacket

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"go.uber.org/zap"
)

type GiftsRanger interface {
	Range(fn func(id int, cnt int))
}

func (s *API) Gifts2Items(gifts GiftsRanger) GiftItems {
	var items GiftItems
	gifts.Range(func(id int, cnt int) {
		gft, err := s.gm.GiftById(id)
		if err != nil {
			s.logger.Error("get gift by id", zap.Error(err), zap.Int("id", id))
			return
		}
		items = append(items, GiftItem{Gift: mixer.LiteGift(gft), GiftId: id, Count: cnt})
	})

	return items
}
