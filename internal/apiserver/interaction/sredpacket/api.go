package sredpacket

import (
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/redpacket"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func Provide(ug user.Getter, gm *gift.Manager, lm *live.Manager, fm *follow.Manager, fundMgr *fund.Manager, rm *room.Manager, rpm *redpacket.Manager, vnd log.Vendor) *API {
	return &API{
		ug:      ug,
		gm:      gm,
		lm:      lm,
		fm:      fm,
		fundMgr: fundMgr,
		rm:      rm,
		rpm:     rpm,
		logger:  vnd.Scope("redpacket"),
	}
}

func Invoke(r *api.Router, s *API, ch *cache.Handler) {
	ar := r.WithAuth()
	{
		g := ar.Group("/redpacket2")
		g.GET("/profile", ch.Middleware(cache.WithExpire(time.Minute*3)), api.Generic(s.Profile))
		g.POST("/create", api.Generic(s.Create))
		g.POST("/grab", api.Generic(s.Grab))
		g.GET("/winners", api.Generic(s.Winners))
		g.POST("/prize/send", api.Generic(s.Send))
	}
	{
		g := r.TryAuth().Group("/redpacket2")
		g.POST("/rooms", api.Generic(s.Rooms))
		g.GET("/rooms", api.Generic(s.Rooms))
	}
}

type API struct {
	ug      user.Getter
	gm      *gift.Manager
	lm      *live.Manager
	fm      *follow.Manager
	rpm     *redpacket.Manager
	rm      *room.Manager
	fundMgr *fund.Manager
	logger  *zap.Logger
}

type RedPacket struct {
	List []*PacketItem `json:"list"` // 红包列表
}

func (s *API) Poll(ctx *api.Context, roomId, sessionId string, at time.Time) (*RedPacket, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	pp, err := s.rpm.SessionPackets(ctx, sessionId, 100, at)
	if err != nil {
		return nil, err
	}

	grabbed, err := s.rpm.UserSessionGrabbed(ctx, uac.UserId, sessionId)
	if err != nil {
		return nil, err
	}

	out := RedPacket{
		List: make([]*PacketItem, 0, len(pp)),
	}

	for _, p := range pp {
		if slices.Contains(grabbed, p.Id.Hex()) {
			continue
		}

		item := &PacketItem{
			Id:          p.Id.Hex(),
			Type:        p.Type,
			Gifts:       s.Gifts2Items(p.Gifts).Sort(),
			GrabStartAt: p.GrabStartAt.UnixMilli(),
			GrabEndAt:   p.GrabEndAt.UnixMilli(),
			Value:       p.Value,
			Countdown:   p.Countdown,
			Count:       p.Count,
		}

		if p.Sender != "" {
			acc, err := s.ug.Account(ctx, p.Sender)
			if err != nil {
				s.logger.Error("get sender account", zap.Error(err), zap.String("userId", p.Sender))
				continue
			}

			item.Sender = mixer.User(ctx, acc)
		}

		if p.Condition != redpacket.ConditionNone {
			if p.Condition == redpacket.ConditionFollowAnchor {
				acc, err := s.ug.Account(ctx, p.Anchor)
				if err != nil {
					s.logger.Error("get anchor account", zap.Error(err), zap.String("userId", p.Anchor))
					continue
				}

				item.Condition = &Condition{Follow: mixer.User(ctx, acc)}
			} else if p.Condition == redpacket.ConditionFollowSender {
				item.Condition = &Condition{Follow: item.Sender}
			}
		}

		out.List = append(out.List, item)
	}

	return &out, nil
}

type WinnersRequest struct {
	Id string `form:"id" binding:"required"` // 红包id
}

type Winner struct {
	User  *types.User `json:"user"`  // 中奖者
	Coins int         `json:"coins"` // 抢到的金币
	Gifts GiftItems   `json:"gifts"` // 抢到的礼物
}

type WinnersResponse struct {
	List []*Winner `json:"list"` // 中奖者
}

// @tags 红包
// @summary 红包中奖者
// @description 红包中奖者
// @Security HeaderAuth
// @accept json
// @produce json
// @param req body WinnersRequest true "请求"
// @success 200 {object} codec.Response{data=WinnersResponse}
// @router /api/v1/redpacket2/winners [get]
func (s *API) Winners(ctx *api.Context, req WinnersRequest) (*WinnersResponse, error) {
	winners, err := s.rpm.Winners(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	out := &WinnersResponse{
		List: make([]*Winner, len(winners)),
	}

	for i, w := range winners {
		acc, err := s.ug.Account(ctx, w.UserId)
		if err != nil {
			s.logger.Error("get user account", zap.Error(err), zap.String("userId", w.UserId))
			continue
		}

		out.List[i] = &Winner{User: mixer.User(ctx, acc), Coins: w.Coins, Gifts: s.Gifts2Items(w.Gifts).Sort()}
	}

	slices.SortFunc(out.List, func(l, r *Winner) int {
		if r.Coins != l.Coins {
			return int(r.Coins - l.Coins)
		}
		return r.Gifts.Price() - l.Gifts.Price()
	})

	return out, nil
}

type GiftPacket struct {
	Id                  int        `json:"id"`                  // 红包id
	Gifts               []GiftItem `json:"gifts"`               // 礼物列表
	Counts              []int      `json:"counts"`              // 支持的红包份数
	Value               int        `json:"value"`               // 红包价值
	SupportGlobalNotify bool       `json:"supportGlobalNotify"` // 是否支持全局通知
	SupportCondition    bool       `json:"supportCondition"`    // 是否支持条件
}

type CoinPacket struct {
	Coins               int   `json:"coins"`               // 红包价值
	Counts              []int `json:"counts"`              // 支持的红包份数
	Value               int   `json:"value"`               // 红包价值
	SupportGlobalNotify bool  `json:"supportGlobalNotify"` // 是否支持全局通知
	SupportCondition    bool  `json:"supportCondition"`    // 是否支持条件
}

type ProfileResponse struct {
	GiftPackets []*GiftPacket `json:"giftPackets"` // 礼物红包
	CoinPackets []*CoinPacket `json:"coinPackets"` // 金币红包
	Countdowns  []int         `json:"countdowns"`  // 倒计时列表
}

type ProfileRequest struct {
	RoomId string `form:"roomId" binding:"required"` // 房间id
}

// @tags 红包
// @summary 红包配置
// @description 红包配置
// @Security HeaderAuth
// @accept json
// @produce json
// @param req body ProfileRequest true "请求"
// @success 200 {object} codec.Response{data=ProfileResponse}
// @router /api/v1/redpacket2/profile [get]
func (s *API) Profile(ctx *api.Context, req ProfileRequest) (*ProfileResponse, error) {
	var (
		cfg = redpacket.Preset()
		out = &ProfileResponse{
			Countdowns: cfg.Countdowns,
		}
	)

	for _, p := range cfg.GiftPacket {
		gii := s.Gifts2Items(p.Gifts)
		itm := &GiftPacket{
			Id:                  p.Id,
			Gifts:               gii.Sort(),
			Counts:              p.Counts,
			Value:               gii.Price(),
			SupportGlobalNotify: gii.Price() >= redpacket.GloNotifyMinDiamonds,
			SupportCondition:    gii.Price() >= redpacket.CondMinDiamonds,
		}

		out.GiftPackets = append(out.GiftPackets, itm)
	}

	for _, p := range cfg.CoinPacket {
		itm := &CoinPacket{
			Coins:               p.Coins,
			Counts:              p.Counts,
			Value:               int(p.Coins),
			SupportGlobalNotify: p.Coins >= redpacket.GloNotifyMinDiamonds,
			SupportCondition:    p.Coins >= redpacket.CondMinDiamonds,
		}

		out.CoinPackets = append(out.CoinPackets, itm)
	}

	return out, nil
}

type CreatePacketRequest struct {
	RoomId       string              `json:"roomId" binding:"required"`    // 房间id
	GiftPacketId int                 `json:"giftPacketId"`                 // 礼物红包id
	Coins        int                 `json:"coins"`                        // 金币红包的金币
	Count        int                 `json:"count"`                        // 金币红包的份数
	CountDown    int                 `json:"countDown" binding:"required"` // 倒计时秒数
	Condition    redpacket.Condition `json:"condition"`                    // 领取条件  0: 没有限制 1: 关注主播 2: 关注发红包的人
	GlobalNotify bool                `json:"globalNotify"`                 // 是否支持全局通知
}

func (c *CreatePacketRequest) Validate() error {
	if c.GiftPacketId == 0 && c.Coins == 0 || c.Count == 0 {
		return fmt.Errorf("giftPacketId or (coins and count) is required")
	}

	cfg := redpacket.Preset()

	if !slices.Contains(cfg.Countdowns, c.CountDown) {
		return fmt.Errorf("countDown must be 10, 30, 60 or 180")
	}

	if c.GiftPacketId != 0 {
		if pkt := cfg.GiftPacketById(c.GiftPacketId); pkt == nil || !slices.Contains(pkt.Counts, c.Count) {
			return fmt.Errorf("invalid count or giftPacketId")
		}
	}

	if c.Coins != 0 {
		if cp := cfg.CoinPacketByCoin(c.Coins); cp == nil || !slices.Contains(cp.Counts, c.Count) {
			return fmt.Errorf("invalid count or coins")
		}
	}

	if c.Condition < redpacket.ConditionNone || c.Condition > redpacket.ConditionFollowSender {
		return fmt.Errorf("invalid condition")
	}

	return nil
}

type CreatePacketResponse struct {
	Id     string            `json:"id"`               // 红包id
	Value  int               `json:"value"`            // 红包价值
	Wallet *types.UserWallet `json:"wallet,omitempty"` // 钱包信息
}

// @tags 红包
// @summary 创建红包
// @description 创建红包
// @Security HeaderAuth
// @accept json
// @produce json
// @param req body CreatePacketRequest true "请求"
// @success 200 {object} codec.Response{data=CreatePacketResponse}
// @router /api/v1/redpacket2/create [post]
func (s *API) Create(ctx *api.Context, req CreatePacketRequest) (*CreatePacketResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, err
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := s.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if !ri.IsLiving() {
		return nil, biz.NewError(biz.ErrRoomNotLiving, "room is not living")
	}

	var (
		at = time.Now()
		bc = &redpacket.PacketConfig{
			RoomId:       req.RoomId,
			SessionId:    ri.SessionId.Hex(),
			Anchor:       ri.UserId,
			Sender:       uac.UserId,
			Countdown:    req.CountDown,
			GiftPacketId: req.GiftPacketId,
			Coins:        req.Coins,
			Count:        req.Count,
			Condition:    req.Condition,
			GlobalNotify: req.GlobalNotify,
		}
	)

	rp, err := s.rpm.CreateRedPacket(ctx, bc, at)
	if err != nil {
		return nil, err
	}

	acc, err := s.fundMgr.Take(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &CreatePacketResponse{Id: rp.Id.Hex(), Value: rp.Value, Wallet: &types.UserWallet{Diamond: acc.BVal(fund.PTypeDiamond).IntPart()}}, nil
}

type GrabRequest struct {
	Id string `json:"id" binding:"required"` // 红包id
}

type GrabResponse struct {
	Id    string     `json:"id"`    // 红包id
	PId   string     `json:"pid"`   // 抢到的礼物集id,后续送完礼提交这个id
	Coins int        `json:"coins"` // 抢到的金币
	Gifts []GiftItem `json:"gifts"` // 抢到的礼物
}

// @tags 红包
// @summary 抢红包
// @description 抢红包,错误码：14011: 红包未开始,14012: 红包已结束,14013: 红包已抢过,14014: 红包已抢完
// @Security HeaderAuth
// @accept json
// @produce json
// @param req body GrabRequest true "请求"
// @success 200 {object} codec.Response{data=GrabResponse}
// @router /api/v1/redpacket2/grab [post]
func (s *API) Grab(ctx *api.Context, req GrabRequest) (*GrabResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		devType = app.DeviceType(ctx)
		cv      = app.Version(ctx)
	)

	if grabbed, err := s.rpm.Grabbed(ctx, req.Id, uac.UserId); err != nil {
		return nil, err
	} else if grabbed {
		return nil, redpacket.ErrRedPacketAlreadyGrabed
	}

	at := time.Now()

	packet, err := s.rpm.GetRedPacket2(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if at.Before(packet.GrabStartAt) {
		return nil, redpacket.ErrRedPacketNotStarted
	}

	if at.After(packet.GrabEndAt) {
		return nil, redpacket.ErrRedPacketAlreadyClosed
	}

	if targetUser := packet.MustFollow(); targetUser != "" && uac.UserId != targetUser {
		success, err := s.fm.TryFollow(ctx, uac.UserId, targetUser, true)
		if err != nil {
			return nil, err
		}
		if targetUser == packet.Anchor && success {
			if ri, _ := s.lm.RoomByUserId2(ctx, targetUser); ri != nil {
				s.rm.Post(func() { _ = s.rm.NotifyRoomFollow(ctx, ri.Id.Hex(), uac.UserId, true) })
			}
		}
	}

	got, err := s.rpm.Grab(ctx, req.Id, uac.UserId, at)
	if err != nil {
		if (errors.Is(err, redpacket.ErrRedPacketAlreadyClosed) || errors.Is(err, redpacket.ErrRedPacketGrabedOut)) && devType == "android" && cv.LessThanOrEqual(app.V180) {
			return &GrabResponse{Id: req.Id, Coins: 0, Gifts: []GiftItem{}}, nil
		}

		return nil, err
	}

	return &GrabResponse{
		Id:    req.Id,
		PId:   got.Id.Hex(),
		Coins: got.Coins,
		Gifts: s.Gifts2Items(got.Gifts).Sort(),
	}, nil
}

type RoomRequest struct {
	RoomId []string `json:"roomId" form:"roomId"` // 房间id
}

type RoomsResponse struct {
	Exist []string `json:"exist"` // 存在红包的房间id
}

// @tags 红包
// @summary 查询有红包的房间列表
// @description 查询有红包的房间列表
// @Security HeaderAuth
// @accept json
// @produce json
// @param req body RoomRequest true "请求"
// @success 200 {object} codec.Response{data=RoomsResponse}
// @router /api/v1/redpacket2/rooms [post]
func (s *API) Rooms(ctx *api.Context, req RoomRequest) (*RoomsResponse, error) {
	var grabbed []string
	if uac, err := ctx.User(); err == nil && uac != nil {
		grabbed, err = s.rpm.UserDayGrabbed(ctx, uac.UserId)
		if err != nil {
			return nil, err
		}
	}

	countBy := func(sid string) bool {
		return !slices.Contains(grabbed, sid)
	}

	exist := make([]string, 0, len(req.RoomId))
	for _, r := range req.RoomId {
		ri, err := s.lm.Room2(r)
		if err != nil {
			return nil, err
		}

		pids, err := s.rpm.SessionPacketIds(ctx, ri.SessionId.Hex())
		if err != nil {
			return nil, err
		}

		if lo.CountBy(pids, countBy) > 0 {
			exist = append(exist, r)
		}
	}

	return &RoomsResponse{Exist: exist}, nil
}

type GiftingRequest struct {
	Id string `json:"id" binding:"required"` // 礼物集id，grab response 返回的pid
}

type GiftingResponse struct {
}

// @tags 红包
// @summary 一键送出红包礼物
// @description 一键送出红包礼物给主播， 必须是礼物红包
// @Security HeaderAuth
// @accept json
// @produce json
// @param req body GiftingRequest true "请求"
// @success 200 {object} codec.Response{data=GiftingResponse}
// @router /api/v1/redpacket2/prize/send [post]
func (s *API) Send(ctx *api.Context, req GiftingRequest) (*GiftingResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.rpm.SendPrize(ctx, req.Id, uac.UserId, time.Now()); err != nil {
		if errors.Is(err, redpacket.ErrAlreadyGifting) {
			return &GiftingResponse{}, nil
		}
		return nil, err
	}

	return &GiftingResponse{}, nil
}
