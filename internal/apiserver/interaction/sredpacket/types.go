package sredpacket

import (
	"slices"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/redpacket"
)

type Condition struct {
	Follow *types.User `json:"follow"` // 关注主播
}

type PacketItem struct {
	Id          string         `json:"id"`
	Type        redpacket.Type `json:"type"`        // 1: 金币红包 2: 礼物红包
	Value       int            `json:"value"`       // 价值
	Gifts       GiftItems      `json:"gifts"`       // 礼物红包的礼物
	Count       int            `json:"count"`       // 总份数（金币红包才展示，礼物红包为0）
	Condition   *Condition     `json:"condition"`   // 领取条件, 没有条件时为null
	Sender      *types.User    `json:"sender"`      // 发送人信息
	GrabStartAt int64          `json:"grabStartAt"` // 抢红包时间, 毫秒级时间戳
	GrabEndAt   int64          `json:"grabEndAt"`   // 抢红包截止时间，毫秒级时间戳
	Countdown   int            `json:"countdown"`   // 倒计时，单位：秒
}

type GiftItem struct {
	Gift   *types.LiteGift `json:"-"`      // 礼物信息
	GiftId int             `json:"giftId"` // 礼物id
	Count  int             `json:"count"`  // 数量
}

type GiftItems []GiftItem

func (g GiftItems) Price() int {
	var price int
	for _, item := range g {
		price += item.Gift.Diamond * item.Count
	}
	return price
}

func (g GiftItems) Sort() GiftItems {
	slices.SortFunc(g, func(a, b GiftItem) int {
		return a.Gift.Diamond - b.Gift.Diamond
	})
	return g
}
