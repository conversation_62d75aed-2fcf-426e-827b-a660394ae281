package admin

import (
	"fmt"
	"slices"
	"time"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func Invoke(
	r *api.Router,
	um *urm.Manager,
	ug user.Getter,
	lm *live.Manager,
	fc fclub.Getter,
	fm *follow.Manager,
	lg *level.Manager,
	vnd log.Vendor,
) {
	a := API{
		um:     um,
		ug:     ug,
		lm:     lm,
		fc:     fc,
		fm:     fm,
		lg:     lg,
		logger: vnd.Scope("admin.api"),
	}

	r = r.WithAuth()
	{
		if false {
			r.GET("room/admin", api.Generic(a.Room))

			// 黑名单
			r.GET("room/admin/blacklist", api.Generic(a.ListBlacklist))
			r.POST("room/admin/blacklist/add", api.Generic(a.AddListBlacklist))
			r.POST("room/admin/blacklist/remove", api.Generic(a.RemoveListBlacklist))

			// 禁言
			r.GET("room/admin/mute", api.Generic(a.RemoveListBlacklist))
			r.POST("room/admin/mute/add", api.Generic(a.AddMuteUser))
			r.POST("room/admin/mute/remove", api.Generic(a.RemoveMuteUser))

			// 管理员
			r.POST("room/admin/managers/add", api.Generic(a.AddManager))
			r.POST("room/admin/managers/remove", api.Generic(a.RemoveManager))
			// 粉丝列表
			r.GET("room/admin/fans/search", api.Generic(a.SearchFans))

			// 禁言词
			r.POST("room/admin/banwords", api.Generic(a.PutBanWords))
		}

		// 用户黑名单
		r.POST("/user/blacklist/add", api.Generic(a.AddUserBlacklist))
		r.POST("/user/blacklist/remove", api.Generic(a.RemoveUserBlacklist))
		r.GET("/user/blacklist", api.Generic(a.ListUserBlacklist))

		// 用户禁言\解禁
		r.POST("/user/mute", api.Generic(a.UserMute))

	}
}

type API struct {
	um     *urm.Manager
	ug     user.Getter
	lm     *live.Manager
	fc     fclub.Getter
	fm     *follow.Manager
	lg     *level.Manager
	logger *zap.Logger
}

type AdminReq struct {
	RoomId string `form:"roomId" validate:"required"`
}

type RoomResp struct {
	BannedWords []string      `json:"bannedWords"`
	Admins      []*types.User `json:"admins"`
}

// @Tags 直播间管理
// @Summary 管理信息
// @Description 管理信息
// @Produce json
// @Security HeaderAuth
// @Param param query AdminReq true "请求参数"
// @Success 200 {object} codec.Response{data=RoomResp}
// @Router /api/v1/room/admin [get]
func (a *API) Room(ctx *api.Context, req AdminReq) (*RoomResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(ctx, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	ua, err := a.um.User(ctx, ri.UserId)
	if err != nil {
		return nil, err
	}

	if uac.UserId != ri.UserId && slices.Contains(ua.Admins, uac.UserId) {
		return nil, biz.Legacy("user has no permission")
	}

	resp := &RoomResp{
		BannedWords: ua.BannedWords,
	}

	for _, userId := range ua.Admins {
		acc, err := a.ug.Account(ctx, userId)
		if err != nil {
			a.logger.Error("get account", zap.Error(err), zap.String("userId", userId))
			continue
		}

		resp.Admins = append(resp.Admins, mixer.User(ctx, acc))
	}

	return resp, nil
}

type GrantUser struct {
	User      *types.User `json:"user"`
	Operator  types.User  `json:"operator"`
	CreatedAt int64       `json:"createdAt"`
}

type ListBlacklistReq struct {
	RoomId string `form:"roomId" validate:"required"`
	Cursor string `form:"cursor"`
}

type ListBlacklistResp struct {
	Total int         `json:"total"`
	Users []GrantUser `json:"list"`
}

// @Tags 直播间管理
// @Summary 黑名单列表
// @Description 黑名单列表
// @Produce json
// @Security HeaderAuth
// @Param param query ListBlacklistReq true "请求参数"
// @Success 200 {object} codec.Response{data=ListBlacklistResp}
// @Router /api/v1/room/admin/blacklist [get]
func (a *API) ListBlacklist(ctx *api.Context, req ListBlacklistReq) (*ListBlacklistResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(ctx, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	gu, err := a.um.ListBlacklist(ctx, ri.UserId, req.Cursor)
	if err != nil {
		return nil, err
	}

	resp := &ListBlacklistResp{
		Users: make([]GrantUser, 0, len(gu.Grants)),
	}

	for _, g := range gu.Grants {
		acc, err := a.ug.Account(ctx, g.UserId)
		if err != nil {
			a.logger.Error("get account", zap.Error(err), zap.String("userId", g.UserId))
			continue
		}

		bu := GrantUser{
			User:      mixer.User(ctx, acc),
			CreatedAt: g.CreatedAt.Unix(),
		}

		op, err := a.ug.Account(ctx, g.Operator)
		if err != nil {
			a.logger.Error("get operator", zap.Error(err), zap.String("userId", g.Operator))
		} else {
			bu.Operator = *mixer.User(ctx, op)
		}

		resp.Users = append(resp.Users, bu)
	}

	return resp, nil
}

type AddListBlacklistReq struct {
	RoomId string `form:"roomId" validate:"required"`
	UserId string `form:"userId" validate:"required"`
}

// @Tags 直播间管理
// @Summary 添加黑名单
// @Description 添加黑名单
// @Produce json
// @Security HeaderAuth
// @Param param query AddListBlacklistReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/blacklist/add [post]
func (a *API) AddListBlacklist(c *api.Context, req AddListBlacklistReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.AddList(c, urm.GrantKindBlacklist, uac.UserId, ri.UserId, req.UserId, ri.SessionId.Hex(), time.Now()); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type RemoveListBlacklistReq struct {
	RoomId string `form:"roomId" validate:"required"`
	UserId string `form:"userId" validate:"required"`
}

// @Tags 直播间管理
// @Summary 移除黑名单
// @Description 移除黑名单
// @Produce json
// @Security HeaderAuth
// @Param param query RemoveListBlacklistReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/blacklist/remove [post]
func (a *API) RemoveListBlacklist(c *api.Context, req RemoveListBlacklistReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(c, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.um.RemoveList(c, urm.GrantKindBlacklist, uac.UserId, ri.UserId, req.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type ListMuteReq struct {
	RoomId string `form:"roomId" validate:"required"`
	Cursor string `form:"cursor"`
}

type ListMuteResp struct {
	Total int         `json:"total"`
	Users []GrantUser `json:"list"`
}

// @Tags 直播间管理
// @Summary 禁言列表
// @Description 禁言列表
// @Produce json
// @Security HeaderAuth
// @Param param query ListMuteReq true "请求参数"
// @Success 200 {object} codec.Response{data=ListMuteResp}
// @Router /api/v1/room/admin/mute [get]
func (a *API) ListMute(ctx *api.Context, req ListMuteReq) (*ListMuteResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(ctx, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	gu, err := a.um.ListMuted(ctx, ri.UserId, req.Cursor)
	if err != nil {
		return nil, err
	}

	resp := &ListMuteResp{
		Users: make([]GrantUser, 0, len(gu.Grants)),
	}

	for _, g := range gu.Grants {
		acc, err := a.ug.Account(ctx, g.UserId)
		if err != nil {
			a.logger.Error("get account", zap.Error(err), zap.String("userId", g.UserId))
			continue
		}

		mu := GrantUser{
			User:      mixer.User(ctx, acc),
			CreatedAt: g.CreatedAt.Unix(),
		}

		op, err := a.ug.Account(ctx, g.Operator)
		if err != nil {
			a.logger.Error("get operator", zap.Error(err), zap.String("userId", g.Operator))
		} else {
			mu.Operator = *mixer.User(ctx, op)
		}

		resp.Users = append(resp.Users, mu)
	}

	return resp, nil
}

type AddMuteUserReq struct {
	RoomId string `form:"roomId" validate:"required"`
	UserId string `form:"userId" validate:"required"`
}

// @Tags 直播间管理
// @Summary 添加禁言
// @Description 添加禁言
// @Produce json
// @Security HeaderAuth
// @Param param query AddMuteUserReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/mute/add [post]
func (a *API) AddMuteUser(c *api.Context, req AddMuteUserReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.AddList(c, urm.GrantKindMute, uac.UserId, ri.UserId, req.UserId, ri.SessionId.Hex(), time.Now()); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type RemoveMuteUserReq struct {
	RoomId string `form:"roomId" validate:"required"`
	UserId string `form:"userId" validate:"required"`
}

// @Tags 直播间管理
// @Summary 移除禁言
// @Description 移除禁言
// @Produce json
// @Security HeaderAuth
// @Param param query RemoveMuteUserReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/mute/remove [post]
func (a *API) RemoveMuteUser(c *api.Context, req RemoveMuteUserReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(c, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.um.RemoveList(c, urm.GrantKindMute, uac.UserId, ri.UserId, req.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type AddUserBlacklistReq struct {
	UserId string `form:"userId"` // 目标用户UserId
	NumId  *int64 `form:"numId"`  // 目标用户NumId
}

// @Tags 用户管理
// @Summary 添加黑名单(im那边添加黑名单调用·)
// @Description 添加黑名单
// @Produce json
// @Security HeaderAuth
// @Param param query AddUserBlacklistReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/user/blacklist/add [post]·
func (a *API) AddUserBlacklist(c *api.Context, req AddUserBlacklistReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	if req.UserId == "" && req.NumId == nil {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	if req.UserId == "" {
		acc, err := a.ug.GetByNumId(c, *req.NumId)
		if err != nil {
			return nil, err
		}
		req.UserId = acc.UserId
	}

	if err := a.um.AddList(c, urm.GrantKindBlacklist, uac.UserId, uac.UserId, req.UserId, "", time.Now()); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type RemoveUserBlacklistReq struct {
	UserId string `form:"userId"` // 目标用户
	NumId  *int64 `form:"numId"`  // 目标用户NumId
}

// @Tags 用户管理
// @Summary 移除黑名单(im那边移除黑名单调用·)
// @Description 移除黑名单
// @Produce json
// @Security HeaderAuth
// @Param param query RemoveUserBlacklistReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/user/blacklist/remove [post]·
func (a *API) RemoveUserBlacklist(c *api.Context, req RemoveUserBlacklistReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	if req.UserId == "" && req.NumId == nil {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	if req.UserId == "" {
		acc, err := a.ug.GetByNumId(c, *req.NumId)
		if err != nil {
			return nil, err
		}
		req.UserId = acc.UserId
	}

	if err := a.um.RemoveList(c, urm.GrantKindBlacklist, uac.UserId, uac.UserId, req.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type AddManagerReq struct {
	RoomId string `form:"roomId" validate:"required"`
	UserId string `form:"userId" validate:"required"`
}

// @Tags 直播间管理
// @Summary 添加管理员
// @Description 添加管理员
// @Produce json
// @Security HeaderAuth
// @Param param query AddManagerReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/managers/add [post]
func (a *API) AddManager(c *api.Context, req AddManagerReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(c, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.um.AddManager(c, uac.UserId, ri.UserId, req.UserId, time.Now()); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type RemoveManagerReq struct {
	RoomId string `form:"roomId" validate:"required"`
	UserId string `form:"userId" validate:"required"`
}

// @Tags 直播间管理
// @Summary 移除管理员
// @Description 移除管理员
// @Produce json
// @Security HeaderAuth
// @Param param query RemoveManagerReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/managers/remove [post]
func (a *API) RemoveManager(c *api.Context, req RemoveManagerReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(c, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.um.RemoveManager(c, uac.UserId, ri.UserId, req.UserId, time.Now()); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type SearchFansReq struct {
	RoomId  string `form:"roomId"`  // 直播间ID
	Keyword string `form:"keyword"` // 搜索关键词
	Cursor  string `form:"cursor"`  // 分页游标
}

type FansUser struct {
	User      *types.User `json:"user"`      // 用户
	IsManager bool        `json:"isManager"` // 是否是管理员
}
type SearchFansResp struct {
	Cursor string      `json:"cursor"` // 分页游标：用于下次请求
	Total  int         `json:"total"`  // 总数量
	List   []*FansUser `json:"list"`   // 列表
}

// @Tags 直播间管理
// @Summary 搜索粉丝列表
// @Description 搜索粉丝列表
// @Produce json
// @Security HeaderAuth
// @Param param query SearchFansReq true "请求参数"
// @Success 200 {object} codec.Response{data=SearchFansResp}
// @Router /api/v1/room/admin/fans/search [get]
func (s *API) SearchFans(ctx *api.Context, req SearchFansReq) (*SearchFansResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := s.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	page, list, err := s.fm.Followers(ctx, uac.UserId, req.Keyword, req.Cursor)
	if err != nil {
		return nil, err
	}

	resp := &SearchFansResp{
		Total:  page.Total,
		Cursor: page.Cursor,
		List:   make([]*FansUser, 0, len(list)),
	}

	ua, err := s.um.User(ctx, ri.UserId)
	if err != nil {
		return nil, fmt.Errorf("get admin user: %w", err)
	}

	for _, f := range list {
		acc, err := s.ug.Account(ctx, f.UserId)
		if err != nil {
			s.logger.Error("get account", zap.Error(err), zap.String("userId", f.UserId))
			continue
		}

		resp.List = append(resp.List, &FansUser{
			User:      mixer.User(ctx, acc),
			IsManager: slices.Contains(ua.Admins, uac.UserId),
		})
	}

	return resp, nil
}

type PutBanWordsReq struct {
	RoomId string   `json:"roomId"`
	Words  []string `json:"words"`
}

// @Tags 直播间管理
// @Summary 设置禁言词
// @Description 设置禁言词
// @Produce json
// @Security HeaderAuth
// @Param param body PutBanWordsReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/room/admin/banwords [post]
func (a *API) PutBanWords(c *api.Context, req PutBanWordsReq) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	ri, err := a.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if err := a.um.HasPermission(c, uac.UserId, ri.UserId); err != nil {
		return nil, err
	}

	if err := a.um.SetBannedWords(c, uac.UserId, ri.UserId, req.Words, time.Now()); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type listUserBlacklistReq struct {
	Cursor string `form:"cursor"`
}

type listUserBlacklistResp struct {
	Total  int64                `json:"total"`
	Cursor string               `json:"cursor"`
	List   []*types.UserWithExt `json:"list"`
}

// @Tags 用户管理
// @Summary 黑名单列表
// @Description 黑名单列表
// @Produce json
// @Security HeaderAuth
// @Param param query listUserBlacklistReq true "请求参数"
// @Success 200 {object} listUserBlacklistResp
// @Router /api/v1/user/blacklist [get]
func (a *API) ListUserBlacklist(ctx *api.Context, req listUserBlacklistReq) (*listUserBlacklistResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	res, err := a.um.ListBlacklist(ctx, uac.UserId, req.Cursor)
	if err != nil {
		return nil, err
	}

	resp := listUserBlacklistResp{
		Total:  res.Total,
		Cursor: res.Cursor,
	}

	resp.List = make([]*types.UserWithExt, 0, len(res.Grants))
	for _, g := range res.Grants {
		acc, err := a.ug.Account(ctx, g.TargetId)
		if err != nil {
			a.logger.Error("get account", zap.Error(err), zap.String("userId", g.UserId))
			continue
		}

		resp.List = append(resp.List, mixer.UserWithExt(ctx, acc))
	}

	return &resp, nil
}

type userMuteRequest struct {
	Mute   bool   `json:"mute"`   // 是否禁言
	RoomId string `json:"roomId"` // 操作时的直播间id,尽量携带
	UserId string `json:"userId"` // 目标用户id
}

// @Tags 用户管理
// @Summary 禁言/解禁用户
// @Description 禁言/解禁用户
// @Produce json
// @Security HeaderAuth
// @Param param body userMuteRequest true "请求参数"
// @Success 200 {object} api.EmptyResp
// @Router /api/v1/user/mute [post]
func (a *API) UserMute(ctx *api.Context, req userMuteRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var sessionId string
	if req.RoomId != "" {
		s, err := a.lm.Room2(req.RoomId)
		if err != nil {
			return nil, err
		}
		sessionId = s.SessionId.Hex()
	}

	if req.Mute {
		if err := a.um.AddList(ctx, urm.GrantKindMute, uac.UserId, uac.UserId, req.UserId, sessionId, time.Now()); err != nil {
			return nil, err
		}
	} else {
		if err := a.um.RemoveList(ctx, urm.GrantKindMute, uac.UserId, uac.UserId, req.UserId); err != nil {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}
