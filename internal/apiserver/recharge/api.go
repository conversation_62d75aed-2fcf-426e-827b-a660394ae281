package recharge

import (
	"context"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/goods/recharge"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Provide(rs *recharge.Manager) *API {
	return &API{rs: rs}
}

func Invoke(r *api.Router, s *API) {
	ar := r.WithAuth()
	{
		ar.GET("/recharge/diamond", api.Generic(s.Diamond))
	}
}

type API struct {
	rs *recharge.Manager
}

type ConfigRequest struct {
	Scene string `form:"scene"` // 场景：官网写pc
}

type Customized struct {
	Min       int   `json:"min"`       // 最小充值数量
	Max       int   `json:"max"`       // 最大充值数量
	Unit      int   `json:"unit"`      // 最小充值单位
	Recommend []int `json:"recommend"` // 推荐充值数量
}

type ConfigResponse struct {
	Default    []int      `json:"default"`    // 默认充值数量
	Customized Customized `json:"customized"` // 自定义金额
}

// @Tags 充值
// @Summary 获取钻石充值配置
// @Description 获取钻石充值配置
// @Produce json
// @Security HeaderAuth
// @Param param query ConfigRequest true "请求参数"
// @Success 200 {object} codec.Response{data=ConfigResponse}
// @Router /api/v1/recharge/diamond [get]
func (s *API) Diamond(ctx *api.Context, req ConfigRequest) (*ConfigResponse, error) {
	return s.GetConfig(ctx, "", req.Scene)
}

func (s *API) GetConfig(ctx context.Context, userId, scene string) (*ConfigResponse, error) {
	var resp ConfigResponse

	list1, err := s.rs.List(ctx, scene)
	if err != nil {
		return nil, err
	}
	resp.Default = lo.Map(list1, func(item *recharge.Recharge, _ int) int { return item.Amount })

	list2, err := s.rs.CustomList(ctx, userId)
	if err != nil {
		return nil, err
	}
	resp.Customized.Recommend = lo.Map(list2, func(item *recharge.Recharge, _ int) int { return item.Amount })

	limit, err := s.rs.CustomLimit(ctx, userId)
	if err != nil {
		return nil, err
	}
	resp.Customized.Min = limit.Min
	resp.Customized.Max = limit.Max
	resp.Customized.Unit = limit.Unit

	return &resp, nil
}
