package upload

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func API(r *api.Router, sm *Scenes) {
	s := &apis{sm: sm}
	ar := r.With<PERSON>()
	{
		ar.GET("/upload/ststoken", api.Generic(s.UploadSTSToken))
	}
}

type apis struct {
	sm *Scenes
}

type tokenReq struct {
	Scene string `form:"scene" binding:"required"` // 场景
}

// @Tags 上传
// @Summary 获取上传配置
// @Description 按照场景获取上传配置
// @Produce json
// @Security HeaderAuth
// @Param param query tokenReq true "请求参数"
// @Success 200 {object} codec.Response{data=types.STSToken}
// @Router /api/v1/upload/ststoken [get]
func (s *apis) UploadSTSToken(c *api.Context, req tokenReq) (*types.STSToken, error) {
	return s.sm.Get(req.Scene, c)
}
