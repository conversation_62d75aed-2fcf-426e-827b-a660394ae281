package upload

import (
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sts"
)

func Provide(sts *sts.Mgr) *Scenes {
	return &Scenes{
		sts:  sts,
		list: make(map[string]provider),
	}
}

type provider func(*api.Context, *sts.Mgr) (*types.STSToken, error)

type Scenes struct {
	sts  *sts.Mgr
	list map[string]provider
}

func (s *Scenes) Add(name string, p provider) {
	if _, has := s.list[name]; has {
		panic(fmt.Sprintf("duplicated provider: %s", name))
	}
	s.list[name] = p
}

func (s *Scenes) Get(name string, c *api.Context) (*types.STSToken, error) {
	p, has := s.list[name]
	if !has {
		return nil, errors.New("no such provider: " + name)
	}
	return p(c, s.sts)
}

func (s *Scenes) Classic(module string, imageSuffix string) provider {
	return func(ctx *api.Context, stm *sts.Mgr) (*types.STSToken, error) {
		uac, err := ctx.User()
		if err != nil {
			return nil, err
		}
		resp, err := stm.AssumeRole(module, uac.UserId, sts.WithPrefix(module+"/"+uac.UserId+"/"+time.Now().Format("20060102")))
		if err != nil {
			return nil, err
		}
		return mixer.STSToken(resp, mixer.ImageSuffix(imageSuffix)), nil
	}
}
