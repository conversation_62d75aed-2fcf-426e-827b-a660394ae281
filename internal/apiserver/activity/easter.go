package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/easter"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type EasterInfoReq struct {
}

type EasterInfoResp struct {
	MyValue       int64 `json:"myValue"`       // 我的今日积分
	TargetValue   int64 `json:"targetValue"`   // 目标积分
	VehicleStatus int   `json:"vehicleStatus"` // 0未获得 1已获得 2已过期
	StartTime     int64 `json:"startTime"`     // 活动开始时间，unix秒级时间戳
	EndTime       int64 `json:"endTime"`       // 活动结束时间，unix秒级时间戳
}

// @Tags 活动
// @Summary 复活节活动
// @Description 复活节活动
// @Produce json
// @Param param query EasterInfoReq true "请求参数"
// @Success 200 {object} codec.Response{data=EasterInfoResp}
// @Router /api/v1/activity/easter/info [get]
func (s *apis) EasterInfo(ctx *api.Context, req EasterInfoReq) (*EasterInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	p := s.em.GetSES(time.Now())
	return &EasterInfoResp{
		MyValue:       s.em.UserValueInfo(ctx, uac.UserId),
		TargetValue:   easter.TargetValue,
		VehicleStatus: s.em.GetVehicleStatus(ctx, uac.UserId),
		StartTime:     p.StartTime.Unix(),
		EndTime:       p.EndTime.Unix(),
	}, nil
}
