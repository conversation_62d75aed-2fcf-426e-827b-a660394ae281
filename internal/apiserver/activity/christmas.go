package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/christmas"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type christmasInfoResp struct {
	StartTime int64              `json:"startTime"` // 活动开始时间：unix秒
	EndTime   int64              `json:"endTime"`   // 活动结束时间：unix秒
	Status    int                `json:"status"`    // 活动状态 1-未开始 2-进行中 3-已结束
	Round     *christmas.Round   `json:"round"`     // 每轮信息
	Records   []christmas.Record `json:"records"`   // 最近10轮开奖记录
}

// @Tags 活动
// @Summary 圣诞节活动-信息
// @Description 圣诞节活动-信息
// @Produce json
// @Success 200 {object} codec.Response{data=christmasInfoResp}
// @Router /api/v1/activity/christmas/info [get]
func (s *apis) christmasInfo(ctx *api.Context, _ api.EmptyReq) (*christmasInfoResp, error) {
	info, err := s.acm.Info(ctx)

	if err != nil {
		return nil, err
	}

	return &christmasInfoResp{
		StartTime: info.StartTime,
		EndTime:   info.EndTime,
		Status:    info.Status,
		Round:     info.Round,
		Records:   info.Records,
	}, nil
}

type christmasJoinReq struct{}

type christmasJoinResp struct{}

// @Tags 活动
// @Summary 圣诞节活动-加入
// @Description 圣诞节活动-加入 13001账户金币余额不足
// @Produce json
// @Security HeaderAuth
// @Param param body christmasJoinReq true "请求参数"
// @Success 200 {object} codec.Response{data=christmasJoinResp}
// @Router /api/v1/activity/christmas/join [post]
func (s *apis) christmasJoin(ctx *api.Context, req christmasJoinReq) (*christmasJoinResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	err = s.acm.Join(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	return &christmasJoinResp{}, nil
}

type christmasJoinRecordReq struct{}

type christmasJoinRecordResp struct {
	Records []christmasJoinRecord `json:"records"`
}

type christmasJoinRecord struct {
	WinGiftId   int   `json:"winGiftId"`   // 获得礼物ID
	RoundStatus int   `json:"roundStatus"` // 轮次状态 1进行中 2已开奖 3流局
	CreateTime  int64 `json:"createTime"`  // 时间，秒时间戳
}

// @Tags 活动
// @Summary 圣诞节活动-参与记录
// @Description 圣诞节活动-参与记录
// @Produce json
// @Security HeaderAuth
// @Param param query christmasJoinRecordReq true "请求参数"
// @Success 200 {object} codec.Response{data=christmasJoinRecordResp}
// @Router /api/v1/activity/christmas/join/record [get]
func (s *apis) christmasJoinRecord(ctx *api.Context, _ christmasJoinRecordReq) (*christmasJoinRecordResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	jrs, err := s.acm.JoinRecord(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	records := make([]christmasJoinRecord, 0, len(jrs))

	for _, v := range jrs {
		records = append(records, christmasJoinRecord{
			WinGiftId:   v.WinGiftId,
			RoundStatus: v.RoundStatus,
			CreateTime:  v.CreateTime,
		})
	}

	return &christmasJoinRecordResp{
		Records: records,
	}, nil
}

type christmasRecordsResp struct {
	Records []christmas.Record `json:"records"`
}

// @Tags 活动
// @Summary 圣诞节活动-幸运记录
// @Description 圣诞节活动-幸运记录
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=christmasRecordsResp}
// @Router /api/v1/activity/christmas/records [get]
func (s *apis) christmasRecords(ctx *api.Context, _ api.EmptyReq) (*christmasRecordsResp, error) {
	return &christmasRecordsResp{
		Records: s.acm.Records(ctx, 30),
	}, nil
}

type christmasRanklistUserResp struct {
	List []christmas.RankItem `json:"list"`
	Mine *christmas.RankMine  `json:"mine"`
}

// @Tags 活动
// @Summary 圣诞节活动-用户排行榜
// @Description 圣诞节活动-用户排行榜
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=christmasRanklistUserResp}
// @Router /api/v1/activity/christmas/ranklist/user [get]
func (s *apis) christmasRanklistUser(ctx *api.Context, _ api.EmptyReq) (*christmasRanklistUserResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	return &christmasRanklistUserResp{
		List: s.acm.RankUserList(ctx),
		Mine: s.acm.GetRankMine(ctx, uac.UserId),
	}, nil
}

type christmasRanklistAnhocrResp struct {
	List []christmas.RankItem `json:"list"`
}

// @Tags 活动
// @Summary 圣诞节活动-主播排行榜
// @Description 圣诞节活动-主播排行榜
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=christmasRanklistAnhocrResp}
// @Router /api/v1/activity/christmas/ranklist/anchor [get]
func (s *apis) christmasRanklistAnchor(ctx *api.Context, _ api.EmptyReq) (*christmasRanklistAnhocrResp, error) {
	return &christmasRanklistAnhocrResp{
		List: s.acm.RankAnchorList(ctx),
	}, nil
}

type christmasResultResp struct {
	UserId string           `json:"userId"`
	Round  *christmas.Round `json:"round"`
}

// @Tags 活动
// @Summary 圣诞节活动-未读的开奖结果
// @Description 圣诞节活动-未读的开奖结果
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=christmasResultResp}
// @Router /api/v1/activity/christmas/result [get]
func (s *apis) christmasResult(ctx *api.Context, _ api.EmptyReq) (*christmasResultResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	round, err := s.acm.Result(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	return &christmasResultResp{
		UserId: uac.UserId,
		Round:  round,
	}, nil
}

type christmasResultReadReq struct{}

type christmasResultReadResp struct{}

// @Tags 活动
// @Summary 圣诞节活动-确认开奖结果
// @Description 圣诞节活动-确认开奖结果
// @Produce json
// @Security HeaderAuth
// @Param param body christmasResultReadReq true "请求参数"
// @Success 200 {object} codec.Response{data=christmasResultReadResp}
// @Router /api/v1/activity/christmas/result/read [post]
func (s *apis) christmasResultRead(ctx *api.Context, req christmasResultReadReq) (*christmasResultReadResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.acm.ReadResult(ctx, uac.UserId); err != nil {
		return nil, err
	}

	return &christmasResultReadResp{}, nil
}
