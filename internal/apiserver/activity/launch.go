package activity

import (
	"context"
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/launch"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 全新上线，全民有礼活动

type launchInfoRequest struct{}

type launchInfoResponse struct {
	Status          int           `json:"status"`          // 活动状态 0：未开始 1：进行中 2：已结束仍能领取奖励 3：已结束
	StartTime       int64         `json:"startTime"`       // 活动开始时间，毫秒时间戳
	EndTime         int64         `json:"endTime"`         // 活动结束时间，毫秒时间戳
	Day             int           `json:"day"`             // 活动第几天
	RankToday       RankToday     `json:"rankToday"`       // 送礼排名今日排名
	RankYesterday   RankYesterday `json:"rankYesterday"`   // 送礼排名昨日排名
	RebateToday     Rebate        `json:"rebateToday"`     // 今日返利数据
	RebateYesterday Rebate        `json:"rebateYesterday"` // 昨日返利数据
}

type RankToday struct {
	Rank
	Countdown int64 `json:"countdown"` // 倒计时，毫秒
}

type RankYesterday struct {
	Rank
	Status int `json:"status"` // 领取状态 0：不可领取 1：可领取 2：已领取
}

type Rank struct {
	List []RankUser `json:"list"` // 排名列表
	Mine *RankUser  `json:"mine"` // 我的排名
}

type RankUser struct {
	User  *types.User `json:"user"`  // 用户信息
	No    int         `json:"no"`    // 排名
	Score int64       `json:"score"` // 分数
}

type Rebate struct {
	SendDiamond   int64 `json:"sendDiamond"`   // 送出金币
	RebateDiamond int64 `json:"rebateDiamond"` // 返利金币
	Status        int   `json:"status"`        // 领取状态 0：不可领取 1：可领取 2：已领取
}

// @Tags 活动
// @Summary 上线活动-信息
// @Description 上线活动-信息
// @Produce json
// @Security HeaderAuth
// @Param param query launchInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=launchInfoResponse}
// @Router /api/v1/activity/launch/info [get]
func (s *apis) LaunchInfo(ctx *api.Context, req launchInfoRequest) (*launchInfoResponse, error) {
	var userId string

	uac, err := ctx.User()

	if err == nil {
		userId = uac.UserId
	}

	// 当前时间
	nn := launch.GetNowNow()

	// 活动状态
	info := s.alm.Info(nn)

	// 今日排名数据
	tr, err := s.alm.GetTodayRank(ctx, nn)

	if err != nil {
		return nil, err
	}

	tur, err := s.alm.GetUserTodayRank(ctx, nn, userId)

	if err != nil {
		return nil, err
	}

	todayRank := Rank{
		List: s.rankUserList(ctx, tr),
		Mine: s.rankUser(ctx, tur),
	}

	// 昨日排名数据
	yr, err := s.alm.GetYesterdayRank(ctx, nn)

	if err != nil {
		return nil, err
	}

	yur, err := s.alm.GetUserYesterdayRank(ctx, nn, userId)

	if err != nil {
		return nil, err
	}

	yesterdayRank := Rank{
		List: s.rankUserList(ctx, yr),
		Mine: s.rankUser(ctx, yur),
	}

	yRankStatus := 0

	if yesterdayRank.Mine.No <= 10 {
		// 查询领取状态
		yRankStatus = s.alm.GetRankReceiveStatus(ctx, userId, nn)
	}

	yRebateDiamond := s.alm.GetRebateAward(yesterdayRank.Mine.Score)

	yRebateStatus := 0

	if yRebateDiamond > 0 {
		// 查询领取状态
		yRebateStatus = s.alm.GetRebateReceiveStatus(ctx, userId, nn)
	}

	return &launchInfoResponse{
		Status:    info.Status,
		StartTime: info.StartTime,
		EndTime:   info.EndTime,
		Day:       info.Day,
		RankToday: RankToday{
			Rank:      todayRank,
			Countdown: launch.TodayCountdown(nn),
		},
		RankYesterday: RankYesterday{
			Rank:   yesterdayRank,
			Status: yRankStatus,
		},
		RebateToday: Rebate{
			SendDiamond:   todayRank.Mine.Score,
			RebateDiamond: s.alm.GetRebateAward(todayRank.Mine.Score),
		},
		RebateYesterday: Rebate{
			SendDiamond:   yesterdayRank.Mine.Score,
			RebateDiamond: yRebateDiamond,
			Status:        yRebateStatus,
		},
	}, nil
}

type launchReceiveRequest struct {
	Award string `json:"award"` // rank：排名 rebate：返利
}

type launchReceiveResponse struct {
	Diamond int64 `json:"diamond"` // 领取钻石数量
}

// @Tags 活动
// @Summary 上线活动-奖励领取
// @Description 上线活动-奖励领取
// @Produce json
// @Security HeaderAuth
// @Param param body launchReceiveRequest true "请求参数"
// @Success 200 {object} codec.Response{data=launchReceiveResponse}
// @Router /api/v1/activity/launch/receive [post]
func (s *apis) LaunchReceive(ctx *api.Context, req launchReceiveRequest) (*launchReceiveResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	award := req.Award

	l, err := s.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:AL:RECEIVE:%s", userId))

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "Please try again later")
	}
	defer l.MustUnlock()

	// 当前时间
	nn := launch.GetNowNow()

	var diamond int64

	switch award {
	case "rank":
		// 领取排名奖励
		diamond, err = s.alm.ReceiveRankAward(ctx, userId, nn)

		if err != nil {
			return nil, err
		}
	case "rebate":
		// 领取返利奖励
		diamond, err = s.alm.ReceiveRebateAward(ctx, userId, nn)

		if err != nil {
			return nil, err
		}
	default:
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid award param")
	}

	return &launchReceiveResponse{
		Diamond: diamond,
	}, nil
}

func (s *apis) getTypesUser(ctx context.Context, userId string) *types.User {
	var acc *user.Account
	var err error

	if userId != "" {
		acc, err = s.ug.Account(ctx, userId)

		if err != nil {
			s.log.Error("活动榜单获取用户信息失败", zap.String("userId", userId), zap.Error(err))
		}
	}

	if acc == nil {
		return &types.User{}
	}

	return mixer.User(ctx, acc)
}

func (s *apis) rankUserList(ctx context.Context, list []launch.Rank) []RankUser {
	ret := make([]RankUser, 0)

	for _, v := range list {
		ret = append(ret, *s.rankUser(ctx, &v))
	}

	return ret
}

func (s *apis) rankUser(ctx context.Context, rank *launch.Rank) *RankUser {
	return &RankUser{
		User:  s.getTypesUser(ctx, rank.UserId),
		No:    rank.No,
		Score: rank.Diamond,
	}
}
