package activity

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type valentinesRankingHistoryReq struct {
}

type valentinesRankingHistoryResp struct {
	LastPeriodTop3  PeriodTop3   `json:"lastPeriodTop3"`
	OtherPeriodTop3 []PeriodTop3 `json:"otherPeriodTop3"`
}

type PeriodTop3 struct {
	StartTime int64                   `json:"startTime"`
	EndTime   int64                   `json:"endTime"`
	Ranks     []ValentinesRankingUser `json:"ranks"`
}

type ValentinesRankingUser struct {
	Rank  int         `json:"rank"`
	Value int         `json:"value"`
	User  *types.User `json:"user"` // 用户信息
}

// @Tags 活动
// @Summary 情人节活动历史榜单
// @Description 情人节活动-历史榜单
// @Produce json
// @Param param query valentinesRankingHistoryReq true "请求参数"
// @Success 200 {object} codec.Response{data=valentinesRankingHistoryResp}
// @Router /api/v1/activity/valentines/ranking/history [get]
func (s *apis) rankingHistory(ctx *api.Context, req valentinesRankingHistoryReq) (*valentinesRankingHistoryResp, error) {
	lastPeriod, otherPeriod := s.vdm.RankingHistory()

	var (
		lastPeriodTop3  PeriodTop3
		otherPeriodTop3 []PeriodTop3
	)
	if !lastPeriod.StartTime.IsZero() {
		lastPeriodTop3.StartTime = lastPeriod.StartTime.Unix()
		lastPeriodTop3.EndTime = lastPeriod.EndTime.Unix()
		if len(lastPeriod.Top3) > 0 {
			for _, v := range lastPeriod.Top3 {
				lastPeriodTop3.Ranks = append(lastPeriodTop3.Ranks, ValentinesRankingUser{
					Rank:  v.Rank,
					Value: v.Value,
					User:  mixer.User(ctx, v.User),
				})
			}
		}
	}

	for _, p := range otherPeriod {
		var periodTop3 PeriodTop3
		periodTop3.StartTime = p.StartTime.Unix()
		periodTop3.EndTime = p.EndTime.Unix()
		if len(p.Top3) > 0 {
			for _, v := range p.Top3 {
				periodTop3.Ranks = append(periodTop3.Ranks, ValentinesRankingUser{
					Rank:  v.Rank,
					Value: v.Value,
					User:  mixer.User(ctx, v.User),
				})
			}
		}
		otherPeriodTop3 = append(otherPeriodTop3, periodTop3)
	}

	return &valentinesRankingHistoryResp{
		LastPeriodTop3:  lastPeriodTop3,
		OtherPeriodTop3: otherPeriodTop3,
	}, nil
}

type ValentinesRankingCurrentReq struct {
	AnchorUserId string `form:"anchorUserId"` // 房间主播id
}

type ValentinesRankingCurrentResp struct {
	RankStatus          int                     `json:"rankStatus"`          // 0未开始，显示剩余开始时间; 1已开始，显示剩余结束时间
	RankStartSeconds    int64                   `json:"rankStartSeconds"`    // 到下一个时段剩余时间
	PeriodRemainSeconds int64                   `json:"periodRemainSeconds"` // 当前时段剩余秒数
	HostRank            *ValentinesRankingUser  `json:"hostRank"`            // 主播排名
	Ranks               []ValentinesRankingUser `json:"ranks"`               // top10
}

// @Tags 活动
// @Summary 情人节活动当前榜单
// @Description 情人节活动-当前时段榜单
// @Produce json
// @Param param query ValentinesRankingCurrentReq true "请求参数"
// @Success 200 {object} codec.Response{data=ValentinesRankingCurrentResp}
// @Router /api/v1/activity/valentines/ranking/current [get]
func (s *apis) rankingCurrent(ctx *api.Context, req ValentinesRankingCurrentReq) (*ValentinesRankingCurrentResp, error) {
	hostRank, rankList, periodRemainSeconds, err := s.vdm.RankingCurrent(ctx, req.AnchorUserId)
	if err != nil {
		return nil, err
	}

	ranks := make([]ValentinesRankingUser, 0, 10)
	for _, v := range rankList {
		ranks = append(ranks, ValentinesRankingUser{
			Rank:  v.Rank,
			Value: v.Value,
			User:  mixer.User(ctx, v.User),
		})
	}

	periodRemainSeconds, rankStartSeconds := s.vdm.GetPeriodSeconds()

	var valentinesRankingUser *ValentinesRankingUser
	if hostRank != nil {
		valentinesRankingUser = &ValentinesRankingUser{
			Rank:  hostRank.Rank,
			Value: hostRank.Value,
			User:  mixer.User(ctx, hostRank.User),
		}
	}

	return &ValentinesRankingCurrentResp{
		RankStatus:          lo.Ternary(periodRemainSeconds > 0, 1, 0),
		RankStartSeconds:    rankStartSeconds,
		PeriodRemainSeconds: periodRemainSeconds,
		HostRank:            valentinesRankingUser,
		Ranks:               ranks,
	}, nil
}
