package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type VehicleInfoReq struct {
}

type VehicleInfoResp struct {
	MyValue   int64        `json:"myValue"`
	MyVehicle *VehicleItem `json:"myVehicle"`
	StartTime int64        `json:"startTime"`
	EndTime   int64        `json:"endTime"`
}

type VehicleItem struct {
	Id     string `json:"id"`
	Expire int64  `json:"expire"`
}

// @Tags 活动
// @Summary 充值送座驾活动
// @Description 充值送座驾活动
// @Produce json
// @Param param query VehicleInfoReq true "请求参数"
// @Success 200 {object} codec.Response{data=VehicleInfoResp}
// @Router /api/v1/activity/vehicle/info [get]
func (s *apis) ActivityVehicleInfo(ctx *api.Context, req VehicleInfoReq) (*VehicleInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var myVehicle *VehicleItem
	if s.pvm.InPeriod(time.Now()) {
		p, err := s.drm.Take(ctx, uac.UserId)
		if err == nil && p != nil && p.Vehicle != nil {
			myVehicle = &VehicleItem{
				Id:     p.Vehicle.Id,
				Expire: p.Vehicle.ExpireAt.In(ctz.Brazil).Unix(),
			}
		}
	}

	p := s.pvm.GetPeriod(time.Now())
	return &VehicleInfoResp{
		MyValue:   s.pvm.UserValueInfo(ctx, uac.UserId),
		MyVehicle: myVehicle,
		StartTime: p.StartTime.Unix(),
		EndTime:   p.EndTime.Unix(),
	}, nil
}
