package activity

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type BlindBoxRankingUser struct {
	Rank  int         `json:"rank"`
	Value int         `json:"value"`
	User  *types.User `json:"user"` // 用户信息
}

type BlindBoxRankingReq struct{}

type BlindBoxRankingResp struct {
	Ranks     []BlindBoxRankingUser `json:"ranks"`     // top20榜单
	MyRank    BlindBoxRankingUser   `json:"myRank"`    // 自己的排名
	StartTime int64                 `json:"startTime"` // 开始时间戳
	EndTime   int64                 `json:"endTime"`   // 结束时间戳
}

// @Tags 活动
// @Summary 盲盒送礼活动-榜单
// @Description 盲盒送礼活动
// @Produce json
// @Param param query BlindBoxRankingReq true "请求参数"
// @Success 200 {object} codec.Response{data=BlindBoxRankingResp}
// @Router /api/v1/activity/blindbox/ranking [get]
func (s *apis) BlindBoxRanking(ctx *api.Context, req BlindBoxRankingReq) (*BlindBoxRankingResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	t := time.Now()
	ses := s.bbm.GetSES(t)
	// 当前不在活动内，查看前一天是否在
	if ses.Stage == "" {
		ses = s.bbm.GetSES(t.AddDate(0, 0, -1))
	}
	// 前一天没有活动，查找7天内最近的活动
	if ses.Stage == "" {
		for i := 1; i <= 7; i++ {
			ses = s.bbm.GetSES(t.AddDate(0, 0, i))
			if ses.Stage != "" {
				break
			}
		}
	}

	var (
		ranks  []BlindBoxRankingUser
		myRank BlindBoxRankingUser
	)
	userRank, ranking, err := s.bbm.GetRanking(ctx, ses.Stage, uac.UserId)
	if err != nil {
		return nil, err
	}

	myRank = BlindBoxRankingUser{
		Rank:  userRank.Rank,
		Value: userRank.Value,
		User:  mixer.User(ctx, userRank.User),
	}

	for _, v := range ranking {
		ranks = append(ranks, BlindBoxRankingUser{
			Rank:  v.Rank,
			Value: v.Value,
			User:  mixer.User(ctx, v.User),
		})
	}

	return &BlindBoxRankingResp{
		Ranks:  ranks,
		MyRank: myRank,
		//RemainSeconds: int64(ses.EndTime.Sub(time.Now().In(ctz.Brazil)).Seconds()),
		StartTime: lo.Ternary(ses.StartTime.IsZero(), 0, ses.StartTime.Unix()),
		EndTime:   lo.Ternary(ses.EndTime.IsZero(), 0, ses.EndTime.Unix()),
	}, nil
}
