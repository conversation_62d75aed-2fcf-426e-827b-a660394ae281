package activity

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type hourlyRankHistoryReq struct {
}

type hourlyRankHistoryResp struct {
	List []HourlyRankDay `json:"list"` // 历史榜单列表
}

type HourlyRankDay struct {
	Day        int64                  `json:"day"`        // Unix时间戳
	PeriodRank []HourlyRankPeriodTop3 `json:"periodRank"` // 时段榜单
}

type HourlyRankPeriodTop3 struct {
	StartTime int64            `json:"startTime"` // Unix时间戳
	EndTime   int64            `json:"endTime"`   // Unix时间戳
	Ranks     []HourlyRankUser `json:"ranks"`     // top3
}

type HourlyRankUser struct {
	Rank    int         `json:"rank"`          // 排名
	Value   int64       `json:"value"`         // 分数值
	Gap     int64       `json:"gap,omitempty"` // 分差,rank==1是与第二名差距、rank!=1为与上一名差距;只在current接口的HostRank中有此字段
	Reward  int         `json:"reward"`        // 奖励水晶
	IsValid bool        `json:"isValid"`       // 是否符合发奖条件，只有在history接口有此字段
	User    *types.User `json:"user"`          // 用户信息
}

// @Tags 活动
// @Summary 小时榜活动历史榜单
// @Description 小时榜活动-历史榜单
// @Produce json
// @Param param query hourlyRankHistoryReq true "请求参数"
// @Success 200 {object} codec.Response{data=hourlyRankHistoryResp}
// @Router /api/v1/activity/houlyrank/history [get]
func (s *apis) hourlyRankHistory(ctx *api.Context, req hourlyRankHistoryReq) (*hourlyRankHistoryResp, error) {
	dr, err := s.hrm.RankingHistory(ctx)
	if err != nil {
		return nil, err
	}

	var list []HourlyRankDay
	for _, v := range dr {
		var periodRank []HourlyRankPeriodTop3
		for _, pr := range v.PeriodRank {
			var ranks []HourlyRankUser
			for _, r := range pr.Top3 {
				ranks = append(ranks, HourlyRankUser{
					Rank:    r.Rank,
					Value:   r.Value,
					Reward:  r.Reward,
					IsValid: r.IsValid,
					User:    mixer.User(ctx, r.User),
				})
			}
			periodRank = append(periodRank, HourlyRankPeriodTop3{
				StartTime: pr.StartTime.Unix(),
				EndTime:   pr.EndTime.Unix(),
				Ranks:     ranks,
			})
		}

		list = append(list, HourlyRankDay{
			Day:        v.Day.Unix(),
			PeriodRank: periodRank,
		})
	}

	return &hourlyRankHistoryResp{
		List: list,
	}, nil
}

type hourlyRankCurrentReq struct {
	AnchorUserId string `form:"anchorUserId"` // 房间主播id
}

type hourlyRankCurrentResp struct {
	StartTime           int64            `json:"startTime"`           // 活动开始时间，Unix秒级时间戳
	EndTime             int64            `json:"endTime"`             // 活动结束时间，Unix秒级时间戳
	PeriodStatus        int              `json:"periodStatus"`        // 阶段状态：0未开始，显示剩余开始时间; 1已开始，显示剩余结束时间
	PeriodWaitSeconds   int64            `json:"periodWaitSeconds"`   // 截止时间戳
	PeriodRemainSeconds int64            `json:"periodRemainSeconds"` // 截止时间戳
	HostRank            *HourlyRankUser  `json:"hostRank"`            // 主播排名
	Ranks               []HourlyRankUser `json:"ranks"`               // top3
}

// @Tags 活动
// @Summary 小时榜活动当前榜单
// @Description 小时榜活动-当前榜单
// @Produce json
// @Param param query hourlyRankCurrentReq true "请求参数"
// @Success 200 {object} codec.Response{data=hourlyRankCurrentResp}
// @Router /api/v1/activity/hourlyrank/current [get]
func (s *apis) hourlyRankCurrent(ctx *api.Context, req hourlyRankCurrentReq) (*hourlyRankCurrentResp, error) {
	var (
		ranks                                  []HourlyRankUser
		hourlyRankUser                         *HourlyRankUser
		startTime, endTime                     int64
		periodRemainSeconds, periodWaitSeconds int64
		now                                    = time.Now().In(ctz.Brazil)
	)
	ses := s.hrm.GetSES(now)
	if ses.Stage == "" {
		ses = s.hrm.GetSES(now.AddDate(0, 0, -1))
	}
	if ses.Stage != "" {
		startTime = ses.StartTime.Unix()
		endTime = ses.EndTime.Unix()
		// period status计算
		if ses.EndTime.After(now) {
			today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, ctz.Brazil)
			for _, p := range ses.Period {
				pts := today.Add(time.Duration(p) * time.Hour)
				pte := today.Add(time.Duration(p+1) * time.Hour)
				if pte.Before(now) {
					continue
				}
				if ses.Connection == false &&
					pts.Month() == ses.StartTime.Month() &&
					pts.Day() == ses.StartTime.Day() &&
					pts.Hour() == 0 {
					continue
				}
				if pts.Before(now) && pte.After(now) {
					periodRemainSeconds = now.Truncate(time.Hour).Add(time.Hour).Unix()
					break
				}
				if pts.After(now) {
					periodWaitSeconds = pts.Unix()
					break
				}
			}
		}
	}

	if s.hrm.InPeriod(now) {
		hostRank, rankList, err := s.hrm.RankingCurrent(ctx, req.AnchorUserId)
		if err != nil {
			return nil, err
		}

		ranks = make([]HourlyRankUser, 0, 10)
		for _, v := range rankList {
			ranks = append(ranks, HourlyRankUser{
				Rank:   v.Rank,
				Value:  v.Value,
				Reward: v.Reward,
				User:   mixer.User(ctx, v.User),
			})
		}

		if hostRank != nil {
			hourlyRankUser = &HourlyRankUser{
				Rank:   hostRank.Rank,
				Value:  hostRank.Value,
				Gap:    hostRank.Gap,
				Reward: hostRank.Reward,
				User:   mixer.User(ctx, hostRank.User),
			}
		}
	}

	return &hourlyRankCurrentResp{
		StartTime:           startTime,
		EndTime:             endTime,
		PeriodStatus:        lo.Ternary(periodRemainSeconds > 0, 1, 0),
		PeriodWaitSeconds:   periodWaitSeconds,
		PeriodRemainSeconds: periodRemainSeconds,
		HostRank:            hourlyRankUser,
		Ranks:               ranks,
	}, nil
}
