package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/foolsday"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type FoolsdayInfoReq struct {
}

type FoolsdayInfoResp struct {
	MyValue     int64    `json:"myValue"`
	TargetValue int64    `json:"targetValue"`
	MyVehicle   []string `json:"myVehicle"`
	StartTime   int64    `json:"startTime"`
	EndTime     int64    `json:"endTime"`
}

// @Tags 活动
// @Summary 愚人节活动
// @Description 愚人节活动
// @Produce json
// @Param param query FoolsdayInfoReq true "请求参数"
// @Success 200 {object} codec.Response{data=FoolsdayInfoResp}
// @Router /api/v1/activity/foolsday/info [get]
func (s *apis) ActivityFoolsdayInfo(ctx *api.Context, req FoolsdayInfoReq) (*FoolsdayInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	p := s.fdm.GetSES(time.Now())
	return &FoolsdayInfoResp{
		MyValue:     s.fdm.UserValueInfo(ctx, uac.UserId),
		TargetValue: foolsday.TargetValue,
		MyVehicle:   s.fdm.UserGetVehicle(ctx, uac.UserId),
		StartTime:   p.StartTime.Unix(),
		EndTime:     p.EndTime.Unix(),
	}, nil
}
