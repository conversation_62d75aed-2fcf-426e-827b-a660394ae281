package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

// @Tags 活动
// @Summary 幸运之星榜单
// @Description 幸运之星榜单
// @Produce json
// @Security HeaderAuth
// @Param param query LuckyInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=LuckyInfoResponse}
// @Router /api/v1/activity/lucky/info [get]
func (s *apis) LuckyInfo(ctx *api.Context, req LuckyInfoRequest) (*LuckyInfoResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		todayRankInfo     []GiftLuckyInfo
		yesterdayRankInfo []GiftLuckyInfo
	)

	info, err := s.lm.GetInfo(ctx, "today", uac.UserId)
	if err != nil {
		return nil, err
	}
	for _, v := range info.RankInfo {
		gli := GiftLuckyInfo{
			GiftId:       v.GiftId,
			GiftImageUrl: v.GiftImageUrl,
			Award:        v.Award,
		}

		if v.LuckyStar != nil && v.LuckyStar.User != nil {
			gli.LuckyStar = UserRank{
				User:       mixer.User(ctx, v.LuckyStar.User),
				LuckyTimes: v.LuckyStar.LuckyTimes,
			}
		}

		if v.MyRanking != nil {
			gli.MyRanking = UserRank{
				User:       mixer.User(ctx, uac),
				LuckyTimes: v.MyRanking.LuckyTimes,
			}
		}

		todayRankInfo = append(todayRankInfo, gli)
	}

	yesterdayInfo, err := s.lm.GetInfo(ctx, "yesterday", uac.UserId)
	if err != nil {
		return nil, err
	}
	for _, v := range yesterdayInfo.RankInfo {
		gli := GiftLuckyInfo{
			GiftId:       v.GiftId,
			GiftImageUrl: v.GiftImageUrl,
			Award:        v.Award,
		}

		if v.LuckyStar != nil && v.LuckyStar.User != nil {
			gli.LuckyStar = UserRank{
				User:       mixer.User(ctx, v.LuckyStar.User),
				LuckyTimes: v.LuckyStar.LuckyTimes,
			}
		}

		if v.MyRanking != nil {
			gli.MyRanking = UserRank{
				User:       mixer.User(ctx, uac),
				LuckyTimes: v.MyRanking.LuckyTimes,
			}
		}

		yesterdayRankInfo = append(yesterdayRankInfo, gli)
	}

	return &LuckyInfoResponse{
		StartAt:           info.StartAt,
		EndAt:             info.EndAt,
		TodayRankInfo:     todayRankInfo,
		YesterdayRankInfo: yesterdayRankInfo,
	}, nil
}
