package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/agency_newbie"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type agencyNewbieInfoReq struct {
}

type agencyNewbieInfoResp struct {
	StageList []agencyNewbieStage `json:"stageList"`
}

type agencyNewbieStage struct {
	StageId         string               `json:"stageId"`
	StartTime       int64                `json:"startTime"`       // 活动开始时间：unix秒
	EndTime         int64                `json:"endTime"`         // 活动结束时间：unix秒
	InviteList      []InviteAnchorDetail `json:"inviteList"`      // 邀请明细
	InviteNum       int                  `json:"inviteNum"`       // 已邀请人数
	EffectiveNum    int                  `json:"effectiveNum"`    // 有效人数
	Award           int64                `json:"reward"`          // 预估数量
	IsAwardReceived bool                 `json:"isAwardReceived"` // 奖励已领取
	InviteCode      string               `json:"inviteCode"`      // 公会邀请码
	Status          int                  `json:"status"`          // 阶段状态 0未开始 1进行中 2已结束
}

type InviteAnchorDetail struct {
	Anchor   *types.UserWithExt `json:"anchor"`   // 主播信息
	JoinTime int64              `json:"joinTime"` // 加入公会时间：unix秒
	Status   int                `json:"status"`   // 0:待主播审核 1:待完成扶持任务 2:有效
}

// @Tags 活动
// @Summary 公会拉新奖励-信息
// @Description 公会拉新奖励-信息
// @Produce json
// @Param param query agencyNewbieInfoReq true "请求参数"
// @Success 200 {object} codec.Response{data=agencyNewbieInfoResp}
// @Router /api/v1/activity/agency/newbie/info [get]
func (s *apis) agencyNewbieInfo(ctx *api.Context, req agencyNewbieInfoReq) (*agencyNewbieInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 公会信息
	agencyInfo, err := s.am.MyOwnAgency(ctx, uac.UserId)
	if err != nil {
		return nil, biz.NewError(biz.ErrNoAuthority, "no authority")
	}

	var (
		res agencyNewbieInfoResp
		now = time.Now().In(ctz.Brazil)
	)
	for _, stage := range s.anm.GetActivityStageInfo() {
		// 获取邀请列表
		anchorList, err := s.anm.GetInviteAnchorList(ctx, stage.StageId, int(agencyInfo.ID))
		if err != nil {
			return nil, err
		}

		// 获取用户信息
		var (
			inviteAnchorList []InviteAnchorDetail
			effectiveNum     int
			award            int64
			status           int
		)
		for _, v := range anchorList {
			acc, err := s.ug.Account(ctx, v.UserId)
			if err != nil {
				return nil, err
			}
			inviteAnchorList = append(inviteAnchorList, InviteAnchorDetail{
				Anchor:   mixer.UserWithExt(ctx, acc),
				JoinTime: v.CreateAt.In(ctz.Brazil).Unix(),
				Status:   v.Status,
			})
			if v.Status == agency_newbie.NewbieAnchorStatusEffective {
				effectiveNum++
			}
		}
		// 奖励计算
		if effectiveNum < 5 {
			award = int64(effectiveNum) * 5000
		} else if effectiveNum >= 5 && effectiveNum < 10 {
			award = int64(effectiveNum) * 10000
		} else {
			award = int64(effectiveNum) * 15000
		}

		if now.Before(stage.StartTime) {
			status = 0
		} else if now.After(stage.StartTime) && now.Before(stage.EndTime) {
			status = 1
		} else if now.After(stage.EndTime) {
			status = 2
		}

		res.StageList = append(res.StageList, agencyNewbieStage{
			StageId:         stage.StageId,
			StartTime:       stage.StartTime.Unix(),
			EndTime:         stage.EndTime.Unix(),
			InviteList:      inviteAnchorList,
			InviteNum:       len(anchorList),
			EffectiveNum:    effectiveNum,
			Award:           award,
			IsAwardReceived: s.anm.IsAwardReceived(ctx, stage.StageId, int(agencyInfo.ID), uac.UserId),
			InviteCode:      agencyInfo.ShowId,
			Status:          status,
		})
	}

	return &res, nil
}

type agencyNewbieAwardReq struct {
	StageId string `json:"stageId" binding:"required"`
}

type agencyNewbieAwardResp struct {
	Award int64 `json:"reward"` // 领取的金币数量
}

// @Tags 活动
// @Summary 公会拉新奖励-领取金币
// @Description 公会拉新奖励-领取金币
// @Produce json
// @Param param query agencyNewbieAwardReq true "请求参数"
// @Success 200 {object} codec.Response{data=agencyNewbieAwardResp}
// @Router /api/v1/activity/agency/newbie/award [post]
func (s *apis) agencyNewbieAward(ctx *api.Context, req agencyNewbieAwardReq) (*agencyNewbieAwardResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 公会信息
	agencyInfo, err := s.am.MyOwnAgency(ctx, uac.UserId)
	if err != nil {
		return nil, biz.NewError(biz.ErrNoAuthority, "no authority")
	}

	// 阶段判断
	var (
		award        int64
		effectiveNum int
	)
	for _, stage := range s.anm.GetActivityStageInfo() {
		if stage.StageId != req.StageId {
			continue
		}

		if time.Now().In(ctz.Brazil).Before(stage.EndTime) {
			continue
		}

		// 获取邀请列表
		anchorList, err := s.anm.GetInviteAnchorList(ctx, stage.StageId, int(agencyInfo.ID))
		if err != nil {
			return nil, err
		}

		// 获取用户信息
		for _, v := range anchorList {
			if v.Status == agency_newbie.NewbieAnchorStatusEffective {
				effectiveNum++
			}
		}
	}

	if effectiveNum == 0 {
		// todo 无奖励领取
		return nil, biz.NewError(biz.ErrBusiness, "no award")
	}

	// 奖励计算
	if effectiveNum < 5 {
		award = int64(effectiveNum) * 5000
	} else if effectiveNum >= 5 && effectiveNum < 10 {
		award = int64(effectiveNum) * 10000
	} else {
		award = int64(effectiveNum) * 15000
	}

	if err := s.anm.AwardReceive(ctx, req.StageId, int(agencyInfo.ID), uac.UserId, award); err != nil {
		return nil, err
	}

	// 过了对应阶段的时间才能领取
	return &agencyNewbieAwardResp{Award: award}, nil
}
