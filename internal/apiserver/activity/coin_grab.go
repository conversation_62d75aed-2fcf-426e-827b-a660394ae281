package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

// @Tags 活动
// @Summary 抢金赛榜单
// @Description 抢金赛榜单
// @Produce json
// @Security HeaderAuth
// @Param param query CoinGrabInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=CoinGrabInfoResponse}
// @Router /api/v1/activity/coingrab/info [get]
func (s *apis) CoinGrabInfo(ctx *api.Context, req CoinGrabInfoRequest) (*CoinGrabInfoResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		todayRankInfo     CoinGrabRankInfo
		yesterdayRankInfo CoinGrabRankInfo
	)

	info, err := s.cgm.GetRank(ctx, "today", uac.UserId)
	if err != nil {
		return nil, err
	}
	for _, v := range info.RankInfo {
		r := CoinGrabUserRank{
			Rank:   v.Rank,
			User:   mixer.User(ctx, v.User),
			Value:  v.Value,
			Reward: v.Reward,
		}
		todayRankInfo.RankList = append(todayRankInfo.RankList, r)
	}

	todayRankInfo.MyRank = CoinGrabUserRank{
		User: mixer.User(ctx, uac),
	}
	if info.MyRank != nil {
		todayRankInfo.MyRank.Rank = info.MyRank.Rank
		todayRankInfo.MyRank.Value = info.MyRank.Value
		todayRankInfo.MyRank.Reward = info.MyRank.Reward
	}

	yesterdayInfo, err := s.cgm.GetRank(ctx, "yesterday", uac.UserId)
	if err != nil {
		return nil, err
	}
	for _, v := range yesterdayInfo.RankInfo {
		r := CoinGrabUserRank{
			Rank:   v.Rank,
			User:   mixer.User(ctx, v.User),
			Value:  v.Value,
			Reward: v.Reward,
		}
		yesterdayRankInfo.RankList = append(yesterdayRankInfo.RankList, r)
	}
	yesterdayRankInfo.MyRank = CoinGrabUserRank{
		User: mixer.User(ctx, uac),
	}
	if yesterdayInfo.MyRank != nil {
		yesterdayRankInfo.MyRank.Rank = yesterdayInfo.MyRank.Rank
		yesterdayRankInfo.MyRank.Value = yesterdayInfo.MyRank.Value
		yesterdayRankInfo.MyRank.Reward = yesterdayInfo.MyRank.Reward
	}

	// 昨日奖励是否已领取
	yesterdayRankInfo.IsReceive = s.cgm.IsReceive(ctx, time.Now().In(ctz.Brazil).AddDate(0, 0, -1), uac.UserId)

	return &CoinGrabInfoResponse{
		StartAt:           info.StartAt,
		EndAt:             info.EndAt,
		CoinPool:          info.CoinPool,
		TodayRankInfo:     todayRankInfo,
		YesterdayRankInfo: yesterdayRankInfo,
	}, nil
}

// @Tags 活动
// @Summary 抢金赛直播间挂件
// @Description 抢金赛直播间挂件
// @Produce json
// @Security HeaderAuth
// @Param param query CoinGrabWidgetRequest true "请求参数"
// @Success 200 {object} codec.Response{data=CoinGrabWidgetResponse}
// @Router /api/v1/activity/coingrab/widget [get]
func (s *apis) CoinGrabRoomWidget(ctx *api.Context, req CoinGrabWidgetRequest) (*CoinGrabWidgetResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	myRank, coinPoll := s.cgm.GetRoomWidget(ctx, uac.UserId)

	return &CoinGrabWidgetResponse{
		CoinPool: coinPoll,
		MyRank:   myRank,
	}, nil
}

// @Tags 活动
// @Summary 抢金赛奖励领取
// @Description 抢金赛奖励领取
// @Produce json
// @Security HeaderAuth
// @Param param query CoinGrabRewardReceiveRequest true "请求参数"
// @Success 200 {object} codec.Response{data=CoinGrabRewardReceiveResponse}
// @Router /api/v1/activity/coingrab/receive [post]
func (s *apis) CoinGrabRewardReceive(ctx *api.Context, req CoinGrabRewardReceiveRequest) (*CoinGrabRewardReceiveResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	rank, reward, err := s.cgm.RewardReceive(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &CoinGrabRewardReceiveResponse{
		User:   mixer.User(ctx, uac),
		Rank:   rank,
		Reward: reward,
	}, nil
}
