package activity

// 主播公会日榜

import (
	"context"
	"strconv"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rlaa"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

type RlaaInfoResponse struct {
	Status              int               `json:"status"`              // 活动状态 0：未开始 1：进行中 2：已结束
	StartTime           int64             `json:"startTime"`           // 活动开始时间，秒时间戳
	EndTime             int64             `json:"endTime"`             // 活动结束时间，秒时间戳
	AnchorRankToday     RlaaRankToday     `json:"anchorRankToday"`     // 主播今日排名
	AnchorRankYesterday RlaaRankYesterday `json:"anchorRankYesterday"` // 主播昨日排名
	AgencyRankToday     RlaaRankToday     `json:"agencyRankToday"`     // 公会今日排名
	AgencyRankYesterday RlaaRankYesterday `json:"agencyRankYesterday"` // 公会昨日排名
}

type RlaaRankToday struct {
	RlaaRank
	Countdown int64 `json:"countdown"` // 倒计时，秒
	EndOfDay  int64 `json:"endOfDay"`  // 今日结束时间，秒时间戳
}

type RlaaRankYesterday struct {
	RlaaRank
}

type RlaaRank struct {
	List []RlaaRankUser `json:"list"` // 排名列表
	Mine *RlaaRankUser  `json:"mine"` // 我的排名
}

type RlaaRankUser struct {
	User   *types.User   `json:"user"`   // 用户信息
	Agency *types.Agency `json:"agency"` // 公会信息
	No     int           `json:"no"`     // 排名
	Score  int64         `json:"score"`  // 分数
	Change int64         `json:"change"` // 排名变化 >0上升名次 <0下降名次 0不变
	Issued bool          `json:"issued"` // 发放了奖励
}

// @Tags 活动
// @Summary 主播公会日榜-信息
// @Description 主播公会日榜-信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=RlaaInfoResponse}
// @Router /api/v1/activity/rlaa/info [get]
func (s *apis) RlaaInfo(ctx *api.Context, req api.EmptyReq) (*RlaaInfoResponse, error) {
	var userId string

	uac, err := ctx.User()

	if err == nil {
		userId = uac.UserId
	}

	// 当前时间
	nn := rlaa.GetNowNow()

	// 活动状态
	info := s.rlaam.Info(nn)

	// 主播昨日排名数据
	anchorYesterdayRanks, err := s.rlaam.GetYesterdayRank(ctx, nn, rlaa.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorYesterdayMine, err := s.rlaam.GetUserYesterdayRank(ctx, nn, userId, rlaa.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorRankYesterday := RlaaRankYesterday{
		RlaaRank: RlaaRank{
			List: s.rankUserListRlaa(ctx, anchorYesterdayRanks),
			Mine: s.rankUserRlaa(ctx, anchorYesterdayMine),
		},
	}

	// 主播今日排名数据
	anchorTodayRanks, err := s.rlaam.GetTodayRank(ctx, nn, rlaa.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorTodayMine, err := s.rlaam.GetUserTodayRank(ctx, nn, userId, rlaa.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorRankToday := RlaaRankToday{
		RlaaRank: RlaaRank{
			List: s.rankUserListRlaa(ctx, anchorTodayRanks),
			Mine: s.rankUserRlaa(ctx, anchorTodayMine),
		},
		Countdown: rlaa.TodayCountdown(nn),
		EndOfDay:  rlaa.TodayEndOfDay(nn),
	}

	var agencyIdStr string
	aa, err := s.am.MyOwnAgency(ctx, userId)

	if err == nil {
		agencyIdStr = strconv.Itoa(int(aa.ID))
	}

	// 公会昨日数据
	agencyYesterdayRanks, err := s.rlaam.GetYesterdayRank(ctx, nn, rlaa.RankRoleAgency)

	if err != nil {
		return nil, err
	}

	var agencyYesterdayMine *rlaa.Rank

	if agencyIdStr != "" {
		agencyYesterdayMine, err = s.rlaam.GetUserYesterdayRank(ctx, nn, agencyIdStr, rlaa.RankRoleAgency)
	}

	if err != nil {
		return nil, err
	}

	agencyRankYesterday := RlaaRankYesterday{
		RlaaRank: RlaaRank{
			List: s.rankAgencyListRlaa(ctx, agencyYesterdayRanks),
			Mine: s.rankAgencyRlaa(ctx, agencyYesterdayMine),
		},
	}

	// 公会今日数据
	agencyTodayRanks, err := s.rlaam.GetTodayRank(ctx, nn, rlaa.RankRoleAgency)

	if err != nil {
		return nil, err
	}

	var agencyTodayMine *rlaa.Rank

	if agencyIdStr != "" {
		agencyTodayMine, err = s.rlaam.GetUserTodayRank(ctx, nn, agencyIdStr, rlaa.RankRoleAgency)
	}

	if err != nil {
		return nil, err
	}

	agencyRankToday := RlaaRankToday{
		RlaaRank: RlaaRank{
			List: s.rankAgencyListRlaa(ctx, agencyTodayRanks),
			Mine: s.rankAgencyRlaa(ctx, agencyTodayMine),
		},
		Countdown: rlaa.TodayCountdown(nn),
		EndOfDay:  rlaa.TodayEndOfDay(nn),
	}

	return &RlaaInfoResponse{
		Status:              info.Status,
		StartTime:           info.StartTime,
		EndTime:             info.EndTime,
		AnchorRankToday:     anchorRankToday,
		AnchorRankYesterday: anchorRankYesterday,
		AgencyRankToday:     agencyRankToday,
		AgencyRankYesterday: agencyRankYesterday,
	}, nil
}

func (s *apis) rankUserListRlaa(ctx context.Context, list []rlaa.Rank) []RlaaRankUser {
	ret := make([]RlaaRankUser, 0)

	for _, v := range list {
		ret = append(ret, *s.rankUserRlaa(ctx, &v))
	}

	return ret
}

func (s *apis) rankUserRlaa(ctx context.Context, rank *rlaa.Rank) *RlaaRankUser {
	return &RlaaRankUser{
		User:   s.getTypesUser(ctx, rank.UserId),
		No:     rank.No,
		Score:  rank.Diamond,
		Issued: rank.Issued,
	}
}

func (s *apis) rankAgencyListRlaa(ctx context.Context, list []rlaa.Rank) []RlaaRankUser {
	ret := make([]RlaaRankUser, 0)

	for _, v := range list {
		ret = append(ret, *s.rankAgencyRlaa(ctx, &v))
	}

	return ret
}

func (s *apis) rankAgencyRlaa(ctx context.Context, rank *rlaa.Rank) *RlaaRankUser {
	if rank == nil {
		return &RlaaRankUser{
			User:   &types.User{},
			Agency: &types.Agency{},
			No:     0,
			Score:  0,
		}
	}

	return &RlaaRankUser{
		Agency: s.getRlaaTypesAgency(ctx, rank.UserId),
		No:     rank.No,
		Score:  rank.Diamond,
		Issued: rank.Issued,
	}
}

func (s *apis) getRlaaTypesAgency(ctx context.Context, userId string) *types.Agency {
	var ay types.Agency
	// 转公会ID
	agencyId, err := strconv.ParseInt(userId, 10, 64)

	if err != nil {
		s.log.Error("活动榜单获取公会信息失败", zap.String("userId", userId), zap.Error(err))
		return &ay
	}

	res, err := s.am.GetAgencyById(ctx, agencyId)

	if err != nil {
		s.log.Error("活动榜单获取公会信息失败", zap.String("userId", userId), zap.Error(err))
		return &ay
	}

	sc := s.si.Conf("agency")

	ay.ChiefId = res.ChiefId
	ay.Name = res.Name
	ay.ImageUrl = sc.ExternalURL(res.ImageUrl)

	return &ay
}
