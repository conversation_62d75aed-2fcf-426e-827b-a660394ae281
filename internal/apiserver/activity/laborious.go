package activity

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/laborious"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type laboriousInfoResp struct {
	laborious.Act
}

// @Tags 活动
// @Summary 勤劳主播-信息
// @Description 勤劳主播-信息
// @Produce json
// @Success 200 {object} codec.Response{data=laboriousInfoResp}
// @Router /api/v1/activity/laborious/info [get]
func (s *apis) laboriousInfo(ctx *api.Context, _ api.EmptyReq) (*laboriousInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	act, err := s.labm.Info(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	return &laboriousInfoResp{Act: *act}, nil
}

type laboriousReceiveReq struct {
	WeekId string `json:"weekId"`
}

type laboriousReceiveResp struct{}

// @Tags 活动
// @Summary 勤劳主播-领取奖励
// @Description 勤劳主播-领取奖励
// @Produce json
// @Param param body laboriousReceiveReq true "请求参数"
// @Success 200 {object} codec.Response{data=laboriousReceiveResp}
// @Router /api/v1/activity/laborious/receive [post]
func (s *apis) laboriousReceive(ctx *api.Context, req laboriousReceiveReq) (*laboriousReceiveResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	err = s.labm.Receive(ctx, uac.UserId, req.WeekId)

	if err != nil {
		return nil, err
	}

	return &laboriousReceiveResp{}, nil
}

type laboriousChangeDateRequest struct {
	UserId    string `form:"userId" binding:"required"`
	StartDate string `form:"startDate" binding:"required"` // 2025-01-01 00:00:00
}

// @Tags 活动
// @Summary 勤劳主播-修改活动日期
// @Description 勤劳主播-修改活动日期
// @Produce json
// @Param param query laboriousChangeDateRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/activity/laborious/change_date [get]
func (s *apis) laboriousChangeDate(ctx *api.Context, req laboriousChangeDateRequest) (*api.EmptyResp, error) {
	dateTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartDate, ctz.Brazil)

	if err != nil {
		return nil, err
	}

	err = s.labm.TestChangeDate(ctx, req.UserId, dateTime)

	if err != nil {
		return nil, err
	}

	return nil, nil
}
