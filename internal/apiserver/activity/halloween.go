package activity

import (
	"strings"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/halloween"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type halloweenMsg struct {
	User  *types.User `json:"user"`  // 中奖用户
	Coins int         `json:"coins"` // 获得金币
	Time  int64       `json:"time"`  // 飞字时间
}

type halloweenMsgsReq struct {
	Time int64 `form:"ts"` // 上一条消息的时间，第一次传0
}

type halloweenMsgsResp struct {
	List []halloweenMsg `json:"list"`
}

// @Tags 活动
// @Summary 万圣节活动-飞字
// @Description 万圣节活动-飞字
// @Produce json
// @Security HeaderAuth
// @Param param query halloweenMsgsReq true "请求参数"
// @Success 200 {object} codec.Response{data=halloweenMsgsResp}
// @Router /api/v1/activity/halloween/msgs [get]
func (s *apis) halloweenMsgs(ctx *api.Context, req halloweenMsgsReq) (*halloweenMsgsResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var resp halloweenMsgsResp

	for _, p := range s.hm.Broadcasts(ctx, uac.UserId, req.Time) {
		acc, err := s.ug.Account(ctx, p.UserId)
		if err != nil {
			continue
		}
		resp.List = append(resp.List, halloweenMsg{
			User:  mixer.User(ctx, acc),
			Coins: p.Coins,
			Time:  p.Time.Unix(),
		})
	}

	return &resp, nil
}

type halloweenInfoResp struct {
	StartTime      int64                  `json:"startTime"`      // 开始时间：unix秒
	EndTime        int64                  `json:"endTime"`        // 结束时间：unix秒
	Rewards        []halloween.DrawResult `json:"rewards"`        // 奖品列表
	ShareUrl       string                 `json:"shareUrl"`       // 分享链接
	Chances        int64                  `json:"chances"`        // 剩余抽奖机会
	InviteUsers    int64                  `json:"inviteUsers"`    // 邀请的新用户数
	InviteRecharge int64                  `json:"inviteRecharge"` // 被邀请人的充值金额
	InviteReward   int64                  `json:"inviteReward"`   // 邀请获得奖励金币
}

// @Tags 活动
// @Summary 万圣节活动-信息
// @Description 万圣节活动-信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=halloweenInfoResp}
// @Router /api/v1/activity/halloween/info [get]
func (s *apis) halloweenInfo(ctx *api.Context, _ api.EmptyReq) (*halloweenInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	resp := halloweenInfoResp{
		StartTime: halloween.StartTime.Unix(),
		EndTime:   halloween.EndTime.Unix(),
		Rewards:   halloween.Rewards,
		ShareUrl:  "https://www.kako.live/download.html?refer=" + halloween.ReferCode(uac),
	}
	if dbg.Ing() {
		resp.ShareUrl = strings.Replace(resp.ShareUrl, "www.kako.live", "www-test.kako.live", 1)
		resp.ShareUrl += "&test=true"
	}

	chance, err := s.hm.TakeChance(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	resp.Chances = chance.Balance()

	inviteUsers, err := s.hm.InviteCount(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	resp.InviteUsers = inviteUsers

	rebate, err := s.hm.RebateInfo(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	resp.InviteRecharge = rebate.Charged.Div(fund.New(10000)).IntPart()
	resp.InviteReward = rebate.Reward()

	return &resp, nil
}

type halloweenTaskListResp struct {
	Tasks []halloween.TaskInfo `json:"tasks"`
}

// @Tags 活动
// @Summary 万圣节活动-任务列表
// @Description 万圣节活动-任务列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=halloweenTaskListResp}
// @Router /api/v1/activity/halloween/task/list [get]
func (s *apis) halloweenTaskList(ctx *api.Context, _ api.EmptyReq) (*halloweenTaskListResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	tasks, err := s.hm.TaskList(ctx, time.Now(), uac.UserId)
	if err != nil {
		return nil, err
	}

	return &halloweenTaskListResp{Tasks: tasks}, nil
}

type halloweenTaskRecvReq struct {
	Id string `json:"id"`
}

// @Tags 活动
// @Summary 万圣节活动-任务领取
// @Description 万圣节活动-任务领取
// @Produce json
// @Security HeaderAuth
// @Param param body halloweenTaskRecvReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/activity/halloween/task/recv [post]
func (s *apis) halloweenTaskRecv(ctx *api.Context, req halloweenTaskRecvReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.hm.TaskRecv(ctx, time.Now(), uac.UserId, req.Id); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type halloweenDrawReq struct {
	Count int `json:"count"`
}

type halloweenDrawResp struct {
	Result []halloween.DrawResult `json:"result"`
}

// @Tags 活动
// @Summary 万圣节活动-抽奖
// @Description 万圣节活动-抽奖
// @Produce json
// @Security HeaderAuth
// @Param param body halloweenDrawReq true "请求参数"
// @Success 200 {object} codec.Response{data=halloweenDrawResp}
// @Router /api/v1/activity/halloween/draw [post]
func (s *apis) halloweenDraw(ctx *api.Context, req halloweenDrawReq) (*halloweenDrawResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	results, err := s.hm.Draw(ctx, time.Now(), uac.UserId, req.Count)
	if err != nil {
		return nil, err
	}

	return &halloweenDrawResp{Result: results}, nil
}
