package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type BlindBoxCollectInfoReq struct {
	AnchorUserId string `form:"anchorUserId"` // 房间主播id
}

type BlindBoxCollectRankingUser struct {
	Rank  int         `json:"rank"`
	Value int         `json:"value"`
	User  *types.User `json:"user"` // 用户信息
}

type BlindBoxCollectInfoResp struct {
	User        *types.User                  `json:"user"`        // 用户信息
	CollectNum  int                          `json:"collectNum"`  // 已集齐套数
	CurrentHave int                          `json:"currentHave"` // 当前已集齐数量
	CollectData map[int]int                  `json:"collectData"` // 当前收集情况, 礼物id：礼物数量
	StartTime   int64                        `json:"startTime"`   // 活动开始时间，unix秒级时间戳
	EndTime     int64                        `json:"endTime"`     // 活动结束时间，unix秒级时间戳
	Ranks       []BlindBoxCollectRankingUser `json:"ranks"`       // top20榜单
}

// @Tags 活动
// @Summary 盲盒收集活动
// @Description 盲盒收集活动
// @Produce json
// @Param param query BlindBoxCollectInfoReq true "请求参数"
// @Success 200 {object} codec.Response{data=BlindBoxCollectInfoResp}
// @Router /activity/blindbox/collect/info [get]
func (s *apis) BlindBoxCollectInfo(ctx *api.Context, req BlindBoxCollectInfoReq) (*BlindBoxCollectInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var userId = uac.UserId
	if req.AnchorUserId != "" {
		userId = req.AnchorUserId
	}

	p := s.bcm.GetSES(time.Now())
	if p.Stage == "" {
		p = s.bcm.GetSES(time.Now().AddDate(0, 0, -1))
	}

	userData, err := s.bcm.GetUserData(ctx, p.Stage, userId)
	if err != nil {
		return nil, err
	}

	// get ranks
	ranks, err := s.bcm.GetRanking(ctx, p.Stage)
	if err != nil {
		return nil, err
	}

	var r []BlindBoxCollectRankingUser
	for _, rank := range ranks {
		r = append(r, BlindBoxCollectRankingUser{
			Rank:  rank.Rank,
			Value: rank.Value,
			User:  mixer.User(ctx, rank.User),
		})
	}

	return &BlindBoxCollectInfoResp{
		User:        mixer.User(ctx, userData.UserRank.User),
		CollectNum:  userData.UserRank.Value,
		CurrentHave: userData.CurrentHave,
		CollectData: userData.CollectData,
		StartTime:   p.StartTime.Unix(),
		EndTime:     p.EndTime.Unix(),
		Ranks:       r,
	}, nil
}

type BlindBoxCollectWidgetReq struct {
	AnchorUserId string `form:"anchorUserId" binding:"required"` // 房间主播id
}

type BlindBoxCollectWidgetResp struct {
	CollectData map[int]bool `json:"collectData"` // 当前轮次收集情况
}

// @Tags 活动
// @Summary 盲盒收集活动挂件
// @Description 盲盒收集活动挂件
// @Produce json
// @Param param query BlindBoxCollectWidgetReq true "请求参数"
// @Success 200 {object} codec.Response{data=BlindBoxCollectWidgetResp}
// @Router /activity/blindbox/collect/widget [get]
func (s *apis) BlindBoxCollectWidget(ctx *api.Context, req BlindBoxCollectWidgetReq) (*BlindBoxCollectWidgetResp, error) {
	p := s.bcm.GetSES(time.Now())
	return &BlindBoxCollectWidgetResp{
		CollectData: s.bcm.GetUserCollectData(ctx, p.Stage, req.AnchorUserId),
	}, nil
}
