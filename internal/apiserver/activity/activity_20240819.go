package activity

import (
	"context"
	"strconv"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/activity_20240819"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

type activity20240819InfoResponse struct {
	Status              int                      `json:"status"`              // 活动状态 0：未开始 1：进行中 2：已结束
	StartTime           int64                    `json:"startTime"`           // 活动开始时间，毫秒时间戳
	EndTime             int64                    `json:"endTime"`             // 活动结束时间，毫秒时间戳
	Day                 int                      `json:"day"`                 // 活动第几天
	TotalDays           int                      `json:"totalDays"`           // 活动总天数
	Issue               int                      `json:"issue"`               // 活动期数
	AnchorRankToday     Act20240819RankToday     `json:"anchorRankToday"`     // 主播今日排名
	AnchorRankYesterday Act20240819RankYesterday `json:"anchorRankYesterday"` // 主播昨日排名
	AgencyRankToday     Act20240819RankToday     `json:"agencyRankToday"`     // 公会今日排名
	AgencyRankYesterday Act20240819RankYesterday `json:"agencyRankYesterday"` // 公会昨日排名
}

type Act20240819RankToday struct {
	Act20240819Rank
	Countdown int64 `json:"countdown"` // 倒计时，毫秒
}

type Act20240819RankYesterday struct {
	Act20240819Rank
}

type Act20240819Rank struct {
	List []Act20240819RankUser `json:"list"` // 排名列表
	Mine *Act20240819RankUser  `json:"mine"` // 我的排名
}

type Act20240819RankUser struct {
	User   *types.User   `json:"user"`   // 用户信息
	Agency *types.Agency `json:"agency"` // 公会信息
	No     int           `json:"no"`     // 排名
	Score  int64         `json:"score"`  // 分数
}

// @Tags 活动
// @Summary 活动20240819-信息
// @Description 活动20240819-信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=activity20240819InfoResponse}
// @Router /api/v1/activity/20240819/info [get]
func (s *apis) Activity20240819Info(ctx *api.Context, req api.EmptyReq) (*activity20240819InfoResponse, error) {
	var userId string

	uac, err := ctx.User()

	if err == nil {
		userId = uac.UserId
	}

	// 当前时间
	nn := activity_20240819.GetNowNow()

	// 活动状态
	info := s.a20240819.Info(nn)

	// 主播昨日排名数据
	anchorYesterdayRanks, err := s.a20240819.GetYesterdayRank(ctx, nn, activity_20240819.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorYesterdayMine, err := s.a20240819.GetUserYesterdayRank(ctx, nn, userId, activity_20240819.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorRankYesterday := Act20240819RankYesterday{
		Act20240819Rank: Act20240819Rank{
			List: s.rankUserList20240819(ctx, anchorYesterdayRanks),
			Mine: s.rankUser20240819(ctx, anchorYesterdayMine),
		},
	}

	// 主播今日排名数据
	anchorTodayRanks, err := s.a20240819.GetTodayRank(ctx, nn, activity_20240819.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorTodayMine, err := s.a20240819.GetUserTodayRank(ctx, nn, userId, activity_20240819.RankRoleAnchor)

	if err != nil {
		return nil, err
	}

	anchorRankToday := Act20240819RankToday{
		Act20240819Rank: Act20240819Rank{
			List: s.rankUserList20240819(ctx, anchorTodayRanks),
			Mine: s.rankUser20240819(ctx, anchorTodayMine),
		},
		Countdown: activity_20240819.TodayCountdown(nn),
	}

	var agencyIdStr string
	aa, err := s.am.MyOwnAgency(ctx, userId)

	if err == nil {
		agencyIdStr = strconv.Itoa(int(aa.ID))
	}

	// 公会昨日数据
	agencyYesterdayRanks, err := s.a20240819.GetYesterdayRank(ctx, nn, activity_20240819.RankRoleAgency)

	if err != nil {
		return nil, err
	}

	var agencyYesterdayMine *activity_20240819.Rank

	if agencyIdStr != "" {
		agencyYesterdayMine, err = s.a20240819.GetUserYesterdayRank(ctx, nn, agencyIdStr, activity_20240819.RankRoleAgency)
	}

	if err != nil {
		return nil, err
	}

	agencyRankYesterday := Act20240819RankYesterday{
		Act20240819Rank: Act20240819Rank{
			List: s.rankAgencyList20240819(ctx, agencyYesterdayRanks),
			Mine: s.rankAgency20240819(ctx, agencyYesterdayMine),
		},
	}

	// 公会今日数据
	agencyTodayRanks, err := s.a20240819.GetTodayRank(ctx, nn, activity_20240819.RankRoleAgency)

	if err != nil {
		return nil, err
	}

	var agencyTodayMine *activity_20240819.Rank

	if agencyIdStr != "" {
		agencyTodayMine, err = s.a20240819.GetUserTodayRank(ctx, nn, agencyIdStr, activity_20240819.RankRoleAgency)
	}

	if err != nil {
		return nil, err
	}

	agencyRankToday := Act20240819RankToday{
		Act20240819Rank: Act20240819Rank{
			List: s.rankAgencyList20240819(ctx, agencyTodayRanks),
			Mine: s.rankAgency20240819(ctx, agencyTodayMine),
		},
		Countdown: activity_20240819.TodayCountdown(nn),
	}

	return &activity20240819InfoResponse{
		Status:              info.Status,
		StartTime:           info.StartTime,
		EndTime:             info.EndTime,
		Day:                 info.Day,
		TotalDays:           info.TotalDays,
		Issue:               info.Issue,
		AnchorRankToday:     anchorRankToday,
		AnchorRankYesterday: anchorRankYesterday,
		AgencyRankToday:     agencyRankToday,
		AgencyRankYesterday: agencyRankYesterday,
	}, nil
}

func (s *apis) rankUserList20240819(ctx context.Context, list []activity_20240819.Rank) []Act20240819RankUser {
	ret := make([]Act20240819RankUser, 0)

	for _, v := range list {
		ret = append(ret, *s.rankUser20240819(ctx, &v))
	}

	return ret
}

func (s *apis) rankUser20240819(ctx context.Context, rank *activity_20240819.Rank) *Act20240819RankUser {
	return &Act20240819RankUser{
		User:  s.getTypesUser(ctx, rank.UserId),
		No:    rank.No,
		Score: rank.Diamond,
	}
}

func (s *apis) rankAgencyList20240819(ctx context.Context, list []activity_20240819.Rank) []Act20240819RankUser {
	ret := make([]Act20240819RankUser, 0)

	for _, v := range list {
		ret = append(ret, *s.rankAgency20240819(ctx, &v))
	}

	return ret
}

func (s *apis) rankAgency20240819(ctx context.Context, rank *activity_20240819.Rank) *Act20240819RankUser {
	if rank == nil {
		return &Act20240819RankUser{
			User:   &types.User{},
			Agency: &types.Agency{},
			No:     0,
			Score:  0,
		}
	}

	return &Act20240819RankUser{
		Agency: s.getTypesAgency(ctx, rank.UserId),
		No:     rank.No,
		Score:  rank.Diamond,
	}
}

func (s *apis) getTypesAgency(ctx context.Context, userId string) *types.Agency {
	var ay types.Agency
	// 转公会ID
	agencyId, err := strconv.ParseInt(userId, 10, 64)

	if err != nil {
		s.log.Error("活动榜单获取公会信息失败", zap.String("userId", userId), zap.Error(err))
		return &ay
	}

	res, err := s.am.GetAgencyById(ctx, agencyId)

	if err != nil {
		s.log.Error("活动榜单获取公会信息失败", zap.String("userId", userId), zap.Error(err))
		return &ay
	}

	sc := s.si.Conf("agency")

	ay.ChiefId = res.ChiefId
	ay.Name = res.Name
	ay.ImageUrl = sc.ExternalURL(res.ImageUrl)

	return &ay
}
