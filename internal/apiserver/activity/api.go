package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox_collect"
	"time"

	blindBoxActivity "gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/coin_grab"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/easter"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/foolsday"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/hourlyrank"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/payment_vehicle"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rebate"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rlaa"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/valentines_day"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/agency_newbie"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/laborious"

	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/activity_20240819"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/agency_invite"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/christmas"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/halloween"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/launch"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/lucky_star"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func Invoke(
	r *api.Router, ml *mux.Locker,
	alm *launch.Manager,
	agm *agency.Manager,
	agim *agency_invite.Manager,
	a20240819 *activity_20240819.Manager,
	lm *lucky_star.Manager,
	hm *halloween.Manager,
	acm *christmas.Manager,
	labm *laborious.Manager,
	anm *agency_newbie.Manager,
	vdm *valentines_day.Manager,
	rm *rebate.Manager,
	ug user.Getter,
	dm *redi.Mutex,
	am *agency.Manager,
	pvm *payment_vehicle.Manager,
	bbm *blindBoxActivity.Manager,
	cgm *coin_grab.Manager,
	fdm *foolsday.Manager,
	hrm *hourlyrank.Manager,
	bcm *blindbox_collect.Manager,
	em *easter.Manager,
	drm *dress.Manager,
	si sto.Instance,
	hc *cache.Handler,
	rlaam *rlaa.Manager,
	vnd log.Vendor,
) {
	s := &apis{
		alm:       alm,
		agm:       agm,
		agim:      agim,
		a20240819: a20240819,
		lm:        lm,
		hm:        hm,
		acm:       acm,
		labm:      labm,
		anm:       anm,
		vdm:       vdm,
		rm:        rm,
		ug:        ug,
		dm:        dm,
		am:        am,
		pvm:       pvm,
		bbm:       bbm,
		cgm:       cgm,
		fdm:       fdm,
		hrm:       hrm,
		bcm:       bcm,
		em:        em,
		drm:       drm,
		si:        si,
		rlaam:     rlaam,
		log:       vnd.Scope("api.activity"),
	}

	kg := func(c *gin.Context) string {
		return c.Request.RequestURI + c.GetHeader("Accept-Language")
	}

	tr := r.TryAuth()
	{
		tr.GET("/activity/launch/info", api.Generic(s.LaunchInfo))
		tr.POST("/activity/launch/receive", api.Generic(s.LaunchReceive))
		tr.GET("/activity/20240819/info", api.Generic(s.Activity20240819Info))
		// 新主播公会日榜活动
		tr.GET("/activity/rlaa/info", api.Generic(s.RlaaInfo))
		// 幸运之星榜单
		tr.GET("/activity/lucky/info", api.Generic(s.LuckyInfo))
		// 圣诞活动
		tr.GET("/activity/christmas/info", hc.Middleware(cache.WithExpire(time.Second), cache.WithCustomKey(kg)), api.Generic(s.christmasInfo))
	}

	ar := r.WithAuth()
	{
		ar.GET("/activity/agency/invite/info", api.Generic(s.InviteInfo))
		// 万圣节活动
		g1 := ar.Group("/activity/halloween", ml.Middleware(mux.WithPOST))
		{
			g1.GET("/info", api.Generic(s.halloweenInfo))
			g1.GET("/msgs", api.Generic(s.halloweenMsgs))
			g1.GET("/task/list", api.Generic(s.halloweenTaskList))
			g1.POST("/task/recv", api.Generic(s.halloweenTaskRecv))
			g1.POST("/draw", api.Generic(s.halloweenDraw))
		}
		// 圣诞节活动
		g2 := ar.Group("/activity/christmas", ml.Middleware(mux.WithPOST))
		{
			g2.POST("/join", api.Generic(s.christmasJoin))
			g2.GET("/join/record", api.Generic(s.christmasJoinRecord))
			g2.GET("/ranklist/user", api.Generic(s.christmasRanklistUser))
			g2.GET("/ranklist/anchor", api.Generic(s.christmasRanklistAnchor))
			// 幸运记录
			g2.GET("/records", api.Generic(s.christmasRecords))
			// 未读的开奖结果
			g2.GET("/result", api.Generic(s.christmasResult))
			// 已读开奖结果
			g2.POST("/result/read", api.Generic(s.christmasResultRead))
		}
		// 公会拉新奖励活动
		g3 := ar.Group("/activity/agency/newbie", ml.Middleware(mux.WithPOST))
		{
			// 拉新数据详情
			g3.GET("/info", api.Generic(s.agencyNewbieInfo))
			// 奖励领取
			g3.POST("/award", api.Generic(s.agencyNewbieAward))
		}
		// 勤劳主播活动
		g4 := ar.Group("/activity/laborious", ml.Middleware(mux.WithPOST))
		{
			g4.GET("/info", api.Generic(s.laboriousInfo))
			g4.POST("/receive", api.Generic(s.laboriousReceive))
			// 日期修改
			// g4.GET("/change_date", api.Generic(s.laboriousChangeDate))
		}
		// 情人节活动
		g5 := ar.Group("/activity/valentines", ml.Middleware(mux.WithPOST))
		{
			// 历史榜单，缓存1分钟
			g5.GET("/ranking/history", hc.Middleware(cache.WithExpire(time.Minute)), api.Generic(s.rankingHistory))
			// 当前时段排名，缓存5秒钟
			g5.GET("/ranking/current", hc.Middleware(cache.WithExpire(5*time.Second)), api.Generic(s.rankingCurrent))
		}
		// 限时返利活动
		g6 := ar.Group("/activity/rebate", ml.Middleware(mux.WithPOST))
		{
			// 活动详情
			g6.GET("/info", api.Generic(s.RebateInfo))
			// 榜单
			g6.GET("/ranking", rlog.Opt(rlog.WithRatio(0)), api.Generic(s.RebateRanking))
			// 奖励领取
			g6.POST("/award/receive", api.Generic(s.RebateAwardReceive))
			// 领取记录
			g6.GET("/award/record", api.Generic(s.RebateReceiveRecord))
		}
		// 充值送座驾
		g7 := ar.Group("/activity/vehicle")
		{
			// 活动详情
			g7.GET("/info", api.Generic(s.ActivityVehicleInfo))
		}
		// 盲盒活动
		g8 := ar.Group("/activity/blindbox")
		{
			// 榜单
			g8.GET("/ranking", api.Generic(s.BlindBoxRanking))
		}
		// 抢金赛
		g9 := ar.Group("/activity/coingrab", ml.Middleware(mux.WithPOST))
		{
			// 榜单
			g9.GET("/info", api.Generic(s.CoinGrabInfo))
			// 直播间挂件
			g9.GET("/widget", api.Generic(s.CoinGrabRoomWidget))
			// 奖励领取
			g9.POST("/receive", api.Generic(s.CoinGrabRewardReceive))
		}
		// 愚人节活动
		g10 := ar.Group("/activity/foolsday")
		{
			// 活动信息
			g10.GET("/info", api.Generic(s.ActivityFoolsdayInfo))
		}
		// 小时榜活动
		g11 := ar.Group("/activity/hourlyrank")
		{
			g11.GET("/history", api.Generic(s.hourlyRankHistory))
			g11.GET("/current", rlog.Opt(rlog.WithRatio(0)), api.Generic(s.hourlyRankCurrent))
		}
		// 复活节活动
		g12 := ar.Group("/activity/easter")
		{
			g12.GET("/info", api.Generic(s.EasterInfo))
		}
		// 盲盒收集活动
		g13 := ar.Group("/activity/blindbox/collect")
		{
			g13.GET("/info", api.Generic(s.BlindBoxCollectInfo))
			g13.GET("/widget", api.Generic(s.BlindBoxCollectWidget))
		}
	}
}

type apis struct {
	alm       *launch.Manager
	agm       *agency.Manager
	agim      *agency_invite.Manager
	a20240819 *activity_20240819.Manager
	lm        *lucky_star.Manager
	hm        *halloween.Manager
	acm       *christmas.Manager
	labm      *laborious.Manager
	anm       *agency_newbie.Manager
	vdm       *valentines_day.Manager
	rm        *rebate.Manager
	ug        user.Getter
	dm        *redi.Mutex
	am        *agency.Manager
	pvm       *payment_vehicle.Manager
	bbm       *blindBoxActivity.Manager
	cgm       *coin_grab.Manager
	fdm       *foolsday.Manager
	hrm       *hourlyrank.Manager
	bcm       *blindbox_collect.Manager
	em        *easter.Manager
	drm       *dress.Manager
	si        sto.Instance
	rlaam     *rlaa.Manager
	log       *zap.Logger
}
