package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rebate"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"time"
)

type RebateInfoReq struct {
}

type RebateInfoResp struct {
	MyRank         *RebateRankingUser `json:"myRank"`
	YesterdayAward YesterdayAward     `json:"yesterdayAward"`
	StartTime      int64              `json:"startTime"`
	EndTime        int64              `json:"endTime"`
}

type RebateRankingUser struct {
	Rank  int         `json:"rank"`
	Value int         `json:"value"`
	User  *types.User `json:"user"` // 用户信息
}

type YesterdayAward struct {
	TotalAward  int  `json:"totalAward"`
	RebateAward int  `json:"rebateAward"`
	RankAward   int  `json:"rankAward"`
	IsReceive   bool `json:"isReceive"`
}

// @Tags 活动
// @Summary 限时返利活动-活动详情
// @Description 限时返利活动
// @Produce json
// @Param param query RebateInfoReq true "请求参数"
// @Success 200 {object} codec.Response{data=RebateInfoResp}
// @Router /api/v1/activity/rebate/info [get]
func (s *apis) RebateInfo(ctx *api.Context, req RebateInfoReq) (*RebateInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 今日
	var todayMyRank RebateRankingUser
	rank, rebateAward := s.rm.GetRankByUserId(ctx, time.Now().In(ctz.Brazil), uac.UserId)
	todayMyRank.Rank = rank
	todayMyRank.Value = rebateAward
	todayMyRank.User = mixer.User(ctx, uac)

	// 昨日
	_, yesterdayRebateAward := s.rm.GetRankByUserId(ctx, time.Now().AddDate(0, 0, -1).In(ctz.Brazil), uac.UserId)
	// 获取昨日前十
	var yesterdayRankAward = 0
	top10 := s.rm.GetTop10Rank(ctx, time.Now().AddDate(0, 0, -1).In(ctz.Brazil))
	for k, v := range top10 {
		uid := v.Member.(string)
		if uid == uac.UserId {
			yesterdayRankAward = rebate.RankAward[k+1]
			break
		}
	}

	// 是否领取
	IsReceive := s.rm.IsReceive(ctx, time.Now().AddDate(0, 0, -1).In(ctz.Brazil), uac.UserId, yesterdayRebateAward, yesterdayRankAward)
	return &RebateInfoResp{
		MyRank: &todayMyRank,
		YesterdayAward: YesterdayAward{
			TotalAward:  yesterdayRebateAward + yesterdayRankAward,
			RebateAward: yesterdayRebateAward,
			RankAward:   yesterdayRankAward,
			IsReceive:   IsReceive,
		},
		StartTime: rebate.StartTime.Unix(),
		EndTime:   rebate.EndTime.Unix(),
	}, nil
}

type RebateRankingReq struct {
	Period string `form:"period"` // today、yesterday
}

type RebateRankingResp struct {
	Ranks     []RebateRankingUser `json:"ranks"`
	MyRank    RebateRankingUser   `json:"myRank"`
	StartTime int64               `json:"startTime"`
	EndTime   int64               `json:"endTime"`
}

// @Tags 活动
// @Summary 限时返利活动-榜单
// @Description 限时返利活动
// @Produce json
// @Param param query RebateRankingReq true "请求参数"
// @Success 200 {object} codec.Response{data=RebateRankingResp}
// @Router /api/v1/activity/rebate/ranking [get]
func (s *apis) RebateRanking(ctx *api.Context, req RebateRankingReq) (*RebateRankingResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	t := time.Now().In(ctz.Brazil)
	if req.Period == "yesterday" {
		t = time.Now().AddDate(0, 0, -1).In(ctz.Brazil)
	}

	var (
		ranks  []RebateRankingUser
		myRank RebateRankingUser
	)
	userRank, ranking, err := s.rm.GetRanking(ctx, t, uac.UserId)
	if err != nil {
		return nil, err
	}

	myRank = RebateRankingUser{
		Rank:  userRank.Rank,
		Value: userRank.Value,
		User:  mixer.User(ctx, userRank.User),
	}

	for _, v := range ranking {
		ranks = append(ranks, RebateRankingUser{
			Rank:  v.Rank,
			Value: v.Value,
			User:  mixer.User(ctx, v.User),
		})
	}

	return &RebateRankingResp{
		Ranks:     ranks,
		MyRank:    myRank,
		StartTime: rebate.StartTime.Unix(),
		EndTime:   rebate.EndTime.Unix(),
	}, nil
}

type RebateAwardReceiveReq struct {
}

type RebateAwardReceiveResp struct {
}

// @Tags 活动
// @Summary 限时返利活动-领取奖励
// @Description 限时返利活动
// @Produce json
// @Param param body RebateAwardReceiveReq true "请求参数"
// @Success 200 {object} codec.Response{data=RebateAwardReceiveResp}
// @Router /api/v1/activity/rebate/award/receive [post]
func (s *apis) RebateAwardReceive(ctx *api.Context, req RebateAwardReceiveReq) (*RebateAwardReceiveResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	t := time.Now().AddDate(0, 0, -1).In(ctz.Brazil)
	_, value := s.rm.GetRankByUserId(ctx, t, uac.UserId)

	// 获取昨日前十
	var rankingAward = 0
	top10 := s.rm.GetTop10Rank(ctx, time.Now().AddDate(0, 0, -1).In(ctz.Brazil))
	for k, v := range top10 {
		uid := v.Member.(string)
		if uid == uac.UserId {
			rankingAward = rebate.RankAward[k+1]
			break
		}
	}

	if value+rankingAward == 0 {
		return nil, biz.NewError(biz.ErrBusiness, "no award")
	}

	if err := s.rm.AwardReceive(ctx, t, uac.UserId, value, rankingAward); err != nil {
		return nil, err
	}

	return &RebateAwardReceiveResp{}, nil
}

type RebateReceiveRecordReq struct {
}

type RebateReceiveRecordResp struct {
	List []rebate.AwardReceiveRecord `json:"list"`
}

// @Tags 活动
// @Summary 限时返利活动-领取记录
// @Description 限时返利活动
// @Produce json
// @Param param query RebateReceiveRecordReq true "请求参数"
// @Success 200 {object} codec.Response{data=RebateReceiveRecordResp}
// @Router /api/v1/activity/rebate/award/record [get]
func (s *apis) RebateReceiveRecord(ctx *api.Context, req RebateReceiveRecordReq) (*RebateReceiveRecordResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	record, err := s.rm.AwardReceiveRecord(ctx, time.Now().AddDate(0, 0, -1).In(ctz.Brazil), uac.UserId)
	if err != nil {
		return nil, err
	}

	return &RebateReceiveRecordResp{List: record}, nil
}
