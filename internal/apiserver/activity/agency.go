package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

// @Tags 活动
// @Summary 公会邀请活动-信息
// @Description 公会邀请活动-信息
// @Produce json
// @Security HeaderAuth
// @Param param query AgencyInviteInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=AgencyInviteInfoResponse}
// @Router /api/v1/activity/agency/invite/info [get]
func (s *apis) InviteInfo(ctx *api.Context, req AgencyInviteInfoRequest) (*AgencyInviteInfoResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 是否以创建公会
	myOwnAgency, err := s.agm.MyOwnAgency(ctx, uac.UserId)
	if err != nil {
		if err == agency.ErrNotFoundAgency {
			return &AgencyInviteInfoResponse{
				Status: 0,
			}, nil
		}

		return nil, err
	}

	// 生成邀请记录
	info, err := s.agim.GetInviteInfo(ctx, myOwnAgency.ChiefId, myOwnAgency.ID, myOwnAgency.ShowId)
	if err != nil {
		return nil, err
	}

	return &AgencyInviteInfoResponse{
		Status: 1,
		InviteData: &AgencyInviteData{
			InviteCode:   info.InviteCode,
			InviteApply:  info.InviteApply,
			InviteCreate: info.InviteCreate,
		},
	}, nil
}
