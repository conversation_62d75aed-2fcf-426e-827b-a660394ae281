package follow

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/es/query"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func Invoke(
	r *api.Router, hm *mux.Locker,
	ug user.Getter, lm *live.Manager, fm *follow.Manager, rm *room.Manager, um *urm.Manager,
	vnd log.Vendor,
) {
	s := &apis{ug: ug, lm: lm, fm: fm, rm: rm, um: um, log: vnd.Scope("api.follow")}
	ar := r.WithAuth(hm.Middleware(mux.WithPOST))
	{
		ar.POST("/user/follow", api.Generic(s.Follow))
		ar.POST("/user/unfollow", api.Generic(s.Unfollow))
		ar.GET("/follow/list", api.Generic(s.List))
		ar.GET("/follow/list/view", api.Generic(s.GView))
		ar.GET("/follow/list/all", api.Generic(s.Export))
		ar.POST("/follow/update", api.Generic(s.Update))
		ar.POST("/follow/remove", api.Generic(s.Remove))
	}
}

type apis struct {
	ug  user.Getter
	lm  *live.Manager
	fm  *follow.Manager
	rm  *room.Manager
	um  *urm.Manager
	log *zap.Logger
}

type followRequest struct {
	UserId string `json:"userId"` // 用户Id
	RoomId string `json:"roomId"` // 房间Id（直播间内需要传）
}

// @Tags 用户
// @Summary 关注用户
// @Description 关注用户
// @Produce json
// @Security HeaderAuth
// @Param param body followRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/user/follow [post]
func (s *apis) Follow(ctx *api.Context, req followRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if blocked, err := s.um.IsBlockTo(ctx, req.UserId, uac.UserId); err != nil {
		return nil, err
	} else if blocked {
		return nil, biz.NewError(biz.ErrUserBlocked, "You have been blocked by the user and cannot follow")
	}

	var ctx2 context.Context = ctx

	if blocked, err := s.um.IsBlockTo(ctx, uac.UserId, req.UserId); err != nil {
		return nil, err
	} else if blocked {
		if err := s.um.RemoveList(ctx, urm.GrantKindBlacklist, uac.UserId, uac.UserId, req.UserId); err != nil {
			return nil, err
		}
		ctx2 = follow.WaitRefresh(ctx2)
	}

	success := true
	if err := s.fm.Follow(ctx2, uac.UserId, req.UserId); err != nil {
		if !errors.Is(err, follow.ErrIsFollowing) {
			return nil, err
		}
		success = false
	}

	if success && req.RoomId != "" {
		ri, err := s.lm.Room2(req.RoomId)
		if err != nil {
			s.log.Error("get room failed", zap.Error(err), zap.String("roomId", req.RoomId))
		} else if ri.UserId == req.UserId {
			s.rm.Post(func() {
				if err := s.rm.NotifyRoomFollow(ctx, req.RoomId, uac.UserId, true); err != nil {
					s.log.Error("send room follow notify failed", zap.Error(err), zap.String("roomId", req.RoomId), zap.String("userId", req.UserId))
				}
			})
		}
	}

	return &api.EmptyResp{}, nil
}

type unfollowRequest struct {
	UserId string `json:"userId"`
}

// @Tags 用户
// @Summary 取消关注
// @Description 取消关注
// @Produce json
// @Security HeaderAuth
// @Param param body unfollowRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/user/unfollow [post]
func (s *apis) Unfollow(ctx *api.Context, req unfollowRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.fm.Unfollow(ctx, uac.UserId, req.UserId); err != nil {
		if !errors.Is(err, follow.ErrNoFollowing) {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}

type listType int

const (
	typFriends    listType = iota + 1 // 朋友
	typFollowings                     // 关注
	typFollowers                      // 粉丝
)

type listRequest struct {
	Type    listType `form:"type"`    // 关系类型
	Keyword string   `form:"keyword"` // 搜索关键词
	Cursor  string   `form:"cursor"`  // 分页游标
}

type listResponse struct {
	Cursor string  `json:"cursor"` // 分页游标：用于下次请求
	Total  int     `json:"total"`  // 总数量
	List   []*Item `json:"list"`   // 列表
}

// @Tags 关注
// @Summary 关注列表
// @Description 朋友、关注、粉丝
// @Produce json
// @Security HeaderAuth
// @Param param query listRequest true "请求参数"
// @Success 200 {object} codec.Response{data=listResponse}
// @Router /api/v1/follow/list [get]
func (s *apis) List(ctx *api.Context, req listRequest) (*listResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		resp listResponse
		list follow.Follows
		page *query.Pagination
	)

	switch req.Type {
	case typFriends:
		page, list, err = s.fm.Friends(ctx, uac.UserId, req.Keyword, req.Cursor)
	case typFollowings:
		page, list, err = s.fm.Followings(ctx, uac.UserId, req.Keyword, req.Cursor)
	case typFollowers:
		page, list, err = s.fm.Followers(ctx, uac.UserId, req.Keyword, req.Cursor)
	default:
		return &resp, nil
	}

	if err != nil {
		return nil, err
	}

	resp.List, err = makeItems(ctx, s.ug, list, req.Type == typFollowers, func(userId string, followed, follower bool) {
		if followed {
			s.unfollow(ctx, uac.UserId, userId)
		}
		if follower {
			s.remove(ctx, uac.UserId, userId)
		}
	})
	if err != nil {
		return nil, err
	}

	resp.Total = page.Total - (len(list) - len(resp.List))
	resp.Cursor = page.Cursor

	return &resp, nil
}

type updateRequest struct {
	UserId string  `json:"userId"` // 目标用户
	Remark *string `json:"remark"` // 设置备注
	Chummy *bool   `json:"chummy"` // 设置密友
	Focus  *bool   `json:"focus"`  // 特别关注
}

// @Tags 关注
// @Summary 更新信息
// @Description 备注、密友、特别关注
// @Produce json
// @Security HeaderAuth
// @Param param body updateRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/follow/update [post]
func (s *apis) Update(ctx *api.Context, req updateRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var errs []error

	if req.Remark != nil {
		if err := s.fm.SetRemark(ctx, uac.UserId, req.UserId, *req.Remark); err != nil {
			errs = append(errs, err)
		}
	}

	if req.Chummy != nil {
		if err := s.fm.SetChummy(ctx, uac.UserId, req.UserId, *req.Chummy); err != nil {
			errs = append(errs, err)
		}
	}

	if req.Focus != nil {
		if err := s.fm.SetFocus(ctx, uac.UserId, req.UserId, *req.Focus); err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return nil, errors.Join(errs...)
	}

	return &api.EmptyResp{}, nil
}

type removeRequest struct {
	UserId string `json:"userId"` // 目标用户
}

// @Tags 关注
// @Summary 移除关注
// @Description 朋友、粉丝
// @Produce json
// @Security HeaderAuth
// @Param param body removeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/follow/remove [post]
func (s *apis) Remove(ctx *api.Context, req removeRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.fm.Remove(ctx, uac.UserId, req.UserId); err != nil {
		if !errors.Is(err, follow.ErrNoFollowing) {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}
