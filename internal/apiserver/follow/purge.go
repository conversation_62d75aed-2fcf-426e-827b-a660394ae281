package follow

import (
	"context"

	"go.uber.org/zap"
)

func (s *apis) unfollow(ctx context.Context, from, to string) {
	log := s.log.With(zap.String("userId", from), zap.String("target", to))
	if err := s.fm.Unfollow(ctx, from, to); err != nil {
		log.Warn("unfollow missing user failed", zap.Error(err))
	} else {
		log.Debug("unfollow missing user")
	}
}

func (s *apis) remove(ctx context.Context, from, to string) {
	log := s.log.With(zap.String("userId", from), zap.String("target", to))
	if err := s.fm.Remove(ctx, from, to); err != nil {
		log.Warn("remove missing user failed", zap.Error(err))
	} else {
		log.Debug("remove missing user")
	}
}
