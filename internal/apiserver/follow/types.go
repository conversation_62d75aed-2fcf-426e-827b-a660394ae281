package follow

import (
	"context"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type onMissing func(userId string, followed, follower bool)

type Item struct {
	User     *types.UserWithExt `json:"user"`     // 用户
	Remark   string             `json:"remark"`   // 备注
	Chummy   bool               `json:"chummy"`   // 密友
	Focus    bool               `json:"focus"`    // 特别关注
	Followed bool               `json:"followed"` // 已关注
	Follower bool               `json:"follower"` // 被关注
}

func makeItems(ctx context.Context, ug user.Getter, raw follow.Follows, fans bool, missF onMissing) ([]*Item, error) {
	out := make([]*Item, 0, len(raw))
	for _, f := range raw {
		var (
			userId   string
			followed bool
			follower bool
		)
		if fans {
			// 粉丝列表
			userId = f.UserId
			followed = f.Duplex
			follower = true
		} else {
			// 关注列表
			userId = f.Target
			followed = true
			follower = f.Duplex
		}
		acc, err := ug.Account(ctx, userId)
		if err != nil {
			continue
		}
		if acc.Status.Deleted() && missF != nil {
			go missF(userId, followed, follower)
			continue
		}
		out = append(out, &Item{
			User:     mixer.UserWithExt(ctx, acc),
			Remark:   f.Remark,
			Chummy:   f.Chummy,
			Focus:    f.Focus,
			Followed: followed,
			Follower: follower,
		})
	}
	return out, nil
}

type GItem struct {
	User     *types.UserWithExt `json:"user"`     // 用户
	Followed bool               `json:"followed"` // 已关注
	Follower bool               `json:"follower"` // 被关注
	fakeData
}

func makeGItems(ctx context.Context, ug user.Getter, fg follow.Getter, raw follow.Follows, fans bool, viewer string, missF onMissing) ([]*GItem, error) {
	out := make([]*GItem, 0, len(raw))
	for _, f := range raw {
		var (
			userId   = lo.Ternary(fans, f.UserId, f.Target)
			followed = mixer.NoErr(fg.Following(ctx, viewer, userId))
			follower = mixer.NoErr(fg.Following(ctx, userId, viewer))
		)
		acc, err := ug.Account(ctx, userId)
		if err != nil {
			continue
		}
		if acc.Status.Deleted() && missF != nil {
			go missF(userId, followed, follower)
			continue
		}
		out = append(out, &GItem{
			User:     mixer.UserWithExt(ctx, acc),
			Followed: followed,
			Follower: follower,
		})
	}
	return out, nil
}

type fakeData struct {
	Remark string `json:"remark"` // 客户端兼容字段不要用
	Chummy bool   `json:"chummy"` // 客户端兼容字段不要用
	Focus  bool   `json:"focus"`  // 客户端兼容字段不要用
}
