package follow

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type Slim struct {
	UserId string `json:"userId"`           // 用户ID
	Remark string `json:"remark,omitempty"` // 备注（没有时为null）
	Duplex bool   `json:"duplex,omitempty"` // 互相关注（没有时为null）
}

type exportResp struct {
	Total int     `json:"total"` // 总数量
	List  []*Slim `json:"list"`  // 列表
}

// @Tags 关注
// @Summary 关注列表（全量）
// @Description 获取用户的全部关注数据
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=exportResp}
// @Router /api/v1/follow/list/all [get]
func (s *apis) Export(ctx *api.Context, _ api.EmptyReq) (*exportResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	page, list, err := s.fm.Export(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	var resp exportResp
	resp.Total = page.Total

	for _, f := range list {
		resp.List = append(resp.List, &Slim{
			UserId: f.Target,
			Remark: f.Remark,
			Duplex: f.Duplex,
		})
	}

	return &resp, nil
}
