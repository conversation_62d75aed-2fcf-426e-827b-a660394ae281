package follow

import (
	"context"
	"slices"

	"gitlab.sskjz.com/go/es/query"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type viewRequest struct {
	UserId string   `form:"userId"` // 目标用户
	Type   listType `form:"type"`   // 关系类型
	Cursor string   `form:"cursor"` // 分页游标
}

type viewResponse struct {
	Cursor string   `json:"cursor"` // 分页游标：用于下次请求
	Total  int      `json:"total"`  // 总数量
	List   []*GItem `json:"list"`   // 列表
}

// @Tags 关注
// @Summary 关注列表（客态）
// @Description 查看别人的关注和粉丝列表
// @Produce json
// @Security HeaderAuth
// @Param param query viewRequest true "请求参数"
// @Success 200 {object} codec.Response{data=viewResponse}
// @Router /api/v1/follow/list/view [get]
func (s *apis) GView(ctx *api.Context, req viewRequest) (*viewResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		resp viewResponse
		list follow.Follows
		page *query.Pagination
	)

	switch req.Type {
	case typFollowings:
		page, list, err = s.fm.Followings(ctx, req.UserId, "", req.Cursor)
	case typFollowers:
		page, list, err = s.fm.Followers(ctx, req.UserId, "", req.Cursor)
	default:
		return &resp, nil
	}

	if err != nil {
		return nil, err
	}

	resp.List, err = makeGItems(ctx, s.ug, s.fm, list, req.Type == typFollowers, uac.UserId, func(userId string, followed, follower bool) {
		switch req.Type {
		case typFollowings:
			s.unfollow(ctx, req.UserId, userId)
		case typFollowers:
			s.remove(ctx, req.UserId, userId)
		}
		if followed {
			s.unfollow(ctx, uac.UserId, userId)
		}
		if follower {
			s.remove(ctx, uac.UserId, userId)
		}
	})
	if err != nil {
		return nil, err
	}

	if len(resp.List) > 0 {
		makeGTopMe(ctx, s.ug, s.fm, req.Type, req.Cursor, &resp.List, req.UserId, uac.UserId)
	}

	resp.Total = page.Total - (len(list) - len(resp.List))
	resp.Cursor = page.Cursor

	return &resp, nil
}

func makeGTopMe(ctx context.Context, ug user.Getter, fg follow.Getter, typ listType, cursor string, raw *[]*GItem, userId, viewer string) {
	var hasMe bool
	switch typ {
	case typFollowings:
		hasMe = mixer.NoErr(fg.Following(ctx, userId, viewer))
	case typFollowers:
		hasMe = mixer.NoErr(fg.Following(ctx, viewer, userId))
	}
	if !hasMe {
		return
	}
	idx := slices.IndexFunc(*raw, func(item *GItem) bool { return item.User.UserId == viewer })
	if cursor != "" {
		if idx >= 0 {
			*raw = slices.Delete(*raw, idx, idx+1)
		}
		return
	}
	if idx == 0 {
		return
	}
	if idx < 0 {
		g1, _ := makeGItems(ctx, ug, fg, follow.Follows{{UserId: viewer, Target: viewer}}, false, viewer, nil)
		if len(g1) > 0 {
			*raw = append([]*GItem{g1[0]}, *raw...)
		}
		return
	}
	me := (*raw)[idx]
	for i := idx; i > 0; i-- {
		(*raw)[i] = (*raw)[i-1]
	}
	(*raw)[0] = me
}
