package types

import "gitlab.sskjz.com/overseas/live/osl/internal/ranklist"

// 榜单

// 分发区榜单信息
type RanklistHourEntranceRequest struct {
	AnchorId string `form:"anchorId"`
}

type RanklistHourEntranceResponse struct {
	ranklist.HourEntrance
}

type RanklistEntranceRequest struct {
	AnchorId string `form:"anchorId"`
}

type RanklistEntranceResponse struct {
	ranklist.Entrance
}

type RanklistHourDetailRequest struct {
	AnchorId string `form:"anchorId"`
}

type RanklistHourDetailResponse struct {
	PeriodRemainSeconds int64                        `json:"periodRemainSeconds"` // 当前时段剩余秒数
	OwnerRank           RanklistHourDetailRankInfo   `json:"ownerRank"`           // 主播排名信息
	Ranks               []RanklistHourDetailRankInfo `json:"ranks"`               // 榜单信息
	AdvanceInfo         []RanklistHourDetailRankInfo `json:"advanceInfo"`         // 上小时前十榜单信息
}

type RanklistHourDetailRankInfo struct {
	Index int      `json:"index"` // 排名名次
	Score int64    `json:"score"` // 分数
	User  User     `json:"user"`  // 用户信息
	Tags  []string `json:"tags"`  // 标签信息
}

type RanklistPopularityRequest struct {
	AnchorId string `form:"anchorId"`
}

type RanklistPopularityResponse struct {
	PeriodRemainSeconds int64                              `json:"periodRemainSeconds"` // 当前时段剩余秒数
	OwnerRank           RanklistPopularityDetailRankInfo   `json:"ownerRank"`           // 主播排名信息
	Ranks               []RanklistPopularityDetailRankInfo `json:"ranks"`               // 榜单信息
}

type RanklistPopularityDetailRankInfo struct {
	Index          int      `json:"index"` // 排名名次
	Score          int64    `json:"score"` // 分数
	ContributorNum int64    `json:"contributorNum"`
	User           User     `json:"user"` // 用户信息
	Tags           []string `json:"tags"` // 标签信息
}

type RanklistAnchorRequest struct {
	Type string `form:"type"` // 日榜或周榜daily、weekly
}

type RanklistAnchorResponse struct {
	Ranks []RanklistHomepageRankInfo `json:"ranks"` // 榜单信息
}

type RanklistHomepageRankInfo struct {
	Index int      `json:"index"` // 排名名次
	User  User     `json:"user"`  // 用户信息
	Score int64    `json:"score"`
	Tags  []string `json:"tags"` // 标签信息
}

type RanklistUserRequest struct {
	Type string `form:"type"` // 日榜或周榜daily、weekly
}

type RanklistUserResponse struct {
	Ranks []RanklistHomepageRankInfo `json:"ranks"` // 榜单信息
}

type RanklistGiftEntranceRequest struct {
	AnchorId string `form:"anchorId"`
}

type RanklistGiftEntranceResponse struct {
	DailyScore  int64 `json:"dailyScore"`
	WeeklyScore int64 `json:"weeklyScore"`
}

type UserGiftRankInRoomRequest struct {
	Id   string `form:"id"`   // 房间ID
	Type string `form:"type"` // 日榜或周榜daily、weekly
}

type RanklistRoomRankInfo struct {
	Index int      `json:"index"` // 排名名次
	User  User     `json:"user"`  // 用户信息
	Score int64    `json:"score"`
	Tags  []string `json:"tags"` // 标签信息
}
