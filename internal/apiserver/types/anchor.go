package types

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
)

// VIP信息
type VipInfo struct {
	Members int `json:"members"` // 成员数量
}

type AnchorLiveRequest struct {
	Period    string `form:"period"`    // 周期 today今日 cw本周 cm本月 custom自定义
	StartTime int64  `form:"startTime"` // period自定义时传 开始时间，秒时间戳
	EndTime   int64  `form:"endTime"`   // period自定义时传 结束时间，秒时间戳
}

type AnchorLiveResponse struct {
	UserWithExt
	Live *AnchorLiveOverview `json:"live"` // 周期内数据
}

type AnchorSessionListRequest struct {
	Cursor int64 `form:"cursor"` // 分页游标
}

type AnchorSessionListResponse struct {
	Cursor int64               `json:"cursor"` // 分页游标
	List   []AnchorLiveSession `json:"list"`   // 场次数据
}

type AnchorSessionDetailRequest struct {
	SessionId string `form:"sessionId" validate:"required"` // 场次ID
}

type AnchorSessionDetailResponse struct {
	AnchorLiveSession
}

// 主播直播数据总览
type AnchorLiveOverview struct {
	Period   string `json:"period"`   // 周期
	ValidDay int    `json:"validDay"` // 有效天数
	AnchorLive
}

// 主播直播数据场次维度
type AnchorLiveSession struct {
	SessionId string `json:"sessionId"` // 场次ID
	Title     string `json:"title"`     // 直播标题
	Cover     string `json:"cover"`     // 直播封面
	StartTime int64  `json:"startTime"` // 开始时间，秒时间戳
	EndTime   int64  `json:"endTime"`   // 结束时间，秒时间戳
	AnchorLive
}

type AnchorLive struct {
	AudCount        int64 `json:"audCount"`        // 观看人数
	GiftUserCount   int64 `json:"giftUserCount"`   // 送礼人数
	NewFanCount     int64 `json:"newFanCount"`     // 新增粉丝
	Duration        int64 `json:"duration"`        // 直播时长，单位分钟
	ValidDuration   int64 `json:"validDuration"`   // 有效时长，单位分钟
	LuckDiamond     int64 `json:"luckDiamond"`     // 幸运礼物价值，金币
	GiftDiamond     int64 `json:"giftDiamond"`     // 特效礼物价值，金币
	Salary          int64 `json:"salary"`          // 薪资，水晶
	TotalGiftIncome int64 `json:"totalGiftIncome"` // 所有礼物收入，水晶
	Income          int64 `json:"income"`          // 总收入，水晶=薪资（水晶）+幸运礼物（水晶）+特效礼物（转换水晶）
}

type AnchorTaskListRequest struct{}

type AnchorTaskListResponse struct {
	Now         int64             `json:"now"`         // 当前秒时间戳
	NowFormat   string            `json:"nowFormat"`   // 当前时间按主播时区格式化
	List        []task.AnchorTask `json:"list"`        // 任务列表
	GiftDiamond int64             `json:"giftDiamond"` // 特效礼物价值，金币
}

type AnchorTaskOverviewResponse struct {
	Now         int64  `json:"now"`         // 当前秒时间戳
	NowFormat   string `json:"nowFormat"`   // 当前时间按主播时区格式化
	Duration    int64  `json:"duration"`    // 直播时长，单位分钟
	LuckDiamond int64  `json:"luckDiamond"` // 幸运礼物价值，金币
	GiftDiamond int64  `json:"giftDiamond"` // 特效礼物价值，金币
}

type AnchorBillHistoryRequest struct {
	Type   fund.JournalType `form:"type"`   // 全部收入 0 直播礼物 2 主播薪资 5
	EndAt  int64            `form:"endAt"`  // 截止时间：unix秒
	Cursor string           `form:"cursor"` // 分页游标
}

type AnchorBillDetailRequest struct {
	Id string `form:"id"` // 交易ID
}

type AnchorBillDetailResponse struct {
	Id      string              `json:"id"`                // 账单ID
	Type    string              `json:"type"`              // 账单类型 gift直播礼物 salary薪资
	Session *salary.BillSession `json:"session,omitempty"` // 直播礼物账单
	Salary  *salary.BillSalary  `json:"salary,omitempty"`  // 薪资账单
}

type AnchorLiveDurationRequest struct {
	ShowId string `form:"showId" validate:"required"` // 用户showId
	Day    string `form:"day"`                        // 日期
}

type AnchorLiveDurationResponse struct {
	UserId   string `json:"userId"`   // 用户ID
	Day      string `json:"day"`      // 日期
	Nickname string `json:"nickname"` // 用户昵称
	Duration int64  `json:"duration"` // 直播时长，单位秒
}

type AnchorEvaluationStatusResponse struct {
	Status        int    `json:"status"`        // -2禁止申请 -1:审核不通过 0:未申请 1:待审核 2:审核通过
	Screenshot    string `json:"screenshot"`    // 直播截图
	ScreenShotUrl string `json:"screenShotUrl"` // 直播截图url
	Video         string `json:"video"`         // 直播录屏
	VideoUrl      string `json:"videoUrl"`      // 直播录屏url
	Reason        string `json:"reason"`        // 审核不通过原因
}

type AnchorEvaluationApplyRequest struct {
	Screenshot string `json:"screenshot"` // 直播截图
	Video      string `json:"video"`      // 直播录屏
}

type AnchorEvaluationV2ApplyRequest struct {
	Screenshot string `json:"screenshot"` // 直播截图
	Video      string `json:"video"`      // 直播录屏
	Attachment string `json:"attachment"` // 其他附件截图
}

// 新主播考核信息：当前所在阶段
// 资料审核阶段：状态
// 资料审核结果&流水考核结果
// 新主播扶持阶段：状态
// 三个步骤：资料审核、流水考核、新主播扶持
// 可以跳过第二个步骤
type AnchorEvaluationV2StatusResponse struct {
	IsQuality bool                        `json:"isQuality"`          // 是否优质主播
	Phase     string                      `json:"phase"`              // 当前所在阶段 document资料审核 fund流水考核 support新主播扶持
	Document  *AnchorEvaluationV2Document `json:"document,omitempty"` // 资料审核阶段
	Fund      *AnchorEvaluationV2Fund     `json:"fund,omitempty"`     // 流水考核阶段
	Support   *AnchorEvaluationV2Support  `json:"support,omitempty"`  // 新主播扶持阶段
}

type AnchorEvaluationV2Document struct {
	Status        int    `json:"status"`        // -2:不允许再申请 -1审核不通过 0待提交 1待审核 2审核通过
	Screenshot    string `json:"screenshot"`    // 直播截图
	ScreenShotUrl string `json:"screenShotUrl"` // 直播截图url
	Video         string `json:"video"`         // 直播录屏
	VideoUrl      string `json:"videoUrl"`      // 直播录屏url
	Attachment    string `json:"attachment"`    // 其他附件截图
	AttachmentUrl string `json:"attachmentUrl"` // 其他附件截图url
	Reason        string `json:"reason"`        // 审核不通过原因
	NeedFund      bool   `json:"needFund"`      // 是否需要进行流水考核
	IsOld         bool   `json:"isOld"`         // 是否老主播
}

type AnchorEvaluationV2Fund struct {
	Status    int   `json:"status"`    // 1进行中 2考核通过 3考核不通过
	RemainDay int   `json:"remainDay"` // 剩余天数
	Diamond   int64 `json:"diamond"`   // 流水，金币
	Target    int64 `json:"target"`    // 流水目标，金币
}

type AnchorEvaluationV2Support struct {
	Status int                            `json:"status"` // -1终止（后台取消资格） 1进行中 2已暂停 3已结束
	Day    int                            `json:"day"`    // 考核第几天
	List   []AnchorEvaluationV2SupportDay `json:"list"`   // 每日任务列表
}

type AnchorEvaluationV2SupportDay struct {
	Day      int    `json:"day"`      // 第几天
	Date     string `json:"date"`     // 日期
	IsToday  bool   `json:"isToday"`  // 是否今日
	Paused   bool   `json:"paused"`   // 暂停
	Duration int64  `json:"duration"` // 直播时长，单位秒
	Diamond  int64  `json:"diamond"`  // 流水，金币
	Status   int    `json:"status"`   // 1未开始 2未完成 3待领取 4已领取
}

// 开始流水考核
type AnchorEvaluationV2FundRequest struct {
	Operation string `json:"operation"` // start开始
}

// 新主播扶持
type AnchorEvaluationV2SupportRequest struct {
	Operation string `json:"operation"` // start开始
	Force     bool   `json:"force"`     // 强制开始
}

// 领取新主播扶持奖励
type AnchorEvaluationV2SupportAwardReceiveRequest struct {
	Date string `json:"date"` // 日期
}
