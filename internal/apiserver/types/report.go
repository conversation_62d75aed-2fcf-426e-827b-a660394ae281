package types

import "gitlab.sskjz.com/overseas/live/osl/internal/report"

type ReportTypeRequest struct {
	ReportTarget int `form:"reportTarget"` // 举报的对象 1动态 2直播 3私信 4用户 5动态评论
}

type ReportTypeResponse struct {
	List []*report.ReportType `json:"list"`
}

type ReportCommitRequest struct {
	ReportTarget       int      `json:"reportTarget" binding:"required"`       // 举报对象的类型：1动态 2直播 3私信 4用户 5动态评论
	ReportTargetId     string   `json:"reportTargetId" binding:"required"`     // 举报对象的Id：动态id roomId 私信id kakoId 评论id
	ReportTargetUserId string   `json:"reportTargetUserId" binding:"required"` // 举报对象的userId
	ReportTypeId       int      `json:"reportTypeId" binding:"required"`       // 举报类型id 色情低俗、违法犯罪、政治敏感等，在type接口中获取的id
	Desc               string   `json:"desc"`                                  // 举报描述
	Images             []string `json:"images"`                                // 图片列表，/api/v1/upload/ststoken的scene参数为report
	IsBlockUser        bool     `json:"isBlockUser"`                           // 举报并拉黑用户
}

type ReportCommitResponse struct{}
