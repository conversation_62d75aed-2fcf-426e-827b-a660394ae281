package types

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

// 用户基础信息
type User struct {
	UserId   string           `json:"userId"`             // 用户ID
	NumId    int64            `json:"numId"`              // 内部用户数字ID 目前用于IM
	Nickname string           `json:"nickname"`           // 用户昵称
	Avatar   string           `json:"avatar"`             // 用户头像
	Level    int              `json:"level"`              // 观众等级
	Role     user.Role        `json:"role,omitempty"`     // 用户角色（空值为null）
	IsLiving bool             `json:"isLiving,omitempty"` // 正在直播（空值为null）
	LStatus  protocol.LStatus `json:"lStatus,omitempty"`  // 直播状态（空值为null）, 主播要忽略连线状态
	UserDress
}

// 用户装扮信息
type UserDress struct {
	AvatarBorder string `json:"avatarBorder,omitempty"` // 头像框（空值为null）
}

// 用户扩展信息
type UserExt struct {
	ShowId    string `json:"showId"`    // 用户自定义ID
	Signature string `json:"signature"` // 个性签名
	Gender    int    `json:"gender"`    // 性别：0-未知 1-男 2-女
	Age       int    `json:"age"`       // 年龄
	Area      string `json:"area"`      // 地区
	School    string `json:"school"`    // 学校
}

// 用户基础+扩展信息
type UserWithExt struct {
	User
	UserExt
}

// 用户社交信息
type UserSocial struct {
	Following int `json:"following"` // 关注数
	Followers int `json:"followers"` // 粉丝数
}

// 用户关注关系
type FollowState struct {
	Followed bool `json:"followed"` // 是否关注
	Follower bool `json:"follower"` // 是否粉丝
}

// 用户动态信息
type UserMoment struct {
	RecvLikes int `json:"recvLikes"` // 获赞数
	Moments   int `json:"moments"`   // 作品数量
}

// 用户等级信息
type LevelInfo struct {
	Level    int `json:"level"`    // 当前用户等级
	TotalExp int `json:"totalExp"` // 总获得的经验值
	GradeMax int `json:"gradeMax"` // 当前等级最大经验值（累计值）
	CurrExp  int `json:"currExp"`  // 当前等级经验值（按等级）
	CurrMax  int `json:"currMax"`  // 当前等级最大经验值（进度=currExp/currMax）
}

type UserGrant struct {
	BlockStatus uint32 `json:"blockStatus"` // 用户屏蔽状态, 按位表示，	RelationTo   Relation = 1 << 0  A block B,	RelationBy   Relation = 1 << 1 // A is blocked by B
	Muted       bool   `json:"muted"`       // 用户禁言状态,主态
}
