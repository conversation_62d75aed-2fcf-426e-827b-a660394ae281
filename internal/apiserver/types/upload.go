package types

type STSToken struct {
	Endpoint        string    `json:"endpoint"`
	Bucket          string    `json:"bucket"`
	AccessKeyId     string    `json:"accessKeyId"`
	AccessKeySecret string    `json:"accessKeySecret"`
	SecurityToken   string    `json:"securityToken"`
	Expiration      int64     `json:"expiration"`
	ServerTime      int64     `json:"serverTime"`
	Prefix          string    `json:"prefix"`  // 文件存储目录
	Pattern         string    `json:"pattern"` // 文件存储URL格式 https://bucket.domain/prefix/$key$
	Suffix          URLSuffix `json:"suffix"`  // URL后缀
}

type URLSuffix struct {
	Image string `json:"image"` // 图片类型后缀
}
