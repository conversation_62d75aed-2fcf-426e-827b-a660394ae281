package types

// 直播在线观众
type Audience struct {
	User
	RoomUser
	Rank      int64  `json:"rank"`      // 排名, 为null时显示-
	RankDesc  string `json:"rankDesc"`  // 排名描述
	Score     int64  `json:"score"`     // 贡献值, 为null时不显示
	ScoreDesc string `json:"scoreDesc"` // 贡献值描述
}

// 观众列表，粉丝团只显示当前直播间的信息
type LiveAudienceRequest struct {
	Id string `form:"id" binding:"required"` // 房间ID
}

type AudienceRank struct {
	*Audience
	Mark string `json:"mark"` // 标记, 为null时不显示
}

type LiveAudienceResponse struct {
	List []Audience    `json:"list"`
	Mine *AudienceRank `json:"mine"` // 自己的排名和信息
}
