package types

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
)

type InListRequest struct {
	Type   int `form:"type"`   // 0全部 1动态点赞 2动态评论与回复 4@消息
	Cursor int `form:"cursor"` // 游标
	Count  int `form:"count"`  // 数量
}

type InListResponse struct {
	List   []InMessageItem `json:"list"`
	Cursor int             `json:"cursor"`
}

type InMessageItem struct {
	Type        int          `json:"type"`        // 0全部 1动态点赞 2动态评论 3评论回复 4@ 5评论或回复点赞
	Mid         uint         `json:"mid"`         // 动态id
	FromUser    *InUser      `json:"fromUser"`    // 消息来源（左侧头像）
	Thumbnail   string       `json:"thumbnail"`   // 右侧缩略图
	MomentText  string       `json:"momentText"`  // 动态为纯文本类型，此字段返回动态文本
	CommentInfo *CommentInfo `json:"commentInfo"` // 当type为2和3、4时
	CreateTime  int64        `json:"createTime"`  // 消息时间，unix秒
}

type CommentInfo struct {
	Mid       uint               `json:"mid"`
	ParentId  uint               `json:"parentId"`
	TargetId  uint               `json:"targetId"`
	CId       uint               `json:"cid"`       // 评论id
	Text      string             `json:"text"`      // 评论内容
	TextExtra []moment.DescExtra `json:"textExtra"` // 处理@内容
	IsLike    bool               `json:"isLike"`    // 我是否点赞
	FromUser  *User              `json:"fromUser"`  // 发表的用户
}

type InUser struct {
	UserWithExt
	FollowState
}

type InViewRequest struct {
}

type InViewResponse struct {
	LastMessage string `json:"item"`     // 最后一条消息
	LastTime    int64  `json:"lastTime"` // 最后一条消息时间
	NewCount    int    `json:"newCount"` // 新消息数量
}

type InUnreadIgnoreRequest struct {
}

type InUnreadIgnoreResponse struct {
}
