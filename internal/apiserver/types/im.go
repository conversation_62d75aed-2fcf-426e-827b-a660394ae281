package types

type ImTokenResponse struct {
	Token        string  `json:"token"`        // IM token
	NoticeNumIds []int64 `json:"noticeNumIds"` // 系统通知消息账号
}

type ImContact struct {
	UserWithExt
}

type ImContactListResponse struct {
	List []ImContact `json:"list"`
}

type ImContactSearchRequest struct {
	Keyword string `form:"keyword"` // 关键字
}
type ImContactSearchResponse struct {
	List []ImContact `json:"list"`
}

type ImMessageSendRequest struct {
	ReceiverUserId string `form:"receiverUserId"` // 不传则为全员接收
	Type           int    `form:"type"`           // 10001文字消息 10012自定义消息
	Content        string `form:"content"`        // 消息内容
	Notice         int    `form:"notice"`         // 0当前登录账号发送 1系统通知 2官方消息
}

type ImMessageSendResponse struct{}

// 获取消息回调处理结果
type ImCallbackResultRequest struct {
	ShortConversationId int64  `form:"shortConversationId" binding:"required"` // 会话ID
	ClientMessageId     string `form:"clientMessageId" binding:"required"`     // 客户端消息ID（uuid）
}

type ImCallbackResultResponse struct {
	Deny   bool `json:"deny"`   // 是否拒绝发送
	Reason int  `json:"reason"` // 拒绝原因 1对方关注你或回复你之前，你无法发送消息 2今日已达陌生人消息发送上限，对方关注你之后可继续聊天 3已达消息发送限制，关注对方后可继续聊天 10我拉黑对方 11对方拉黑我 100其他原因
}

type ImUserInfoRequest struct {
	NumId int64 `form:"numId"`
}

type ImUserInfoResponse struct {
	User UserWithExt       `json:"user"`
	Ext  map[string]string `json:"ext"`
}

type ImCsRefreshRequest struct {
	ShortConversationId int64 `json:"shortConversationId" binding:"required"` // 会话ID
}
