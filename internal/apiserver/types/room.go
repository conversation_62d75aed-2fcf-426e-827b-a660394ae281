package types

type Room struct {
	User        *UserWithExt      `json:"user"`        // 主播信息
	Followed    bool              `json:"followed"`    // 是否关注主播
	Appointment bool              `json:"appointment"` // 是否预约了下一场直播
	RoomId      string            `json:"roomId"`      // 房间ID
	SessionId   string            `json:"sessionId"`   // 场次ID
	Title       string            `json:"title"`       // 直播标题
	Cover       string            `json:"cover"`       // 直播封面
	Stream      string            `json:"stream"`      // 直播流地址 rtc是火山实时音视频
	Like        int64             `json:"like"`        // 点赞数
	LabelUrl    string            `json:"labelUrl"`    // 角标图片地址
	Ext         map[string]string `json:"ext"`         // 扩展字段（开播时客户端传入的ext数据）
	Status      int               `json:"status"`      // 直播状态 0未知 1开播中 10未开播
	Mode        int               `json:"mode"`        // 直播模式 视频直播/9人语音房等
	ShowAgr     bool              `json:"showAgr"`     // 是否显示主播直播协议（主播态）
	StartTime   int64             `json:"startTime"`   // 当前直播状态的开始时间 秒时间戳
	EndTime     int64             `json:"endTime"`     // 当前直播状态的结束时间 秒时间戳
	Paused      bool              `json:"paused"`      // 是否暂离中
	ForLucky    bool              `json:"forLucky"`    // 是否是幸运礼物体验房
	ForGame     bool              `json:"forGame"`     // 是否是游戏房
	Resolution  string            `json:"resolution"`  // 分辨率 360P 720P
	Sticker     string            `json:"sticker"`     // 贴纸
}

type RoomUser struct {
	FansLevel int `json:"fansLevel,omitempty"` // 粉丝团等级（没有为null）
}

type RoomFans struct {
	Level    int  `json:"level"`    // 粉丝团等级
	Inactive bool `json:"inactive"` // 熄灭状态
}

type RoomMe struct {
	Fansclub *RoomFans `json:"fansclub,omitempty"` // 粉丝团信息（没有时为null）
}
