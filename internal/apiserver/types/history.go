package types

type HistoryWriteRequest struct {
	ArtId   string `json:"artId"`   // 作品ID
	ArtType string `json:"artType"` // 作品类型 直播live 动态moment
}

type HistoryWriteResponse struct{}

type HistoryDeleteRequest struct {
	ArtIds  []string `json:"artIds"`  // 作品ID（roomId/momentId）
	ArtType string   `json:"artType"` // 作品类型 直播live 动态moment
	Clear   bool     `json:"clear"`   // 是否清空 true清空 false按artIds删除
}

type HistoryDeleteResponse struct{}

type HistoryLiveListRequest struct {
	Page int `form:"page"` // 1开始
}

type HistoryLiveListResponse struct {
	List []HistoryLive `json:"list"`
}

type HistoryLive struct {
	Room         *Room        `json:"room"`         // 直播间
	UserRelation *FollowState `json:"userRelation"` // 用户社交关系
	At           int64        `json:"at"`           // 观看时间（秒时间戳）
}
