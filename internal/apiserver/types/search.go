package types

// 综合查询
type SearchGeneralRequest struct {
	Keyword string `form:"keyword"` // 关键字
}

// 综合查询
type SearchGeneralResponse struct {
	List []SearchItem `json:"list"`
}

type SearchItem struct {
	Type int             `json:"type"` // 类型 1用户
	User *SearchItemUser `json:"user"`
}

type SearchItemUser struct {
	UserWithExt
	UserSocial
	FollowState
	Room *Room `json:"room"`
}

// 猜你想搜
type SearchSuggestLiveRequest struct {
	Eid string `form:"eid"` // 排除的直播间ID，逗号分隔
}

// 猜你想搜
type SearchSuggestLiveResponse struct {
	List []Room `json:"list"` // 直播间
}

type SearchClickRequest struct {
	UserId string `json:"userId"` // 点击的用户ID
}

type SearchUsersRequest struct {
	Scene   int    `form:"scene"`   // 场景：0不限制 1动态 2直播间 3私信好友
	Keyword string `form:"keyword"` // 关键字
}

type SearchUsersResponse struct {
	List []*User `json:"list"` // 用户列表
}
