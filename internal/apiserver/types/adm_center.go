package types

type AdmCenterInfoResponse struct {
	User        *User  `json:"user"`        // ADM用户信息
	InviteCode  string `json:"inviteCode"`  // ADM邀请码
	InviteUrl   string `json:"inviteUrl"`   // ADM邀请链接
	Status      int    `json:"status"`      // ADM状态 0取消 1试用 2转正
	TrialPeriod int64  `json:"trialPeriod"` // 试用期截止时间，秒时间戳
	Days        int    `json:"days"`        // ADM转正后显示的成为ADM天数
}

type AdmCenterOverviewRequest struct {
	Period string `form:"period" binding:"required"` // 周期 cm本月 lm上月 custom自定义
	Month  string `form:"month"`                     // 自定义周期时传的月份 202409
}

type AdmCenterOverviewResponse struct {
	AdmCenterOverview
	Seller *SellerCenterView `json:"seller"` // 币商数据（不是币商为null）
}

type AdmCenterOverview struct {
	AgencyCount            int64  `json:"agencyCount"`            // 旗下公会数
	AnchorIncome           int64  `json:"anchorIncome"`           // 旗下主播收入，水晶
	EligibleAgencyCount    int64  `json:"eligibleAgencyCount"`    // 达标公会数
	NewEligibleAgencyCount int64  `json:"newEligibleAgencyCount"` // 新增达标公会数
	IneligibleAgencyCount  int64  `json:"ineligibleAgencyCount"`  // 未达标公会数
	EstimatedRatio         string `json:"estimatedRatio"`         // 预估收益比例
	EstimatedIncome        int64  `json:"estimatedIncome"`        // 预估我的收益，水晶
	RatioDescUrl           string `json:"ratioDescUrl"`           // 预估分成比例图片地址
}

type SellerCenterView struct {
	Balance int64 `json:"balance"` // 币商余额
}

type AdmCenterAgencyListRequest struct {
	Period   string `form:"period" binding:"required"`     // 周期 cm本月 lm上月 custom自定义
	Month    string `form:"month"`                         // 自定义周期时传的月份 202409
	Filter   string `form:"filter" binding:"required"`     // 过滤条件 all全部 eligible达标 ineligible未达标
	Page     int64  `form:"page" binding:"required,min=1"` // 页码
	PageSize int64  `form:"pageSize"`                      // 每页数量
}

type AdmCenterAgencyListResponse struct {
	Page     int64             `json:"page"`
	PageSize int64             `json:"pageSize"`
	List     []AdmCenterAgency `json:"list"`
}

type AdmCenterAgency struct {
	Name        string `json:"name"`        // 公会名称
	ImageUrl    string `json:"imageUrl"`    // 公会头像
	LuckDiamond int64  `json:"luckDiamond"` // 幸运礼物流水，金币
	JoinTime    int64  `json:"joinTime"`    // 加入时间
	Eligible    bool   `json:"eligible"`    // 达标状态 true达标 false未达标
}

type AdmCenterInviteInfoResponse struct {
	InviteCode string `json:"inviteCode"` // 公会邀请码
	InviteUrl  string `json:"inviteUrl"`  // ADM邀请链接
}
