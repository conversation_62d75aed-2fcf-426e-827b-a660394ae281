package types

type SendChatMessageRequest struct {
	RoomId  string   `json:"roomId"`
	Content string   `json:"content"`
	Quotes  []string `json:"quotes"`  // @desc 引用的消息id
	Sticker string   `json:"sticker"` // @desc 贴纸id
}

type SendChatMessageResponse struct {
}

type ChatHistoryRequest struct {
	RoomId string `json:"roomId" form:"roomId"`
}

type HistoryChat struct {
	Id      string      `json:"id"`
	User    UserWithExt `json:"user"`
	Content string      `json:"content"`
	Sticker string      `json:"sticker"`
	Quotes  []User      `json:"quotes"` // @desc 引用的消息id
	At      int64       `json:"at"`     // @desc 发送时间 ms
}

type ChatHistoryResponse struct {
	List []HistoryChat `json:"list"`
}
