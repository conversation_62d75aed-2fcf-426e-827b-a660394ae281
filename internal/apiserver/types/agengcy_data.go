package types

type AgencyDataOverviewRequest struct {
	Period    string `form:"period" binding:"required"` // 周期 today今日 cw本周 cm本月 custom自定义
	StartTime int64  `form:"startTime"`                 // 开始时间
	EndTime   int64  `form:"endTime"`                   // 结束时间
}

type AgencyDataOverviewResponse struct {
	AgencyDataOverview
}

type AgencyDataDayListRequest struct {
	StartTime int64  `form:"startTime"` // 开始时间
	EndTime   int64  `form:"endTime"`   // 结束时间
	Cursor    string `form:"cursor"`    // 分页游标
	AnchorId  string `form:"anchorId"`  // 主播ID
	Agg       int    `form:"agg"`       // 聚合方式 0按月 1按周 2按天 3总计
}

type AgencyDataDayListResponse struct {
	Cursor      string          `json:"cursor"`         // 分页游标
	Aggs        []Summary       `json:"aggs"`           // 汇总
	List        []AgencyDataDay `json:"list"`           // 列表
	User        *UserWithExt    `json:"user,omitempty"` // 主播信息
	IsNewAnchor bool            `json:"isNewAnchor"`    // 是否新主播
}

type AgencyDataDayDetailRequest struct {
	StartTime int64 `form:"startTime" binding:"required"` // 开始时间
	EndTime   int64 `form:"endTime" binding:"required"`   // 结束时间
}

type AgencyDataDayDetailResponse struct {
	AgencyDataDay
}

type AgencyDataAnchorListRequest struct {
	StartTime int64  `form:"startTime"` // 开始时间
	EndTime   int64  `form:"endTime"`   // 结束时间
	Cursor    string `form:"cursor"`    // 分页游标
	Agg       int    `form:"agg"`       // 聚合方式 0按月 1按周 2按天 3总计
}

type AgencyDataAnchorListResponse struct {
	Cursor string             `json:"cursor"` // 分页游标
	Aggs   []Summary          `json:"aggs"`   // 月份汇总
	List   []AgencyDataAnchor `json:"list"`   // 列表
}

type AgencyDataAnchorDetailRequest struct {
	AnchorId  string `form:"anchorId" binding:"required"`  // 主播ID
	StartTime int64  `form:"startTime" binding:"required"` // 开始时间
	EndTime   int64  `form:"endTime" binding:"required"`   // 结束时间
}

type AgencyDataAnchorDetailResponse struct {
	AgencyDataAnchor
}

type AgencyDataOverview struct {
	AnchorAmount     int64 `json:"anchorAmount"`     // 主播流水，金币
	LuckDiamond      int64 `json:"luckDiamond"`      // 幸运礼物流水，金币
	AnchorIncome     int64 `json:"anchorIncome"`     // 主播收入，水晶
	AgencyIncome     int64 `json:"agencyIncome"`     // 公会收入，水晶
	LiveAnchorCount  int64 `json:"liveAnchorCount"`  // 开播主播数
	ValidAnchorCount int64 `json:"validAnchorCount"` // 有效主播数
	NewAnchorCount   int64 `json:"newAnchorCount"`   // 新增主播数
}

type AgencyDataDay struct {
	AnchorAmount     int64 `json:"anchorAmount"`     // 主播总流水，金币
	LuckDiamond      int64 `json:"luckDiamond"`      // 幸运礼物流水，金币
	GiftDiamond      int64 `json:"giftDiamond"`      // 特效礼物流水，金币
	AnchorIncome     int64 `json:"anchorIncome"`     // 主播收入，水晶
	AgencyIncome     int64 `json:"agencyIncome"`     // 公会收入，水晶
	LiveAnchorCount  int64 `json:"liveAnchorCount"`  // 开播主播数
	ValidAnchorCount int64 `json:"validAnchorCount"` // 有效主播数
	NewAnchorCount   int64 `json:"newAnchorCount"`   // 新增主播数
	ValidDuration    int64 `json:"validDuration"`    // 有效时长，分钟
	Time             int64 `json:"time"`             // 时间：unix秒
}

type AgencyDataAnchor struct {
	User          *UserWithExt `json:"user,omitempty"` // 主播信息
	IsNewAnchor   bool         `json:"isNewAnchor"`    // 是否新主播
	AnchorAmount  int64        `json:"anchorAmount"`   // 主播流水，钻石
	LuckDiamond   int64        `json:"luckDiamond"`    // 幸运礼物流水，金币
	GiftDiamond   int64        `json:"giftDiamond"`    // 特效礼物流水，金币
	AnchorIncome  int64        `json:"anchorIncome"`   // 主播收入，水晶
	AgencyIncome  int64        `json:"agencyIncome"`   // 公会收入，水晶
	LiveDayCount  int64        `json:"liveDayCount"`   // 总开播天数
	ValidDayCount int64        `json:"validDayCount"`  // 有效开播天数
	ValidDuration int64        `json:"validDuration"`  // 有效时长，分钟
	Time          int64        `json:"time"`           // 时间：unix秒
}

// 时间段汇总数据
type Summary struct {
	Time             int64 `json:"time"`             // 聚合的开始时间
	Incomes          int64 `json:"incomes"`          // 公会总收入，水晶
	Amount           int64 `json:"amount"`           // 总流水，钻石
	LuckDiamond      int64 `json:"luckDiamond"`      // 幸运礼物流水，金币
	GiftDiamond      int64 `json:"giftDiamond"`      // 特效礼物流水，金币
	AnchorIncome     int64 `json:"anchorIncome"`     // 主播收入，水晶
	AgencyIncome     int64 `json:"agencyIncome"`     // 公会收入，水晶
	LiveDayCount     int64 `json:"liveDayCount"`     // 总开播天数
	ValidDayCount    int64 `json:"validDayCount"`    // 有效开播天数
	ValidDuration    int64 `json:"validDuration"`    // 有效时长，分钟
	LiveAnchorCount  int64 `json:"liveAnchorCount"`  // 开播主播数
	ValidAnchorCount int64 `json:"validAnchorCount"` // 有效主播数
	NewAnchorCount   int64 `json:"newAnchorCount"`   // 新增主播数
}
