package types

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
)

type MomentGetRequest struct {
	MId uint `form:"mid"` // 动态id
}

type MomentGetResponse struct {
	MomentFeedItem
}

// MomentFeedRequest 动态feed请求参数
type MomentFeedRequest struct {
	Cursor int `form:"cursor"` // 上次请求时接口返回的cursor
	Count  int `form:"count"`  // 请求数量
}

// MomentFeedResponse 动态feed返回
type MomentFeedResponse struct {
	List   []MomentFeedItem `json:"list"`
	Cursor int              `json:"cursor"` // offset下次请求时使用
}

// MomentReadRequest 动态已读
type MomentReadRequest struct {
	Mid uint `json:"mid"`
}

// MomentReadResponse 动态已读
type MomentReadResponse struct {
}

type MomentFeedItem struct {
	MId           uint                           `json:"mid"`           // 动态id
	MomentType    int                            `json:"momentType"`    // 动态类型 1.纯文字 2.图文 3.视频
	Author        *User                          `json:"author"`        // 作者信息
	Followed      bool                           `json:"followed"`      // 是否关注
	Title         string                         `json:"title"`         // 标题
	Desc          string                         `json:"desc"`          // 描述
	DescExtra     []moment.DescExtra             `json:"descExtra"`     // 描述扩展
	CharacterInfo *moment.MomentCharacter        `json:"characterInfo"` // 文本信息，适用于MomentType为1，否则为null
	PictureInfo   *moment.MomentPicture          `json:"pictureInfo"`   // 图片信息，适用于MomentType为2，否则为null
	VideoInfo     *moment.MomentVideo            `json:"videoInfo"`     // 视频信息，适用于MomentType为3，否则为null
	OptionAllow   *moment.MomentOption           `json:"optionAllow"`   // 开关
	Statistics    *moment.MomentStatisticsSimple `json:"statistics"`    // 统计数据
	ShareInfo     *moment.MomentShareInfo        `json:"shareInfo"`
	Status        *moment.MomentStatus           `json:"status"`        // 是否已经操作
	IsTop         bool                           `json:"isTop"`         // 是否置顶
	VisibleStatus int                            `json:"visibleStatus"` // 可见状态，0所有，1自己，2朋友
	CreateTime    int64                          `json:"createTime"`
}

// MomentPublishRequest 发布动态
type MomentPublishRequest struct {
	MomentType    int                     `json:"momentType"`    // 动态类型 1.纯文字 2.图文 3.视频
	Title         string                  `json:"title"`         // 标题
	Desc          string                  `json:"desc"`          // 描述
	DescExtra     []moment.DescExtra      `json:"descExtra"`     // 描述扩展
	CharacterInfo *moment.MomentCharacter `json:"characterInfo"` // 文本信息，适用于MomentType为1，否则为null
	PictureInfo   *moment.MomentPicture   `json:"pictureInfo"`   // 图片信息，适用于MomentType为2，否则为null
	VideoInfo     *moment.MomentVideo     `json:"videoInfo"`     // 视频信息，适用于MomentType为3，否则为null
	VisibleStatus int                     `json:"visibleStatus"` // 可见状态，0所有，1自己，2朋友
}

// MomentPublishResponse 发布动态返回
type MomentPublishResponse struct {
	MId           uint                           `json:"mid"`           // 动态id
	MomentType    int                            `json:"momentType"`    // 动态类型 1.纯文字 2.图文 3.视频
	Author        User                           `json:"author"`        // 作者信息
	Title         string                         `json:"title"`         // 标题
	Desc          string                         `json:"desc"`          // 描述
	DescExtra     []moment.DescExtra             `json:"descExtra"`     // 描述扩展
	CharacterInfo *moment.MomentCharacter        `json:"characterInfo"` // 文本信息，适用于MomentType为1，否则为null
	PictureInfo   *moment.MomentPicture          `json:"pictureInfo"`   // 图片信息，适用于MomentType为2，否则为null
	VideoInfo     *moment.MomentVideo            `json:"videoInfo"`     // 视频信息，适用于MomentType为3，否则为null
	OptionAllow   *moment.MomentOption           `json:"optionAllow"`   // 开关
	Statistics    *moment.MomentStatisticsSimple `json:"statistics"`    // 统计数据
	Status        *moment.MomentStatus           `json:"status"`        // 是否已经操作
	ShareInfo     *moment.MomentShareInfo        `json:"shareInfo"`     // 分享信息
	IsTop         bool                           `json:"isTop"`         // 是否置顶
	VisibleStatus int                            `json:"visibleStatus"` // 可见状态，0所有，1自己，2朋友
	CreateTime    int64                          `json:"createTime"`
}

type MomentModifyRequest struct {
	MId           uint                    `json:"mid"`           // 动态id
	MomentType    int                     `json:"momentType"`    // 动态类型 1.纯文字 2.图文 3.视频
	Title         string                  `json:"title"`         // 标题
	Desc          string                  `json:"desc"`          // 描述
	DescExtra     []moment.DescExtra      `json:"descExtra"`     // 描述扩展
	CharacterInfo *moment.MomentCharacter `json:"characterInfo"` // 文本信息，适用于MomentType为1，否则为null
}

type MomentDeleteRequest struct {
	MId uint `json:"mid"` // 动态id
}

type MomentDeleteResponse struct {
}

// MomentLikeRequest 点赞/取消点赞
type MomentLikeRequest struct {
	MId uint `json:"mid"` // 动态id
}

type MomentLikeResponse struct{}

// MomentReportRequest 举报
type MomentReportRequest struct {
	MId    uint     `json:"mid"`  // 动态id
	Type   int      `json:"type"` // 1~12
	Reason string   `json:"reason"`
	Desc   string   `json:"desc"`   // 描述
	Images []string `json:"images"` // 图片
}

type MomentReportResponse struct{}

type MomentVisibleRequest struct {
	MId           uint `json:"mid"`           // 动态id
	VisibleStatus int  `json:"visibleStatus"` // 可见状态，0所有，1自己，2朋友
}

type MomentVisibleResponse struct{}

type MomentTopRequest struct {
	MId uint `json:"mid"` // 动态id
}

type MomentTopResponse struct{}

// MomentCommentPublishRequest 获取评论列表
type MomentCommentPublishRequest struct {
	MId       uint               `json:"mid"`       // 动态id
	ParentId  uint               `json:"parentId"`  // 回复时需要填写：最上层一级评论id
	TargetId  uint               `json:"targetId"`  // 回复时需要填写：回复目标评论id
	Text      string             `json:"text"`      // 评论内容
	TextExtra []moment.DescExtra `json:"textExtra"` // 处理@内容
	ImageList []string           `json:"imageList"` // 图片内容
}

type MomentCommentPublishResponse struct {
	MomentComment // 评论内容
}

type MomentComment struct {
	MId             uint               `json:"mid"`             // 动态id
	CId             uint               `json:"cid"`             // 评论id
	FromUser        *User              `json:"fromUser"`        // 发表的用户
	ToUser          *User              `json:"toUser"`          // 回复的目标用户，只有回复列表中才有值
	LikeCount       int                `json:"likeCount"`       // 点赞数
	ReplyCount      int                `json:"replyCount"`      // 回复数量
	LabelList       []int              `json:"labelList"`       // 标签列表 1作者 2自己 3首评 4朋友
	ToUserLabelList []int              `json:"toUserLabelList"` // touser标签列表 1作者 2自己 3首评 4朋友
	IsLike          bool               `json:"isLike"`          // 我是否点赞
	IsFold          bool               `json:"isFold"`          // 是否折叠（点踩）
	IsReport        bool               `json:"isReport"`        // 是否被举报
	IpLabel         string             `json:"ipLabel"`         // 地址
	Text            string             `json:"text"`            // 评论内容
	TextExtra       []moment.DescExtra `json:"textExtra"`       // 处理@内容
	ImageList       []string           `json:"imageList"`       // 图片内容
	CreateTime      int64              `json:"createTime"`      // 评论时间，秒级时间戳
}

// MomentCommentDeleteRequest 删除评论
type MomentCommentDeleteRequest struct {
	CId uint `json:"cid"` // 评论id
}

type MomentCommentDeleteResponse struct{}

// MomentCommentListRequest 评论列表
type MomentCommentListRequest struct {
	MId    uint `form:"mid"`    // 动态id
	CId    uint `form:"cid"`    // 需要置顶返回的评论或回复
	Cursor int  `form:"cursor"` // 游标
	Count  int  `form:"count"`  //数量
}

type MomentCommentListResponse struct {
	Comments []MomentComment `json:"comments"` // 评论列表
	Cursor   int             `json:"cursor"`   // 游标
}

// MomentCommentReplyRequest 回复列表请求参数
type MomentCommentReplyRequest struct {
	MId        uint `form:"mid"`        // 动态id
	ParentId   uint `form:"parentId"`   // 评论id
	WithoutCid uint `form:"withoutCid"` // 排除评论id
	Cursor     int  `form:"cursor"`     // 游标
	Count      int  `form:"count"`      // 数量
}

type MomentCommentReplyResponse struct {
	Comments []MomentComment `json:"comments"` // 评论列表
	Cursor   int             `json:"cursor"`   // 游标
}

type MomentCommentLikeRequest struct {
	CId uint `form:"cid"` // 评论id/回复id
}

type MomentCommentLikeResponse struct {
}

type MomentCommentUnlikeRequest struct {
	CId uint `form:"cid"` // 评论id/回复id
}

type MomentCommentUnlikeResponse struct {
}

type MomentUploadSignRequest struct {
}

type MomentUserOwnRequest struct {
	UserId string `form:"userId"` // 用户id
	Cursor int    `form:"cursor"` // 上次请求时接口返回的cursor
	Count  int    `form:"count"`  // 请求数量
}

type MomentMeta struct {
	MId           uint                    `json:"mid"`           // 动态id
	MomentType    int                     `json:"momentType"`    // 动态类型 1.纯文字 2.图文 3.视频
	Author        User                    `json:"author"`        // 作者信息
	Title         string                  `json:"title"`         // 标题
	Desc          string                  `json:"desc"`          // 描述
	DescExtra     []moment.DescExtra      `json:"descExtra"`     // 描述扩展
	CharacterInfo *moment.MomentCharacter `json:"characterInfo"` // 文本信息，适用于MomentType为1，否则为null
	PictureInfo   *moment.MomentPicture   `json:"pictureInfo"`   // 图片信息，适用于MomentType为2，否则为null
	VideoInfo     *moment.MomentVideo     `json:"videoInfo"`     // 视频信息，适用于MomentType为3，否则为null
	CreateTime    int64                   `json:"createTime"`
}

type MomentSuccessRequest struct {
	MId uint `json:"mid"` // 动态id
}

type MomentSuccessResponse struct{}

// MomentLikeListRequest 点赞列表
type MomentLikeListRequest struct {
	MId    uint `form:"mid"`    // 动态id
	Cursor int  `form:"cursor"` // 游标
	Count  int  `form:"count"`  //数量
}

type UserWithRelation struct {
	User
	FollowState
}

type MomentLikeListResponse struct {
	Users  []UserWithRelation `json:"users"`  // 评论列表
	Cursor int                `json:"cursor"` // 游标
}
