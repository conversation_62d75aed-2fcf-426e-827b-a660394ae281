package types

type Asset struct {
	Id       int    `json:"id"`       // ID
	Name     string `json:"name"`     // 名称
	Format   string `json:"format"`   // 格式 mp4 / svga
	Url      string `json:"url"`      // 资源地址
	MD5      string `json:"md5"`      // 资源MD5
	Duration int64  `json:"duration"` // 资源时长 单位秒
	Priority int    `json:"priority"` // 资源下载优先级
}

type AssetEffectsResponse struct {
	List []Asset `json:"list"` // 资源列表
}
