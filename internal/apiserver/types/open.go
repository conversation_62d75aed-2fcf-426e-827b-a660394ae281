package types

type OpenSessionRequest struct {
	AppId string `json:"appId" binding:"required"`
	Token string `json:"token" binding:"required"`
}

type OpenSessionResponse struct {
	OpenId   string `json:"openId"`
	Nickname string `json:"nickname"`
}

type OpenDiamondBalanceRequest struct {
	AppId  string `json:"appId" binding:"required"`
	OpenId string `json:"openId" binding:"required"`
}

type OpenDiamondBalanceResponse struct {
	Diamond int64 `json:"diamond"`
}

type OpenDiamondUpdateRequest struct {
	AppId   string `json:"appId" binding:"required"`
	OpenId  string `json:"openId" binding:"required"`
	Diamond int64  `json:"diamond" binding:"required,gt=0"`
	Operate string `json:"operate" binding:"required,oneof=add subtract"`
	OrderId string `json:"orderId" binding:"required,lte=32"`
}

type OpenDiamondUpdateResponse struct {
	Status  int   `json:"status"`  // 状态2成功 0失败
	Diamond int64 `json:"diamond"` // 余额
}

type OpenOrderQueryRequest struct {
	AppId   string `json:"appId" binding:"required"`
	OrderId string `json:"orderId" binding:"required"`
}

type OpenOrderQueryResponse struct {
	Status int `json:"status"` // 2成功 0失败
}
