package types

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

type GiftPage struct {
	PageId   uint   `json:"pageId"`   // pageId
	PageName string `json:"pageName"` // page名称
	Key      string `json:"key"`      // page标识
	Display  bool   `json:"display"`  // 是否显示
	Gifts    []Gift `json:"gifts"`    // 礼物道具列表
}

type Gift struct {
	Id            uint         `json:"id"`            // 道具id
	Name          string       `json:"name"`          // 道具名称
	Diamond       int          `json:"diamond"`       // 价值钻石
	Display       bool         `json:"display"`       // 是否在面板显示
	Combo         bool         `json:"combo"`         // 是否可以combo计数
	Group         bool         `json:"group"`         // 是否可以分组送礼
	QuickSend     bool         `json:"quickSend"`     // 是否可以快速送礼
	ForFansclub   bool         `json:"forFansclub"`   // 是否粉丝团礼物
	HonorLevel    uint         `json:"honorLevel"`    // 用户解锁等级
	FansclubLevel uint         `json:"fansclubLevel"` // 粉丝团解锁等级
	EffectId      uint         `json:"effectId"`      // 特效资源ID
	ImageUrl      string       `json:"imageUrl"`      // 道具图片资源地址
	GifUrl        string       `json:"gifUrl"`        // 道具动图资源地址
	LabelUrl      string       `json:"labelUrl"`      // 道具角标资源地址
	Describe      string       `json:"describe"`      // 送出描述，e.g. 送出粉丝团灯牌
	GroupInfo     []gift.Group `json:"groupInfo"`     // 礼物数量和对应文案
}

type LiteGift struct {
	Id       uint   `json:"id"`       // 道具id
	Name     string `json:"name"`     // 道具名称
	Diamond  int    `json:"diamond"`  // 价值钻石
	ImageUrl string `json:"imageUrl"` // 道具图片资源地址
}

type GiftListRequest struct {
	UserId string `json:"userId" form:"userId"` // 主播userId
}

type GiftListResponse struct {
	Pages []GiftPage `json:"pages"`
}

type GiftSendRequest struct {
	RoomId    string   `json:"roomId" binding:"required"`
	GiftId    int      `json:"giftId" binding:"required,gt=0"`
	GiftCount int      `json:"giftCount" binding:"required,gte=1,lte=9999"`
	ComboId   string   `json:"comboId" binding:"required"`
	Receivers []string `json:"receivers"` // 收礼的对象，必须有数据，不能为空
}

type LuckDraw struct {
	GiftId    int                      `json:"giftId"`
	GiftCount int                      `json:"giftCount"`
	Prizes    []protocol.LuckDrawPrize `json:"prizes"`
}

type LuckyGiftRoom struct {
}

type GiftSendResponse struct {
	Wallet    *UserWallet `json:"wallet,omitempty"`    // 钱包信息
	LevelInfo *LevelInfo  `json:"levelInfo,omitempty"` // 等级信息
	LuckDraw  *LuckDraw   `json:"luckDraw,omitempty"`  // 中奖信息
}
