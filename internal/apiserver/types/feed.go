package types

// feed-推荐
type FeedListRequest struct {
	Page int `form:"page"` // 1开始
}

type FeedListResponse struct {
	List []Room `json:"list"`
}

// feed-关注
type FeedFollowRequest struct {
	Cursor int64 `form:"cursor"` // 请求第一页数据时不传（或传默认值0），下一页数据传上一页返回的cursor
}

type FeedFollowResponse struct {
	List   []Room `json:"list"`
	Cursor int64  `json:"cursor"` // 下一页数据cursor
}

// feed-关注直播间
type FeedFollowLiveRequest struct {
	Cursor int64 `form:"cursor"` // 请求第一页数据时不传（或传默认值0），下一页数据传上一页返回的cursor
}

type FeedFollowLiveResponse struct {
	List   []Room `json:"list"`
	Cursor int64  `json:"cursor"` // 下一页数据cursor
}

// feed-热门tab
type FeedHotRequest struct{}

type FeedHotResponse struct {
	Banner []Banner         `json:"banner"` // 轮播图
	Rec    []Room           `json:"rec"`    // 推荐位直播间 固定4个
	Latest []Room           `json:"latest"` // 热门新人直播间 固定10个
	Rocket []FeedRocketItem `json:"rocket"` // 火箭位 固定4个
}

type Popup struct {
	Id       string `json:"id"`       // id
	ImageUrl string `json:"imageUrl"` // 图片地址
	LinkUrl  string `json:"linkUrl"`  // 跳转地址
	Interval int64  `json:"interval"` // 自然日，1天 7天等等
}

// feed-热门直播
type FeedPopularRequest struct {
	Page int    `form:"page"` // 1开始
	Eid  string `form:"eid"`  // 不返回的直播间ID 英文逗号分隔
}

type FeedPopularResponse struct {
	List []Room `json:"list"`
}

// 火箭位
type FeedRocketRequest struct{}

type FeedRocketItem struct {
	Room       Room         `json:"room"`
	SenderUser *UserWithExt `json:"senderUser"` // 送出火箭用户的信息
}

type FeedRocketResponse struct {
	List []FeedRocketItem `json:"list"` // 火箭位 固定4个
}
