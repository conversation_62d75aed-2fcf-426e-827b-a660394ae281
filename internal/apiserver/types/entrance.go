package types

type EntranceShortenRequest struct {
	LongUrl string `json:"longUrl" binding:"required,max=1024"` // 长链接
}

type EntranceShortenResponse struct {
	ShortUrl string `json:"shortUrl"`
}

type EntranceBannerRequest struct {
	RoomId string `form:"roomId"`
	Scene  string `form:"scene"` // live_room:直播间右上角 live_beginning:直播开始前 live_end:直播结束后
}

type EntranceBannerResponse struct {
	List []Banner `json:"list"`
}

type Banner struct {
	Title    string `json:"title"`    // 标题
	ImageUrl string `json:"imageUrl"` // 图片地址
	LinkUrl  string `json:"linkUrl"`  // 跳转地址
}

type EntranceShareRequest struct {
	Id   string `form:"id"`   // 直播间id或动态id
	Type string `form:"type"` // live:直播间 moment:动态
}

type EntranceShareResponse struct {
	Title string `json:"title"` // 标题
	Url   string `json:"url"`   // 分享地址
	Thumb string `json:"thumb"` // 缩略图
}

type EntranceShareParseRequest struct {
	Text string `form:"text"` // 分享文本
}

type EntranceShareParseResponse struct {
	Url  string `json:"url"`  // 原始分享地址
	Id   string `json:"id"`   // 直播间id或动态id
	Type string `json:"type"` // live:直播间 moment:动态
}
