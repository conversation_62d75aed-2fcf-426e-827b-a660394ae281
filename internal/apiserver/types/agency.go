package types

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
)

// Agency 公会
type Agency struct {
	NumId      int64     `json:"numId,omitempty"`
	Name       string    `json:"name,omitempty"`       // 公会名称
	ImageUrl   string    `json:"imageUrl,omitempty"`   // 公会图片
	WhatsappId string    `json:"whatsappId,omitempty"` // WhatsappId
	MemberNum  int       `json:"memberNum,omitempty"`  // 成员数量
	Platform   string    `json:"platform,omitempty"`   // 合作平台
	Material   []string  `json:"material,omitempty"`   // 上传资料，最多三张
	ShowId     string    `json:"showId,omitempty"`     // 公会ID 6位数字
	Ratio      float64   `json:"ratio,omitempty"`      // 公会分成比例，指占主播收入的比例
	Phone      string    `json:"phone,omitempty"`      // 发送验证码电话
	InviteCode string    `json:"inviteCode,omitempty"` // 邀请码
	ChiefId    string    `json:"chiefId,omitempty"`    // 公会长ID
	ChiefInfo  *User     `json:"chiefInfo,omitempty"`  // 公会长信息
	CreatedAt  time.Time `json:"createdAt,omitempty"`  // 创建时间
	UpdatedAt  time.Time `json:"updatedAt,omitempty"`
}

// AgencyApply 公会申请信息
type AgencyApply struct {
	Id         uint   `json:"id,omitempty"` // 申请ID
	Name       string `json:"name"`         // 公会名称
	ImageUrl   string `json:"imageUrl"`     // 公会图片
	WhatsappId string `json:"whatsappId"`   // WhatsappId
	MemberNum  int    `json:"memberNum"`    // 成员数量
	Platform   string `json:"platform"`     // 合作平台
	Status     int    `json:"status"`       // 审核状态 -1:拒绝 0:待审核 1:通过
	Phone      string `json:"phone"`        // 发送验证码电话
}

type AgencyCreateInfoRequest struct {
}

type AgencyCreateInfoResponse struct {
	AgencyApply *AgencyApply `json:"agencyApply,omitempty"` // 已创建公会信息，omitempty
	Status      int          `json:"status"`                // 创建公会状态，0未创建、1待审核(待审核时会有agencyapply数据)
}

type AgencyCreateRequest struct {
	Name       string   `json:"name" binding:"required"`       // 公会名称
	ImageUrl   string   `json:"imageUrl" binding:"required"`   // 公会图片
	WhatsappId string   `json:"whatsappId" binding:"required"` // WhatsappId
	MemberNum  int      `json:"memberNum"`                     // 成员数量
	Platform   string   `json:"platform"`                      // 合作平台
	Material   []string `json:"material"`                      // 资料信息
	InviteCode string   `json:"inviteCode"`                    // 邀请码
	Phone      string   `json:"phone"`                         // 发送验证码电话
	Code       string   `json:"code"`                          // 需要验证码
}

type AgencyCreateResponse struct {
}

type AgencyGetRequest struct {
	ShowId string `form:"showId"` // 公会showId
}

type AgencyGetResponse struct {
	Agency
}

type AgencyJoinApplyRequest struct {
	NumId int64 `json:"numId"` // 公会numId
}

type AgencyJoinApplyResponse struct {
}

type AgencyJoinCancelRequest struct {
}

type AgencyJoinCancelResponse struct {
}

type AgencyQuitApplyRequest struct {
	NumId int64 `json:"numId"` // 公会numId
}

type AgencyQuitApplyResponse struct {
}

type AgencyQuitCancelRequest struct {
}

type AgencyQuitCancelResponse struct {
}

type AgencyMyRequest struct{}

type AgencyMyResponse struct {
	Agency *Agency `json:"agency,omitempty"` // 公会信息，omitempty
	Status int     `json:"status"`           // 0未加入 1已申请加入公会 2已加入公会 3已申请退出公会 4已申请创建公会
}

type AgencyVerifyListRequest struct {
	NumId int64 `form:"numId"` // 公会numId
}

type AgencyVerifyListResponse struct {
	JoinList []*UserWithExt `json:"joinList"` // 入会申请列表
	QuitList []*UserWithExt `json:"quitList"` // 退出申请列表
}

type AgencyMemberVerifyRequest struct {
	NumId        int64               `json:"numId"`        // 公会numId
	UserId       string              `json:"userId"`       // 被审核人的userid
	VerifyResult agency.VerifyStatus `json:"verifyResult"` // 1审核通过，-1审核拒绝。其他值无效，会报错
}

type AgencyMemberVerifyResponse struct {
}

type AgencyMemberListRequest struct {
	NumId  int64  `form:"numId"`  // 公会numId
	Sort   string `form:"sort"`   // time,score
	Cursor int    `form:"cursor"` // 游标
	Count  int    `form:"count"`  //数量
}

type AgencyMemberListResponse struct {
	List   []*AgencyMember `json:"list"`
	Cursor int             `json:"cursor"` // 游标
	Total  int             `json:"total"`
}

type AgencyMember struct {
	*UserWithExt
	IsNewAnchor bool  `json:"isNewAnchor"`
	JoinTime    int64 `json:"joinTime"`
}

type InviteCodeInfoRequest struct {
	InviteCode string `form:"inviteCode"` // 邀请码
}

type InviteCodeInfoResponse struct {
	Agency
}
