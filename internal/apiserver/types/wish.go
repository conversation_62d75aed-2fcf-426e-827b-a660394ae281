package types

type Wish struct {
	WishName     string `json:"wishName"`     // 心愿名称
	WishStatus   int    `json:"wishStatus"`   // 心愿状态
	CurrentCount int    `json:"currentCount"` // 心愿当前值
	TargetCount  int    `json:"targetCount"`  // 心愿目标值
	GiftId       uint   `json:"giftId"`       // 礼物ID
	GiftIcon     string `json:"giftIcon"`     // 礼物图标
	GiftDiamond  int64  `json:"giftDiamond"`  // 礼物钻石价值
}

type WishListRequest struct {
	UserId string `form:"userId"` // 主播ID，获取最近一次直播的心愿列表
	RoomId string `form:"roomId"` // 直播ID，根据直播ID获取心愿列表
}

type WishListResponse struct {
	List []Wish `json:"list"` // 心愿列表
}
