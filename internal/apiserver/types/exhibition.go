package types

type ExhibitionHomeRequest struct {
	UserId string `form:"userId"` // 用户ID
}

type ExhibitionTask struct {
	StageId   int  `json:"stageId"`   // 任务ID
	Finished  bool `json:"finished"`  // 是否完成
	Threshold int  `json:"threshold"` // 完成需要数量
	Received  int  `json:"received"`  // 当前已经收到数量
	Rewards   int  `json:"rewards"`   // 完成奖励星数
	Ceiling   int  `json:"ceiling"`   // 最多奖励星数
}

type ExhibitionItem struct {
	Gift      *LiteGift       `json:"gift"`      // 关联礼物
	Task      *ExhibitionTask `json:"task"`      // 集星任务
	Lighted   bool            `json:"lighted"`   // 是否点亮
	Threshold int             `json:"threshold"` // 点亮需要数量
	Received  int             `json:"received"`  // 总收到的数量
}

type ExhibitionHomeResponse struct {
	Nickname  string            `json:"nickname"`  // 用户昵称
	Lighted   []*ExhibitionItem `json:"lighted"`   // 点亮的礼物列表
	Unlighted []*ExhibitionItem `json:"unlighted"` // 未点亮的礼物列表
}

type ExhibitionDetailRequest struct {
	UserId string `form:"userId"` // 用户ID
	GiftId int    `form:"giftId"` // 礼物ID
}

type ExhibitionRank struct {
	Rank int   `json:"rank"` // 排名
	Send int   `json:"send"` // 送出数
	User *User `json:"user"` // 用户信息
}

type ExhibitionDetailResponse struct {
	Item *ExhibitionItem   `json:"item"` // 展馆礼物
	Top  []*ExhibitionRank `json:"top"`  // 排名信息
	Me   *ExhibitionRank   `json:"me"`   // 我的排名
}

type ExhibitionMedalRequest struct {
	UserId string `form:"userId"` // 用户ID
}

type ExhibitionBrand struct {
	Name  string `json:"name"`  // 铭牌名称
	Desc  string `json:"desc"`  // 描述信息
	Image string `json:"image"` // 图片地址
	Own   bool   `json:"own"`   // 是否拥有
}

type ExhibitionStage struct {
	Obtains   int `json:"obtains"`   // 获得数量
	Threshold int `json:"threshold"` // 目标数量
}

type ExhibitionMedalResponse struct {
	Achievement []*ExhibitionBrand `json:"achievement"` // 集星铭牌
	StarInfo    *ExhibitionStage   `json:"starInfo"`    // 当前目标
}
