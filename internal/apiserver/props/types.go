package props

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
)

func makeItem(pm *props.Manager, raw *props.Item) *propItem {
	prop, _ := pm.TakeProp(context.TODO(), raw.PropId)
	return &propItem{
		Id:        prop.Key,
		Name:      prop.Name,
		Diamond:   prop.Diamond,
		IconUrl:   prop.IconUrl,
		propStock: makeStock(raw),
	}
}

type propItem struct {
	Id      string `json:"id"`      // 道具Id
	Name    string `json:"name"`    // 显示名称
	Diamond int    `json:"diamond"` // 钻石价值
	IconUrl string `json:"iconUrl"` // 图标资源地址
	propStock
}

func makeStock(raw *props.Item) propStock {
	return propStock{
		Count:    raw.Stock,
		ExpireAt: raw.ExpireAt,
	}
}

type propStock struct {
	Count    int   `json:"count"`    // 库存数量
	ExpireAt int64 `json:"expireAt"` // 过期时间
}

func makeMixed(gm *gift.Manager, pm *props.Manager, raw *props.Item) *propMixed {
	mixed := &propMixed{
		propItem: *makeItem(pm, raw),
	}
	if raw.Class == props.CGift {
		gft, err := gm.GiftById(propc.PGiftId(raw.PropId))
		if err != nil {
			return nil
		}
		mixed.Gift = &giftAttr{
			Id:        gft.ID,
			Combo:     gft.Combo,
			Group:     gft.Group,
			GroupInfo: gft.GroupInfo,
		}
	}
	return mixed
}

type giftAttr struct {
	Id        uint         `json:"id"`        // 道具id
	Combo     bool         `json:"combo"`     // 是否可以combo计数
	Group     bool         `json:"group"`     // 是否可以分组送礼
	GroupInfo []gift.Group `json:"groupInfo"` // 礼物数量和对应文案
}

type propMixed struct {
	propItem
	Gift *giftAttr `json:"gift,omitempty"` // 礼物道具才有值
}

type popup struct {
	list []any
}
