package props

import (
	"context"
	"errors"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
)

type listGiftResp struct {
	List []*propMixed `json:"list"`
}

// @Tags 道具
// @Summary 礼物背包
// @Description 礼物背包
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=listGiftResp}
// @Router /api/v1/props/gift/list [get]
func (s *apis) giftList(ctx *api.Context, _ api.EmptyReq) (*listGiftResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	items, err := s.pm.ListItems(ctx, uac.UserId, props.CGift)
	if err != nil {
		return nil, err
	}

	return &listGiftResp{
		List: lo.Map(items.Stack(), func(item *props.Item, _ int) *propMixed {
			return makeMixed(s.gm, s.pm, item)
		}),
	}, nil
}

type sendGiftReq struct {
	RoomId  string `json:"roomId" binding:"required"`               // 房间Id
	PropId  string `json:"propId" binding:"required"`               // 道具Id
	Count   int    `json:"count" binding:"required,gte=1,lte=9999"` // 使用数量
	ComboId string `json:"comboId" binding:"required"`              // 连击Id
}

type sendGiftResp struct {
	PropStock propStock        `json:"propStock"`           // 道具信息
	LevelInfo *types.LevelInfo `json:"levelInfo,omitempty"` // 等级信息
	LuckDraw  *types.LuckDraw  `json:"luckDraw,omitempty"`  // 中奖信息
}

// @Tags 道具
// @Summary 背包送礼
// @Description 背包送礼
// @Produce json
// @Security HeaderAuth
// @Param param body sendGiftReq true "请求参数"
// @Success 200 {object} codec.Response{data=sendGiftResp}
// @Router /api/v1/props/gift/send [post]
func (s *apis) giftSend(ctx *api.Context, req sendGiftReq) (*sendGiftResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	prop, err := s.pm.TakeProp(ctx, req.PropId)
	if err != nil {
		return nil, err
	} else if prop.Class != props.CGift {
		return nil, errors.New("invalid prop class")
	}

	gft, err := s.gm.GiftById(propc.PGiftId(prop.Key))
	if err != nil {
		return nil, err
	}

	sr, err := s.im.SendGiftProps(context.TODO(), rlog.RequestId(ctx), req.RoomId, uac.UserId, req.ComboId, propc.PGiftId(prop.Key), req.Count, time.Now())
	if err != nil {
		return nil, err
	}

	var resp sendGiftResp

	resp.PropStock.Count = sr.PropStock.Remains
	resp.PropStock.ExpireAt = sr.PropStock.ExpireAt

	if sr.Level != nil {
		resp.LevelInfo = mixer.LevelInfo(sr.Level)
	}

	if !sr.Pows.Empty() {
		resp.LuckDraw = &types.LuckDraw{
			GiftId:    propc.PGiftId(prop.Key),
			GiftCount: req.Count,
			Prizes:    sr.Pows.Protocol(gft.Diamond),
		}
	}

	return &resp, nil
}
