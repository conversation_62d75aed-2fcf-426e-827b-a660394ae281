package props

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
)

func Invoke(r *api.Router, hm *mux.Locker, pm *props.Manager, gm *gift.Manager, lm *live.Manager, im *interact.Manager) {
	s := &apis{pm: pm, gm: gm, lm: lm, im: im}
	ar := r.WithAuth(hm.Middleware(mux.WithPOST)).Group("/props")
	{
		g1 := ar.Group("/gift")
		{
			g1.GET("/list", api.Generic(s.giftList))
			g1.POST("/send", api.Generic(s.giftSend))
		}
	}
}

type apis struct {
	pm *props.Manager
	gm *gift.Manager
	lm *live.Manager
	im *interact.Manager
}
