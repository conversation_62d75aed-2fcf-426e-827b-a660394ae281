package location

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/geoip"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/region"
)

func API(r *api.Router, rq *region.Query) {
	s := &apis{rq: rq}
	ar := r.<PERSON>()
	{
		ar.GET("/location/list", api.Generic(s.list))
		ar.GET("/location/sugg", api.Generic(s.sugg))
	}
}

type apis struct {
	rq *region.Query
}

type listResp struct {
	List []*region.Region `json:"list"`
}

// @Tags 区域位置
// @Summary 获取区域列表
// @Description 获取区域列表信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=listResp}
// @Router /api/v1/location/list [get]
func (s *apis) list(ctx *api.Context, _ api.EmptyReq) (*listResp, error) {
	return &listResp{List: s.rq.Dump(ctx)}, nil
}

type suggResp struct {
	Code string `json:"code"` // 地区编码：猜不到时为空
	Name string `json:"name"` // 显示名称：猜不到时为空
}

// @Tags 区域位置
// @Summary 获取当前位置
// @Description 根据客户端IP猜测用户位置
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=suggResp}
// @Router /api/v1/location/sugg [get]
func (s *apis) sugg(ctx *api.Context, _ api.EmptyReq) (*suggResp, error) {
	var resp suggResp

	client := geoip.Client(ctx)
	code := s.rq.Query(ctx, client.CityName)
	if code != "" {
		name := s.rq.Take(ctx, code)
		if name != "" {
			resp.Code = code
			resp.Name = name
		}
	}

	return &resp, nil
}
