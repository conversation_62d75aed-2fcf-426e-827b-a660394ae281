package seller

import (
	"errors"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Invoke2(r *api.Router, db *db.Client, fm *fund.Manager, sm *seller.Manager) {
	s := &apis2{db: db, fm: fm, sm: sm}
	ar := r.WithAuth()
	{
		ar.GET("/seller/exchange", api.Generic(s.exchangeInfo))
		ar.POST("/seller/exchange", api.Generic(s.doExchange))
	}
}

type apis2 struct {
	db *db.Client
	fm *fund.Manager
	sm *seller.Manager
}

var gears = map[int]int{
	1000000:  1100000,
	5000000:  5500000,
	10000000: 11100000,
}

type exchangeItem struct {
	Fruit  int    `json:"fruit"`          // 价格：水晶
	Coins  int    `json:"coins"`          // 获得代币数
	Extras int    `json:"extras"`         // 额外获赠比例
	Tips   string `json:"tips,omitempty"` // 角标提示文案
}

type exchangeInfo struct {
	List []exchangeItem `json:"list"`
}

// @Tags 币商
// @Summary 兑换SKU
// @Description 兑换SKU
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=exchangeInfo}
// @Router /api/v1/seller/exchange [get]
func (s *apis2) exchangeInfo(ctx *api.Context, _ api.EmptyReq) (*exchangeInfo, error) {
	amounts := lo.Keys(gears)
	slices.Sort(amounts)
	return &exchangeInfo{
		List: lo.Map(amounts, func(fruit int, _ int) exchangeItem {
			i := exchangeItem{Fruit: fruit, Coins: gears[fruit]}
			i.Extras = int(float64(i.Coins-i.Fruit) / float64(i.Fruit) * 100)
			i.Tips = "Tempo limitado"
			return i
		}),
	}, nil
}

type exchangeReq struct {
	Fruit int `json:"fruit"`
}

type exchangeResp struct {
	types.AnchorWallet
}

// @Tags 币商
// @Summary 兑换金币
// @Description 兑换金币
// @Produce json
// @Security HeaderAuth
// @Param param body exchangeReq true "请求参数"
// @Success 200 {object} codec.Response{data=exchangeResp}
// @Router /api/v1/seller/exchange [post]
func (s *apis2) doExchange(ctx *api.Context, req exchangeReq) (*exchangeResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if !seller.Has(uac.Roles) {
		return nil, errors.New("not a seller")
	}

	coins := gears[req.Fruit]
	if coins == 0 {
		return nil, errors.New("invalid amount")
	}

	if err := s.sm.Exchange(ctx, time.Now(), uac.UserId, req.Fruit, coins); err != nil {
		return nil, err
	}

	return &exchangeResp{
		AnchorWallet: *mixer.NoErr(mixer.AnchorWallet(ctx, uac.UserId)),
	}, nil
}
