package seller

import (
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/jstream"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/goods/bscharge"
	"gitlab.sskjz.com/overseas/live/osl/internal/goods/recharge"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
)

func Invoke(r *api.Router, ug user.Getter, js *jstream.API, jm *journal.Manager, sm *seller.Manager) {
	s := &apis{ug: ug, js: js, jm: jm, sm: sm}
	ar := r.WithAuth()
	{
		ar.GET("/seller/info", api.Generic(s.info))
		ar.POST("/seller/transfer", api.Generic(s.transfer))
		ar.POST("/seller/update/pwd", api.Generic(s.updatePwd))
		ar.POST("/seller/update/conf", api.Generic(s.updateConf))
		ar.GET("/seller/trade/history", api.Generic(s.tradeHistory))
		ar.GET("/seller/trade/detail", api.Generic(s.tradeDetail))
		ar.GET("/seller/trade/users", api.Generic(s.tradeUsers))
		ar.GET("/seller/recharge", api.Generic(s.recharge))
		{
			ar.GET("/seller/assistant/list", api.Generic(s.listAssistant))
			ar.POST("/seller/assistant/add", api.Generic(s.addAssistant))
			ar.POST("/seller/assistant/set", api.Generic(s.setAssistant))
			ar.POST("/seller/assistant/del", api.Generic(s.delAssistant))
		}
	}
}

type apis struct {
	ug user.Getter
	js *jstream.API
	jm *journal.Manager
	sm *seller.Manager
}

type infoResp struct {
	Assistant    bool  `json:"assistant"`    // 助理身份
	Balance      int64 `json:"balance"`      // 账户余额
	Passwordless bool  `json:"passwordless"` // 无需密码
	InputAmounts []int `json:"inputAmounts"` // 常用金额
}

// @Tags 币商
// @Summary 获取信息
// @Description 获取币商信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/seller/info [get]
func (s *apis) info(ctx *api.Context, _ api.EmptyReq) (*infoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	p := s.sm.Take(ctx, uac.UserId)
	if !p.Valid() {
		return nil, errors.New("invalid user")
	}
	balance, err := s.sm.Balance(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	if len(p.Config.InputAmounts) == 0 {
		p.Config.InputAmounts = []int{5000, 10000, 20000, 50000}
	}
	return &infoResp{
		Assistant:    !p.Master(),
		Balance:      balance.IntPart(),
		Passwordless: p.Config.Passwordless,
		InputAmounts: p.Config.InputAmounts,
	}, nil
}

type transferReq struct {
	UserId   string `json:"userId"`
	Amount   int64  `json:"amount"`
	Password string `json:"password"` // 交易密码
}

type transferResp struct {
	From   *types.UserWithExt `json:"from"`   // 付款方
	To     *types.UserWithExt `json:"to"`     // 收款方
	Amount int64              `json:"amount"` // 交易金额
	Time   int64              `json:"time"`   // 交易时间
}

// @Tags 币商
// @Summary 对用户转账
// @Description 对用户转账
// @Produce json
// @Security HeaderAuth
// @Param param body transferReq true "请求参数"
// @Success 200 {object} codec.Response{data=transferResp}
// @Router /api/v1/seller/transfer [post]
func (s *apis) transfer(ctx *api.Context, req transferReq) (*transferResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if !seller.Has(uac.Roles) {
		return nil, errors.New("not a seller")
	}

	at := time.Now()
	tr, err := s.sm.Trade(ctx, at, uac.UserId, req.UserId, int(req.Amount), req.Password)
	if err != nil {
		return nil, err
	}

	return &transferResp{
		From:   mixer.UserWithExt(ctx, mixer.NoErr(s.ug.Account(ctx, tr.AccId))),
		To:     mixer.UserWithExt(ctx, mixer.NoErr(s.ug.Account(ctx, req.UserId))),
		Amount: req.Amount,
		Time:   at.Unix(),
	}, nil
}

type updatePwdReq struct {
	Old string `json:"old"` // 旧密码
	New string `json:"new"` // 新密码
}

// @Tags 币商
// @Summary 修改交易密码
// @Description 修改交易密码
// @Produce json
// @Security HeaderAuth
// @Param param body updatePwdReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/seller/update/pwd [post]
func (s *apis) updatePwd(ctx *api.Context, req updatePwdReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	if err := s.sm.SetPassword(ctx, uac.UserId, req.Old, req.New); err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, nil
}

type updateConfigReq struct {
	Passwordless *bool  `json:"passwordless"` // 无需密码，不改不要传
	InputAmounts *[]int `json:"inputAmounts"` // 常用金额，不改不要传
}

// @Tags 币商
// @Summary 修改配置信息
// @Description 修改配置信息
// @Produce json
// @Security HeaderAuth
// @Param param body updateConfigReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/seller/update/conf [post]
func (s *apis) updateConf(ctx *api.Context, req updateConfigReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	if req.InputAmounts != nil && len(*req.InputAmounts) > 8 {
		return nil, biz.Legacy("too many input amounts")
	}
	if err := s.sm.SetConfig(ctx, uac.UserId, func(c *seller.Config) {
		if req.Passwordless != nil {
			c.Passwordless = *req.Passwordless
		}
		if req.InputAmounts != nil {
			c.InputAmounts = *req.InputAmounts
		}
	}); err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, nil
}

type tradeHistoryReq struct {
	EndAt  int64  `form:"endAt"`  // 截止时间：unix秒
	Cursor string `form:"cursor"` // 分页游标
}

// @Tags 币商
// @Summary 交易记录
// @Description 交易记录
// @Produce json
// @Security HeaderAuth
// @Param param query tradeHistoryReq true "请求参数"
// @Success 200 {object} codec.Response{data=jstream.Response}
// @Router /api/v1/seller/trade/history [get]
func (s *apis) tradeHistory(ctx *api.Context, req tradeHistoryReq) (*jstream.Response, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var opts []journal.ReadOpt

	p := s.sm.Take(ctx, uac.UserId)
	if !p.Master() {
		uac = mixer.NoErr(s.ug.Account(ctx, p.Parent))
		opts = append(opts, journal.WithModifier(func(filter bson.M) {
			filter["extra.operator"] = p.UserId
		}))
	}

	jr, err := s.js.Journal2(ctx, uac, jstream.Request{
		Prop:   fund.PTypeTokens,
		Types:  []fund.JournalType{fund.JTypeRecharge, fund.JTypeExchange},
		EndAt:  req.EndAt,
		Cursor: req.Cursor,
	}, opts...)
	if err != nil {
		return nil, err
	}

	if !p.Master() {
		for _, agg := range jr.Aggs {
			agg.Incomes = -1
			agg.Expends = -1
		}
	}

	for _, j := range jr.List {
		if j.Type == fund.JTypeRecharge && j.Amount < 0 {
			if j.WithUser != nil {
				acc, _ := s.ug.Account(ctx, j.WithUser.UserId)
				j.Remark = i3n.T(ctx, "recharge for %s", fmt.Sprintf("ID:%s", acc.ShowId))
				j.HasDetail = true
			}
		}
	}

	return jr, nil
}

type tradeDetailReq struct {
	Id string `form:"id"` // 交易ID
}

// @Tags 币商
// @Summary 交易详情
// @Description 交易详情
// @Produce json
// @Security HeaderAuth
// @Param param query tradeDetailReq true "请求参数"
// @Success 200 {object} codec.Response{data=transferResp}
// @Router /api/v1/seller/trade/detail [get]
func (s *apis) tradeDetail(ctx *api.Context, req tradeDetailReq) (*transferResp, error) {
	jr, err := s.jm.Detail(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &transferResp{
		From:   mixer.UserWithExt(ctx, mixer.NoErr(s.ug.Account(ctx, jr.UserId))),
		To:     mixer.UserWithExt(ctx, mixer.NoErr(s.ug.Account(ctx, jr.WithUser))),
		Amount: jr.Amount.Abs().IntPart(),
		Time:   jr.CreatedAt.Unix(),
	}, nil
}

type tradeUsersResp struct {
	List []*types.UserWithExt `json:"list"`
}

// @Tags 币商
// @Summary 最近交易用户
// @Description 最近交易用户
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=tradeUsersResp}
// @Router /api/v1/seller/trade/users [get]
func (s *apis) tradeUsers(ctx *api.Context, _ api.EmptyReq) (*tradeUsersResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	peers, err := s.sm.RecentTradePeers(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &tradeUsersResp{List: lo.Map(peers, func(userId string, _ int) *types.UserWithExt {
		return mixer.UserWithExt(ctx, mixer.NoErr(s.ug.Account(ctx, userId)))
	})}, nil
}

type rechargeItem struct {
	SKU    string  `json:"sku"`            // SKU
	Price  float64 `json:"price"`          // 价格：USD
	Coins  int     `json:"coins"`          // 获得代币数
	Extras int     `json:"extras"`         // 额外获赠比例
	Tips   string  `json:"tips,omitempty"` // 角标提示文案
}

type rechargeResp struct {
	List []rechargeItem `json:"list"`
}

// @Tags 币商
// @Summary 充值SKU
// @Description 充值SKU
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=rechargeResp}
// @Router /api/v1/seller/recharge [get]
func (s *apis) recharge(ctx *api.Context, _ api.EmptyReq) (*rechargeResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var resp rechargeResp
	if !s.sm.Take(ctx, uac.UserId).Chargeable() {
		return &resp, nil
	}

	for _, sku := range bscharge.SKUs {
		if sku.Id == "100" && !slices.Contains([]string{"cd3d3c4d59974ca1ab53b779c65c7043", "44d632d3d7fb42ce8efe1ad191b53d43"}, uac.UserId) {
			continue
		}
		raw := int(sku.Price / recharge.Ratio(pay.USD))
		resp.List = append(resp.List, rechargeItem{
			SKU:    sku.Id,
			Price:  sku.Price,
			Coins:  sku.Amount,
			Extras: int(float64(sku.Amount-raw) / float64(raw) * 100),
			Tips: lo.Ternary(sku.Promote, "Tempo limitado",
				lo.Ternary(sku.Test, "Teste", ""),
			),
		})
	}

	return &resp, nil
}

type assistantAcc struct {
	User       *types.UserWithExt `json:"user"`       // 用户信息
	TradeLimit int                `json:"tradeLimit"` // 每日限额
}

type listAssistantResp struct {
	List []*assistantAcc `json:"list"`
}

// @Tags 币商
// @Summary 助理账号列表
// @Description 助理账号列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=listAssistantResp}
// @Router /api/v1/seller/assistant/list [get]
func (s *apis) listAssistant(ctx *api.Context, _ api.EmptyReq) (*listAssistantResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	list, err := s.sm.ListAssistant(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &listAssistantResp{
		List: lo.Map(list, func(p *seller.Profile, _ int) *assistantAcc {
			return &assistantAcc{
				User:       mixer.UserWithExt(ctx, mixer.NoErr(s.ug.Account(ctx, p.UserId))),
				TradeLimit: p.Config.TradeLimit,
			}
		}),
	}, nil
}

type addAssistantReq struct {
	ShowId     string `json:"showId"`
	Password   string `json:"password"`   // 初始密码
	TradeLimit int    `json:"tradeLimit"` // 每日限额
}

// @Tags 币商
// @Summary 添加助理账号
// @Description 添加助理账号
// @Produce json
// @Security HeaderAuth
// @Param param body addAssistantReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/seller/assistant/add [post]
func (s *apis) addAssistant(ctx *api.Context, req addAssistantReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	acc, err := s.ug.GetByShowId(ctx, req.ShowId)
	if err != nil {
		return nil, err
	}

	if err := s.sm.AddAssistant(ctx, uac.UserId, acc.UserId, req.Password, req.TradeLimit); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type setAssistantReq struct {
	UserId     string `json:"userId"`     // 用户ID
	TradeLimit *int   `json:"tradeLimit"` // 每日限额（不改不要传）
}

// @Tags 币商
// @Summary 设置助理账号
// @Description 设置助理账号
// @Produce json
// @Security HeaderAuth
// @Param param body setAssistantReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/seller/assistant/set [post]
func (s *apis) setAssistant(ctx *api.Context, req setAssistantReq) (*api.EmptyResp, error) {
	if err := s.sm.SetConfig(ctx, req.UserId, func(c *seller.Config) {
		if req.TradeLimit != nil {
			c.TradeLimit = *req.TradeLimit
		}
	}); err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, nil
}

type delAssistantReq struct {
	ShowId string `json:"showId"`
}

// @Tags 币商
// @Summary 删除助理账号
// @Description 删除助理账号
// @Produce json
// @Security HeaderAuth
// @Param param body delAssistantReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/seller/assistant/del [post]
func (s *apis) delAssistant(ctx *api.Context, req delAssistantReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	acc, err := s.ug.GetByShowId(ctx, req.ShowId)
	if err != nil {
		return nil, err
	}

	if err := s.sm.DelAssistant(ctx, uac.UserId, acc.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}
