package phone

import (
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

type Scene int

const (
	Register    Scene = iota + 1 // 注册
	Binding                      // 绑定
	ResetPwd                     // 重设密码
	ChangePhone                  // 换绑手机号
)

type smsVerifyReq struct {
	Scene Scene  `json:"scene"` // 场景
	Phone string `json:"phone"`
	recaptchaReq
}

type smsVerifyResp struct {
	WaitSecs int `json:"waitSecs"` // 重发等待时间
}

// @Tags 手机登录
// @Summary 发送验证码
// @Description 发送验证码
// @Produce json
// @Param param body smsVerifyReq true "请求参数"
// @Success 200 {object} codec.Response{data=smsVerifyResp}
// @Router /api/v1/sms/verify [post]
func (s *apis) verify(ctx *api.Context, req smsVerifyReq) (*smsVerifyResp, error) {
	if req.Scene == 0 && app.IsAndroid(ctx) && app.Version(ctx).GreaterThanOrEqual(app.V1110) {
		req.Scene = ResetPwd
	}

	if isFake(ctx, req.Phone) {
		return nil, ErrIsFakePhone
	}

	if s.riskDevice(ctx, req.Scene, req.Phone) {
		return &smsVerifyResp{WaitSecs: 59}, nil
	}

	switch req.Scene {
	case Register:
		if valid, err := s.valid(ctx.Context, app.ChannelId(ctx), req.GToken); err != nil {
			s.log.Warn("recaptcha verify failed", zap.String("token", req.GToken), zap.Error(err))
		} else if !valid {
			return &smsVerifyResp{WaitSecs: 58}, nil
		}
	case Binding:
		if has, err := s.lp.Exists(ctx, req.Phone); err != nil {
			return nil, err
		} else if has {
			return nil, biz.NewError(biz.ErrBindingConflict, "conflict binding of phone")
		}
	case ResetPwd:
		if app.IsAndroid(ctx) && app.Version(ctx).GreaterThanOrEqual(app.V1102) {
			if has, err := s.lp.Exists(ctx, req.Phone); err != nil {
				return nil, err
			} else if !has {
				return nil, biz.NewError(biz.ErrInvalidPhoneNum, "phone not exists")
			}
		}
	case ChangePhone:
	default:
		return nil, errors.New("unknown scene")
	}

	wait, err := s.sv.Send(ctx, req.Phone)
	if err != nil {
		return nil, err
	}

	if wait.Seconds() == 60 {
		// only for success send
		s.riskWatch(ctx, req.Scene, req.Phone)
	}

	return &smsVerifyResp{WaitSecs: int(wait.Seconds())}, nil
}

type phoneVerifyReq struct {
	Scene Scene `json:"scene"` // 场景
}

// @Tags 手机绑定
// @Summary 发送验证码（到当前手机）
// @Description 发送验证码（到当前手机）
// @Produce json
// @Security HeaderAuth
// @Param param body phoneVerifyReq true "请求参数"
// @Success 200 {object} codec.Response{data=smsVerifyResp}
// @Router /api/v1/phone/verify [post]
func (s *apis) verifyMe(ctx *api.Context, req phoneVerifyReq) (*smsVerifyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	phone, err := s.lp.Phone(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	return s.verify(ctx, smsVerifyReq{Scene: req.Scene, Phone: phone})
}
