package phone

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type phoneCheckReq struct {
	Phone string `json:"phone"` // 手机号
}

type phoneCheckResp struct {
	Status int `json:"status"` // 1已注册 2未注册
}

// @Tags 手机登录
// @Summary 检查状态
// @Description 检查手机号收否已注册
// @Produce json
// @Param param body phoneCheckReq true "请求参数"
// @Success 200 {object} codec.Response{data=phoneCheckResp}
// @Router /api/v1/login/phone/check [post]
func (s *apis) check(ctx *api.Context, req phoneCheckReq) (*phoneCheckResp, error) {
	if isFake(ctx, req.Phone) {
		return &phoneCheckResp{Status: 1}, nil
	}
	has, err := s.lp.Exists(ctx, req.Phone)
	if err != nil {
		return nil, err
	}
	return &phoneCheckResp{Status: lo.Ternary(has, 1, 2)}, nil
}

type phoneSubmitReq struct {
	Phone    string `json:"phone"`    // 手机号格式 +8613812345678
	Code     string `json:"code"`     // 设定或重设密码需要验证码
	Password string `json:"password"` // 登录密码或用户设定的新密码
}

// @Tags 手机登录
// @Summary 验证登录
// @Description 设定、重设、验证密码并登录
// @Produce json
// @Param param body phoneSubmitReq true "请求参数"
// @Success 200 {object} codec.Response{data=auth.Payload}
// @Router /api/v1/login/phone/submit [post]
func (s *apis) submit(ctx *api.Context, req phoneSubmitReq) error {
	if isFake(ctx, req.Phone) {
		return fakeLogin(ctx, s.lp, s.jwt, req.Phone, req.Password)
	}

	if req.Code == "" {
		// login
		acc, err := s.lp.Login(ctx, req.Phone, req.Password)
		if err != nil {
			return err
		}
		return s.jwt.AssignLogin(ctx.Context, acc)
	}

	has, err := s.lp.Exists(ctx, req.Phone)
	if err != nil {
		return err
	}

	if err := checkPassword(req.Password); err != nil {
		return err
	}

	if err := s.sv.Check(ctx, req.Phone, req.Code); err != nil {
		return err
	}

	if !has {
		// register
		acc, err := s.lp.Register(ctx, req.Phone, req.Password)
		if err != nil {
			return err
		}
		return s.jwt.AssignLogin(ctx.Context, acc)
	}

	{
		// reset password
		acc, err := s.lp.ResetPwd(ctx, req.Phone, req.Password)
		if err != nil {
			return err
		}
		return s.jwt.AssignLogin(ctx.Context, acc)
	}
}
