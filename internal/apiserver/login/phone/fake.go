package phone

import (
	"context"
	"errors"

	phoneL "gitlab.sskjz.com/overseas/live/osl/internal/login/phone"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sms"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var (
	ErrIsFakePhone = sms.ErrInvalidPhoneNum
)

var fakeLogins = map[string]string{
	// 审核账号
	"+************": "Gp_kako2024",  // google
	"+************": "As_kako2024",  // apple
	"+************": "KakoApp@2024", // apple 巴西
	// 支付测试
	"+************": "Pay_test1",
}

func isFake(ctx context.Context, phone string) bool {
	_, phone, _ = sms.TrimPhone(ctx, phone)
	return fakeLogins[phone] != ""
}

func fakeLogin(ctx *api.Context, lp *phoneL.Manager, jwt *auth.JWT, phone, password string) error {
	acc, err := lp.Login(ctx, phone, password)
	if err != nil {
		if errors.Is(err, user.ErrAccountNotExists) {
			_, phone, _ = sms.TrimPhone(ctx, phone)
			acc, err = lp.Register(ctx, phone, fakeLogins[phone])
			if err != nil {
				return err
			}
			goto login
		}
		return err
	}
login:
	return jwt.AssignLogin(ctx.Context, acc)
}
