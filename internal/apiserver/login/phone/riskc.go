package phone

import (
	"context"
	"fmt"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"go.uber.org/zap"
)

var (
	badDevModels = []string{"Google mainline"}
	badDevTzones = []string{"Etc/UTC"}
)

func (s *apis) riskDevice(ctx *api.Context, scene Scene, phone string) bool {
	var (
		ip = ctx.ClientIP()
		id = app.DeviceId(ctx)
	)

	riskDev := slices.Contains(badDevModels, app.DeviceName(ctx)) ||
		slices.Contains(badDevTzones, app.Timezone(ctx).String()) ||
		s.getSendCount(ctx, ip) >= 8 ||
		s.getSendCount(ctx, id) >= 2

	if riskDev {
		s.log.Info("risk device detected", zap.String("requestId", rlog.RequestId(ctx)),
			zap.String("clientIP", ip), zap.String("clientID", id),
			zap.Int("scene", int(scene)), zap.String("phone", phone),
		)
		return true
	}

	return false
}

func (s *apis) riskWatch(ctx *api.Context, scene Scene, phone string) {
	var (
		ip = ctx.ClientIP()
		id = app.DeviceId(ctx)
	)

	s.incrSendCount(ctx, ip, time.Hour)
	s.incrSendCount(ctx, id, time.Minute)

	s.log.Info("sms verify request", zap.String("requestId", rlog.RequestId(ctx)),
		zap.String("clientIP", ip), zap.String("clientID", id),
		zap.Int("scene", int(scene)), zap.String("phone", phone),
	)
}

const (
	keyRisk = "PHONE:RISK:%s"
)

func (s *apis) getSendCount(ctx *api.Context, id string) int64 {
	v, _ := s.rc.Get(ctx, fmt.Sprintf(keyRisk, id)).Int64()
	return v
}

func (s *apis) incrSendCount(ctx context.Context, id string, ttl time.Duration) {
	key := fmt.Sprintf(keyRisk, id)
	txp := s.rc.Pipeline()
	txp.Incr(ctx, key)
	txp.Expire(ctx, key, ttl)
	if _, err := txp.Exec(ctx); err != nil {
		s.log.Warn("failed to execute pipeline", zap.Error(err))
	}
}
