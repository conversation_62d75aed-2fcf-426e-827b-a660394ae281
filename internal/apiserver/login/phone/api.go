package phone

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	phoneL "gitlab.sskjz.com/overseas/live/osl/internal/login/phone"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlimit"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sms"
	"go.uber.org/zap"
)

func API(
	r *api.Router, rl *rlimit.Handler, rc *redi.Client,
	sv sms.Verify, lp *phoneL.Manager, jwt *auth.JWT, vnd log.Vendor,
) {
	s := &apis{rc: rc, sv: sv, lp: lp, jwt: jwt, log: vnd.Scope("api.phone")}
	{
		s.initCaptcha(vnd.Scope("recaptcha"))
	}
	rr := r.With(rl.Middleware())
	{
		rr.POST("/sms/verify", api.Generic(s.verify))
		rr.POST("/login/phone/check", api.Generic(s.check))
		rr.POST("/login/phone/submit", api.Request(s.submit))
	}
	ar := r.WithAuth()
	{
		ar.POST("/phone/bind", api.Generic(s.doBind))
		ar.POST("/phone/verify", api.Generic(s.verifyMe))
		ar.POST("/phone/change/auth", api.Generic(s.changeAuth))
		ar.POST("/phone/change/bind", api.Generic(s.changeBind))
		ar.POST("/phone/change/pwd", api.Generic(s.changePwd))
	}
}

type apis struct {
	rc  *redi.Client
	sv  sms.Verify
	lp  *phoneL.Manager
	jwt *auth.JWT
	log *zap.Logger
	recaptchaMgr
}
