package phone

import (
	"unicode"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

var (
	ErrInvalidPassword = biz.NewError(biz.ErrInvalidPassword, "password is too simple")
)

const (
	passwordMin = 6
	passwordMax = 20
	passwordMix = 2
)

func checkPassword(raw string) error {
	if len(raw) < passwordMin || len(raw) > passwordMax {
		return ErrInvalidPassword
	}
	has := make(map[string]bool)
	for _, c := range raw {
		if unicode.IsLetter(c) {
			has["L"] = true
		}
		if unicode.IsNumber(c) {
			has["N"] = true
		}
		if unicode.IsPunct(c) {
			has["P"] = true
		}
	}
	if len(has) < passwordMix {
		return ErrInvalidPassword
	}
	return nil
}
