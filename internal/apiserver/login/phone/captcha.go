package phone

import (
	"context"
	"fmt"

	recaptcha "cloud.google.com/go/recaptchaenterprise/v2/apiv1"
	recaptchapb "cloud.google.com/go/recaptchaenterprise/v2/apiv1/recaptchaenterprisepb"
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

var (
	projectId  = "kako-live-app"
	serviceKey = "AIzaSyBMw9LPyM-Xs3sql7Knhcs_c5Q1SUpnJUk"
	captchaKey = map[string]string{
		"google":    "6LfHl9oqAAAAAJj72c5NlpeBA2PeO3A9SCC3Rire",
		"official":  "6LfcydoqAAAAAKu6vslpPauHJZmqwXUDPI2xc4y8",
		"app store": "6Ld5ytoqAAAAAP-wJTmKythzTACVHhnfXZyerLaB",
	}
)

func init() {
	if dbg.Ing() {
		captchaKey["app store"] = "6Lc5etoqAAAAAI9e3RayprtKhc7gMhdwiwxy1JhA"
		captchaKey["official"] = "6LfSutoqAAAAANlcdV-14HWY36EQSqBl1yCI-BR3"
		captchaKey["google"] = captchaKey["official"]
	}
}

type recaptchaReq struct {
	GToken string `json:"recaptchaToken"`
}

type recaptchaMgr struct {
	rCli *recaptcha.Client
	rLog *zap.Logger
}

func (r *recaptchaMgr) initCaptcha(log *zap.Logger) {
	cli, err := recaptcha.NewClient(context.TODO(), option.WithAPIKey(serviceKey))
	if err != nil {
		log.Warn("init failed", zap.Error(err))
		return
	}
	r.rCli = cli
	r.rLog = log
}

func (r *recaptchaMgr) valid(ctx *gin.Context, channel, token string) (bool, error) {
	if token == "" || r.rCli == nil {
		return dbg.Ing(), nil
	}

	evt := &recaptchapb.Event{
		Token:          token,
		SiteKey:        captchaKey[channel],
		UserAgent:      ctx.Request.UserAgent(),
		UserIpAddress:  ctx.ClientIP(),
		ExpectedAction: "signup",
		RequestedUri:   "/api/v1/sms/verify",
	}

	for key := range ctx.Request.Header {
		if key == "User-Agent" || key == "Authorization" || key == "X-Request-Id" {
			continue
		}
		evt.Headers = append(evt.Headers, fmt.Sprintf("%s: %s", key, ctx.GetHeader(key)))
	}

	resp, err := r.rCli.CreateAssessment(ctx, &recaptchapb.CreateAssessmentRequest{
		Parent: fmt.Sprintf("projects/%s", projectId),
		Assessment: &recaptchapb.Assessment{
			Event: evt,
		},
	})
	if err != nil {
		return false, err
	}

	if !resp.TokenProperties.Valid {
		r.rLog.Info("invalid token", zap.String("reason", resp.TokenProperties.InvalidReason.String()), zap.Any("resp", resp))
		return false, nil
	}

	if resp.TokenProperties.Action != resp.Event.ExpectedAction {
		r.rLog.Info("invalid action", zap.String("action", resp.TokenProperties.Action), zap.Any("resp", resp))
		return false, nil
	}

	r.rLog.Info("verify success", zap.String("requestId", rlog.RequestId(ctx)),
		zap.Float32("score", resp.RiskAnalysis.Score*10),
		zap.Any("resp", resp.RiskAnalysis),
	)

	return resp.RiskAnalysis.Score > 0.3, nil
}
