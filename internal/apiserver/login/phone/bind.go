package phone

import (
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/est"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type phoneBindReq struct {
	Phone    string `json:"phone"`    // 新的手机号
	Code     string `json:"code"`     // 需要验证码
	Password string `json:"password"` // 设定新密码
}

// @Tags 手机绑定
// @Summary 绑定手机号
// @Description 绑定手机号
// @Produce json
// @Security HeaderAuth
// @Param param body phoneBindReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/phone/bind [post]
func (s *apis) doBind(ctx *api.Context, req phoneBindReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := checkPassword(req.Password); err != nil {
		return nil, err
	}

	if err := s.sv.Check(ctx, req.Phone, req.Code); err != nil {
		return nil, err
	}

	if err := s.lp.Binding(ctx, uac.UserId, req.Phone, req.Password); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type changeAuthReq struct {
	Code string `json:"code"` // 验证码
}

type changeAuthResp struct {
	State string `json:"state"` // 授权状态
}

// @Tags 手机绑定
// @Summary 换绑手机验证
// @Description 验证当前手机号码
// @Produce json
// @Security HeaderAuth
// @Param param body changeAuthReq true "请求参数"
// @Success 200 {object} codec.Response{data=changeAuthResp}
// @Router /api/v1/phone/change/auth [post]
func (s *apis) changeAuth(ctx *api.Context, req changeAuthReq) (*changeAuthResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	phone, err := s.lp.Phone(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	if err := s.sv.Check(ctx, phone, req.Code); err != nil {
		return nil, err
	}

	return &changeAuthResp{State: est.New(phone).String()}, nil
}

type changeBindReq struct {
	State string `json:"state"` // 授权状态
	Phone string `json:"phone"` // 新的手机号
	Code  string `json:"code"`  // 需要验证码
}

// @Tags 手机绑定
// @Summary 换绑手机设置
// @Description 验证新的手机号码
// @Produce json
// @Security HeaderAuth
// @Param param body changeBindReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/phone/change/bind [post]
func (s *apis) changeBind(ctx *api.Context, req changeBindReq) (*api.EmptyResp, error) {
	state := est.Parse(req.State)
	if !state.Valid() {
		return nil, errors.New("invalid state")
	}

	if err := s.sv.Check(ctx, req.Phone, req.Code); err != nil {
		return nil, err
	}

	if err := s.lp.Replace(ctx, state.Data, req.Phone); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type changePwdReq struct {
	Code     string `json:"code"`     // 需要验证码
	Password string `json:"password"` // 设定新密码
}

// @Tags 手机绑定
// @Summary 修改登录密码
// @Description 修改登录密码
// @Produce json
// @Security HeaderAuth
// @Param param body changePwdReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/phone/change/pwd [post]
func (s *apis) changePwd(ctx *api.Context, req changePwdReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	phone, err := s.lp.Phone(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	if err := checkPassword(req.Password); err != nil {
		return nil, err
	}

	if err := s.sv.Check(ctx, phone, req.Code); err != nil {
		return nil, err
	}

	if _, err := s.lp.ResetPwd(ctx, phone, req.Password); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}
