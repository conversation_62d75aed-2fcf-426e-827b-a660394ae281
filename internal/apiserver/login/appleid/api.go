package appleid

import (
	"errors"
	"slices"

	"gitlab.sskjz.com/go/appleid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const (
	appId = "appleid"
)

var pkgIds = []string{
	"live.kako.global",
	"live.kako.oversea",
}

func bundleId(ctx *api.Context) (string, error) {
	pkgId := app.BundleId(ctx)
	if slices.Contains(pkgIds, pkgId) {
		return pkgId, nil
	}
	return "", errors.New("illegal bundle id")
}

func API(r *api.Router, um *ulink.Manager, jwt *auth.JWT) {
	s := &apis{um: um, jwt: jwt}
	r.POST("/appleid/auth", api.Request(s.auth))
	r.With<PERSON>uth().POST("/appleid/bind", api.Generic(s.bind))
}

type apis struct {
	um  *ulink.Manager
	jwt *auth.JWT
}

type loginReq struct {
	Token string `json:"token" binding:"required"`
	Name  string `json:"name"` // 用户名
}

// @Tags 苹果登录
// @Summary 登录
// @Description 登录
// @Produce json
// @Param param body loginReq true "请求参数"
// @Success 200 {object} codec.Response{data=auth.Payload}
// @Router /api/v1/appleid/auth [post]
func (s *apis) auth(ctx *api.Context, req loginReq) error {
	pkgId, err := bundleId(ctx)
	if err != nil {
		return err
	}

	idr, err := appleid.VerifyIdToken(pkgId, req.Token)
	if err != nil {
		return err
	}

	acc, err := s.um.Take(ctx, appId, idr.Sub)
	if err != nil && !errors.Is(err, user.ErrAccountNotExists) {
		return err
	}

	if acc != nil {
		// login
		return s.jwt.AssignLogin(ctx.Context, acc)
	}

	// register
	acc, err = s.um.Linking(ctx, initConn(idr, req.Name))
	if err != nil {
		return err
	}

	return s.jwt.AssignLogin(ctx.Context, acc)
}

// @Tags 苹果登录
// @Summary 绑定
// @Description 绑定
// @Produce json
// @Param param body loginReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/appleid/bind [post]
func (s *apis) bind(ctx *api.Context, req loginReq) (*api.EmptyResp, error) {
	pkgId, err := bundleId(ctx)
	if err != nil {
		return nil, err
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	idr, err := appleid.VerifyIdToken(pkgId, req.Token)
	if err != nil {
		return nil, err
	}

	if err := s.um.Binding(ctx, initConn(idr, req.Name), uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

func initConn(idr *appleid.IdTokenResponse, name string) *ulink.Connect {
	uc := &ulink.Connect{
		AppId:    appId,
		OpenId:   idr.Sub,
		Nickname: name,
	}
	if idr.EmailVerified {
		uc.Email = idr.Email
	}
	return uc
}
