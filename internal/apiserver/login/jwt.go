package login

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
)

func JWT(r *api.Router, j *auth.JWT) {
	r.POST("/refresh_token", j.<PERSON>f<PERSON><PERSON><PERSON><PERSON>)
}

// @Tags 登录
// @Summary 刷新Token
// @Description jwt过期之后也可以刷新
// @Produce json
// @Security HeaderAuth
// @Param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=auth.Payload}
// @Router /api/v1/refresh_token [post]
func jwtDoc() {}
