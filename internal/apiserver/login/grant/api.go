package grant

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/login/grant"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlimit"
)

func API(r *api.Router, rl *rlimit.Handler, gm *grant.Manager) {
	s := &apis{gm: gm}
	rr := r.With(rl.Middleware())
	{
		rr.GET("/login/grant/apply", api.Generic(s.Apply))
		rr.GET("/login/grant/status", api.Generic(s.Status))
	}
	ar := r.WithAuth()
	{
		ar.POST("/login/grant/viewing", api.Generic(s.Viewing))
		ar.POST("/login/grant/permitted", api.Generic(s.Permitted))
	}
}

type apis struct {
	gm *grant.Manager
}

type applyResponse struct {
	Code     string `json:"code"`     // 授权码
	ExpireAt int64  `json:"expireAt"` // 过期时间
}

// @Tags 授权登录
// @Summary 获取授权码
// @Description 获取授权码
// @Produce json
// @Security HeaderAuth
// @Param param query api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=applyResponse}
// @Router /login/grant/apply [get]
func (s *apis) Apply(ctx *api.Context, _ api.EmptyReq) (*applyResponse, error) {
	code, expireAt, err := s.gm.Apply(ctx)
	if err != nil {
		return nil, err
	}
	return &applyResponse{Code: code, ExpireAt: expireAt.Unix()}, nil
}

type statusRequest struct {
	Code string `form:"code"` // 授权码
}

type statusResponse struct {
	Status grant.Status `json:"status"` // 状态：0无效 1等待 2确认中 3成功
	Token  string       `json:"token"`  // 成功时返回的Token
}

// @Tags 授权登录
// @Summary 授权状态
// @Description 授权状态，定时2秒刷新
// @Produce json
// @Security HeaderAuth
// @Param param query statusRequest true "请求参数"
// @Success 200 {object} codec.Response{data=statusResponse}
// @Router /login/grant/status [get]
func (s *apis) Status(ctx *api.Context, req statusRequest) (*statusResponse, error) {
	status, err := s.gm.Status(ctx, req.Code)
	if err != nil {
		return nil, err
	}
	var token string
	if status == grant.StaSuccess {
		token, err = s.gm.ExchangeToken(ctx, req.Code)
		if err != nil {
			return nil, err
		}
	}
	return &statusResponse{Status: status, Token: token}, nil
}

type viewingRequest struct {
	Code string `json:"code"` // 授权码
}

// @Tags 授权登录
// @Summary 上报正在查看状态
// @Description 查看状态会通过status接口返回给客户端
// @Produce json
// @Security HeaderAuth
// @Param param body viewingRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /login/grant/viewing [post]
func (s *apis) Viewing(ctx *api.Context, req viewingRequest) (*api.EmptyResp, error) {
	_, err := ctx.User()
	if err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, s.gm.Viewing(ctx, req.Code)
}

type permittedRequest struct {
	Code string `json:"code"` // 授权码
}

// @Tags 授权登录
// @Summary 允许授权登录
// @Description 用户允许授权登录
// @Produce json
// @Security HeaderAuth
// @Param param body permittedRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /login/grant/permitted [post]
func (s *apis) Permitted(ctx *api.Context, req permittedRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, s.gm.Permitted(ctx, req.Code, uac.UserId)
}
