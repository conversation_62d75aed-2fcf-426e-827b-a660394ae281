package profile

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/nickname"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/region"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func API(
	r *api.Router, us *upload.Scenes,
	ug user.Getter, um *user.Manager, pm *privacy.Manager, lm *level.Manager, fg follow.Getter,
	as *avatar.Show, nl *nickname.Limiter, mm *moment.Manager, rq *region.Query, rm *review.Manager,
	vnd log.Vendor,
) {
	s := &apis{ug: ug, um: um, pm: pm, lm: lm, fg: fg, as: as, nl: nl, mm: mm, rq: rq, rm: rm, log: vnd.Scope("api.profile")}
	ar := r.WithAuth()
	{
		ar.GET("/profile/view", api.Generic(s.Public))
		ar.GET("/profile/self", api.Generic(s.Private))
		ar.POST("/profile/update", api.Generic(s.Update))
		ar.GET("/profile/edit/conf", api.Generic(s.EditConf))
		ar.GET("/profile/avatar/big", api.Generic(s.BigAvatar))
		ar.POST("/profile/update/avatar", api.Generic(s.UpdateAvatar))
	}
	{
		ar.GET("/privacy/setting", api.Generic(s.PrivacySetting))
		ar.POST("/privacy/update", api.Generic(s.UpdatePrivacy))
	}
	{
		us.Add("avatar", us.Classic("avatar", as.Style().Thumb))
	}
}

type apis struct {
	ug  user.Getter
	um  *user.Manager
	pm  *privacy.Manager
	lm  *level.Manager
	fg  follow.Getter
	mm  *moment.Manager
	as  *avatar.Show
	nl  *nickname.Limiter
	rq  *region.Query
	rm  *review.Manager
	log *zap.Logger
}

// @Tags 个人资料
// @Summary 客态
// @Description 查看别人的个人资料
// @Produce json
// @Security HeaderAuth
// @Param param query types.ViewProfileRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.PublicProfile}
// @Router /api/v1/profile/view [get]
func (s *apis) Public(ctx *api.Context, req types.ViewProfileRequest) (*types.PublicProfile, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	view, err := s.ug.Account(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	var resp types.PublicProfile

	resp.User = *mixer.User(ctx, view)
	resp.UserExt = *mixer.UserExt(ctx, view)
	resp.UserSocial = *mixer.FollowInfo(ctx, view.UserId)
	resp.FollowState = *mixer.FollowState(ctx, uac.UserId, view.UserId)
	resp.UserGrant = *mixer.UserGrant(ctx, uac.UserId, view.UserId)

	isFriend, _ := s.fg.IsFriend(ctx, uac.UserId, view.UserId)
	momentStats, err := s.mm.Stats(ctx, view.UserId)
	if err != nil {
		return nil, err
	}
	resp.UserMoment = *mixer.UserMoment(momentStats, uac.UserId == view.UserId, isFriend)

	{
		setting, err := s.pm.Take(ctx, view.UserId)
		if err != nil {
			return nil, err
		}
		resp.Privacy = mixer.PublicPrivacy(setting)
	}

	return &resp, nil
}

// @Tags 个人资料
// @Summary 主态
// @Description 查看自己的个人资料
// @Produce json
// @Security HeaderAuth
// @Param param query api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=types.PrivateProfile}
// @Router /api/v1/profile/self [get]
func (s *apis) Private(ctx *api.Context, _ api.EmptyReq) (*types.PrivateProfile, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	{
		forbidDevices := map[string]string{
			"59ba974094164621872886fc59196c09": "6b3bd68b-ccc1-4df7-ad19-2d7e81e4675d",
			"c6c7445f0a3e41ccbe1783f3cef7d5dd": "6b3bd68b-ccc1-4df7-ad19-2d7e81e4675d",
			"05aec316f042480c9651708a10afb898": "d877f799-53c1-4533-891e-6a400f7ef5af",
		}
		if did, has := forbidDevices[uac.UserId]; has && did == app.DeviceId(ctx) {
			return nil, biz.MakeError(biz.ErrUnauthorized)
		}
	}

	acc, err := s.um.Take(privacy.Ignore(ctx), uac.UserId)
	if err != nil {
		return nil, err
	}

	var resp types.PrivateProfile

	resp.User = *mixer.User(ctx, acc)
	resp.UserExt = *mixer.UserExt(ctx, acc)

	{
		if patrol.Has(acc.Roles) {
			resp.Role = resp.Role.Set(user.RolePatroller)
		}
		if seller.Has(acc.Roles) {
			resp.Role = resp.Role.Set(user.RoleSeller)
		}
	}

	stats, err := s.fg.Stats(ctx, acc.UserId)
	if err != nil {
		return nil, err
	}
	resp.UserSocial = *mixer.UserSocial(stats)

	resp.UserPrivate = *mixer.UserPrivate(acc, stats)

	{
		lvInfo, err := s.lm.LevelInfo(ctx, acc.UserId)
		if err != nil {
			return nil, err
		}
		resp.LevelInfo = mixer.LevelInfo(lvInfo)
	}

	{
		wallet, err := mixer.UserWallet(ctx, acc.UserId)
		if err != nil {
			return nil, err
		}
		resp.Wallet = wallet
	}

	{
		setting, err := s.pm.Take(ctx, acc.UserId)
		if err != nil {
			return nil, err
		}
		resp.Privacy = mixer.PrivatePrivacy(setting)
		{
			if setting.HideGender {
				resp.Gender = 0
			}
			if setting.HideBirthday {
				resp.Age = 0
			}
		}
	}

	isFriend, _ := s.fg.IsFriend(ctx, uac.UserId, acc.UserId)
	momentStats, err := s.mm.Stats(ctx, acc.UserId)
	if err != nil {
		return nil, err
	}
	resp.UserMoment = *mixer.UserMoment(momentStats, uac.UserId == acc.UserId, isFriend)

	return &resp, nil
}
