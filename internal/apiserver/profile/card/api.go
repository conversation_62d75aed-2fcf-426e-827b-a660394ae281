package card

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func API(
	r *api.Router,
	ug user.Getter, fc fclub.Getter, lm *level.Manager, ll *live.Manager, rm *rocket.Manager,
	vnd log.Vendor,
) {
	u := &apis{ug: ug, fc: fc, lm: lm, ll: ll, rm: rm, log: vnd.Scope("api.user")}
	ar := r.WithAuth()
	{
		ar.GET("/user/me", api.Generic(u.Me))
		ar.GET("/user/card", api.Generic(u.Card))
		ar.GET("/user/info", api.Generic(u.Info))
	}
}

type apis struct {
	ug  user.Getter
	fc  fclub.Getter
	lm  *level.Manager
	ll  *live.Manager
	rm  *rocket.Manager
	log *zap.Logger
}

// @Tags 用户
// @Summary 我的信息
// @Description 我的信息（一般在直播间内使用）
// @Produce json
// @Security HeaderAuth
// @Param param query api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=types.SelfCard}
// @Router /api/v1/user/me [get]
func (u *apis) Me(ctx *api.Context, _ api.EmptyReq) (*types.SelfCard, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	lvInfo, err := u.lm.LevelInfo(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &types.SelfCard{
		User:      *mixer.User(ctx, uac),
		UserExt:   *mixer.UserExt(ctx, uac),
		LevelInfo: mixer.LevelInfo(lvInfo),
	}, nil
}

type cardRequest struct {
	UserId string `form:"userId"` // 用户Id
	RoomId string `form:"roomId"` // 房间Id（直播间内需要传）
}

// @Tags 用户
// @Summary 卡片信息
// @Description 卡片信息
// @Produce json
// @Security HeaderAuth
// @Param param query cardRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.UserCard}
// @Router /api/v1/user/card [get]
func (u *apis) Card(ctx *api.Context, req cardRequest) (*types.UserCard, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	view, err := u.ug.Account(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	var room *live.Room
	if req.RoomId != "" {
		room, err = u.ll.Room2(req.RoomId)
		if err != nil {
			return nil, err
		}
	}

	resp := &types.UserCard{
		User:        *mixer.User(ctx, view),
		UserExt:     *mixer.UserExt(ctx, view),
		UserSocial:  *mixer.FollowInfo(ctx, view.UserId),
		FollowState: *mixer.FollowState(ctx, uac.UserId, view.UserId),
		UserGrant:   *mixer.UserGrant(ctx, uac.UserId, view.UserId),
	}

	if room == nil {
		return resp, nil
	}

	// 火箭头像框
	rocketUser, _ := u.rm.GetRoomAvatarBorderUserList(ctx, room.SessionId.Hex())
	if lo.Contains(rocketUser, view.UserId) {
		resp.User.AvatarBorder = rocket.AvatarBorder
	}

	// 粉丝团信息
	if room.UserId == view.UserId {
		// 主播资料卡
		if members, err := u.fc.Members(ctx, room.UserId); err != nil {
			return nil, err
		} else {
			resp.Fansclub = &types.FansCard{
				Members: members,
			}
		}
	} else {
		// 房间用户资料
		if lv, err := u.fc.Level(ctx, room.UserId, view.UserId); err != nil {
			return nil, err
		} else if lv > 0 {
			resp.Fansclub = &types.FansCard{
				Level: lv,
			}
		}
	}

	return resp, nil
}

type infoRequest struct {
	UserId string `form:"userId"`
	ShowId string `form:"showId"`
}

type infoResponse struct {
	types.UserWithExt
}

// @Tags 用户
// @Summary 基础信息
// @Description 基础信息
// @Produce json
// @Security HeaderAuth
// @Param param query infoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=infoResponse}
// @Router /api/v1/user/info [get]
func (u *apis) Info(ctx *api.Context, req infoRequest) (*infoResponse, error) {
	var uac *user.Account
	if req.UserId != "" {
		uac, _ = u.ug.Account(ctx, req.UserId)
	} else if req.ShowId != "" {
		uac, _ = u.ug.GetByShowId(ctx, req.ShowId)
	}
	if uac == nil {
		return nil, user.ErrAccountNotExists
	}
	return &infoResponse{
		UserWithExt: *mixer.UserWithExt(ctx, uac),
	}, nil
}
