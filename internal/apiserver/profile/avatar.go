package profile

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type AuditStatus int

const (
	StatusSuccess AuditStatus = iota
	StatusReview
	StatusReject
)

type bigAvatarReq struct {
	UserId string `form:"userId"`
}

type bigAvatarResp struct {
	Url string `json:"url"`
}

// @Tags 个人资料
// @Summary 查看用户头像大图
// @Description 查看用户头像大图
// @Security HeaderAuth
// @Param param query bigAvatarReq true "请求参数"
// @Success 200 {object} codec.Response{data=bigAvatarResp}
// @Router /api/v1/profile/avatar/big [get]
func (s *apis) BigAvatar(ctx *api.Context, req bigAvatarReq) (*bigAvatarResp, error) {
	acc, err := s.ug.Account(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	return &bigAvatarResp{Url: s.as.LargeOf(acc.Avatar)}, nil
}

type updateAvatarReq struct {
	Path string `json:"path"` // 文件路径（prefix+key）
}

type updateAvatarResp struct {
	Status AuditStatus `json:"status"` // 审核状态
	Result string      `json:"result"` // 头像地址
}

// @Tags 个人资料
// @Summary 更新用户头像
// @Description 更新用户头像
// @Produce json
// @Security HeaderAuth
// @Param param body updateAvatarReq true "请求参数"
// @Success 200 {object} codec.Response{data=updateAvatarResp}
// @Router /api/v1/profile/update/avatar [post]
func (s *apis) UpdateAvatar(ctx *api.Context, req updateAvatarReq) (*updateAvatarResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	uri, err := s.as.Check(req.Path)
	if err != nil {
		return nil, err
	}

	if rr, err := s.rm.ReviewImage(ctx, s.as.URL(uri, avatar.Large())); err != nil {
		return nil, err
	} else if rr.RiskLevel == review.RiskLevelHigh {
		return &updateAvatarResp{Status: StatusReject}, nil
	}

	if err := s.um.Update(ctx, uac.UserId, user.UpAccount(&user.Account{Avatar: uri})); err != nil {
		return nil, err
	}

	return &updateAvatarResp{
		Status: StatusSuccess,
		Result: s.as.URL(uri),
	}, nil
}
