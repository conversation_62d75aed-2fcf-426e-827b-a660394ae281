package profile

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

// @Tags 个人资料
// @Summary 获取隐私设置
// @Description 获取隐私设置
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.UserPrivacy}
// @Router /api/v1/privacy/setting [get]
func (s *apis) PrivacySetting(ctx *api.Context, _ api.EmptyReq) (*types.UserPrivacy, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	setting, err := s.pm.Take(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	return mixer.PrivatePrivacy(setting), nil
}

type privacyReq struct {
	HideBirthday    *bool `json:"hideBirthday"`    // 不展示生日
	HideMomentLike  *bool `json:"hideMomentLike"`  // 不展示喜欢的动态
	HideLiveSession *bool `json:"hideLiveSession"` // 不展示直播动态
}

// @Tags 个人资料
// @Summary 更新隐私设置
// @Description 更新隐私设置
// @Produce json
// @Security HeaderAuth
// @Param param body privacyReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/privacy/update [post]
func (s *apis) UpdatePrivacy(ctx *api.Context, req privacyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var pyMask privacy.Mask

	if req.HideBirthday != nil {
		pyMask = pyMask.Set(lo.Ternary(*req.HideBirthday, privacy.HideBirthday, privacy.ShowBirthday))
	}
	if req.HideMomentLike != nil {
		pyMask = pyMask.Set(lo.Ternary(*req.HideMomentLike, privacy.HideMomentLike, privacy.ShowMomentLike))
	}
	if req.HideLiveSession != nil {
		pyMask = pyMask.Set(lo.Ternary(*req.HideLiveSession, privacy.HideLiveSession, privacy.ShowLiveSession))
	}

	if err := s.pm.Update(ctx, uac.UserId, pyMask); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}
