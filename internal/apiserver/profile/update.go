package profile

import (
	"errors"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var (
	ErrSensitiveWords = biz.NewError(biz.ErrSensitiveContent, "sensitive words detected")
)

type updateReq struct {
	Nickname     *string `json:"nickname"`     // 修改昵称
	Signature    *string `json:"signature"`    // 修改签名
	Gender       *int    `json:"gender"`       // 修改性别：0不展示 1男 2女
	Birthday     *string `json:"birthday"`     // 修改生日
	HideBirthday *bool   `json:"hideBirthday"` // 不展示生日
	Region       *string `json:"region"`       // 修改所在地（区域代码）
	School       *string `json:"school"`       // 修改学校
}

// @Tags 个人资料
// @Summary 更新信息
// @Description 更新信息
// @Produce json
// @Security HeaderAuth
// @Param param body updateReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/profile/update [post]
func (s *apis) Update(ctx *api.Context, req updateReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	{
		if req.Nickname != nil && len(s.rm.SensitiveWords(*req.Nickname)) > 0 {
			return nil, user.ErrInvalidNickname
		}
		if req.Signature != nil && len(s.rm.SensitiveWords(*req.Signature)) > 0 {
			return nil, ErrSensitiveWords
		}
		if req.School != nil && len(s.rm.SensitiveWords(*req.School)) > 0 {
			return nil, ErrSensitiveWords
		}
	}

	var upAcc user.Account
	{
		if req.Nickname != nil {
			if status, err := s.nl.Status(ctx, uac.UserId); err != nil {
				return nil, err
			} else if status.Limited() {
				return nil, biz.Legacy("nickname update limited")
			}
			upAcc.Nickname = *req.Nickname
		}

		if req.Gender != nil {
			upAcc.Gender = *req.Gender
		}

		if req.Birthday != nil {
			t, err := time.ParseInLocation("********", *req.Birthday, app.Timezone(ctx, uac))
			if err != nil {
				return nil, err
			}
			upAcc.Birthday = t
		}
	}

	var upNil user.Nullable
	{
		upNil.Signature = req.Signature
		{
			if req.Region != nil && *req.Region != "" {
				if s.rq.Take(ctx, *req.Region) == "" {
					return nil, errors.New("invalid region")
				}
			}
			upNil.Region = req.Region
		}
		upNil.School = req.School
	}

	var pyMask privacy.Mask
	{
		if req.Gender != nil {
			pyMask = pyMask.Set(lo.Ternary(*req.Gender == 0, privacy.HideGender, privacy.ShowGender))
		}
		if req.HideBirthday != nil {
			pyMask = pyMask.Set(lo.Ternary(*req.HideBirthday, privacy.HideBirthday, privacy.ShowBirthday))
		}
	}

	if err := s.pm.Update(ctx, uac.UserId, pyMask); err != nil {
		return nil, err
	}

	if err := s.um.Update(ctx, uac.UserId, user.UpAccount(&upAcc), user.UpNullable(&upNil)); err != nil {
		if !errors.Is(err, user.ErrNoUpdateValue) {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}

type editConfig struct {
	NicknameTips string `json:"nicknameTips"` // 昵称的修改提示
}

// @Tags 个人资料
// @Summary 编辑页配置
// @Description 在个人资料编辑页可能会用到的一些配置
// @Produce json
// @Security HeaderAuth
// @Param param query api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=editConfig}
// @Router /api/v1/profile/edit/conf [get]
func (s *apis) EditConf(ctx *api.Context, _ api.EmptyReq) (*editConfig, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	status, err := s.nl.Status(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	return &editConfig{
		NicknameTips: i3n.T2(ctx, "nickname tips {{.period}}{{.limit}}{{.expire}}{{.remain}}", i3n.Args{
			"period": status.Period,
			"limit":  status.Limit,
			"expire": status.Expire.Format(time.DateOnly),
			"remain": status.Limit - status.Used,
		}),
	}, nil
}
