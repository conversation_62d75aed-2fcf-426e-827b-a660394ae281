package game

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type GetCodeReq struct {
	AppId string `json:"appId"`
}

type GetCodeResp struct {
	Code string `json:"code"`
}

// @Tags MiniGame
// @Summary 获取用户登陆游戏code
// @Description 获取用户登陆游戏code
// @Accept json
// @Produce json
// @Security HeaderAuth
// @Param req body GetCodeReq true "GetCodeReq"
// @Success 200 {object} codec.Response{data=GetCodeResp}
// @Router /api/v1/game/session/code [post]
func (a *Api) GetCode(ctx *api.Context, req GetCodeReq) (*GetCodeResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	code, err := a.mgr.MakeSessionCode(uac.UserId, req.AppId)
	if err != nil {
		return nil, err
	}

	return &GetCodeResp{Code: code}, nil
}
