package ipk

import (
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

type Peer struct {
	types.User
	Online     int64      `json:"online"`
	LiveStatus LiveStatus `json:"liveStatus"` // 直播间状态
}

func (p *Peer) String() string {
	var sb strings.Builder
	sb.WriteString("{")
	sb.WriteString(p.UserId)
	sb.WriteString(":")
	sb.WriteString(p.Nickname)
	sb.WriteString(":")
	sb.WriteString(p.LiveStatus.LStatus.String())
	sb.WriteString("}")
	return sb.String()
}

type Invitee struct {
	Peer
	InviteAt int64 `json:"inviteAt"`
}

type LiveStatus struct {
	PkSession *Session         `json:"pkSession"` // PK会话信息
	LStatus   protocol.LStatus `json:"status"`
}

type Session struct {
	SessionId string `json:"sessionId"`
}

type MatchingResp struct {
	Expected   int        `json:"expected"`   // 等待时间
	Matching   bool       `json:"matching"`   // 是否在匹配中
	InPK       bool       `json:"inPK"`       // 是否在PK中
	LiveStatus LiveStatus `json:"liveStatus"` // 直播间状态
	Invitees   []Invitee  `json:"invitees"`   // 邀请的人
	Peers      []string   `json:"peers"`      // 随机匹配的对手信息，目前主要包含固定的一组头像
	Friends    []Peer     `json:"friends"`    // 好友状态
	Recommends []Peer     `json:"recommends"` // 为你推荐
}

type InviteReq struct {
	UserId string `json:"userId" binding:"required"`
	Source int    `json:"source" binding:"required"` // 1 来自好友 2 来自推荐
}

type InviteResp struct {
	SessionId string `json:"sessionId"`
}

type CancelInviteReq struct {
	UserId string `json:"userId" binding:"required"`
}

type AcceptInviteReq struct {
	SessionId string `json:"sessionId" binding:"required"` // 匹配邀请的会话ID
}

type RejectInviteReq struct {
	SessionId string `json:"sessionId" binding:"required"` // 匹配邀请的会话ID
}

type ReadyReq struct {
	SessionId string `json:"sessionId" binding:"required"`
	Ready     bool   `json:"ready"`
}

type TerminateReq struct {
	SessionId string `json:"sessionId" binding:"required"`
}

type BlockUser struct {
	User   types.User `json:"user"`
	Exp    int64      `json:"exp"`    //  自动移除时间 unix时间戳
	Status int        `json:"status"` //  0 未直播 1 直播中
}

type ListInviteBlackUserResp struct {
	List []BlockUser `json:"list"`
}

type RemoveBlockUserReq struct {
	UserId string `json:"userId" binding:"required"`
}

type AddBlockTodayReq struct {
	SessionId string `json:"sessionId"`
	ToUserId  string `json:"userId" binding:"required"` // 被操作的用户id
}

type AddBlackSessionAnyReq struct {
	SessionId string `json:"sessionId"`
}

type AnchorConfig struct {
	PKDuration   int  `json:"pkDuration"` // by seconds, default 300s, 5min pk持续时间
	Acceptable   bool `json:"acceptable"`
	TodayRefused int  `json:"todayRefused"` // 今天拒绝
}

type SetAnchorConfigReq struct {
	PKDuration int  `json:"pkDuration"` // by seconds, default 300s, 5min pk持续时间
	Acceptable bool `json:"acceptable"`
}

type SearchUserReq struct {
	Keyword string `form:"keyword"` // 关键字
}

type SocialUser struct {
	types.UserWithExt
	types.UserSocial
}

type SearchedUser struct {
	User       SocialUser `json:"user"`
	LiveStatus LiveStatus `json:"liveStatus"` // 直播间状态
}

type SearchAnchorResp struct {
	List []SearchedUser `json:"list"`
}

type HistoryItem struct {
	types.UserWithExt
	IsFriend   bool       `json:"isFriend"`
	LiveStatus LiveStatus `json:"liveStatus"` // 直播间状态
	Winner     string     `json:"winner"`
	Winning    int        `json:"winning"` // 连胜
	StartAt    int64      `json:"startAt"` // 开始时间
}

type HistoryReq struct {
	Cursor int64 `form:"cursor"` // 分页游标
}

type HistoryResp struct {
	Cursor int64         `json:"cursor"` // 分页游标：用于下次请求
	List   []HistoryItem `json:"list"`   // 列表
}

type StatusReq struct {
	AnchorId string `form:"anchorId" binding:"required"`
}

type Status struct {
	Status         protocol.LStatus `json:"status"`
	SessionId      string           `json:"sessionId"`
	PKDuration     int              `json:"pkDuration"`     // pk持续时间 by seconds with hstyle
	ConfigDuration int              `json:"configDuration"` // pk持续时间 by seconds
	Users          []types.User     `json:"users"`          // 参与的用户列表
	LinkedAt       int64            `json:"linkedAt"`       // 连接时间
}

type RestartReq struct {
	SessionId string `json:"sessionId" binding:"required"`
}

type LastResultReq struct {
	AnchorId string `form:"anchorId" binding:"required"`
}

type RankedUser struct {
	User     types.User     `json:"user"`
	RoomUser types.RoomUser `json:"roomUser"`
	Rank     int            `json:"rank"`
	Score    int            `json:"score"`
}

type ScoredUser struct {
	User  types.User `json:"user"`
	Score int64      `json:"score"`
}

type LastResultResp struct {
	SessionId string       `json:"sessionId"`
	Users     []ScoredUser `json:"users"`
	Winner    string       `json:"winner"`
	Top       []RankedUser `json:"top"`
}
