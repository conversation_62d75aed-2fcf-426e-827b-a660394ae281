package ipk

import (
	"cmp"
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/link"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/ufind"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"

	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
)

var ErrUserInLinking = biz.NewError(biz.ErrLinking, "In Link, the pk function cannot be used")

type API struct {
	ug     user.Getter
	lm     *live.Manager
	pm     *pk.Manager
	rm     *room.Manager
	fg     follow.Getter
	as     *avatar.Show
	uf     *ufind.Indexer
	sm     *rsd.Stats
	lk     *link.Manager
	logger *zap.Logger
}

func Invoke(
	r *api.Router,
	ug user.Getter,
	lm *live.Manager,
	pm *pk.Manager,
	rm *room.Manager,
	fg follow.Getter,
	as *avatar.Show,
	uf *ufind.Indexer,
	sm *rsd.Stats,
	lk *link.Manager,
	vnd log.Vendor,
) error {
	h := API{ug: ug, fg: fg, as: as, uf: uf, lm: lm, pm: pm, rm: rm, sm: sm, lk: lk, logger: vnd.Scope("api.pk")}

	{
		r := r.WithAuth()
		r.GET("pk/matching", api.Generic(h.matching))
		r.POST("pk/matching/start", api.Generic(h.start))
		r.POST("pk/matching/stop", api.Generic(h.stop))
		r.POST("pk/matching/ready", api.Generic(h.PKUserReady))
		r.POST("pk/matching/invite", api.Generic(h.invite))
		r.POST("pk/matching/invite/cancel", api.Generic(h.cancelInvite))
		r.POST("pk/matching/accept", api.Generic(h.accept))
		r.POST("pk/matching/reject", api.Generic(h.reject))

		r.GET("pk/contribution", api.Generic(h.contribution))

		r.GET("pk/status", api.Generic(h.status))
		r.POST("pk/terminate", api.Generic(h.terminate))
		r.POST("pk/restart", api.Generic(h.restart))
		r.GET("pk/search", api.Generic(h.search))
		r.GET("pk/history", api.Generic(h.history))
		r.GET("pk/result", api.Generic(h.lastResult))

		r.GET("pk/block/today", api.Generic(h.listTodayBlockList))
		r.POST("pk/block/today/add", api.Generic(h.addBlacklist))
		r.POST("pk/block/today/remove", api.Generic(h.removeFromTodayBlacklist))
		r.POST("pk/block/session/any", api.Generic(h.addBlackSessionAny))

		r.GET("pk/anchor/config", api.Generic(h.anchorConfig))
		r.POST("pk/anchor/config", api.Generic(h.setAnchorConfig))
	}

	{
		r.TryAuth().GET("pk/live", api.Generic(h.PKLive))
	}

	return nil
}

func (m *API) LinkingError(ctx context.Context, userId string) error {
	ri, err := m.lm.RoomByUserId2(ctx, userId)
	if err != nil {
		return fmt.Errorf("linking error: get room by user id failed: %w", err)
	}

	if !ri.IsLiving() {
		return nil
	}

	is, err := m.lk.IsLinking(ctx, ri.SessionId.Hex())
	if err != nil {
		return fmt.Errorf("linking error: get linking failed: %w", err)
	}
	if is {
		return ErrUserInLinking
	}

	return nil
}

func (m *API) Friends(ctx context.Context, userId string) ([]string, error) {
	friends, err := m.fg.FriendIds(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("get friends: %w", err)
	}

	if len(friends) == 0 {
		return nil, nil
	}

	livingFriends := make([]string, 0, len(friends))
	for _, friend := range friends {
		fr, err := m.lm.RoomByUserId2(ctx, friend)
		if err != nil {
			m.logger.Error("get room by user id failed", zap.Error(err), zap.String("userId", friend))
			continue
		}

		if fr.IsLiving() {
			livingFriends = append(livingFriends, friend)
		}
	}

	if len(livingFriends) == 0 {
		return nil, nil
	}

	sts, err := m.pm.MUserStatus(ctx, livingFriends...)
	if err != nil {
		return nil, fmt.Errorf("get anchor status: %w", err)
	}

	out := make([]string, 0, 128)
	for _, f := range livingFriends {
		st := sts.User(f)
		if st == nil {
			continue
		}

		if !st.Acceptable || st.Matching || st.PK {
			continue
		}

		out = append(out, f)
	}

	return out, nil
}

// @Tags PK
// @Summary PK页信息
// @Description PK页信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=MatchingResp}
// @Router /api/v1/pk/matching [get]
func (p *API) matching(ctx *api.Context, _ api.EmptyReq) (*MatchingResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	friends, err := p.Friends(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	ms, err := p.pm.Status(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	var expected int = 15
	if ms.Matching {
		expected = 15 - int(time.Now().Unix()-ms.MatchingAt.Unix())
		if expected < 3 {
			expected = rng.Intn(3) + 1
		}
	}

	invitee, err := p.pm.UserSentInvitee(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	const maxPeers = 2
	resp := &MatchingResp{
		Matching: ms.Matching,
		Expected: expected,
		InPK:     ms.InPK,
		Peers:    make([]string, 0, maxPeers),
	}

	for userId, at := range invitee {
		ri, err := p.lm.RoomByUserId2(ctx, userId)
		if err != nil {
			p.logger.Error("get room by user id failed", zap.Error(err), zap.String("userId", userId))
			continue
		}

		if ri.Status != live.LiveStatusLiving {
			continue
		}

		acc, err := p.ug.Account(ctx, userId)
		if err != nil {
			p.logger.Error("get user failed", zap.Error(err), zap.String("userId", userId))
			continue
		}

		lst, err := p.makeLiveStatus(ctx, userId)
		if err != nil {
			p.logger.Error("get live status failed", zap.Error(err))
			continue
		}

		itm := Invitee{
			Peer: Peer{
				User:       *mixer.User(ctx, acc),
				LiveStatus: *lst,
			},
			InviteAt: time.UnixMilli(at).Unix(),
		}

		if n, err := p.sm.GetSessionOnline2(ctx, ri.SessionId.Hex()); err == nil {
			itm.Online = n
		}

		resp.Invitees = append(resp.Invitees, itm)
	}

	slices.SortFunc(resp.Invitees, func(a, b Invitee) int {
		return cmp.Compare(a.InviteAt, b.InviteAt)
	})

	for _, userId := range friends {
		peer, err := p.makePeer(ctx, userId)
		if err != nil {
			p.logger.Error("get peer failed", zap.Error(err), zap.String("userId", userId))
			continue
		}

		resp.Friends = append(resp.Friends, peer)
	}

	peers, err := p.pm.UserRecentPKPeers(ctx, uac.UserId, 5)
	if err != nil {
		return nil, err
	}

	getFollowTime := func(userId string) time.Time {
		f, err := p.fg.Take(ctx, uac.UserId, userId)
		if err != nil {
			p.logger.Error("get follow failed", zap.Error(err), zap.String("userId", userId))
			return time.Time{}
		}
		return f.Time
	}

	slices.SortFunc(resp.Friends, func(a, b Peer) int {
		// if in peers sort by index in peers, put the into the front, else sort by friend time
		ai, bi := slices.Index(peers, a.UserId), slices.Index(peers, b.UserId)
		if ai > -1 && bi > -1 {
			return ai - bi
		}
		if ai > -1 {
			return -1
		}
		if bi > -1 {
			return 1
		}

		return int(getFollowTime(b.UserId).Sub(getFollowTime(a.UserId)).Milliseconds())
	})

	{
		recommends, err := p.pm.Recommends(ctx, uac.UserId, 10, friends, time.Now())
		if err != nil {
			return nil, fmt.Errorf("get near peers failed: %w", err)
		}

		for _, userId := range recommends {
			peer, err := p.makePeer(ctx, userId)
			if err != nil {
				p.logger.Error("get peer failed", zap.Error(err), zap.String("userId", userId))
				continue
			}

			resp.Recommends = append(resp.Recommends, peer)
		}
	}

	if len(resp.Friends) == 0 {
		resp.Friends = []Peer{}
	}

	if len(resp.Recommends) == 0 {
		resp.Recommends = []Peer{}
	}

	{
		avatar := p.as.Default()
		for i := 0; i < maxPeers; i++ {
			resp.Peers = append(resp.Peers, avatar)
		}

		peers := lo.Samples(resp.Recommends, maxPeers)
		for i, r := range peers {
			acc, err := p.ug.Account(ctx, r.UserId)
			if err != nil {
				p.logger.Error("get user failed", zap.Error(err), zap.String("userId", r.UserId))
				continue
			}
			resp.Peers[i] = acc.Avatar
		}
	}

	return resp, nil
}

// @Tags PK
// @Summary 加入匹配
// @Description 加入随机匹配
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/matching/start [post]
func (p *API) start(ctx *api.Context, _ api.EmptyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := p.LinkingError(ctx, uac.UserId); err != nil {
		return nil, err
	}

	p.logger.Debug("start matching", zap.String("userId", uac.UserId))
	if err := p.pm.StartMatching(ctx, uac.UserId); err != nil {
		p.logger.Error("start matching failed", zap.Error(err))
		return nil, biz.Legacy("start matching failed")
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 停止匹配
// @Description 停止匹配
// @Produce json
// @Security HeaderAuth
// @param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/matching/stop [post]
func (p *API) stop(ctx *api.Context, _ api.EmptyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	p.logger.Debug("stop matching", zap.String("userId", uac.UserId))
	if err := p.pm.StopMatching(ctx, uac.UserId); err != nil {
		p.logger.Error("stop matching failed", zap.Error(err))
		return nil, biz.Legacy("stop matching failed")
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 邀请
// @Description 邀请一起PK
// @Produce json
// @Security HeaderAuth
// @Param param body InviteReq true "请求参数"
// @Success 200 {object} codec.Response{data=InviteResp}
// @Router /api/v1/pk/matching/invite [post]
func (p *API) invite(ctx *api.Context, req InviteReq) (*InviteResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	p.logger.Debug("invite", zap.String("userId", uac.UserId), zap.String("toUserId", req.UserId))

	if err := p.LinkingError(ctx, uac.UserId); err != nil {
		return nil, err
	}

	if err := p.LinkingError(ctx, req.UserId); err != nil {
		return nil, err
	}

	ri, err := p.lm.RoomByUserId2(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	if ri.Status != live.LiveStatusLiving {
		return nil, biz.Legacy("user not living")
	}

	if err := p.pm.InBlocked2(ctx, req.UserId, uac.UserId); err != nil {
		return nil, err
	}

	letter, err := p.pm.Invite(ctx, req.Source, uac.UserId, req.UserId)
	if err != nil {
		return nil, err
	}

	return &InviteResp{SessionId: letter.Id}, nil
}

// @Tags PK
// @Summary 取消邀请
// @Description 取消邀请
// @Produce json
// @Security HeaderAuth
// @Param param body CancelInviteReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/matching/invite/cancel [post]
func (p *API) cancelInvite(ctx *api.Context, req CancelInviteReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := p.pm.CancelInvite(ctx, uac.UserId, req.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 接受邀请
// @Description 接受邀请
// @Produce json
// @Security HeaderAuth
// @param param body AcceptInviteReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/matching/accept [post]
func (p *API) accept(ctx *api.Context, req AcceptInviteReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	p.logger.Debug("accept", zap.String("userId", uac.UserId), zap.String("sessionId", req.SessionId))

	letter, err := p.pm.GetLetter(ctx, req.SessionId)
	if err != nil {
		return nil, err
	}

	if letter == nil {
		return nil, pk.ErrUserCancelInvitee
	}

	if err := p.LinkingError(ctx, letter.From); err != nil {
		return nil, err
	}

	if err := p.LinkingError(ctx, letter.To); err != nil {
		return nil, err
	}

	if err := p.pm.Accept(ctx, req.SessionId, uac.UserId); err != nil {
		if !biz.Is(err) {
			p.logger.Error("accept user session failed",
				zap.Error(err),
				zap.String("userId", uac.UserId),
				zap.String("sessionId", req.SessionId),
			)
		}
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 拒绝邀请
// @Description 拒绝邀请
// @Produce json
// @Security HeaderAuth
// @param param body RejectInviteReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/matching/reject [post]
func (p *API) reject(ctx *api.Context, req RejectInviteReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	p.logger.Debug("reject", zap.String("userId", uac.UserId), zap.String("sessionId", req.SessionId))
	if err := p.pm.Reject(ctx, req.SessionId, uac.UserId); err != nil {
		if !errors.Is(err, pk.ErrUserCancelInvitee) {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 终止pk(提前)
// @Description 终止pk
// @Produce json
// @Security HeaderAuth
// @param param body TerminateReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/terminate [post]
func (p *API) terminate(ctx *api.Context, req TerminateReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	p.logger.Debug("terminate", zap.String("userId", uac.UserId), zap.String("sessionId", req.SessionId))
	if err := p.pm.TerminalUserSession(ctx, uac.UserId, req.SessionId); err != nil {
		if !biz.Is(err) {
			p.logger.Error("terminate user session failed", zap.Error(err), zap.String("userId", uac.UserId), zap.String("sessionId", req.SessionId))
		}
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary ready
// @Description 进入ready状态
// @Produce json
// @Security HeaderAuth
// @param param body ReadyReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/matching/ready [post]
func (p *API) PKUserReady(ctx *api.Context, req ReadyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	logger := p.logger.With(zap.String("userId", uac.UserId), zap.String("sessionId", req.SessionId), zap.Bool("ready", req.Ready))
	logger.Debug("ready")

	if err := p.LinkingError(ctx, uac.UserId); err != nil {
		return nil, err
	}

	if req.Ready {
		if err := p.pm.MarkUserReady(ctx, uac.UserId, req.SessionId); err != nil {
			logger.Error("mark user ready failed", zap.Error(err))
			return nil, err
		}
	} else {
		if err := p.pm.MarkUserUnready(ctx, uac.UserId, req.SessionId); err != nil {
			logger.Error("mark user unready failed", zap.Error(err))
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 获取今天的黑名单列表
// @Description 获取今天的黑名单列表
// @Produce json
// @Security HeaderAuth
// @param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=ListInviteBlackUserResp}
// @Router /api/v1/pk/block/today [get]
func (p *API) listTodayBlockList(ctx *api.Context, _ api.EmptyReq) (*ListInviteBlackUserResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	s := time.Now()

	uu, err := p.pm.ListUserTodayBlocks(ctx, uac.UserId, s)
	if err != nil {
		return nil, err
	}

	resp := &ListInviteBlackUserResp{List: make([]BlockUser, 0, len(uu))}
	for _, u := range uu {
		acc, err := p.ug.Account(ctx, u)
		if err != nil {
			p.logger.Error("get user failed", zap.Error(err), zap.String("userId", u))
			continue
		}

		bu := BlockUser{
			User: *mixer.User(ctx, acc),
			Exp:  now.New(s).EndOfDay().Unix(),
		}

		ri, err := p.lm.RoomByUserId2(ctx, u)
		if err != nil {
			p.logger.Error("get room by user id failed", zap.Error(err), zap.String("userId", u))
		} else {
			if ri.Status == live.LiveStatusLiving {
				bu.Status = 1
			}
		}

		resp.List = append(resp.List, bu)
	}

	return resp, nil
}

// @Tags PK
// @Summary 移除今天的黑名单列表成员
// @Description 移除今天的黑名单列表成员
// @Produce json
// @Security HeaderAuth
// @param param body RemoveBlockUserReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/block/today/remove [post]
func (p *API) removeFromTodayBlacklist(ctx *api.Context, req RemoveBlockUserReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	s := time.Now()

	if err := p.pm.RemoveTodayBlockUser(ctx, uac.UserId, req.UserId, s); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 勿扰设置-添加今日黑名单
// @Description 添加今日黑名单
// @Produce json
// @Security HeaderAuth
// @param param body AddBlockTodayReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/block/today/add [post]
func (p *API) addBlacklist(ctx *api.Context, req AddBlockTodayReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := p.pm.AddTodayBlockUser(ctx, uac.UserId, req.ToUserId); err != nil {
		return nil, fmt.Errorf("add user today blacklist failed: %w", err)
	}

	if req.SessionId != "" {
		if letter, err := p.pm.GetLetter(ctx, req.SessionId); err != nil {
			p.logger.Error("get letter failed", zap.Error(err))
		} else if letter != nil {
			if err := p.pm.Reject(ctx, req.SessionId, uac.UserId); err != nil {
				p.logger.Error("add block user :reject failed", zap.Error(err))
			} else {
				p.rm.Post(func() {
					_ = p.rm.NotifyPKInviterCancellation(ctx, req.SessionId, letter.From, letter.To)
				})
			}
		} else {
			p.logger.Debug("letter is nil", zap.String("sessionId", req.SessionId))
		}
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 勿扰设置-本场拒绝任何人
// @Description 勿扰设置
// @Produce json
// @Security HeaderAuth
// @param param body AddBlackSessionAnyReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/block/session/any [post]
func (p *API) addBlackSessionAny(ctx *api.Context, req AddBlackSessionAnyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := p.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	if err := p.pm.SetRoomSessionBlockAny(ctx, ri.SessionId.Hex()); err != nil {
		return nil, fmt.Errorf("add user today blacklist failed: %w", err)
	}

	if req.SessionId != "" {
		if letter, err := p.pm.GetLetter(ctx, req.SessionId); err != nil {
			p.logger.Error("get letter failed", zap.Error(err))
		} else if letter != nil {
			if err := p.pm.Reject(ctx, req.SessionId, uac.UserId); err != nil {
				p.logger.Error("add block user :reject failed", zap.Error(err))
			} else {
				p.rm.Post(func() {
					_ = p.rm.NotifyPKInviterCancellation(ctx, req.SessionId, letter.From, letter.To)
				})
			}
		} else {
			p.logger.Debug("letter is nil", zap.String("sessionId", req.SessionId))
		}
	}

	return &api.EmptyResp{}, nil
}

// @Tags PK
// @Summary 获取主播互动设置
// @Description
// @Produce json
// @Security HeaderAuth
// @param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=AnchorConfig}
// @Router /api/v1/pk/anchor/config [get]
func (p *API) anchorConfig(ctx *api.Context, _ api.EmptyReq) (*AnchorConfig, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	c, err := p.pm.UserConfig(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	uu, _ := p.pm.ListUserTodayBlocks(ctx, uac.UserId, time.Now())

	return &AnchorConfig{PKDuration: c.PKDuration, Acceptable: c.Acceptable, TodayRefused: len(uu)}, nil
}

// @Tags PK
// @Summary 修改主播互动设置
// @Description
// @Produce json
// @Security HeaderAuth
// @param param body SetAnchorConfigReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/pk/anchor/config [post]
func (p *API) setAnchorConfig(ctx *api.Context, req SetAnchorConfigReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := p.pm.SetUserConfig(ctx, uac.UserId, &pk.Config{Acceptable: req.Acceptable}); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

func (p *API) makePeer(ctx context.Context, userId string) (Peer, error) {
	ri, err := p.lm.RoomByUserId2(ctx, userId)
	if err != nil {
		return Peer{}, fmt.Errorf("get room by user id failed: %w", err)
	}

	online, err := p.sm.GetSessionOnline2(ctx, ri.SessionId.Hex())
	if err != nil {
		return Peer{}, fmt.Errorf("get online by user id failed: %w", err)
	}

	lst, err := p.makeLiveStatus(ctx, userId)
	if err != nil {
		return Peer{}, fmt.Errorf("get live status by user id failed: %w", err)
	}

	acc, err := p.ug.Account(ctx, userId)
	if err != nil {
		return Peer{}, fmt.Errorf("get account by user id failed: %w", err)
	}

	return Peer{
		User:       *mixer.User(ctx, acc),
		Online:     online,
		LiveStatus: *lst,
	}, nil
}

func (p *API) makeLiveStatus(ctx context.Context, userId string) (*LiveStatus, error) {
	ri, err := p.lm.RoomByUserId2(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("get room by user id failed: %w", err)
	}

	out := LiveStatus{}
	if ri.Status != live.LiveStatusLiving {
		return &out, nil
	}

	out.LStatus |= protocol.LStatusLive

	// get status from pk module
	sess, err := p.pm.UserCurSession(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("get user current session failed: %w", err)
	}

	if sess == nil {
		return &out, nil
	}

	out.LStatus |= protocol.LStatusPK
	out.PkSession = &Session{
		SessionId: sess.ID.Hex(),
	}

	switch sess.Status {
	case pk.SessionStatusFighting:
		out.LStatus |= protocol.LStatusPKFighting
	case pk.SessionStatusPublish:
		out.LStatus |= protocol.LStatusPKPublish
	case pk.SessionStatusLinked:
		out.LStatus |= protocol.LStatusPKLinked
	default:

	}

	return &out, nil
}

// @Tags PK
// @Summary 搜索用户
// @Description 搜索用户
// @Produce json
// @Security HeaderAuth
// @param param body SearchUserReq true "请求参数"
// @Success 200 {object} codec.Response{data=SearchAnchorResp}
// @Router /api/v1/pk/search [get]
func (p *API) search(ctx *api.Context, req SearchUserReq) (*SearchAnchorResp, error) {
	if req.Keyword == "" {
		return &SearchAnchorResp{List: []SearchedUser{}}, nil
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	aa, err := p.uf.Search(ctx, uac.UserId, req.Keyword, 20)
	if err != nil {
		return nil, err
	}

	var out SearchAnchorResp
	for _, uid := range aa {
		acc, err := p.ug.Account(ctx, uid)
		if err != nil {
			p.logger.Error("get account by user id failed", zap.Error(err), zap.String("userId", uid))
			continue
		}

		st, err := p.fg.Stats(ctx, uid)
		if err != nil {
			p.logger.Error("get stats by user id failed", zap.Error(err), zap.String("userId", uid))
			continue
		}

		lst, err := p.makeLiveStatus(ctx, uid)
		if err != nil {
			p.logger.Error("get live status by user id failed", zap.Error(err), zap.String("userId", uid))
			continue
		}

		pu := SearchedUser{
			User: SocialUser{
				UserWithExt: *mixer.UserWithExt(ctx, acc),
				UserSocial:  *mixer.UserSocial(st),
			},
			LiveStatus: *lst,
		}

		out.List = append(out.List, pu)
	}

	if len(out.List) == 0 {
		return &SearchAnchorResp{List: []SearchedUser{}}, nil
	}

	return &out, nil
}

// @Tags PK
// @Summary PK记录
// @Description PK记录
// @Produce json
// @Security HeaderAuth
// @param param body HistoryReq true "请求参数"
// @Success 200 {object} codec.Response{data=HistoryResp}
// @Router /api/v1/pk/history [get]
func (p *API) history(ctx *api.Context, req HistoryReq) (*HistoryResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ss, err := p.pm.UserHistory(ctx, uac.UserId, req.Cursor, 20)
	if err != nil {
		return nil, err
	}

	out := HistoryResp{List: make([]HistoryItem, 0, len(ss)), Cursor: req.Cursor + int64(len(ss))}
	for _, s := range ss {
		other := s.OtherSide(uac.UserId)

		anchor, err := p.ug.Account(ctx, other)
		if err != nil {
			p.logger.Error("get account failed", zap.Error(err), zap.String("userId", other))
			continue
		}

		is, _ := p.fg.IsFriend(ctx, uac.UserId, other)

		lst, err := p.makeLiveStatus(ctx, anchor.UserId)
		if err != nil {
			p.logger.Error("get live status by user id failed", zap.Error(err), zap.String("userId", anchor.UserId))
			continue
		}

		itm := HistoryItem{
			UserWithExt: *mixer.UserWithExt(ctx, anchor),
			IsFriend:    is,
			Winner:      s.Winner,
			LiveStatus:  *lst,
			StartAt:     s.CreatedAt.Unix(),
		}

		if s.Winner == uac.UserId && s.Winning > 1 {
			itm.Winning = s.Winning
		}

		out.List = append(out.List, itm)
	}

	return &out, nil
}

// @Tags PK
// @Summary 再来一场
// @Description
// @Produce json
// @Security HeaderAuth
// @param param body RestartReq true "请求参数"
// @Success 200 {object} codec.Response{data=InviteResp}
// @Router /api/v1/pk/restart [post]
func (p *API) restart(ctx *api.Context, req RestartReq) (*InviteResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	p.logger.Debug("restart", zap.String("userId", uac.UserId), zap.String("sessionId", req.SessionId))

	s, err := p.pm.GetSession2(ctx, req.SessionId)
	if err != nil {
		return nil, err
	}

	if s == nil {
		return nil, pk.ErrSessionsNotExist
	}

	if err := p.LinkingError(ctx, s.P1); err != nil {
		return nil, err
	}

	if err := p.LinkingError(ctx, s.P2); err != nil {
		return nil, err
	}

	l, err := p.pm.Restart(ctx, uac.UserId, req.SessionId)
	if err != nil {
		p.logger.Info("restart failed", zap.Error(err), zap.String("userId", uac.UserId))
		return nil, err
	}

	return &InviteResp{SessionId: l.Id}, nil
}

// @Tags PK
// @Summary 当前状态
// @Description
// @Produce json
// @Security HeaderAuth
// @param param body StatusReq true "请求参数"
// @Success 200 {object} codec.Response{data=Status}
// @Router /api/v1/pk/status [get]
func (p *API) status(ctx *api.Context, req StatusReq) (*Status, error) {
	sess, err := p.pm.UserCurSession(ctx, req.AnchorId)
	if err != nil {
		return nil, err
	}

	if sess == nil {
		return nil, biz.Legacy("user not in session")
	}

	st, err := p.pm.UserStatus(ctx, req.AnchorId, sess)
	if err != nil {
		return nil, err
	}

	out := Status{Status: st, SessionId: sess.ID.Hex()}
	for _, uid := range []string{sess.P1, sess.P2} {
		acc, err := p.ug.Account(ctx, uid)
		if err != nil {
			p.logger.Error("get account failed", zap.Error(err), zap.String("userId", uid))
			continue
		}

		out.Users = append(out.Users, *mixer.User(ctx, acc))
	}

	if st&protocol.LStatusPKFighting > 0 || st&protocol.LStatusPKPublish > 0 {
		out.PKDuration = int(sess.Duration / time.Second)
	} else {
		ac, err := p.pm.UserConfig2(ctx, req.AnchorId)
		if err != nil {
			p.logger.Error("get anchor config failed", zap.Error(err))
		} else {
			out.ConfigDuration = ac.PKDuration
		}
	}

	if st&protocol.LStatusPKLinked > 0 {
		out.LinkedAt = sess.LinkedAt.Unix()
	}

	return &out, nil
}

// @Tags PK
// @Summary 上一局结果
// @Description
// @Produce json
// @Security HeaderAuth
// @param param body LastResultReq true "请求参数"
// @Success 200 {object} codec.Response{data=LastResultResp}
// @Router /api/v1/pk/result [get]
func (p *API) lastResult(ctx *api.Context, req LastResultReq) (*LastResultResp, error) {
	sess, err := p.pm.UserCurSession(ctx, req.AnchorId)
	if err != nil {
		return nil, err
	}

	if sess == nil {
		return nil, biz.Legacy("user not in session")
	}

	if sess.Status != pk.SessionStatusLinked {
		return nil, biz.Legacy("pk not in linked")
	}

	out := LastResultResp{
		SessionId: sess.ID.Hex(),
		Winner:    sess.Winner,
	}

	for _, uid := range []string{sess.P1, sess.P2} {
		acc, err := p.ug.Account(ctx, uid)
		if err != nil {
			p.logger.Error("get account failed", zap.Error(err), zap.String("userId", uid))
			continue
		}

		out.Users = append(out.Users, ScoredUser{
			User:  *mixer.User(ctx, acc),
			Score: sess.Scores[uid],
		})
	}

	topU, err := p.pm.SessionUserTop(ctx, sess.ID.Hex(), req.AnchorId, 10)
	if err != nil {
		return nil, err
	}

	for r, tu := range topU {
		acc, err := p.ug.Account(ctx, tu.UserId)
		if err != nil {
			p.logger.Error("get account failed", zap.Error(err), zap.String("userId", tu.UserId))
			continue
		}

		ru, err := mixer.RoomUser(ctx, req.AnchorId, tu.UserId)
		if err != nil {
			p.logger.Error("get room user failed", zap.Error(err), zap.String("userId", tu.UserId))
			continue
		}

		out.Top = append(out.Top, RankedUser{
			Rank:     r + 1,
			User:     *mixer.User(ctx, acc),
			RoomUser: *ru,
			Score:    int(tu.Score),
		})
	}

	return &out, nil
}

type ContributionReq struct {
	SessionId string `form:"sessionId" binding:"required"` // pk session id
	AnchorId  string `form:"anchorId" binding:"required"`  // query anchor id
}

// @Tags PK
// @Summary 贡献榜
// @Description 贡献榜
// @Produce json
// @Security HeaderAuth
// @param param query ContributionReq true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveAudienceResponse}
// @Router /api/v1/pk/contribution [get]
func (p *API) contribution(ctx *api.Context, req ContributionReq) (*types.LiveAudienceResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	r, err := p.lm.RoomByUserId2(ctx, req.AnchorId)
	if err != nil {
		return nil, errors.New("get audience failed")
	}

	session, err := p.pm.GetSession2(ctx, req.SessionId)
	if err != nil {
		return nil, err
	}

	if session == nil {
		return nil, errors.New("pk session not found")
	}

	const maxAudience = 200

	isPlayer := session.P1 == uac.UserId || session.P2 == uac.UserId

	users, err := p.pm.SessionUserTop(ctx, req.SessionId, req.AnchorId, maxAudience)
	if err != nil {
		return nil, err
	}

	var ranked *pk.RankedUser
	if index := slices.IndexFunc(users, func(u pk.RankedUser) bool {
		return u.UserId == uac.UserId
	}); index != -1 {
		ranked = &users[index]
	} else {
		r, err := p.pm.SessionUserRank(ctx, req.SessionId, req.AnchorId, uac.UserId)
		if err != nil {
			return nil, err
		}
		ranked = r
	}

	var (
		audiences   = make([]types.Audience, 0, len(users))
		showContrib = ranked.Rank <= maxAudience && ranked.Score > 0
	)

	p.logger.Debug("get pk contribution",
		zap.String("anchorId", req.AnchorId),
		zap.String("sessionId", req.SessionId),
		zap.Any("session", session),
		zap.Any("audience", users),
		zap.Bool("showContrib", showContrib),
		zap.Any("ranked", ranked),
	)

	for _, u := range users {
		roomUser, err := mixer.RoomUser(ctx, r.UserId, u.UserId)
		if err != nil {
			return nil, err
		}

		acc, err := p.ug.Account(ctx, u.UserId)
		if err != nil {
			p.logger.Error("get account failed",
				zap.Error(err),
				zap.String("userId", u.UserId))
			continue
		}

		aud := types.Audience{
			User:     *mixer.User(ctx, acc),
			RoomUser: *roomUser,
			Rank:     u.Rank,
			Score:    u.Score,
		}

		if u.Score == 0 {
			aud.RankDesc = "-"
		} else {
			aud.RankDesc = strconv.Itoa(int(u.Rank))
		}

		if ((showContrib && (u.Rank-ranked.Rank)*(u.Rank-ranked.Rank) <= 25) || u.UserId == uac.UserId || isPlayer) && u.Score > 0 {
			aud.ScoreDesc = i3n.T(ctx, "%s points", i18n.NumberString(ctx, float64(u.Score)))
		} else {
			aud.ScoreDesc = ""
		}

		audiences = append(audiences, aud)
	}

	roomUser, err := mixer.RoomUser(ctx, r.UserId, uac.UserId)
	if err != nil {
		return nil, err
	}

	mime := &types.AudienceRank{
		Audience: &types.Audience{
			User:     *mixer.User(ctx, uac),
			RoomUser: *roomUser,
			Score:    ranked.Score, // 榜单分
			Rank:     ranked.Rank,
		},
	}

	if ranked.Score > 0 {
		mime.Mark = i3n.T(ctx, "Gifted")
	} else {
		mime.Mark = i3n.T(ctx, "Not assisted")
	}

	if showContrib {
		mime.ScoreDesc = i3n.T(ctx, "%s points", i18n.NumberString(ctx, float64(ranked.Score)))
		mime.RankDesc = strconv.Itoa(int(ranked.Rank))
	} else {
		mime.ScoreDesc = ""
		mime.RankDesc = "-"
	}

	return &types.LiveAudienceResponse{List: audiences, Mine: mime}, nil
}

type PKLiveReq struct {
	Cursor int64 `json:"cursor"` // 下一页数据cursor
}

type PKLiveResponse struct {
	List   []types.Room `json:"list"`
	Cursor int64        `json:"cursor,omitempty"` // 下一页数据cursor,最后一页为null
}

const (
	pkLivePageSize = 200
)

// @Tags PK
// @Summary ✅PK列表
// @Description PK列表，按直播间热度排序, 和上次数据有可能重复需要客户端去重
// @Produce json
// @Security HeaderAuth
// @Param param query PKLiveReq true "请求参数"
// @Success 200 {object} codec.Response{data=PKLiveResponse}
// @Router /api/v1/pk/live [get]
func (p *API) PKLive(ctx *api.Context, req PKLiveReq) (*PKLiveResponse, error) {
	res := &PKLiveResponse{
		List:   make([]types.Room, 0),
		Cursor: 0,
	}

	uac, _ := ctx.User()

	anchors, err := p.pm.ListLiveUsers(ctx)
	if err != nil {
		return nil, err
	}

	sort.Slice(anchors, func(i, j int) bool {
		ri, err := p.lm.RoomByUserId2(ctx, anchors[i])
		if err != nil {
			return false
		}

		rj, err := p.lm.RoomByUserId2(ctx, anchors[j])
		if err != nil {
			return false
		}

		return ri.Popularity > rj.Popularity
	})

	if req.Cursor > int64(len(anchors)) {
		return res, nil
	}

	anchors = anchors[req.Cursor:]
	if len(anchors) > pkLivePageSize {
		anchors = anchors[:pkLivePageSize]
	}

	res.Cursor = req.Cursor + int64(len(anchors))

	for _, userId := range anchors {
		ri, err := p.lm.RoomByUserId2(ctx, userId)
		if err != nil || !ri.IsLiving() {
			continue
		}

		r, err := mixer.Room(ctx, lo.Ternary(uac != nil, uac.UserId, ""), p.ug, p.fg, p.sm, ri)
		if err != nil {
			continue
		}

		res.List = append(res.List, *r)
	}

	return res, nil
}
