package connect

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"go.uber.org/zap"
)

type AppId string

const (
	Mobi     AppId = "mobi" // 手机
	Facebook AppId = "facebook"
	Google   AppId = "google"
	Twitter  AppId = "twitter"
	AppleId  AppId = "appleid"
)

type accBindItem struct {
	Id   AppId  `json:"id"`   // 渠道ID
	Name string `json:"name"` // 三方昵称
}

type accBindsResp struct {
	ShowId string         `json:"showId"` // 用户自定义ID
	List   []*accBindItem `json:"list"`   // 绑定列表
}

// @Tags 账号相关
// @Summary 账号与安全
// @Description 绑定的账户列表
// @Produce json
// @Security HeaderAuth
// @Param param query api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=accBindsResp}
// @Router /api/v1/account/binds [get]
func (s *apis) Binds(ctx *api.Context, _ api.EmptyReq) (*accBindsResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var resp accBindsResp
	resp.ShowId = uac.ShowId

	cs, err := s.um.List(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	resp.List = lo.Map(cs, func(item *ulink.Connect, _ int) *accBindItem {
		return &accBindItem{Id: AppId(item.AppId), Name: item.Nickname}
	})

	return &resp, nil
}

// @Tags 账号相关
// @Summary 申请注销账号
// @Description 申请注销账号
// @Produce json
// @Security HeaderAuth
// @Param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/account/unregister [post]
func (s *apis) Unregister(ctx *api.Context, _ api.EmptyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	s.log.Info("unregister request", zap.String("userId", uac.UserId))
	return nil, nil
}
