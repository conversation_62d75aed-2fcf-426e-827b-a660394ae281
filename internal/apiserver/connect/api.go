package connect

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"go.uber.org/zap"
)

func API(r *api.Router, um *ulink.Manager, jwt *auth.JWT, vnd log.Vendor) {
	s := &apis{um: um, jwt: jwt, log: vnd.Scope("api.connect")}
	r.POST("/connect/auth", api.Generic(s.auth))
	r.GET("/connect/with/:name", api.Request(s.with))
	ar := r.WithAuth()
	{
		ar.POST("/connect/bind", api.Generic(s.bind))
		ar.POST("/connect/unbind", api.Generic(s.unbind))
		// account
		ar.GET("/account/binds", api.Generic(s.Binds))
		ar.POST("/account/unregister", api.Generic(s.Unregister))
	}
}

type apis struct {
	um  *ulink.Manager
	jwt *auth.JWT
	log *zap.Logger
}

type connAuthReq struct {
	Id string `json:"id"` // 三方渠道
}

type connAuthResp struct {
	AuthUrl string `json:"authUrl"` // 三方授权网页地址
}

// @Tags 三方登录
// @Summary 获取授权地址
// @Description 获取到url后用webview打开
// @Produce json
// @Param param body connAuthReq true "请求参数"
// @Success 200 {object} codec.Response{data=connAuthResp}
// @Router /api/v1/connect/auth [post]
func (s *apis) auth(ctx *api.Context, req connAuthReq) (*connAuthResp, error) {
	hdr, err := packHeaders(ctx)
	if err != nil {
		return nil, err
	}
	url, err := s.um.AuthURL(ctx, req.Id, hdr)
	if err != nil {
		return nil, err
	}
	return &connAuthResp{AuthUrl: url}, nil
}

type connBindReq struct {
	Id string `json:"id"` // 需要绑定的三方渠道
}

type connBindResp struct {
	AuthUrl string `json:"authUrl"` // 三方授权网页地址
}

// @Tags 三方登录
// @Summary 绑定三方账号
// @Description 获取到url后用webview打开
// @Produce json
// @Security HeaderAuth
// @Param param body connBindReq true "请求参数"
// @Success 200 {object} codec.Response{data=connBindResp}
// @Router /api/v1/connect/bind [post]
func (s *apis) bind(ctx *api.Context, req connBindReq) (*connBindResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	url, err := s.um.BindURL(ctx, req.Id, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &connBindResp{AuthUrl: url}, nil
}

type connUnbindReq struct {
	Id string `json:"id"` // 需要解绑的三方渠道
}

// @Tags 三方登录
// @Summary 解绑三方账号
// @Description 解绑三方账号
// @Produce json
// @Security HeaderAuth
// @Param param body connUnbindReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/connect/unbind [post]
func (s *apis) unbind(ctx *api.Context, req connUnbindReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.um.Forget(ctx, uac.UserId, req.Id); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}
