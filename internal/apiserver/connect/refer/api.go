package refer

import (
	"net/url"
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/halloween"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
)

func API(r *api.Router, ml *mux.Locker, lm *luckyroom.Manager, hm *halloween.Manager) {
	s := &apis{lm: lm, hm: hm}
	ar := r.WithAuth(ml.Middleware(mux.WithPOST))
	{
		ar.POST("/referrer/touch", api.Generic(s.touchReferrer))
	}
}

type apis struct {
	lm *luckyroom.Manager
	hm *halloween.Manager
}

type shareTraceData struct {
	Channel    string `json:"channel"`
	ParamsData string `json:"paramsData"`
}

type touchReferrerReq struct {
	ShareTrace *shareTraceData `json:"shareTrace"` // shareTrace获取到的数据
}

// @Tags 账号相关
// @Summary 邀请注册上报
// @Description 有refer值时再调用这个接口
// @Produce json
// @Security HeaderAuth
// @Param param body touchReferrerReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/referrer/touch [post]
func (s *apis) touchReferrer(ctx *api.Context, req touchReferrerReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	stp, _ := url.ParseQuery(req.ShareTrace.ParamsData)
	refer := stp.Get("refer")

	if strings.HasPrefix(refer, "LR") {
		if err := s.lm.ReferrerChk(ctx, app.DeviceId(ctx), uac.UserId, refer); err != nil {
			return nil, err
		}
	}

	if strings.HasPrefix(refer, "HW") {
		if err := s.hm.ReferrerChk(ctx, app.DeviceId(ctx), uac.UserId, refer); err != nil {
			return nil, err
		}
	}

	return &api.EmptyResp{}, nil
}
