package connect

import (
	"net/http"

	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

var fwdHeaders = []string{app.HdrCountry, app.HdrTimezone, app.HdrDeviceId}

func packHeaders(ctx *api.Context) (string, error) {
	hdr := make(http.Header)
	for _, k := range fwdHeaders {
		if v := ctx.GetHeader(k); v != "" {
			hdr.Set(k, v)
		}
	}
	return sonic.MarshalString(hdr)
}

func unpackHeaders(raw string, to http.Header) error {
	var hdr http.Header
	if err := sonic.UnmarshalString(raw, &hdr); err != nil {
		return err
	}
	for k := range hdr {
		to.Set(k, hdr.Get(k))
	}
	return nil
}
