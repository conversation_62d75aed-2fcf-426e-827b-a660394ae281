package connect

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"go.uber.org/zap"
)

const (
	toAuth = "kakolive://login"
	toBind = "kakolive://bind"
)

type connWithReq struct {
	State string `form:"state"`
	Code  string `form:"code"`
}

func (s *apis) with(ctx *api.Context, req connWithReq) error {
	state := ulink.ParseState(req.State)
	switch state.Type {
	case ulink.TypLogin:
		if err := unpackHeaders(state.Data, ctx.Request.Header); err != nil {
			return err
		}
		return s.doLogin(ctx, state, req.Code)
	case ulink.TypBind:
		return s.doBind(ctx, state, req.Code, state.Data)
	default:
		return errors.New("unknown state")
	}
}

func (s *apis) doLogin(ctx *api.Context, state *ulink.State, code string) error {
	acc, err := s.um.AuthCode(ctx, ctx.Param("name"), state, code)
	if err != nil {
		return s.redirect(ctx, toAuth, err)
	}
	token, expire, err := s.jwt.TokenGenerator(ctx, acc)
	if err != nil {
		return s.redirect(ctx, toAuth, err)
	}
	return s.redirect(ctx, toAuth, "userId", acc.UserId, "token", token, "expire", expire.Unix())
}

func (s *apis) doBind(ctx *api.Context, state *ulink.State, code string, userId string) error {
	if err := s.um.BindCode(ctx, ctx.Param("name"), state, code, userId); err != nil {
		return s.redirect(ctx, toBind, err)
	}
	return s.redirect(ctx, toBind, "userId", userId)
}

func (s *apis) redirect(ctx *api.Context, to string, args ...any) error {
	u, err := url.Parse(to)
	if err != nil {
		return err
	}

	q := make(url.Values)
	if len(args) > 0 {
		offset := 0
		if err, is := args[0].(error); is {
			offset++
			if bErr, is := biz.As(err); is {
				q.Add("code", strconv.Itoa(bErr.Code))
				q.Add("msg", bErr.Msg)
			} else {
				q.Add("code", strconv.Itoa(biz.ErrSystem))
				s.log.Info("auth failed", zap.Error(err))
			}
		} else {
			q.Add("code", "0")
			q.Add("msg", "")
		}
		for i := offset; i < len(args); i += 2 {
			switch v := args[i].(type) {
			case string:
				q.Add(v, fmt.Sprintf("%v", args[i+1]))
			}
		}
	}
	u.RawQuery = q.Encode()

	ctx.Redirect(http.StatusTemporaryRedirect, u.String())
	return nil
}
