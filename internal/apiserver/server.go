package apiserver

import (
	"context"
	"errors"
	"net"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	ginmetrics "gitlab.sskjz.com/go/gin-metrics"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/up"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/geoip"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"gitlab.sskjz.com/overseas/live/osl/pkg/translate"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// @title OSL API
// @version 1.0
// @BasePath /
// @description Overseas Live API
// @schemes http https
// @securityDefinitions.apiKey HeaderAuth
// @in header
// @name Authorization
func NewServer(lc fx.Lifecycle, desc *conf.Setting, dm *up.Daemon, geo *geoip.Query, mix *mixer.Mixer, vnd log.Vendor) *gin.Engine {
	gin.SetMode(gin.ReleaseMode)
	api.SetErrMsg(i18nErrMsg)

	if desc.Debug {
		gin.SetMode(gin.DebugMode)
		api.SetErrMsg(debugErrMsg)
	}

	logger := vnd.Scope("apiserver")

	g := gin.New()
	g.Use(cors.New(cors.Config{
		AllowAllOrigins:  true,
		AllowHeaders:     app.AllowHeaders(),
		AllowCredentials: true,
	}))

	g.Use(api.WithLogger(logger))
	g.Use(ginzap.RecoveryWithZap(logger, true))
	g.Use(rlog.RequestLogger(logger))
	g.Use(i3n.Middleware())
	g.Use(translate.Middleware(translate.WithDefaultLang("en")))
	g.Use(geo.Middleware())
	g.Use(mix.Middleware())

	m := ginmetrics.GetMonitor()
	m.SetMetricPath("/health/ping") // ignore ping
	m.SetDuration([]float64{0.01, 0.03, 0.2, 0.5, 1})
	m.UseWithoutExposingEndpoint(g)

	dm.StatusApi("/health", g)

	hs := &http.Server{Addr: desc.ApiServer.Addr, Handler: g}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			ln, err := net.Listen("tcp", hs.Addr)
			if err != nil {
				return err
			}

			go func() {
				logger.Info("server start", zap.String("addr", hs.Addr))
				if err := hs.Serve(ln); err != nil {
					if !errors.Is(err, http.ErrServerClosed) {
						logger.Fatal("http server error", zap.Error(err))
						return
					}
				}
				logger.Info("server stop")
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			c, f := context.WithTimeout(ctx, time.Second*10)
			defer f()
			return hs.Shutdown(c)
		},
	})

	return g
}
