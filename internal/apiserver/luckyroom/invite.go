package luckyroom

import (
	"strings"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type inviteRecord struct {
	User *types.User `json:"user"`
	Time int64       `json:"time"` // 注册时间
}

type inviteInfoResp struct {
	Today    int64          `json:"today"`    // 今日邀请获得
	Total    int64          `json:"total"`    // 累计邀请获得
	Records  []inviteRecord `json:"records"`  // 邀请注册列表
	ShareUrl string         `json:"shareUrl"` // 分享链接
}

// @Tags 幸运房间
// @Summary 获取邀请信息
// @Description 获取邀请信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/luckyroom/inviting [get]
func (s *API) inviteInfo(ctx *api.Context, _ api.EmptyReq) (*inviteInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ii, err := s.lmm.InviteInfo(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	shareUrl := "https://www.kako.live/download.html?refer=" + luckyroom.ReferCode(uac)
	if dbg.Ing() {
		shareUrl = strings.Replace(shareUrl, "www.kako.live", "www-test.kako.live", 1)
		shareUrl += "&test=true"
	}

	return &inviteInfoResp{
		Today: ii.EarnToday,
		Total: ii.EarnTotal,
		Records: lo.Map(ii.Records, func(r *luckyroom.Invite, _ int) inviteRecord {
			return inviteRecord{
				User: mixer.User(ctx, mixer.NoErr(s.ug.Account(ctx, r.NewUserId))),
				Time: r.CreatedAt.Unix(),
			}
		}),
		ShareUrl: shareUrl,
	}, nil
}
