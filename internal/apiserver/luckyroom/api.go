package luckyroom

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type API struct {
	lmm *luckyroom.Manager
	ug  user.Getter
	gm  *gift.Manager
	lm  *live.Manager
	im  *interact.Manager

	dm *device.Manager

	um *ulink.Manager

	imm    *im.Manager // im 发送系统消息
	logger *zap.Logger
}

type PrizeBoxProgress struct {
	Percent int64 `json:"percent"` // 达成百分比
	Opened  bool  `json:"opened"`  // 是否已经开奖
}

// @Tags 幸运房间
// @Summary 获取幸运房间的进度
// @Description 获取幸运房间的进度
// @Accept json
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=PrizeBoxProgress}
// @Router /api/v1/luckyroom/prizebox/progress [get]
func (a *API) PrizeBoxProgress(ctx *api.Context, _ api.EmptyReq) (*PrizeBoxProgress, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	amount, err := a.lmm.UserAmount2(ctx, uac.UserId, time.Now())
	if err != nil {
		return nil, err
	}

	var opened bool

	if amount >= luckyroom.OpenBoxRequired {
		opened2, err := a.lmm.UserOpened(ctx, uac.UserId, time.Now())
		if err != nil {
			return nil, err
		}
		opened = opened2
	}

	return &PrizeBoxProgress{Percent: int64(luckyroom.PrizeBoxProgress(amount) * 100), Opened: opened}, nil
}

type PrizeBoxStatus struct {
	TotalOpened     int64   `json:"totalOpened"`     // 总开奖次数
	Countdown       int64   `json:"countdown"`       // 倒计时秒数
	SentDiamond     int64   `json:"sentDiamond"`     // 自己当前的送礼流水
	Progress        float64 `json:"progress"`        // 进度百分比
	Opened          bool    `json:"opened"`          // 自己是否已经领取宝箱
	GotDiamond      int64   `json:"gotDiamond"`      // 自己领取的钻石
	OpenBoxRequired int64   `json:"openBoxRequired"` // 打开宝箱需要的流水
}

// @Tags 幸运房间
// @Summary 获取幸运房间的宝箱状态
// @Description 获取幸运房间的宝箱状态
// @Accept json
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=PrizeBoxStatus}
// @Router /api/v1/luckyroom/prizebox/status [get]
func (a *API) PrizeBoxStatus(ctx *api.Context, _ api.EmptyReq) (*PrizeBoxStatus, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		at = time.Now()
	)

	bss, err := a.lmm.BoxStatus(ctx, uac.UserId, at)
	if err != nil {
		return nil, err
	}

	status := &PrizeBoxStatus{
		Countdown:       time.Date(at.Year(), at.Month(), at.Day()+1, 0, 0, 0, 0, at.Location()).Unix() - at.Unix(),
		SentDiamond:     bss.Amount,
		Progress:        bss.Progress * 100,
		TotalOpened:     bss.TotalOpened,
		OpenBoxRequired: luckyroom.OpenBoxRequired,
	}

	if time.Now().Before(luckyroom.StartTime) {
		status.Countdown = -1
	}

	if bss.GotDiamond != nil {
		status.Opened = true
		status.GotDiamond = *bss.GotDiamond
	}

	return status, nil
}

type PrizeBoxHistoryItem struct {
	User       *types.User `json:"user"`
	GotDiamond int64       `json:"gotDiamond"`
	Timestamp  int64       `json:"timestamp"` // unix timestamp
}

type PrizeBoxHistory struct {
	List []*PrizeBoxHistoryItem `json:"list"` // 最多100条
}

// @Tags 幸运房间
// @Summary 获取幸运房间的历史开奖记录
// @Description 获取幸运房间的历史开奖记录
// @Accept json
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=PrizeBoxHistory}
// @Router /api/v1/luckyroom/prizebox/history [get]
func (a *API) PrizeBoxHistory(ctx *api.Context, _ api.EmptyReq) (*PrizeBoxHistory, error) {
	items, err := a.lmm.PrizeBoxHistory(ctx, time.Now())
	if err != nil {
		return nil, err
	}

	resp := &PrizeBoxHistory{
		List: make([]*PrizeBoxHistoryItem, 0, len(items)),
	}

	for _, item := range items {
		acc, err := a.ug.Account(ctx, item.UserId)
		if err != nil {
			return nil, err
		}

		resp.List = append(resp.List, &PrizeBoxHistoryItem{
			User:       mixer.User(ctx, acc),
			GotDiamond: item.Diamond,
			Timestamp:  item.Timestamp,
		})
	}

	slices.SortFunc(resp.List, func(a, b *PrizeBoxHistoryItem) int {
		return int(b.Timestamp - a.Timestamp)
	})

	if len(resp.List) > 100 {
		resp.List = resp.List[:100]
	}

	return resp, nil
}

type RankTopItem struct {
	User  *types.User `json:"user"`
	Score int64       `json:"score"`
}

type RankTop struct {
	List []*RankTopItem `json:"list"`
}

// @Tags 幸运房间
// @Summary 获取幸运房间的送礼排行榜
// @Description 获取幸运房间的送礼排行榜
// @Accept json
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=RankTop}
// @Router /api/v1/luckyroom/rank/top [get]
func (a *API) RankTop(ctx *api.Context, _ api.EmptyReq) (*RankTop, error) {
	items, err := a.lmm.TopN(ctx, time.Now(), 3)
	if err != nil {
		return nil, err
	}

	resp := &RankTop{
		List: make([]*RankTopItem, 0, len(items)),
	}

	for _, item := range items {
		acc, err := a.ug.Account(ctx, item.UserId)
		if err != nil {
			return nil, err
		}

		resp.List = append(resp.List, &RankTopItem{
			User:  mixer.User(ctx, acc),
			Score: item.Diamond,
		})
	}

	return resp, nil
}

type OpenPrizeBoxReq struct {
	DeviceId string `json:"deviceId"`
}

type OpenPrizeBoxResp struct {
	GotDiamond int64 `json:"gotDiamond"`
}

// @Tags 幸运房间
// @Summary 打开宝箱
// @Description 打开宝箱
// @Accept json
// @Produce json
// @Security HeaderAuth
// @Param deviceId path string true "deviceId"
// @Success 200 {object} codec.Response{data=OpenPrizeBoxResp}
// @Router /api/v1/luckyroom/prizebox/open [post]
func (a *API) OpenPrizeBox(ctx *api.Context, req OpenPrizeBoxReq) (*OpenPrizeBoxResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if _, err := a.um.UserApp(ctx, uac.UserId, "mobi"); err != nil {
		if errors.Is(err, user.ErrAccountNotExists) {
			a.logger.Info("open prize box without mobi bind", zap.String("userId", uac.UserId), zap.String("showId", uac.ShowId))
			return nil, biz.Legacy("Vá para [Perfil] para vincular seu número de celular")
		}
		return nil, err
	}

	devId := req.DeviceId
	if devId == "" {
		dev, err := a.dm.Last(ctx, uac.UserId)
		if err != nil {
			return nil, err
		}
		devId = dev.Id
	}

	p, err := a.lmm.OpenPrizeBox(ctx, uac.UserId, devId, ctx.ClientIP(), time.Now())
	if err != nil {
		return nil, err
	}

	text := `Parabéns por completar a tarefa de evento na Sala de coinzinhos e ganhou %d coins no Baú de coins. A recompensa foi enviada para a sua conta, por favor, verifique!`

	if err := a.imm.SendSystemNoticeTextToUser(context.TODO(), uac.UserId, fmt.Sprintf(text, p.Diamond)); err != nil {
		a.logger.Error("send system notice text to user failed", zap.Error(err))
	}

	return &OpenPrizeBoxResp{GotDiamond: p.Diamond}, nil
}
