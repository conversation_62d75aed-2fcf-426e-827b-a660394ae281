package luckyroom

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type balanceResp struct {
	Coins int64 `json:"coins"` // 体验币余额
}

// @Tags 幸运房间
// @Summary 获取体验币余额
// @Description 获取体验币余额
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=balanceResp}
// @Router /api/v1/luckyroom/balance [get]
func (s *API) balance(ctx *api.Context, _ api.EmptyReq) (*balanceResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	coins, err := s.lmm.Balance(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &balanceResp{Coins: coins.IntPart()}, nil
}

// @Tags 幸运房间
// @Summary 送礼
// @Description 送礼
// @Produce json
// @Security HeaderAuth
// @Param param body types.GiftSendRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.GiftSendResponse}
// @Router /api/v1/luckyroom/gift/send [post]
func (s *API) sendGift(ctx *api.Context, req types.GiftSendRequest) (*types.GiftSendResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	gft, err := s.gm.GiftById(req.GiftId)
	if err != nil {
		return nil, err
	}

	if len(req.ComboId) > 512 {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	ri, err := s.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if !ri.IsLuckyRoom() {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params: not lucky room")
	}

	if !luckyroom.Open() {
		return nil, biz.NewError(biz.ErrInvalidParam, "Lucky room is not open for this time")
	}

	if !luckyroom.Accept(app.DeviceType(ctx), app.Version(ctx), app.BuildId(ctx)) {
		return nil, biz.NewError(biz.ErrInvalidParam, "Lucky room is not open for this version")
	}

	var (
		sr  *interact.SendResult
		lsr *interact.SendGiftIndLuckyResult
		now = time.Now()
	)

	if lsr, err = s.im.SendGiftIndLucky(context.TODO(), "", req.RoomId, uac.UserId, req.ComboId, req.GiftId, req.GiftCount, now); err == nil {
		sr = &lsr.SendResult
	}

	if err != nil {
		return nil, err
	}

	var resp types.GiftSendResponse

	if sr.Wallet != nil {
		resp.Wallet = &types.UserWallet{
			Diamond: sr.Wallet.BVal(fund.PTypeDiamond).IntPart(),
		}
	}

	if !sr.Pows.Empty() {
		resp.LuckDraw = &types.LuckDraw{
			GiftId:    req.GiftId,
			GiftCount: req.GiftCount,
			Prizes:    sr.Pows.Protocol(gft.Diamond),
		}
	}

	return &resp, nil
}
