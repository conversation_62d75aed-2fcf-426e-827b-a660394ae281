package luckyroom

import (
	"time"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Invoke(
	r *api.Router, hm *mux.Locker,
	lmm *luckyroom.Manager,
	ug user.Getter,
	gm *gift.Manager,
	lm *live.Manager,
	im *interact.Manager,
	ch *cache.Handler,
	imm *im.Manager,
	dm *device.Manager,
	um *ulink.Manager,
	vnd log.Vendor,
) {
	apis := API{
		lmm:    lmm,
		ug:     ug,
		gm:     gm,
		lm:     lm,
		im:     im,
		imm:    imm,
		dm:     dm,
		um:     um,
		logger: vnd.Scope("luckyroom.api"),
	}

	gg := r.Group("/luckyroom")
	{
		gg.GET("/info", api.Generic(apis.info))

		gg.GET("/rank/top", ch.Middleware(cache.WithExpire(time.Millisecond*500)), api.Generic(apis.RankTop))
	}

	rg := r.WithAuth().Group("/luckyroom", hm.Middleware(mux.WithPOST))
	{
		rg.GET("/balance", api.Generic(apis.balance))
		rg.POST("/gift/send", api.Generic(apis.sendGift))

		rg.GET("/prizebox/progress", api.Generic(apis.PrizeBoxProgress))
		rg.GET("/prizebox/history", api.Generic(apis.PrizeBoxHistory))
		rg.GET("/prizebox/status", api.Generic(apis.PrizeBoxStatus))

		rg.POST("/prizebox/open", api.Generic(apis.OpenPrizeBox))

		rg.GET("/inviting", api.Generic(apis.inviteInfo))
	}
}
