package luckyroom

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type infoResp struct {
	Whatsapp     string `json:"whatsapp"`     // 平台客服
	StartTime    int64  `json:"startTime"`    // 开始时间：unix秒
	EndTime      int64  `json:"endTime"`      // 结束时间：unix秒
	DailyCoins   int64  `json:"dailyCoins"`   // 每日获得体验币
	InviteReward int64  `json:"inviteReward"` // 邀请获得体验币
}

// @Tags 幸运房间
// @Summary 获取活动信息
// @Description 获取活动信息
// @Produce json
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/luckyroom/info [get]
func (s *API) info(ctx *api.Context, _ api.EmptyReq) (*infoResp, error) {
	return &infoResp{
		Whatsapp:     "+5511980789526",
		StartTime:    luckyroom.StartTime.Unix(),
		EndTime:      luckyroom.EndTime.Unix(),
		DailyCoins:   luckyroom.DailyCoins,
		InviteReward: luckyroom.InviteReward,
	}, nil
}
