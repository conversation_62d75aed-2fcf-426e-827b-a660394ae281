package sflytext

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/room/flytext"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Invoke(r *api.Router) {
	apis := &apis{}
	{
		r.GET("/flytext/templates", api.Generic(apis.ListPresetTemplate))
	}
}

type apis struct {
}

type ListPresetTemplateReq struct {
}

type ListPresetTemplateResp struct {
	List []flytext.Template `json:"list"`
}

// @Summary 获取飞字预设模板
// @Description 获取飞字预设模板
// @Tags 飞字
// @Produce json
// @Success 200 {object} codec.Response{data=ListPresetTemplateResp} "响应参数"
// @Router /api/v1/flytext/templates [get]
func (a *apis) ListPresetTemplate(ctx *api.Context, req ListPresetTemplateReq) (*ListPresetTemplateResp, error) {
	return &ListPresetTemplateResp{List: flytext.ListPresetTemplate()}, nil
}
