package fansclub

import (
	"cmp"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub/fctask"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/ranklist"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Invoke(
	r *api.Router, hm *mux.Locker,
	ug user.Getter, gm *gift.Manager, lm *live.Manager, fc *fclub.Manager, ft *fctask.Manager, fm *follow.Manager, om *room.Manager, rm *ranklist.Manager,
) {
	s := &apis{ug: ug, gm: gm, lm: lm, fc: fc, ft: ft, fm: fm, om: om, rm: rm}
	g := r.WithAuth().Group("/fansclub", hm.Middleware(mux.WithPOST))
	{
		g.GET("/info", api.Generic(s.info))
		g.GET("/anchor", api.Generic(s.anchor))
		g.POST("/join", api.Generic(s.join))
		g.POST("/leave", api.Generic(s.leave))
		g.GET("/members", api.Generic(s.members))
	}
}

type apis struct {
	ug user.Getter
	gm *gift.Manager
	lm *live.Manager
	fc *fclub.Manager
	ft *fctask.Manager
	fm *follow.Manager
	om *room.Manager
	rm *ranklist.Manager
}

type infoReq struct {
	RoomId string `form:"roomId"` // 房间Id
}

type userTask struct {
	Type   fctask.Type     `json:"type"`           // 任务类型：1 人气票 2 粉丝灯牌 3 赠送礼物 4 观看直播
	Gift   *types.LiteGift `json:"gift,omitempty"` // 关联礼物信息（类型1和2才有）
	Status int             `json:"status"`         // 0 未完成 1 已完成
	Gain   int             `json:"gain"`           // 已获得亲密度
	Curr   int             `json:"curr"`           // 当前进度值（类型3和4才有）
	Max    int             `json:"max"`            // 进度最大值（类型3和4才有）
}

type rankInfo struct {
	Rank     int   `json:"rank"`     // 排名，从1开始
	GapScore int64 `json:"gapScore"` // 分数差距，如果rank==1，此值为与第二名差距；如果rank>1 && rank <= 100，此份数值为与上一名差距，如果rank==0 || rank >100 此值为上榜差距
}

type infoResp struct {
	Anchor       *types.User      `json:"anchor"`                 // 主播信息
	Members      int              `json:"members"`                // 成员数量
	JoinPrice    int              `json:"joinPrice"`              // 加入价格
	JoinTime     int64            `json:"joinTime"`               // 加入时间：unix秒（未加入时为0）
	JoinDays     int              `json:"joinDays"`               // 入团天数（未加入时为0）
	Inactive     bool             `json:"inactive"`               // 熄灭状态
	ActivateGift *types.LiteGift  `json:"activateGift,omitempty"` // 激活时送的礼物（熄灭时才有值）
	LevelInfo    *types.LevelInfo `json:"levelInfo,omitempty"`    // 等级信息（未加入时为null）
	TodayExp     int              `json:"todayExp"`               // 今日经验值
	Tasks        []userTask       `json:"tasks"`                  // 任务列表
	RankInfo     rankInfo         `json:"rankInfo"`               // 人气榜信息
}

// @Tags 粉丝团
// @Summary 直播间粉丝团信息(用户侧)
// @Description 直播间粉丝团信息(用户侧)
// @Produce json
// @Security HeaderAuth
// @Param param query infoReq true "请求参数"
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/fansclub/info [get]
func (s *apis) info(ctx *api.Context, req infoReq) (*infoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := s.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	anchor, err := s.ug.Account(ctx, ri.UserId)
	if err != nil {
		return nil, err
	}

	membersCnt, err := s.fc.Members(ctx, ri.UserId)
	if err != nil {
		return nil, err
	}

	resp := &infoResp{
		Anchor:    mixer.User(ctx, anchor),
		Members:   membersCnt,
		JoinPrice: fclub.JoinPrice,
	}

	lv, err := s.fc.LevelInfo(ctx, ri.UserId, uac.UserId)
	if err != nil {
		return nil, err
	} else if lv.Valid() {
		now := time.Now()
		resp.JoinTime = lv.JoinTime.Unix()
		resp.JoinDays = int(now.Sub(lv.JoinTime).Hours()/24) + 1
		resp.Inactive = !lv.Active
		if resp.Inactive {
			resp.ActivateGift = mixer.LiteGift(mixer.NoErr(s.gm.GiftById(fctask.Gift1Id)))
		}
		resp.LevelInfo = mixer.LevelInfo(lv.Detail())
		resp.TodayExp, _ = s.ft.TodayIntimacy(ctx, now, ri.UserId, uac.UserId)
		for _, tt := range fctask.TTypes {
			ti, err := s.ft.TaskInfo(ctx, now, ri.UserId, uac.UserId, tt)
			if err != nil {
				return nil, err
			}
			ut := userTask{
				Type:   tt,
				Status: ti.Status,
				Gain:   ti.Gain,
				Curr:   ti.Curr,
				Max:    ti.Max,
			}
			switch tt {
			case fctask.TTGift1:
				ut.Gift = mixer.LiteGift(mixer.NoErr(s.gm.GiftById(fctask.Gift1Id)))
			case fctask.TTGift2:
				ut.Gift = mixer.LiteGift(mixer.NoErr(s.gm.GiftById(fctask.Gift2Id)))
			}
			resp.Tasks = append(resp.Tasks, ut)
		}
		slices.SortStableFunc(resp.Tasks, func(a, b userTask) int {
			return cmp.Compare(a.Status, b.Status)
		})
	}

	// 人气榜信息
	rank, gapScore := s.rm.GetPopularRankAndGap(ctx, anchor.UserId)
	resp.RankInfo = rankInfo{
		Rank:     rank,
		GapScore: gapScore,
	}

	return resp, nil
}

type anchorTask struct {
	Type  fctask.Type     `json:"type"`           // 任务类型：1 人气票 2 粉丝灯牌 3 赠送礼物 4 观看直播
	Gift  *types.LiteGift `json:"gift,omitempty"` // 关联礼物信息（类型1和2才有）
	Users int             `json:"users"`          // 完成人数
}

type anchorInfoResp struct {
	Members   int          `json:"members"`   // 成员数量
	TodayJoin int          `json:"todayJoin"` // 今日新加入
	Tasks     []anchorTask `json:"tasks"`     // 任务列表
	RankInfo  rankInfo     `json:"rankInfo"`  // 人气榜信息
}

// @Tags 粉丝团
// @Summary 直播间粉丝团信息(主播侧)
// @Description 直播间粉丝团信息(主播侧)
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=anchorInfoResp}
// @Router /api/v1/fansclub/anchor [get]
func (s *apis) anchor(ctx *api.Context, _ api.EmptyReq) (*anchorInfoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	membersCnt, err := s.fc.Members(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	resp := &anchorInfoResp{
		Members: membersCnt,
	}

	now := time.Now()
	resp.TodayJoin, _ = s.ft.TodayMembers(ctx, now, uac.UserId)

	for _, tt := range fctask.TTypes {
		at := anchorTask{
			Type: tt,
		}
		at.Users, _ = s.ft.TodayTaskDone(ctx, now, uac.UserId, tt)
		switch tt {
		case fctask.TTGift1:
			at.Gift = mixer.LiteGift(mixer.NoErr(s.gm.GiftById(fctask.Gift1Id)))
		case fctask.TTGift2:
			at.Gift = mixer.LiteGift(mixer.NoErr(s.gm.GiftById(fctask.Gift2Id)))
		}
		resp.Tasks = append(resp.Tasks, at)
	}

	// 人气榜信息
	rank, gapScore := s.rm.GetPopularRankAndGap(ctx, uac.UserId)
	resp.RankInfo = rankInfo{
		Rank:     rank,
		GapScore: gapScore,
	}

	return resp, nil
}

type joinReq struct {
	RoomId string `json:"roomId"` // 房间Id
}

// @Tags 粉丝团
// @Summary 加入粉丝团
// @Description 加入粉丝团
// @Produce json
// @Security HeaderAuth
// @Param param body joinReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/fansclub/join [post]
func (s *apis) join(ctx *api.Context, req joinReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := s.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if success, err := s.fm.TryFollow(ctx, uac.UserId, ri.UserId, false); err != nil {
		return nil, err
	} else if success {
		s.om.Post(func() { _ = s.om.NotifyRoomFollow(ctx, req.RoomId, uac.UserId, true) })
	}

	if err := s.fc.Join(ctx, time.Now(), req.RoomId, uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type leaveReq struct {
	AnchorId string `json:"anchorId"` // 主播Id
}

// @Tags 粉丝团
// @Summary 退出粉丝团
// @Description 退出粉丝团
// @Produce json
// @Security HeaderAuth
// @Param param body leaveReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/fansclub/leave [post]
func (s *apis) leave(ctx *api.Context, req leaveReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.fc.Leave(ctx, req.AnchorId, uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type membersReq struct {
	AnchorId string `form:"anchorId"` // 主播Id
	Cursor   int    `form:"cursor"`   // 分页游标
}

type memberInfo struct {
	User     *types.User `json:"user"`     // 用户信息
	Level    int         `json:"level"`    // 粉丝团等级
	Inactive bool        `json:"inactive"` // 熄灭状态
	Intimacy int         `json:"intimacy"` // 亲密度
}

type membersResp struct {
	Cursor int           `json:"cursor,omitempty"` // 分页游标（最后一页时为null）
	Total  int           `json:"total,omitempty"`  // 总人数（只有第一页会给值）
	List   []*memberInfo `json:"list"`             // 成员列表
}

// @Tags 粉丝团
// @Summary 获取粉丝团成员列表
// @Description 用户侧会隐藏不活跃成员
// @Produce json
// @Security HeaderAuth
// @Param param query membersReq true "请求参数"
// @Success 200 {object} codec.Response{data=membersResp}
// @Router /api/v1/fansclub/members [get]
func (s *apis) members(ctx *api.Context, req membersReq) (*membersResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	limit := 20

	list, err := s.fc.ListMembers(ctx, req.AnchorId, uac.UserId != req.AnchorId, req.Cursor, limit)
	if err != nil {
		return nil, err
	}

	var resp membersResp
	if req.Cursor == 0 {
		resp.Total, _ = s.fc.Members(ctx, req.AnchorId)
	}

	if len(list) == limit {
		resp.Cursor = req.Cursor + limit
	}

	at := time.Now()
	for _, mm := range list {
		lv := mm.LevelInfo(at)
		resp.List = append(resp.List, &memberInfo{
			User:     mixer.User(ctx, mixer.NoErr(s.ug.Account(ctx, mm.UserId))),
			Level:    lv.Level,
			Inactive: !lv.Active,
			Intimacy: lv.Detail().TotalExp,
		})
	}

	return &resp, nil
}
