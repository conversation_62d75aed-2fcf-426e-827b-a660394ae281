package withdraw

import (
	"errors"
	"maps"
	"slices"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/jstream"
	"gitlab.sskjz.com/overseas/live/osl/internal/face"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/withdraw"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
)

func Invoke(r *api.Router, hm *mux.Locker, js *jstream.API, wm *withdraw.Manager, jm *journal.Manager, fm *face.Manager) {
	s := &API{js: js, wm: wm, jm: jm, fm: fm}
	ar := r.WithAuth(hm.Middleware(mux.WithPOST))
	{
		ar.GET("/wallet/withdraw/info", api.Generic(s.info))
		ar.GET("/wallet/withdraw/banks", api.Generic(s.banks))
		ar.GET("/wallet/withdraw/payee", api.Generic(s.getPayee))
		ar.POST("/wallet/withdraw/payee", api.Generic(s.savePayee))
		ar.POST("/wallet/withdraw/money", api.Generic(s.money))
		ar.POST("/wallet/withdraw/submit", api.Generic(s.submit))
		ar.GET("/wallet/withdraw/history", api.Generic(s.history))
		ar.GET("/wallet/withdraw/detail", api.Generic(s.detail))
	}
}

type API struct {
	js *jstream.API
	wm *withdraw.Manager
	jm *journal.Manager
	fm *face.Manager
}

type infoResp struct {
	Limit    *withdraw.Limit    `json:"limit"`    // 额度限制
	Currency withdraw.Currency  `json:"currency"` // 货币信息
	Ratio    float64            `json:"ratio"`    // 兑换比例：1水晶=x货币
	Payee    *withdraw.PayeePub `json:"payee"`    // 提现方式
}

// @Tags 提现
// @Summary 概要信息
// @Description 概要信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/wallet/withdraw/info [get]
func (s *API) info(ctx *api.Context, _ api.EmptyReq) (*infoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var resp infoResp

	limit, err := s.wm.Limit(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	resp.Limit = limit

	payee, err := s.wm.TakePayee(ctx, uac.UserId)
	if err != nil && !errors.Is(err, withdraw.ErrPayeeNotFound) {
		return nil, err
	}

	if payee != nil {
		resp.Currency = withdraw.Symbol(payee.Currency)
		resp.Ratio = withdraw.Ratio(payee.Currency)
		resp.Payee = withdraw.PubPayee(payee.Export())
	}

	return &resp, nil
}

type banksReq struct {
	Country pay.Country `form:"country" binding:"required,iso3166_1_alpha2"`
}

type banksResp struct {
	List []*withdraw.Bank `json:"list"`
}

// @Tags 提现
// @Summary 银行列表
// @Description 银行列表
// @Produce json
// @Security HeaderAuth
// @Param param query banksReq true "请求参数"
// @Success 200 {object} codec.Response{data=banksResp}
// @Router /api/v1/wallet/withdraw/banks [get]
func (s *API) banks(ctx *api.Context, req banksReq) (*banksResp, error) {
	banks, err := s.wm.Banks(ctx, "", req.Country)
	if err != nil {
		return nil, err
	}
	return &banksResp{List: banks}, nil
}

type getPayeeReq struct {
	Ticket string `form:"ticket"` // 人脸检测凭证
}

// @Tags 提现
// @Summary 获取提现信息
// @Description 获取提现信息
// @Produce json
// @Security HeaderAuth
// @Param param query getPayeeReq true "请求参数"
// @Success 200 {object} codec.Response{data=withdraw.PayeePri}
// @Router /api/v1/wallet/withdraw/payee [get]
func (s *API) getPayee(ctx *api.Context, req getPayeeReq) (*withdraw.PayeePri, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.fm.ValidTicket(ctx, uac.UserId, req.Ticket); err != nil {
		return nil, err
	}

	payee, err := s.wm.TakePayee(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return withdraw.PriPayee(payee.Export()), nil
}

type savePayeeReq struct {
	Ticket   string      `json:"ticket"` // 人脸检测凭证
	Country  pay.Country `json:"country" binding:"required,iso3166_1_alpha2"`
	BankId   string      `json:"bankId" binding:"required"`
	FullName string      `json:"fullName" binding:"required"`
	Account  string      `json:"account" binding:"required"`
	Identity string      `json:"identity"` // 税号（部分渠道会校验值）
	Phone    string      `json:"phone"`
}

// @Tags 提现
// @Summary 保存提现信息
// @Description 保存提现信息
// @Produce json
// @Security HeaderAuth
// @Param param body savePayeeReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/wallet/withdraw/payee [post]
func (s *API) savePayee(ctx *api.Context, req savePayeeReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.fm.ValidTicket(ctx, uac.UserId, req.Ticket); err != nil {
		return nil, err
	}

	if err := s.wm.SavePayee(ctx, uac.UserId, req.Country, req.BankId, req.FullName, req.Account, req.Identity, req.Phone); err != nil {
		return nil, err
	}

	_ = s.fm.ResetTicket(ctx, uac.UserId)

	return &api.EmptyResp{}, nil
}

type moneyReq struct {
	Amount int `json:"amount" binding:"required,gt=0"`
}

type moneyResp struct {
	Money float64 `json:"money"`
	Fee   float64 `json:"fee"`
}

// @Tags 提现
// @Summary 查询可提金额
// @Description 查询扣除手续费后实际到手金额
// @Produce json
// @Security HeaderAuth
// @Param param body moneyReq true "请求参数"
// @Success 200 {object} codec.Response{data=moneyResp}
// @Router /api/v1/wallet/withdraw/money [post]
func (s *API) money(ctx *api.Context, req moneyReq) (*moneyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	money, fee, err := s.wm.Money(ctx, uac.UserId, req.Amount)
	if err != nil {
		return nil, err
	}

	return &moneyResp{Money: money, Fee: fee}, nil
}

type submitReq struct {
	Amount int `json:"amount" binding:"required,gt=0"`
}

// @Tags 提现
// @Summary 申请提现
// @Description 申请提现
// @Produce json
// @Security HeaderAuth
// @Param param body submitReq true "请求参数"
// @Success 200 {object} codec.Response{data=types.AnchorWallet}
// @Router /api/v1/wallet/withdraw/submit [post]
func (s *API) submit(ctx *api.Context, req submitReq) (*types.AnchorWallet, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := s.wm.Submit(ctx, uac.UserId, req.Amount); err != nil {
		return nil, err
	}

	return mixer.AnchorWallet(ctx, uac.UserId)
}

type historyReq struct {
	EndAt  int64  `form:"endAt"`  // 截止时间：unix秒
	Cursor string `form:"cursor"` // 分页游标
}

type historyResp struct {
	Cursor string             `json:"cursor"`
	Aggs   []*jstream.Summary `json:"aggs"`
	List   []*historyItem     `json:"list"`
}

type historyItem struct {
	Id     string           `json:"id"`     // 交易ID
	Type   fund.JournalType `json:"type"`   // 交易类型
	Amount int64            `json:"amount"` // 金额（有正负）
	Remark string           `json:"remark"` // 备注（直接显示）
	Time   int64            `json:"time"`   // 时间：unix秒
	Status withdraw.Status  `json:"status"` // 状态
}

// @Tags 提现
// @Summary 提现记录
// @Description 提现记录
// @Produce json
// @Security HeaderAuth
// @Param param query historyReq true "请求参数"
// @Success 200 {object} codec.Response{data=historyResp}
// @Router /api/v1/wallet/withdraw/history [get]
func (s *API) history(ctx *api.Context, req historyReq) (*historyResp, error) {
	jr, err := s.js.Journal(ctx, jstream.Request{Prop: fund.PTypeFruits, Type: fund.JTypeWithdraw, EndAt: req.EndAt, Cursor: req.Cursor})
	if err != nil {
		return nil, err
	}

	var resp historyResp
	resp.Cursor = jr.Cursor
	resp.Aggs = jr.Aggs

	for _, agg := range jr.Aggs {
		if agg.Incomes > 0 {
			// 提现的数额要减去退回的
			agg.Expends -= agg.Incomes
			agg.Incomes = 0
		}
	}

	tradeState := make(map[string]withdraw.Status)
	tradeNos := make([]string, 0, len(resp.List))
	skipIdx := make([]int, 0, len(resp.List)/2)
	for i, rec := range jr.List {
		if rec.Amount > 0 {
			// 有退回记录的一定是提现失败的
			tradeState[rec.Raw.Trade] = withdraw.StateReject
			skipIdx = append(skipIdx, i)
		} else {
			tradeNos = append(tradeNos, rec.Raw.Trade)
		}
	}

	states, err := s.wm.Status(ctx, tradeNos)
	if err != nil {
		return nil, err
	}
	maps.Copy(tradeState, states)

	for i, rec := range jr.List {
		if slices.Contains(skipIdx, i) {
			continue
		}
		resp.List = append(resp.List, &historyItem{
			Id:     rec.Id,
			Type:   rec.Type,
			Amount: rec.Amount,
			Remark: rec.Remark,
			Time:   rec.Time,
			Status: tradeState[rec.Raw.Trade],
		})
	}

	return &resp, nil
}

type detailReq struct {
	Id string `form:"id"` // 交易ID
}

type detailResp struct {
	TradeNo   string            `json:"tradeNo"`          // 流水号
	Amount    int64             `json:"amount"`           // 提现数额
	Currency  withdraw.Currency `json:"currency"`         // 货币信息
	Money     float64           `json:"money"`            // 提现金额
	Status    withdraw.Status   `json:"status"`           // 处理状态
	Reason    string            `json:"reason,omitempty"` // 失败原因
	Payee     withdraw.PayeePub `json:"payee"`            // 提现方式
	CreatedAt int64             `json:"createdAt"`        // 申请时间
	UpdatedAt int64             `json:"updatedAt"`        // 处理时间（完成时间）
}

// @Tags 提现
// @Summary 详情信息
// @Description 详情信息
// @Produce json
// @Security HeaderAuth
// @Param param query detailReq true "请求参数"
// @Success 200 {object} codec.Response{data=detailResp}
// @Router /api/v1/wallet/withdraw/detail [get]
func (s *API) detail(ctx *api.Context, req detailReq) (*detailResp, error) {
	jr, err := s.jm.Detail(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	rec, err := s.wm.TakeRecord(ctx, jr.Trade)
	if err != nil {
		return nil, err
	}

	return &detailResp{
		TradeNo:   rec.TradeNo,
		Amount:    rec.Amount,
		Currency:  withdraw.Symbol(rec.Payee.Currency),
		Money:     rec.Money,
		Status:    rec.Status,
		Reason:    rec.Reason,
		Payee:     *withdraw.PubPayee(rec.Payee),
		CreatedAt: rec.CreatedAt.Unix(),
		UpdatedAt: rec.UpdatedAt.Unix(),
	}, nil
}
