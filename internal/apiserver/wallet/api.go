package wallet

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Invoke(r *api.Router) {
	s := &apis{}
	ar := r.WithAuth()
	{
		ar.GET("/wallet/info", api.Generic(s.info))
	}
}

type apis struct {
}

// @Tags 钱包
// @Summary 钱包数据
// @Description 钱包数据
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.GlobalWallet}
// @Router /api/v1/wallet/info [get]
func (s *apis) info(ctx *api.Context, _ api.EmptyReq) (*types.GlobalWallet, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	return mixer.GlobalWallet(ctx, uac.UserId)
}
