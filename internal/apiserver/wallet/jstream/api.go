package jstream

import (
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(ug user.Getter, gm *gift.Manager, jm *journal.Manager, dm *draw2.Manager) *API {
	return &API{ug: ug, gm: gm, jm: jm, dm: dm}
}

func Invoke(r *api.Router, s *API) {
	ar := r.WithAuth()
	{
		ar.GET("/wallet/journal", api.Generic(s.Journal))
		ar.GET("/wallet/journal/types", api.Generic(s.jTypes))
		ar.GET("/wallet/journal/detail/gift", api.Generic(s.giftDetail))
		ar.GET("/wallet/journal/detail/draw", api.Generic(s.drawDetail))
		ar.POST("/wallet/journal/delete", api.Generic(s.delete))
	}
}

type API struct {
	ug user.Getter
	gm *gift.Manager
	jm *journal.Manager
	dm *draw2.Manager
}

type Request struct {
	Prop   fund.PropType    `form:"prop"`   // 货币类型
	Type   fund.JournalType `form:"type"`   // 流水类型
	EndAt  int64            `form:"endAt"`  // 截止时间：unix秒
	Cursor string           `form:"cursor"` // 分页游标
	// internal use
	Types []fund.JournalType `swaggerignore:"true"`
}

type Summary struct {
	Time    int64 `json:"time"`    // 时间（月的开始时间）
	Incomes int64 `json:"incomes"` // 总收入
	Expends int64 `json:"expends"` // 总支出
	// internal use
	Raw *journal.TSummary `json:"-"`
}

type Record struct {
	Id        string           `json:"id"`                  // 交易ID
	Type      fund.JournalType `json:"type"`                // 交易类型
	Amount    int64            `json:"amount"`              // 金额（有正负）
	Remark    string           `json:"remark"`              // 备注（直接显示）
	Time      int64            `json:"time"`                // 时间：unix秒
	Extra     fund.Extra       `json:"extra,omitempty"`     // 附加信息
	WithUser  *types.User      `json:"withUser,omitempty"`  // 关联目标用户
	HasDetail bool             `json:"hasDetail,omitempty"` // 是否有明细
	// internal use
	Raw *journal.Record `json:"-"`
}

type Response struct {
	Cursor string     `json:"cursor"` // 分页游标：用于下次请求
	Aggs   []*Summary `json:"aggs"`   // 月份汇总
	List   []*Record  `json:"list"`   // 流水列表
}

// @Tags 钱包
// @Summary 交易明细
// @Description 交易流水列表
// @Produce json
// @Security HeaderAuth
// @Param param query Request true "请求参数"
// @Success 200 {object} codec.Response{data=Response}
// @Router /api/v1/wallet/journal [get]
func (s *API) Journal(ctx *api.Context, req Request) (*Response, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	return s.Journal2(ctx, uac, req)
}

func (s *API) Journal2(ctx *api.Context, uac *user.Account, req Request, opts ...journal.ReadOpt) (*Response, error) {
	var endAt time.Time
	if req.Cursor == "" && req.EndAt > 0 {
		endAt = time.Unix(req.EndAt, 0)
	}

	if req.Type != 0 {
		req.Types = []fund.JournalType{req.Type}
	} else if len(req.Types) == 0 {
		req.Types = []fund.JournalType{
			fund.JTypeRecharge,
			fund.JTypeSendGift,
			fund.JTypeExchange,
			fund.JTypeLuckGift,
			fund.JTypeGameplay,
			fund.JTypeRewards,
			fund.JTypeOthers,
		}
	}

	cursor, records, err := s.jm.Records(ctx, uac.UserId, req.Prop, req.Types, endAt, req.Cursor, opts...)
	if err != nil {
		return nil, err
	}

	var resp Response
	resp.Cursor = cursor

	for _, rec := range records {
		jr := &Record{
			Id:     rec.Id.Hex(),
			Type:   rec.Type,
			Amount: rec.Amount.IntPart(),
			Remark: s.jm.Remark(ctx, rec),
			Time:   rec.CreatedAt.Unix(),
			Raw:    rec,
		}

		if rec.WithUser != "" {
			acc, err := s.ug.Account(ctx, rec.WithUser)
			if err != nil {
				return nil, err
			}
			jr.WithUser = mixer.User(ctx, acc)
		}

		if rec.Merged > 1 {
			jr.HasDetail = true
		}

		s.fixingRec(ctx, jr.Raw, jr)

		resp.List = append(resp.List, jr)
	}

	if len(records) > 0 {
		summary, err := s.jm.Summary(ctx, uac.UserId, req.Prop, req.Types, records[len(records)-1].CreatedAt, records[0].CreatedAt, app.Timezone(ctx, uac), journal.Monthly)
		if err != nil {
			return nil, err
		}
		slices.SortFunc(summary, func(a, b *journal.TSummary) int {
			return b.Time.Compare(a.Time)
		})
		for _, sum := range summary {
			resp.Aggs = append(resp.Aggs, &Summary{
				Time:    sum.Time.Unix(),
				Incomes: sum.Incomes.IntPart(),
				Expends: sum.Expends.IntPart(),
				Raw:     sum,
			})
		}
	}

	return &resp, nil
}

type jType struct {
	Type fund.JournalType `json:"type"` // 流水类型
	Name string           `json:"name"` // 展示名称
}

type typesResp struct {
	List []*jType `json:"list"`
}

// @Tags 钱包
// @Summary 交易类型列表
// @Description 明细中展示时可能需要
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=typesResp}
// @Router /api/v1/wallet/journal/types [get]
func (s *API) jTypes(ctx *api.Context, _ api.EmptyReq) (*typesResp, error) {
	return &typesResp{List: []*jType{}}, nil
}

type deleteReq struct {
	Ids []string `json:"ids"` // 交易ID列表
}

// @Tags 钱包
// @Summary 删除流水记录
// @Description 删除流水记录
// @Produce json
// @Security HeaderAuth
// @Param param body deleteReq true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/wallet/journal/delete [post]
func (s *API) delete(ctx *api.Context, req deleteReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	if err := s.jm.Delete(ctx, uac.UserId, req.Ids); err != nil {
		return nil, err
	}
	return &api.EmptyResp{}, nil
}
