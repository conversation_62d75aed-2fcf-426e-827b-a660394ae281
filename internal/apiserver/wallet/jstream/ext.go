package jstream

import (
	"context"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
)

var visibleGameExtra = map[string]string{
	"game.platform": "game.platform",
	"game.id":       "game.id",
	"game.name":     "game.name",
	"game.roundId":  "game.roundId",
}

func (s *API) fixingRec(ctx context.Context, rec *journal.Record, jr *Record) {
	if len(rec.Extra) > 0 && rec.Extra["game.id"] != "" {
		if rec.Extra["renderRemark"] == "true" && rec.Extra["game.name"] != "" {
			if rec.Amount.IsPositive() {
				jr.Remark = i3n.T(ctx, "gameplay win") + " - " + rec.Extra["game.name"]
			} else {
				jr.Remark = i3n.T(ctx, "gameplay cost") + " - " + rec.Extra["game.name"]
			}
		}

		for k, tk := range visibleGameExtra {
			if jr.Extra == nil {
				jr.Extra = make(map[string]string)
			}
			jr.Extra[tk] = rec.Extra[k]
		}
	}
}
