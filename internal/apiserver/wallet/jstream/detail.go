package jstream

import (
	"context"
	"errors"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type detailReq struct {
	Id     string `form:"id"`     // 交易ID
	Cursor int64  `form:"cursor"` // 分页游标
}

type detailResp[T any] struct {
	Cursor int64       `json:"cursor,omitempty"` // 分页游标：用于下次请求
	Anchor *types.User `json:"anchor"`           // 主播信息
	Total  int         `json:"total"`            // 记录数
	Amount int64       `json:"amount"`           // 总金额
	List   []T         `json:"list"`             // 详细列表
}

type giftInfo struct {
	Gift   *types.LiteGift `json:"gift"`   // 礼物
	Count  int             `json:"count"`  // 数量
	Amount int64           `json:"amount"` // 金额
	Time   int64           `json:"time"`   // 时间
}

type giftResp = detailResp[*giftInfo]

// @Tags 钱包
// @Summary 送礼详情
// @Description 送礼详情
// @Produce json
// @Security HeaderAuth
// @Param param query detailReq true "请求参数"
// @Success 200 {object} codec.Response{data=giftResp}
// @Router /api/v1/wallet/journal/detail/gift [get]
func (s *API) giftDetail(ctx *api.Context, req detailReq) (*giftResp, error) {
	return makeDetail(ctx, s.ug, s.jm, req.Id, req.Cursor, fund.JTypeSendGift, func(log *journal.GiftLog) *giftInfo {
		gInfo, _ := s.gm.GiftById(log.GiftId)
		if gInfo == nil {
			return nil
		}
		return &giftInfo{
			Gift:   mixer.LiteGift(gInfo),
			Count:  log.Count,
			Amount: log.Amount,
			Time:   log.Time.Unix(),
		}
	})
}

type drawInfo struct {
	Gift   *types.LiteGift `json:"gift"`   // 礼物
	Multi  int             `json:"multi"`  // 倍数
	Amount int64           `json:"amount"` // 金额
	Time   int64           `json:"time"`   // 时间
}

type drawResp = detailResp[*drawInfo]

// @Tags 钱包
// @Summary 中奖详情
// @Description 中奖详情
// @Produce json
// @Security HeaderAuth
// @Param param query detailReq true "请求参数"
// @Success 200 {object} codec.Response{data=drawResp}
// @Router /api/v1/wallet/journal/detail/draw [get]
func (s *API) drawDetail(ctx *api.Context, req detailReq) (*drawResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}
	if dbg.Ing() || uac.Country == "CN" {
		jr, err := s.jm.Detail(ctx, req.Id)
		if err != nil {
			return nil, err
		}
		logs, err := s.dm.History(ctx, jr.UserId, jr.GKey(), req.Cursor, 20, jr.CreatedAt)
		if err != nil {
			return nil, err
		}

		return &drawResp{
			Cursor: logs.Cursor,
			Anchor: mixer.User(ctx, mixer.NoErr(s.ug.Account(ctx, jr.WithUser))),
			Total:  int(logs.Total),
			Amount: jr.Amount.IntPart(),
			List: lo.Map(logs.Logs, func(log draw2.HistoryLog, _ int) *drawInfo {
				return &drawInfo{
					Gift:   mixer.LiteGift(mixer.NoErr(s.gm.GiftById(log.GiftId))),
					Multi:  log.Mul,
					Amount: log.Gain,
					Time:   log.Time.Unix(),
				}
			}),
		}, nil
	}
	return makeDetail(ctx, s.ug, s.jm, req.Id, req.Cursor, fund.JTypeLuckGift, func(log *journal.DrawLog) *drawInfo {
		gInfo, _ := s.gm.GiftById(log.GiftId)
		if gInfo == nil {
			return nil
		}
		return &drawInfo{
			Gift:   mixer.LiteGift(gInfo),
			Multi:  log.Multi,
			Amount: log.Amount,
			Time:   log.Time.Unix(),
		}
	})
}

func makeDetail[T comparable, R any](
	ctx context.Context,
	ug user.Getter, jm *journal.Manager,
	id string, cursor int64,
	typ fund.JournalType,
	make func(log R) T,
) (*detailResp[T], error) {
	var resp detailResp[T]

	record, err := jm.Details(ctx, id, cursor)
	if err != nil {
		return nil, err
	}

	if record.Type != typ {
		return nil, errors.New("mismatch journal type")
	}

	resp.Total = record.Merged
	if resp.Total == 0 {
		return &resp, nil
	}

	resp.Amount = record.Amount.IntPart()

	acc, err := ug.Account(ctx, record.WithUser)
	if err != nil {
		return nil, err
	}
	resp.Anchor = mixer.User(ctx, acc)

	var void T
	logs := journal.History[R](record)
	for _, log := range logs {
		out := make(log)
		if out == void {
			continue
		}
		resp.List = append(resp.List, out)
	}

	if next := int(cursor) + len(logs); next < resp.Total {
		resp.Cursor = int64(next)
	}

	return &resp, nil
}
