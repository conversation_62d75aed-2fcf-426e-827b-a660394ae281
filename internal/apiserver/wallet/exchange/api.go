package exchange

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/jstream"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Invoke(r *api.Router, js *jstream.API, fm *fund.Manager) {
	s := &apis{js: js, fm: fm}
	ar := r.WithAuth()
	{
		ar.POST("/wallet/exchange", api.Generic(s.exchange))
		ar.GET("/wallet/exchange/history", api.Generic(s.history))
	}
}

type apis struct {
	js *jstream.API
	fm *fund.Manager
}

type exchangeReq struct {
	From   fund.PropType `json:"from"`   // 水晶写2
	To     fund.PropType `json:"to"`     // 金币写1
	Amount int           `json:"amount"` // 兑换数量
}

// @Tags 钱包
// @Summary 货币兑换
// @Description 水晶换金币
// @Produce json
// @Security HeaderAuth
// @Param param body exchangeReq true "请求参数"
// @Success 200 {object} codec.Response{data=types.GlobalWallet}
// @Router /api/v1/wallet/exchange [post]
func (s *apis) exchange(ctx *api.Context, req exchangeReq) (*types.GlobalWallet, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	fac, err := s.fm.Exchange(ctx, uac.UserId, req.From, req.To, req.Amount)
	if err != nil {
		return nil, err
	}

	return mixer.GlobalWallet(fund.WithAccount(ctx, fac), uac.UserId)
}

type exHistoryReq struct {
	Prop   fund.PropType `form:"prop"`   // 货币类型
	EndAt  int64         `form:"endAt"`  // 截止时间：unix秒
	Cursor string        `form:"cursor"` // 分页游标
}

// @Tags 钱包
// @Summary 兑换记录
// @Description 货币兑换记录
// @Produce json
// @Security HeaderAuth
// @Param param query exHistoryReq true "请求参数"
// @Success 200 {object} codec.Response{data=jstream.Response}
// @Router /api/v1/wallet/exchange/history [get]
func (s *apis) history(ctx *api.Context, req exHistoryReq) (*jstream.Response, error) {
	return s.js.Journal(ctx, jstream.Request{Prop: req.Prop, Type: fund.JTypeExchange, EndAt: req.EndAt, Cursor: req.Cursor})
}
