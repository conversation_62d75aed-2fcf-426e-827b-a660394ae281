package translate

import (
	"slices"
	"strings"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/translate"
)

type API struct {
	tc *translate.Translator
}

func Invoke(r *api.Router, tc *translate.Translator) {
	a := &API{
		tc: tc,
	}
	r = r.WithAuth()
	{
		r.POST("/translate/text", api.Generic(a.Translate))
	}
}

type TranslateReq struct {
	Text       string `json:"text" binding:"required"`
	ToLanguage string `json:"toLanguage" binding:"required"`
	Excludes   string `json:"excludes"` //  exclude source language split by comma
}

type TranslateResp struct {
	Translation string `json:"translation"`
	SourceLang  string `json:"sourceLang"`
}

// @Tags 翻译
// @Summary 翻译文本
// @Description 翻译文本
// @Produce json
// @Security HeaderAuth
// @Param req body TranslateReq true "请求"
// @Success 200 {object} TranslateResp
// @Router /api/v1/translate/text [post]
func (a *API) Translate(c *api.Context, req TranslateReq) (*TranslateResp, error) {
	toLang := i3n.TrimLang(req.ToLanguage)

	translated, err := a.tc.Translate(c, req.Text, toLang)
	if err != nil {
		return nil, err
	}

	if slices.Contains(strings.Split(req.Excludes, ","), translated.Lang) {
		return &TranslateResp{Translation: req.Text, SourceLang: translated.Lang}, nil
	}

	return &TranslateResp{
		Translation: translated.Translation[toLang],
		SourceLang:  translated.Lang,
	}, nil
}
