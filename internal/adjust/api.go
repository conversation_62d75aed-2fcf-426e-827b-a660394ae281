package adjust

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type Push struct {
	GpsADId      string `form:"gps_adid"`
	TrackerName  string `form:"tracker_name"`
	NetworkName  string `form:"network_name"`
	AppToken     string `form:"app_token"`
	DeviceName   string `form:"device_name"`
	DeviceType   string `form:"device_type"`
	AndroidId    string `form:"android_id"`
	ADId         string `form:"adid"`
	Country      string `form:"country"`
	EventName    string `form:"event_name"`
	Event        string `form:"event"`
	CampaignName string `form:"campaign_name"`
}

func InvokeAPI(r *api.Router, mgr *Manager) {
	r.GET("/adjust/notify", api.Request(func(ctx *api.Context, req Push) error {
		adId := req.GpsADId
		if adId == "" {
			adId = req.AndroidId
		}
		if adId == "" || req.NetworkName == "" {
			return nil
		}
		_ = mgr.touch(ctx, adId, req.NetworkName)
		return nil
	}))
}
