package adjust

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/cc"
)

type cached struct {
	cc cc.Cache[string, *Profile] // userId
	nc cc.Cache[string, Network]  // by adId
}

func (s *Manager) initCache(syn cc.Sync) {
	s.cc = cc.New[string, *Profile](
		20000, cc.LRU,
		cc.LoaderExpireFunc(func(userId string) (*Profile, time.Duration, error) {
			p, err := s.take(context.TODO(), userId)
			if err != nil {
				return nil, 0, err
			} else if p.Network == "" {
				return p, time.Minute, nil
			}
			return p, -1, nil
		}),
		cc.ExportStats("adjust.profile"),
		cc.WithSync(syn, "adjust.profile"),
	)
	s.nc = cc.New[string, Network](
		20000, cc.LRU,
		cc.LoaderExpireFunc(func(adId string) (Network, time.Duration, error) {
			p, err := s.take2(context.TODO(), adId)
			if err != nil {
				return "", 0, err
			} else if p.Network == "" {
				return s.tmpNetwork(context.TODO(), adId), time.Hour, nil
			}
			return Network(p.Network), -1, nil
		}),
		cc.ExportStats("adjust.network"),
		cc.WithSync(syn, "adjust.network"),
	)
}

func (s *cached) Profile(ctx context.Context, userId string) *Profile {
	p, _ := s.cc.Get(userId)
	if p == nil {
		return &Profile{Id: userId}
	}
	return p
}

func (s *cached) Network(ctx context.Context, adId string) Network {
	n, _ := s.nc.Get(adId)
	return n
}

func (s *cached) invalidCC(userId string) {
	s.cc.Remove(userId)
}

func (s *cached) invalidNC(adId string) {
	s.nc.Remove(adId)
}
