package adjust

import (
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.MongoClient, rc *redi.Client, ug user.Getter, vm *device.Manager, syn cc.Sync, vnd log.Vendor) *Manager {
	db.SyncSchema(profileDB, 1, profileIdx...)
	mgr := &Manager{db: db, rc: rc, ug: ug, vm: vm, log: vnd.Scope("adjust.mgr")}
	mgr.initCache(syn)
	return mgr
}

func Invoke(evb ev.Bus, mgr *Manager) {
	evb.Watch(user.EvUserCreated, "adjust.tracking", ev.<PERSON>(mgr.onRegister))
}
