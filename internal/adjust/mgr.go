package adjust

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type Manager struct {
	db  *db.MongoClient
	rc  *redi.Client
	ug  user.Getter
	vm  *device.Manager
	log *zap.Logger
	cached
}

func (s *Manager) take(ctx context.Context, userId string) (*Profile, error) {
	prof := &Profile{Id: userId}
	return prof, s.takeBy(ctx, bson.M{"_id": userId}, prof)
}

func (s *Manager) take2(ctx context.Context, adId string) (*Profile, error) {
	prof := &Profile{ADId: adId}
	return prof, s.takeBy(ctx, bson.M{"adId": adId}, prof)
}

func (s *Manager) takeBy(ctx context.Context, filter bson.M, to *Profile) error {
	if err := s.db.Collection(profileDB).FindOne(ctx, filter).Decode(to); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return err
		}
	}
	return nil
}

func (s *Manager) touch(ctx context.Context, adId, network string) error {
	dev, err := s.vm.Take(ctx, adId)
	if err != nil {
		return err
	}
	if dev.UserId == "" {
		return s.saveTemp(ctx, adId, network)
	}
	if uac, _ := s.ug.Account(ctx, dev.UserId); uac != nil {
		if time.Since(uac.CreatedAt) > time.Hour {
			network = "Kako Organic"
		}
	}
	return s.touch2(ctx, dev.UserId, adId, network)
}

func (s *Manager) touch2(ctx context.Context, userId, adId, network string) error {
	prof := s.Profile(ctx, userId)
	if prof.ADId != "" {
		return nil
	}

	prof.ADId = adId
	prof.Network = network
	prof.CreatedAt = time.Now()

	if _, err := s.db.Collection(profileDB).InsertOne(ctx, prof); err != nil {
		return err
	}

	s.log.Debug("profile init", zap.String("userId", userId), zap.String("deviceId", adId), zap.String("network", network))

	s.invalidCC(userId)
	s.invalidNC(adId)
	return nil
}
