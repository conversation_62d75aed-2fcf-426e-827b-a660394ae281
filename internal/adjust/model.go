package adjust

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

var profileIdx = []db.Indexer{
	{
		Name: "adId",
		Keys: bson.D{
			{Key: "adId", Value: 1},
		},
	},
}

type Profile struct {
	Id        string    `bson:"_id"` // userId
	ADId      string    `bson:"adId"`
	Network   string    `bson:"network"`
	CreatedAt time.Time `bson:"createdAt"`
}

const profileDB = "adjust.profile"
