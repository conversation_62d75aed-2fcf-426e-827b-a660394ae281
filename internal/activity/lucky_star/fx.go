package lucky_star

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	ug user.Getter,
	gm *gift.Manager,
	rc *redi.Client,
	fm *fund.Manager,
	im *im.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(ug, gm, rc.Cluster("rank"), fm, im, vnd.Scope("lucky_star.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
	room *room.Manager,
) {
	// evb.Watch(evt.GiftSend, "lucky.star.gift", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())

	room.GiftNotifyApplier(func(ctx context.Context, data *protocol.SendGiftNotify) error {
		is, err := mgr.IsLuckyStar(ctx, data.GiftId, data.User.UserId)
		if err != nil {
			return fmt.Errorf("check lucky star: %w", err)
		}

		if is {
			data.TagUrl = starTagUrl
		}

		return nil
	})

	// 巴西时间每天00:01发送奖励：UTC 03:01 = Sao_Paulo 00:01
	//sch.CronWithSeconds("0 1 3 * * *").Do(sch.Exclusive("activity.luckystar.reward", func(ctx context.Context) error {
	//	return mgr.SendReward(ctx)
	//}))
}
