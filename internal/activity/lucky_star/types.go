package lucky_star

import "gitlab.sskjz.com/overseas/live/osl/pkg/user"

type LuckyInfo struct {
	StartAt  int64           // 活动开始时间
	EndAt    int64           // 活动结束时间
	RankInfo []GiftLuckyInfo // 幸运之星榜单
}

type GiftLuckyInfo struct {
	GiftId       int       // 礼物id
	GiftImageUrl string    // 礼物图片地址
	Award        int       // 奖励金额
	LuckyStar    *UserRank // 幸运之星
	MyRanking    *UserRank // 我的信息
}

type UserRank struct {
	User       *user.Account // 用户信息
	LuckyTimes int           // 中奖次数
}
