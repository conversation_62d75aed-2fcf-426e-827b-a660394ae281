package lucky_star

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if evd.GiftId == 0 || evd.Count <= 0 || evd.AnchorId == "" || len(evd.Prizes) == 0 {
		return nil
	}

	// 活动时间判断
	if evd.At.Before(startTime) || evd.At.After(endTime) {
		return nil
	}

	// 不在列表
	if !lo.Contains(giftList, evd.GiftId) {
		return nil
	}

	var luckyTimes int
	for _, prize := range evd.Prizes {
		if prize < 500 {
			continue
		} else if prize >= 500 && prize < 1000 {
			luckyTimes++
		} else if prize >= 1000 {
			luckyTimes += 2
		}
	}

	if luckyTimes == 0 {
		return nil
	}

	rankKey := fmt.Sprintf(keyLuckyStarActivityRank, evd.At.In(ctz.Brazil).Format("20060102"), evd.GiftId)
	err := m.rc.ZIncrBy(ctx, rankKey, float64(luckyTimes), evd.UserId).Err()
	if err != nil {
		m.log.Error("luckystar onSendGift zincrBy score err", zap.Error(err), zap.Any("evd", evd))
		return err
	}
	m.rc.Expire(ctx, rankKey, ttlLuckyStarActivityRank)

	// 记录用户分数最后更新时间
	updateKey := fmt.Sprintf(keyLuckyStarUpdateTime, evd.At.In(ctz.Brazil).Format("20060102"), evd.GiftId)
	m.rc.HSet(ctx, updateKey, evd.UserId, evd.At.Unix())
	m.rc.Expire(ctx, updateKey, ttlLuckyStarUpdateTime)

	return nil
}
