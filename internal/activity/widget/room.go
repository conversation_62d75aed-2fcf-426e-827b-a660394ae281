package widget

import (
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox_collect"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/coin_grab"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/hourlyrank"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rebate"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"strings"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

// LiveRT 直播间右上角
func LiveRT(ctx *api.Context, ri *live.Room) *Widget {
	var token string
	if _, err := ctx.User(); err == nil {
		token = strings.TrimPrefix(ctx.GetHeader("Authorization"), "Bearer ")
	}

	var lng = i18n.PreferLang(ctx, "en")

	//if token != "" {
	//	if christmas.Open() {
	//		actUrl := "https://www.kako.live/act/christmas_indeicator.html?app_notitle=true&needLogin&lng=%s&token=%s&v=2"
	//		if dbg.Ing() {
	//			actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
	//		}
	//		return &Widget{
	//			Id:           "2024-christmas-activity",
	//			WebviewUrl:   fmt.Sprintf(actUrl, lng, token),
	//			CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2024-christmas-widget-icon.png",
	//		}
	//	}
	//}
	//
	//if token != "" {
	//	if valentines_day.Open() {
	//		actUrl := "https://www.kako.live/act/valentine_indicator.html?app_notitle=true&needLogin&lng=%s&token=%s&anchorUserId=%s"
	//		if dbg.Ing() {
	//			actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
	//		}
	//		return &Widget{
	//			Id:           "2025-valentines-day-activity",
	//			WebviewUrl:   fmt.Sprintf(actUrl, lng, token, lo.Ternary(ri != nil, ri.UserId, "")),
	//			CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2025-valentine-widget-icon.png",
	//		}
	//	}
	//}

	if token != "" {
		t := time.Now().In(ctz.Brazil)
		// 小时榜活动在区间内：第一优先级
		if hourlyrank.InPeriod(t) {
			actUrl := "https://www.kako.live/act/hourly_rank_indicator.html?app_notitle=true&token=%s&lng=%s&anchorUid=%s"
			if dbg.Ing() {
				actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
			}
			return &Widget{
				Id:           "2025-hourlyrank-activity",
				WebviewUrl:   fmt.Sprintf(actUrl, token, lng, lo.Ternary(ri != nil, ri.UserId, "")),
				CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2025-hourly-rank-widget-icon.png",
			}
		}
		if blindbox_collect.IsOpen(t) {
			actUrl := "https://www.kako.live/act/gift_wall_indicator.html?app_notitle=true&token=%s&lng=%s&anchorUserId=%s"
			if dbg.Ing() {
				actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
			}
			return &Widget{
				Id:           "2025-gift-wall",
				WebviewUrl:   fmt.Sprintf(actUrl, token, lng, lo.Ternary(ri != nil, ri.UserId, "")),
				CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2025-gift-wall-widget-icon.png",
			}
		}
		if t.After(rebate.StartTime) && t.Before(rebate.EndTime) {
			actUrl := "https://www.kako.live/act/rebate_indicator.html?app_notitle=true&token=%s&lng=%s"
			if dbg.Ing() {
				actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
			}
			return &Widget{
				Id:           "2025-rebate-activity",
				WebviewUrl:   fmt.Sprintf(actUrl, token, lng),
				CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2025-rebate-widget-icon.png",
			}
		}
		if coin_grab.IsOpen(t) {
			actUrl := "https://www.kako.live/act/grab_gold_indicator.html?app_notitle=true&token=%s&lng=%s&v=1"
			if dbg.Ing() {
				actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
			}
			return &Widget{
				Id:           "2025-coingrab-activity",
				WebviewUrl:   fmt.Sprintf(actUrl, token, lng),
				CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2025-grab-coin-widget-icon.png",
			}
		}
		// 小时榜活动不在区间内：最后查优先级
		if hourlyrank.IsOpen(t) {
			actUrl := "https://www.kako.live/act/hourly_rank_indicator.html?app_notitle=true&token=%s&lng=%s&anchorUid=%s"
			if dbg.Ing() {
				actUrl = strings.Replace(actUrl, "www.kako.live", "www-test.kako.live", 1)
			}
			return &Widget{
				Id:           "2025-hourlyrank-activity",
				WebviewUrl:   fmt.Sprintf(actUrl, token, lng, lo.Ternary(ri != nil, ri.UserId, "")),
				CollapseIcon: "https://godzilla-live-oss.kako.live/res/activity/2025-hourly-rank-widget-icon.png",
			}
		}
	}

	return nil
}
