package widget

import "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"

type Widget struct {
	Id           string `json:"id"`           // 组件id
	WebviewUrl   string `json:"webviewUrl"`   // webview打开的地址（不需要过security/check检查）
	CollapseIcon string `json:"collapseIcon"` // 折叠后显示的icon
}

type hourlyRankWidget struct {
	StartTime           int64            `json:"startTime"`           // 活动开始时间，Unix秒级时间戳
	EndTime             int64            `json:"endTime"`             // 活动结束时间，Unix秒级时间戳
	PeriodStatus        int              `json:"periodStatus"`        // 阶段状态：0未开始，显示剩余开始时间; 1已开始，显示剩余结束时间
	PeriodWaitSeconds   int64            `json:"periodWaitSeconds"`   // 截止时间戳
	PeriodRemainSeconds int64            `json:"periodRemainSeconds"` // 截止时间戳
	Ranks               []hourlyRankUser `json:"ranks"`               // top3
}

type hourlyRankUser struct {
	Rank    int         `json:"rank"`          // 排名
	Value   int64       `json:"value"`         // 分数值
	Gap     int64       `json:"gap,omitempty"` // 分差,rank==1是与第二名差距、rank!=1为与上一名差距;只在current接口的HostRank中有此字段
	Reward  int         `json:"reward"`        // 奖励水晶
	IsValid bool        `json:"isValid"`       // 是否符合发奖条件，只有在history接口有此字段
	User    *types.User `json:"user"`          // 用户信息
}

type rebateWidget struct {
	MyRank rebateRankingUser `json:"myRank"`
}

type rebateRankingUser struct {
	Rank  int         `json:"rank"`
	Value int         `json:"value"`
	User  *types.User `json:"user"` // 用户信息，此处不需要赋值，保持为nil就可以
}

type coinGrabWidget struct {
	CoinPool int64 `json:"coinPool"` // 当前总奖池
	MyRank   int   `json:"myRank"`   // 我的排名
}

type blindBoxCollectWidgetResp struct {
	CollectData map[int]bool `json:"collectData"` // 当前轮次收集情况
}
