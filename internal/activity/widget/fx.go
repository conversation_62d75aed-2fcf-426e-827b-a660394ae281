package widget

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox_collect"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/coin_grab"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/hourlyrank"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rebate"
)

func Provide(
	hrm *hourlyrank.Manager,
	rm *rebate.Manager,
	cgm *coin_grab.Manager,
	bcm *blindbox_collect.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(hrm, rm, cgm, bcm, vnd.Scope("widget.mgr"))

	return m, nil
}
