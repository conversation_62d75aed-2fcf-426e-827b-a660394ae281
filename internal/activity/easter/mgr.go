package easter

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress/vehicle"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

type Manager struct {
	dm  *dress.Manager
	rc  *redi.Client
	log *zap.Logger
}

func newManager(dm *dress.Manager, rc *redi.Client, log *zap.Logger) *Manager {
	m := &Manager{
		dm:  dm,
		rc:  rc,
		log: log,
	}

	return m
}

type StartEndAndStage struct {
	StartTime time.Time
	EndTime   time.Time
	Stage     string
}

var startEndTime = []StartEndAndStage{
	{
		StartTime: time.Date(2025, 04, 19, 0, 0, 0, 0, ctz.Brazil),
		EndTime:   time.Date(2025, 04, 21, 23, 59, 59, 0, ctz.Brazil),
		Stage:     "20250416",
	},
}

func GetSES(t time.Time) StartEndAndStage {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return ses
		}
	}
	return StartEndAndStage{}
}

func (m *Manager) GetSES(t time.Time) StartEndAndStage {
	return GetSES(t)
}

func (m *Manager) UserValueInfo(ctx context.Context, userId string) int64 {
	now := time.Now().In(ctz.Brazil)
	ses := m.GetSES(now)
	if ses.Stage == "" {
		return 0
	}
	key := fmt.Sprintf(keyEasterUser, now.Format("20060102"), userId)
	value, _ := m.rc.Get(ctx, key).Int64()
	return value
}

func (m *Manager) GetVehicleStatus(ctx context.Context, userId string) int {
	p, err := m.dm.Take(ctx, userId)
	if err == nil && p != nil {
		if p.Vehicle == nil || p.Vehicle.Id == vehicle.RabbitCar {
			if p.Vehicle.Valid() {
				return 1
			} else {
				return 2
			}
		}
	}

	return 0
}
