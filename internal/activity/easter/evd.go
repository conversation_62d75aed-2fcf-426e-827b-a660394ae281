package easter

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress/vehicle"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
	"time"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if !lo.Contains(easterGift, evd.GiftId) || evd.Count <= 0 {
		return nil
	}

	// 活动时间判断
	ses := m.GetSES(evd.At)
	if ses.Stage == "" {
		return nil
	}

	var value int64
	if evd.Gift.Lucky {
		value = int64(evd.Diamond)
	} else {
		value = int64(evd.Diamond) * 10
	}

	key := fmt.Sprintf(keyEasterUser, evd.At.In(ctz.Brazil).Format("20060102"), evd.UserId)
	v := m.rc.IncrBy(ctx, key, value).Val()
	m.rc.Expire(ctx, key, ttlEasterUser)

	// 分数达到之后发放座驾
	if v >= TargetValue && v-value < TargetValue {
		if err := m.dm.SetVehicle(ctx, evd.UserId, vehicle.RabbitCar, time.Now().Add(24*time.Hour)); err != nil {
			return err
		}
		m.log.Info("获取兔子座驾成功", zap.String("userId", evd.UserId), zap.Int64("value", v))
	}

	return nil
}
