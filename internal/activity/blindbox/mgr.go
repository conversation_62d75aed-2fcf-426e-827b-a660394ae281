package blindbox

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/im"

	"github.com/redis/go-redis/v9"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	ug  user.Getter
	gm  *gift.Manager
	rc  *redi.Client
	fm  *fund.Manager
	im  *im.Manager
	log *zap.Logger
}

func newManager(ug user.Getter, gm *gift.Manager, rc *redi.Client, fm *fund.Manager, im *im.Manager, log *zap.Logger) *Manager {
	m := &Manager{
		ug:  ug,
		gm:  gm,
		rc:  rc,
		fm:  fm,
		im:  im,
		log: log,
	}

	return m
}

type SES struct {
	StartTime time.Time
	EndTime   time.Time
	Stage     string
}

var (
	sesList = []SES{
		{
			StartTime: time.Date(2025, 05, 16, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 05, 18, 23, 59, 59, 0, ctz.Brazil),
			Stage:     "20250516",
		},
	}
	rankAward = map[int]int{
		1: 240000,
		2: 120000,
		3: 80000,
		4: 50000,
		5: 10000,
	}
)

func (m *Manager) GetSES(t time.Time) SES {
	t = t.In(ctz.Brazil)
	for _, ses := range sesList {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return ses
		}
	}
	return SES{}
}

// GetRanking 获取榜单
func (m *Manager) GetRanking(ctx context.Context, stage, userId string) (*UserRank, []UserRank, error) {
	var (
		myRank   *UserRank
		rankList []UserRank
	)

	ranks := m.getRank(context.Background(), stage)
	for k, r := range ranks {
		anchorId := r.Member.(string)
		uac, err := m.ug.Account(ctx, anchorId)
		if err != nil {
			return nil, nil, err
		}

		userRank := UserRank{
			Rank:  k + 1,
			Value: int(r.Score),
			User:  uac,
		}

		if anchorId == userId {
			myRank = &userRank
		}

		rankList = append(rankList, userRank)

	}

	if userId != "" && myRank == nil {
		uac, err := m.ug.Account(ctx, userId)
		if err != nil {
			return nil, nil, err
		}

		rank, value := m.GetRankByUserId(ctx, stage, userId)
		myRank = &UserRank{
			Rank:  rank,
			Value: value,
			User:  uac,
		}
	}

	return myRank, rankList, nil
}

func (m *Manager) GetRankByUserId(ctx context.Context, stage string, userId string) (int, int) {
	rankKey := fmt.Sprintf(keyBlindBoxActivityRank, stage)
	value, err := m.rc.ZScore(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}

	rank, err := m.rc.ZRevRank(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}
	return int(rank) + 1, int(value)
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, stage string) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keyBlindBoxActivityRank, stage)
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 19).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyBlindBoxUpdateTime, stage), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

func (m *Manager) SendReward(ctx context.Context) error {
	t := time.Now().AddDate(0, 0, -1).In(ctz.Brazil)
	m.log.Info("盲盒送礼活动励发送开始", zap.Int64("time", t.Unix()))
	// 是否是发奖日
	ses := m.GetSES(t)
	if ses.Stage == "" || time.Now().In(ctz.Brazil).Before(ses.EndTime) {
		m.log.Error("盲盒送礼活动奖励发送失败:非发奖时间", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "非发奖时间")
	}

	rewardKey := fmt.Sprintf(keyBlindBoxActivityReward, ses.Stage)
	if m.rc.Exists(ctx, rewardKey).Val() != 0 {
		m.log.Info("盲盒送礼活动奖励发送失败:已发放完成", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "奖励已经发放")
	}

	rank := m.getRank(ctx, ses.Stage)
	if len(rank) > 0 {
		for i, v := range rank {
			if i+1 > 5 {
				break
			}
			userId := v.Member.(string)
			reward := rankAward[i+1]
			if reward == 0 {
				continue
			}
			if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, reward); err != nil {
				m.log.Info("盲盒送礼活动奖励发送失败:调用加币方法错误",
					zap.Int("rank", i+1),
					zap.String("userId", userId),
					zap.Int("reward", reward),
					zap.Int64("time", t.Unix()),
					zap.Error(err))
				continue
			}

			// 发送消息
			if err := m.im.SendSystemNoticeTextToUser(
				ctx,
				userId,
				fmt.Sprintf(
					"Parabéns por ficar em %dº lugar no evento [Sortudo da caixa surpresa]. A recompensa de %d coins já foi distribuída ao seu saldo.",
					i+1,
					reward,
				),
			); err != nil {
				m.log.Error("盲盒送礼活动奖励私信通知失败",
					zap.Error(err),
					zap.String("userId", userId),
					zap.Int("reward", reward),
					zap.Int64("time", t.Unix()))
			}

			m.log.Info("盲盒送礼活动奖励发送成功",
				zap.Int("rank", i+1),
				zap.String("userId", userId),
				zap.Int("reward", reward),
				zap.Int64("time", t.Unix()))
		}
	} else {
		m.log.Info("盲盒送礼活动奖励发送失败:榜单为空",
			zap.Int64("time", t.Unix()))
	}

	m.rc.SetEx(ctx, rewardKey, 1, ttlBlindBoxActivityReward)
	m.log.Info("盲盒送礼活动奖励发送完成", zap.Int64("time", t.Unix()))

	return nil
}
