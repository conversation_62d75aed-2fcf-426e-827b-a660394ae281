package blindbox

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if evd.GiftId == 0 || evd.Count <= 0 || !lo.Contains([]int{10076, 10048}, evd.BlindBox) {
		return nil
	}

	// 活动时间判断
	ses := m.GetSES(evd.At)
	if evd.At.Before(ses.StartTime) || evd.At.After(ses.EndTime) {
		return nil
	}

	rankKey := fmt.Sprintf(keyBlindBoxActivityRank, ses.Stage)
	err := m.rc.ZIncrBy(ctx, rankKey, float64(evd.Diamond), evd.UserId).Err()
	if err != nil {
		m.log.Error("blindbox onSendGift zincrBy score err", zap.Error(err), zap.Any("evd", evd))
		return err
	}
	m.rc.Expire(ctx, rankKey, ttlBlindBoxActivityRank)

	// 记录用户分数最后更新时间
	updateKey := fmt.Sprintf(keyBlindBoxUpdateTime, ses.Stage)
	m.rc.HSet(ctx, updateKey, evd.UserId, evd.At.Unix())
	m.rc.Expire(ctx, updateKey, ttlBlindBoxUpdateTime)

	return nil
}
