package blindbox_collect

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
	"math"
	"strconv"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if evd.GiftId == 0 || evd.Count <= 0 || !lo.Contains([]int{10087, 10088}, evd.BlindBox) {
		return nil
	}

	// 活动时间判断
	ses := m.GetSES(evd.At)
	if evd.At.Before(ses.StartTime) || evd.At.After(ses.EndTime) {
		return nil
	}

	// 用户级别锁
	l, err := m.rm.Lock(ctx, fmt.Sprintf(keyLockUserRank, evd.AnchorId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	// 拿出背包
	userKey := fmt.Sprintf(keyBlindBoxCollect, ses.Stage, evd.AnchorId)
	currentInfo, err := m.rc.HGetAll(ctx, userKey).Result()
	if err != nil {
		return err
	}
	// 本次是否是一次集齐
	var (
		collectData      = make(map[int]int)
		isFull      bool = true
		minNum      int  = math.MaxInt32          // 背包礼物最小数量的值
		minMap           = make(map[int]struct{}) // 最小数量的礼物有几种
	)
	// 找到礼物最少的数量
	for _, id := range allCollectGiftId {
		var haveInt int
		if haveStr, ok := currentInfo[strconv.Itoa(id)]; ok {
			haveInt, _ = strconv.Atoi(haveStr)
		}
		collectData[id] = haveInt
		if haveInt < minNum {
			minNum = haveInt
		}
	}
	// 最小数量的礼物有几种
	for _, id := range allCollectGiftId {
		if collectData[id] == minNum {
			minMap[id] = struct{}{}
		}
	}
	// 数量最少得礼物不是本次获得的礼物，一定是集不齐的
	// 数量最少得礼物超过两种，一定是集不齐的
	if _, ok := minMap[evd.GiftId]; !ok || len(minMap) > 1 {
		isFull = false
	}

	if isFull {
		rankKey := fmt.Sprintf(keyBlindBoxCollectActivityRank, ses.Stage)
		err := m.rc.ZIncrBy(ctx, rankKey, 1, evd.AnchorId).Err()
		if err != nil {
			m.log.Error("blindboxcollect onSendGift zincrBy score err", zap.Error(err), zap.Any("evd", evd))
			return err
		}
		m.rc.Expire(ctx, rankKey, ttlBlindBoxCollectActivityRank)

		// 记录用户分数最后更新时间
		updateKey := fmt.Sprintf(keyBlindBoxCollectUpdateTime, ses.Stage)
		m.rc.HSet(ctx, updateKey, evd.AnchorId, evd.At.Unix())
		m.rc.Expire(ctx, updateKey, ttlBlindBoxCollectUpdateTime)

		// 发送奖励
		if err := m.SendCollectReward(ctx, evd.AnchorId); err != nil {
			return err
		}
	}

	// 更新个人数据
	if err := m.rc.HIncrBy(ctx, userKey, strconv.Itoa(evd.GiftId), 1).Err(); err != nil {
		return err
	}
	m.rc.Expire(ctx, userKey, ttlBlindBoxCollect)

	m.log.Debug("collect debug",
		zap.Any("evd", evd),
		zap.Any("isFull", isFull),
		zap.Any("currentInfo", currentInfo),
		zap.Any("collectData", collectData),
	)

	return nil
}
