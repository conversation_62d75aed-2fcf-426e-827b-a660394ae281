package blindbox_collect

import (
	"fmt"
	"math"
	"strconv"
	"testing"
)

func TestManager_onSendGift(t *testing.T) {
	giftIdList := []int{
		10089,
		10090,
		10091,
		10092,
		10093,
		10094,
		10095,
	}
	for _, giftId := range giftIdList {
		var (
			currentInfo = map[string]string{
				"10089": "87",
				"10090": "66",
				"10091": "2",
				"10092": "61",
				"10093": "30",
				"10094": "17",
				"10095": "3",
			}
			collectData      = make(map[int]int)
			isFull      bool = true
			minNum      int  = math.MaxInt32          // 背包礼物最小数量的值
			minMap           = make(map[int]struct{}) // 最小数量的礼物有几种
		)
		// 找到礼物最少的数量
		for _, id := range allCollectGiftId {
			var haveInt int
			if haveStr, ok := currentInfo[strconv.Itoa(id)]; ok {
				haveInt, _ = strconv.Atoi(haveStr)
			}
			collectData[id] = haveInt
			if haveInt < minNum {
				minNum = haveInt
			}
		}
		// 最小数量的礼物有几种
		for _, id := range allCollectGiftId {
			if collectData[id] == minNum {
				minMap[id] = struct{}{}
			}
		}
		// 数量最少得礼物不是本次获得的礼物，一定是集不齐的
		// 数量最少得礼物超过两种，一定是集不齐的
		if _, ok := minMap[giftId]; !ok || len(minMap) > 1 {
			isFull = false
		}
		fmt.Println(currentInfo)
		fmt.Println(giftId)
		fmt.Println(isFull)
	}
}
