package blindbox_collect

import "time"

const (
	keyBlindBoxCollect = "HASH:BLINDBOXCOLLECT:ACTIVITY:%s:%s" // stage:userId
	ttlBlindBoxCollect = time.Hour * 24 * 14

	keyBlindBoxCollectActivityRank = "ZSET:BLINDBOXCOLLECT:ACTIVITY:%s" // stage
	ttlBlindBoxCollectActivityRank = time.Hour * 24 * 14

	keyBlindBoxCollectUpdateTime = "HASH:BLINDBOXCOLLECT:ACTIVITY:TIME:%s" // stage
	ttlBlindBoxCollectUpdateTime = time.Hour * 24 * 14

	keyBlindBoxCollectActivityReward = "STR:BLINDBOXCOLLECT:ACTIVITY:REWARD:%s" // stage
	ttlBlindBoxCollectActivityReward = time.Hour * 24 * 14

	keyLockUserRank = "STR:MUTEX:BLINDBOXCOLLECT:%s" // userId

	// CollectFruits 集齐水晶奖励
	CollectFruits = 50000
	// CollectBadge 集齐铭牌
	CollectBadge = "https://godzilla-live-oss.kako.live/res/badge/giftwall_badge.png?w=58&h=15"
	// CollectAvatarBorder 集齐头像框
	CollectAvatarBorder = "https://godzilla-live-oss.kako.live/res/avatar/giftwall_avatarborder.png"
)

var allCollectGiftId = []int{10089, 10090, 10091, 10092, 10093, 10094, 10095}
