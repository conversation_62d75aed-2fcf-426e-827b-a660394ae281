package agency_newbie

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

func newManager(
	fm *fund.Manager,
	dbmc *db.MongoClient,
	acm *anchor.Manager,
	am *agency.Manager,
	log *zap.Logger,
) (*Manager, error) {
	return &Manager{
		fm:   fm,
		dbmc: dbmc,
		acm:  acm,
		am:   am,
		log:  log,
	}, nil
}

type Manager struct {
	fm   *fund.Manager
	dbmc *db.MongoClient
	acm  *anchor.Manager
	am   *agency.Manager
	log  *zap.Logger
}
