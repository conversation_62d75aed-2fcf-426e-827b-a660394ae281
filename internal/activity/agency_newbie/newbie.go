package agency_newbie

import (
	"context"
	"errors"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (m *Manager) GetActivityStageInfo() []StageInfo {
	return StageInfoList
}

func (m *Manager) GetInviteAnchorList(ctx context.Context, stageId string, agencyId int) ([]AgencyNewbieAnchor, error) {
	cursor, err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).Find(ctx, bson.M{
		"stageId":  stageId,
		"agencyId": agencyId,
	}, options.Find().SetSort(bson.M{"_id": -1}))
	if err != nil {
		return nil, err
	}

	var res []AgencyNewbieAnchor
	if err := cursor.All(ctx, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (m *Manager) AwardReceive(ctx context.Context, stageId string, agencyId int, userId string, award int64) error {
	// mongo 判断是否已领取
	if m.IsAwardReceived(ctx, stageId, agencyId, userId) {
		return biz.NewError(biz.ErrBusiness, "received")
	}

	if _, err := m.dbmc.Collection(AgencyNewbieReceiveCollectionName()).InsertOne(ctx, bson.M{
		"stageId":  stageId,
		"agencyId": agencyId,
		"userId":   userId,
	}); err != nil {
		return err
	}

	// 发放金币
	if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, award); err != nil {
		m.log.Error("agency newbie award receive err",
			zap.Error(err),
			zap.String("userId", userId),
			zap.Int("agencyId", agencyId),
		)
		return err
	}

	return nil
}

// IsAwardReceived 是否已经领取奖励
func (m *Manager) IsAwardReceived(ctx context.Context, stageId string, agencyId int, userId string) bool {
	var record AgencyNewbieReceive
	err := m.dbmc.Collection(AgencyNewbieReceiveCollectionName()).FindOne(
		ctx,
		bson.M{
			"stageId":  stageId,
			"agencyId": agencyId,
			"userId":   userId,
		},
	).Decode(&record)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false
		} else {
			m.log.Error("isAwardReceived err",
				zap.Error(err),
				zap.String("userId", userId),
				zap.Int("agencyId", agencyId),
			)
			return true
		}
	}

	return true
}
