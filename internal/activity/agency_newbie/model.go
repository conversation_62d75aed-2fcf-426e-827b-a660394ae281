package agency_newbie

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

const (
	NewbieAnchorStatusNoVerify  = 0 // 未审核
	NewbieAnchorStatusNoMission = 1 // 未完成一个任务
	NewbieAnchorStatusEffective = 2 // 有效
)

// AgencyNewbieAnchor 公会拉新
type AgencyNewbieAnchor struct {
	Id       primitive.ObjectID `bson:"_id"`
	StageId  string             `bson:"stageId"`  // 阶段id
	AgencyId int                `bson:"agencyId"` // 邀请人公会id
	UserId   string             `bson:"userId"`   // 被邀请主播id
	Status   int                `bson:"status"`   // 0:待主播审核 1:待完成扶持任务 2:有效
	UpdateAt time.Time          `bson:"updateAt"` // 更新时间
	CreateAt time.Time          `bson:"createAt"` // 创建时间
}

func AgencyNewbieAnchorCollectionName() string {
	return "activity.agency.newbie.anchor"
}

// AgencyNewbieReceive 阶段奖励领取记录
type AgencyNewbieReceive struct {
	Id       primitive.ObjectID `bson:"_id"`
	StageId  string             `bson:"stageId"`  // 阶段id
	AgencyId int                `bson:"agencyId"` // 公会id
	UserId   string             `bson:"userId"`
	CreateAt time.Time          `bson:"createAt"` // 创建时间 领取时间
}

func AgencyNewbieReceiveCollectionName() string {
	return "activity.agency.newbie.receive"
}
