package christmas

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
)

const (
	StatusNotStart     = 1      // 为开始
	StatusRunning      = 2      // 进行中
	StatusEnd          = 3      // 已结束
	RoundPhaseJoin     = "join" // 参与阶段
	RoundPhaseDraw     = "draw" // 开奖阶段：结束的时候开奖
	RoundPhaseShow     = "show" // 展示阶段
	RoundStatusRunning = 1      // 轮次进行中
	RoundStatusEnd     = 2      // 轮次已结束已开奖
	RoundStatusNullify = 3      // 轮次流局
)

type Info struct {
	StartTime int64    `json:"startTime"` // 活动开始时间：unix秒
	EndTime   int64    `json:"endTime"`   // 活动结束时间：unix秒
	Status    int      `json:"status"`    // 活动状态 1-未开始 2-进行中 3-已结束
	Round     *Round   `json:"round"`     // 每轮信息
	Records   []Record `json:"records"`   // 最近10轮开奖记录
}

type Round struct {
	RoundId   string       `json:"roundId"`   // 轮次ID
	Phase     Phase        `json:"phase"`     // 阶段
	TotalSeat int          `json:"totalSeat"` // 座位总数
	Joined    int          `json:"joined"`    // 已参与人数
	Seats     map[int]Seat `json:"seats"`     // 座位信息
	WinSeatNo int          `json:"winSeatNo"` // 中奖座位编号
	WinGiftId int          `json:"winGiftId"` // 中奖礼物ID
	WinUser   *types.User  `json:"winUser"`   // 中奖用户
}

type Phase struct {
	Name      string `json:"name"`      // 阶段名称 加入join 开奖draw 展示show
	Countdown int64  `json:"countdown"` // 剩余毫秒数
}

type Seat struct {
	User *types.User `json:"user"` // 用户
}

// 最近10轮中奖记录
type Record struct {
	RoundId    string      `json:"roundId"`    // 轮次ID
	User       *types.User `json:"user"`       // 用户
	WinGiftId  int         `json:"winGiftId"`  // 中奖礼物ID
	SeatNo     int         `json:"seatNo"`     // 中奖座位编号（提示文案：上一轮这个位置有开出大奖哦）
	UpdateTime int64       `json:"updateTime"` // 最后更新时间
}

// 用户个人参与记录
type JoinRecord struct {
	WinGiftId   int   `json:"winGiftId"`   // 获得礼物ID
	RoundStatus int   `json:"roundStatus"` // 轮次状态 1进行中 2已开奖 3流局
	CreateTime  int64 `json:"createTime"`  // 时间，秒时间戳
}

type RankItem struct {
	User  *types.User `json:"user"`    // 用户信息
	Gifts [][]int     `json:"giftIds"` // 获得礼物 [[giftId1, gift1Num], [giftId2, gift2Num]]
}

type RankMine struct {
	No  int   `json:"no"`  // 排名 1开始
	Gap int64 `json:"gap"` // 与上一名差距
}

type RankAward struct {
	Diamond   int64
	Fruits    int64
	AvatarDay int
}
