package christmas

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

const (
	keyRankAnchor = "ZSET:ACT:CHRISTMAS:RANK:ANCHOR:%s"
	keyRankUser   = "ZSET:ACT:CHRISTMAS:RANK:USER:%s" // 用户获得的活动礼物排行
	keyAnchor     = "HASH:ACT:CHRISTMAS:ANCHOR:%s:%s" // 主播获得的礼物统计
	keyUser       = "HASH:ACT:CHRISTMAS:USER:%s:%s"   // 用户获得的礼物统计
)

func (m *Manager) onGiftSentAdvanced(ctx context.Context, evd *evt.SendGiftAdvanced) error {
	giftId := evd.GiftId

	if !lo.Contains(actGiftIds, giftId) {
		return nil
	}

	// 活动是否结束
	if closed() {
		return nil
	}

	giftNum := evd.Count
	diamond := evd.Diamond

	anchorId := evd.AnchorId
	day := evd.At.In(ctz.Brazil).Format("20060102")

	res := m.rc.ZIncrBy(ctx, m.getRankAnchorKey(day), float64(diamond), anchorId).Val()
	// 首次增加增加时间权重
	if res == float64(diamond) {
		m.rc.ZIncrBy(ctx, m.getRankAnchorKey(day), 1-float64(time.Now().Unix()/1e10), anchorId)
	}
	m.rc.Expire(ctx, m.getRankAnchorKey(day), time.Hour*24*15)
	m.rc.HIncrBy(ctx, m.getAnchorKey(day, anchorId), fmt.Sprintf("%d", giftId), int64(giftNum))
	m.rc.Expire(ctx, m.getAnchorKey(day, anchorId), time.Hour*24*15)

	logger := m.log.With(
		zap.String("anchorId", anchorId),
		zap.String("userId", evd.UserId),
		zap.Int("giftId", giftId),
	)

	logger.Info("赠送圣诞活动礼物")

	return nil
}

func (m *Manager) getRankAnchorKey(day string) string {
	return fmt.Sprintf(keyRankAnchor, day)
}

func (m *Manager) getRankUserKey(day string) string {
	return fmt.Sprintf(keyRankUser, day)
}

func (m *Manager) getAnchorKey(day, anchorId string) string {
	return fmt.Sprintf(keyAnchor, day, anchorId)
}

func (m *Manager) getUserKey(day, userId string) string {
	return fmt.Sprintf(keyUser, day, userId)
}

func (m *Manager) giftSortValue(giftId int) int {
	for k, v := range actGiftIds {
		if v == giftId {
			return k
		}
	}

	return 0
}

// 榜单奖励

func (m *Manager) rankAward() error {
	ctx := context.Background()

	settleDay := time.Now().In(ctz.Brazil).AddDate(0, 0, -1)
	day := settleDay.Format("20060102")

	logger := m.log.With(
		zap.String("day", day),
	)

	logger.With(zap.String("key", "开始发放")).Info("榜单奖励")

	// 主播奖励
	anchorIds, err := m.rc.ZRevRange(ctx, m.getRankAnchorKey(day), 0, 9).Result()

	if err != nil {
		logger.With(
			zap.Error(err),
			zap.String("key", "获取主播排行失败"),
		).Error("榜单奖励")
	} else {
		for k, v := range anchorIds {
			rankNo := k + 1

			if rankNo > 10 {
				break
			}

			anchorAward, ok := rankAnchorAward[rankNo]

			if !ok {
				continue
			}

			err := m.sendRankAward(ctx, settleDay, v, rankNo, anchorAward)

			if err != nil {
				logger.With(
					zap.Error(err),
					zap.String("key", "发送主播奖励失败"),
				).Error("榜单奖励")
			} else {
				logger.With(
					zap.String("userId", v),
					zap.Int("gain", rankNo),
					zap.Int64("diamond", anchorAward.Diamond),
					zap.Int64("fruits", anchorAward.Fruits),
					zap.Int("count", anchorAward.AvatarDay),
					zap.String("content", "主播奖励"),
				).Info("榜单奖励")
			}
		}
	}

	// 用户奖励
	userIds, err := m.rc.ZRevRange(ctx, m.getRankUserKey(day), 0, 9).Result()

	if err != nil {
		logger.With(
			zap.Error(err),
			zap.String("key", "获取用户排行失败"),
		).Error("榜单奖励")
	} else {
		for k, v := range userIds {
			rankNo := k + 1

			if rankNo > 10 {
				break
			}

			userAward, ok := rankUserAward[rankNo]

			if !ok {
				continue
			}

			err := m.sendRankAward(ctx, settleDay, v, rankNo, userAward)

			if err != nil {
				logger.With(
					zap.Error(err),
					zap.String("key", "发送用户奖励失败"),
				).Error("榜单奖励")
			} else {
				logger.With(
					zap.String("userId", v),
					zap.Int("gain", rankNo),
					zap.Int64("diamond", userAward.Diamond),
					zap.Int64("fruits", userAward.Fruits),
					zap.Int("count", userAward.AvatarDay),
					zap.String("content", "用户奖励"),
				).Info("榜单奖励")
			}
		}
	}

	return nil
}

func (m *Manager) sendRankAward(ctx context.Context, settleDay time.Time, userId string, rankNo int, rw RankAward) error {
	awards := []string{}
	rankTypeMsg := ""

	if rw.Diamond > 0 {
		if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, rw.Diamond); err != nil {
			return err
		}

		rankTypeMsg = "Ranking de usuário"
		awards = append(awards, fmt.Sprintf("%d coins", rw.Diamond))
	}

	if rw.Fruits > 0 {
		if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeFruits, rw.Fruits); err != nil {
			return err
		}

		rankTypeMsg = "Ranking de Host"
		awards = append(awards, fmt.Sprintf("%d cristais", rw.Fruits))
	}

	if rw.AvatarDay > 0 {
		if err := m.sendAvatarBorder(ctx, userId, rw.AvatarDay); err != nil {
			return err
		}
		awards = append(awards, fmt.Sprintf("Quadro de Avatar(%d dias)", rw.AvatarDay))
	}

	if rankTypeMsg != "" && len(awards) > 0 {
		msg := fmt.Sprintf(
			// 圣诞快乐！你在【x月x日】参与的【圣诞幸运星】活动中，获得【用户榜】第x名，已为你发放奖励内容：xxxxx、xxxxx。
			// 圣诞快乐！你在【x月x日】参与的【圣诞幸运星】活动中，获得【主播榜】第x名，已为你发放奖励内容：xxxxx、xxxxx。
			"Feliz Natal! Você participou do evento [Natal Sortudo] em [%s] e ficou em %dº lugar no [%s]. A recompensa correspondente já foi enviada: %s.",
			settleDay.Format("02/01"),
			rankNo,
			rankTypeMsg,
			strings.Join(awards, ", "),
		)

		m.imm.SendSystemNoticeTextToUser(
			ctx,
			userId,
			msg,
		)
	}

	return nil
}

func (m *Manager) sendAvatarBorder(ctx context.Context, userId string, day int) error {
	profile, err := m.dsm.Take(ctx, userId)

	if err != nil {
		return err
	}

	update := true
	newExpireAt := time.Now().AddDate(0, 0, day)

	if profile.AvatarBorder != nil && newExpireAt.Before(profile.AvatarBorder.ExpireAt) {
		update = false
	}

	if update {
		if err := m.dsm.SetAvatarBorder(ctx, userId, "https://godzilla-live-oss.kako.live/res/avatar/act_christmas.png", newExpireAt); err != nil {
			return err
		}
	}

	return nil
}
