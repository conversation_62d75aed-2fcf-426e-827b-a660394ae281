package christmas

import (
	"time"

	"github.com/shopspring/decimal"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

var (
	totalSeat         = 10
	costDiamond int64 = 1000
)

var (
	durationPhaseJoin = time.Minute * 30
	durationPhaseDraw = time.Second * 3
	durationPhaseShow = time.Second * 5
)

var (
	StartTime = time.Date(2024, 12, 23, 0, 0, 0, 0, ctz.Brazil)
	EndTime   = time.Date(2025, 1, 5, 23, 59, 59, 0, ctz.Brazil)
)

var (
	randomBase = decimal.NewFromInt(10000000)

	rt = Table{
		Rows: []Row{
			{GiftId: 10022, Name: "Alce de Natal", Diamond: 8000, Probability: 0.1}, // 麋鹿
			{GiftId: 10023, Name: "Carrossel", Diamond: 10000, Probability: 0.6},    // 旋转木马
			{GiftId: 10024, Name: "<PERSON><PERSON> Noel", Diamond: 20000, Probability: 0.3},   // 圣诞老人
		},
	}
)

var (
	actGiftIds = []int{10022, 10023, 10024} // 从小礼物到大礼物排序
)

// 榜单排名奖励
var (
	rankAnchorAward = map[int]RankAward{
		1:  {Diamond: 0, Fruits: 30000, AvatarDay: 14},
		2:  {Diamond: 0, Fruits: 20000, AvatarDay: 7},
		3:  {Diamond: 0, Fruits: 10000, AvatarDay: 5},
		4:  {Diamond: 0, Fruits: 2000, AvatarDay: 3},
		5:  {Diamond: 0, Fruits: 2000, AvatarDay: 3},
		6:  {Diamond: 0, Fruits: 2000, AvatarDay: 3},
		7:  {Diamond: 0, Fruits: 2000, AvatarDay: 3},
		8:  {Diamond: 0, Fruits: 2000, AvatarDay: 3},
		9:  {Diamond: 0, Fruits: 2000, AvatarDay: 3},
		10: {Diamond: 0, Fruits: 2000, AvatarDay: 3},
	}

	rankUserAward = map[int]RankAward{
		1:  {Diamond: 30000, Fruits: 0, AvatarDay: 14},
		2:  {Diamond: 20000, Fruits: 0, AvatarDay: 7},
		3:  {Diamond: 10000, Fruits: 0, AvatarDay: 5},
		4:  {Diamond: 2000, Fruits: 0, AvatarDay: 3},
		5:  {Diamond: 2000, Fruits: 0, AvatarDay: 3},
		6:  {Diamond: 2000, Fruits: 0, AvatarDay: 3},
		7:  {Diamond: 2000, Fruits: 0, AvatarDay: 3},
		8:  {Diamond: 2000, Fruits: 0, AvatarDay: 3},
		9:  {Diamond: 2000, Fruits: 0, AvatarDay: 3},
		10: {Diamond: 2000, Fruits: 0, AvatarDay: 3},
	}
)

func giftName(giftId int) string {
	for _, g := range rt.Rows {
		if g.GiftId == giftId {
			return g.Name
		}
	}

	return "unknown"
}

func giftDiamond(giftId int) int64 {
	for _, g := range rt.Rows {
		if g.GiftId == giftId {
			return g.Diamond
		}
	}

	return 0
}

func Open() bool {
	if dbg.Ing() {
		return true
	}
	now := time.Now()
	return now.After(StartTime) && now.Before(EndTime)
}

func closed() bool {
	if dbg.Ing() {
		return false
	}
	return time.Now().After(EndTime)
}

func status() int {
	if time.Now().Before(StartTime) {
		return StatusNotStart
	}

	if time.Now().After(EndTime) {
		return StatusEnd
	}

	return StatusRunning
}
