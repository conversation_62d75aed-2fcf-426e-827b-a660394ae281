package christmas

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// 流局
func (m *Manager) nullify(ctx context.Context, rr *RoundRecord) error {
	logger := m.log.With(
		zap.String("roundId", rr.Id.Hex()),
		zap.Strings("userIds", rr.UserIds),
	)

	ur, err := m.mc.Collection(RoundRecordCollectionName()).UpdateOne(
		ctx,
		bson.M{"_id": rr.Id, "phase": RoundPhaseJoin, "status": RoundStatusRunning},
		bson.M{
			"$set": bson.M{
				"status":    RoundStatusNullify,
				"updatedAt": time.Now(),
			},
		},
	)

	if err != nil {
		logger.Error("圣诞活动流局更新失败1")
		return err
	}

	if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
		logger.Error("圣诞活动流局更新失败2")
		return ErrRoundUpdated
	}

	cursor, err := m.mc.Collection(UserRecordCollectionName()).Find(
		ctx,
		bson.M{"roundId": rr.Id.Hex()},
	)

	if err != nil {
		// 记录人工补退
		logger.Error("圣诞活动退换报名费获取参与者失败")
		return err
	}

	defer cursor.Close(ctx)

	var urs []UserRecord

	if err := cursor.All(ctx, &urs); err != nil {
		logger.Error("圣诞活动退换报名费获取参与者失败")
		return err
	}

	for _, v := range urs {
		err := m.fm.Income(ctx, v.UserId, fund.JTypeOthers, fund.PTypeDiamond, v.Diamond)

		if err != nil {
			// 记录人工补退
			logger.With(
				zap.String("userId", v.UserId),
				zap.Int64("diamond", v.Diamond),
			).Error("圣诞活动退换报名费失败")
		}

		m.imm.SendSystemNoticeTextToUser(
			ctx,
			v.UserId,
			fmt.Sprintf(
				// 你在【x月x日 xx点xx分】参与的【圣诞幸运星】活动，因未按时凑满10人，本局以取消。报名金币1000，已退回至你的金币余额。
				"O evento [Natal Sortudo] do qual você participou em [%s] foi cancelado porque não atingiu %d pessoas na roda a tempo. Foram cobrados %d coins para começar a roda, os %d coins já foram devolvidos ao seu saldo.",
				v.CreatedAt.In(ctz.Brazil).Format("15:04 do 02/01"),
				rr.TotalSeat,
				v.Diamond,
				v.Diamond,
			),
		)
	}

	return nil
}
