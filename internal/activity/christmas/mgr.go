package christmas

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func newManager(
	mc *db.MongoClient,
	rc *redi.Client,
	dm *redi.Mutex,
	ug user.Getter,
	fm *fund.Manager,
	fg fund.Getter,
	imm *im.Manager,
	dsm *dress.Manager,
	pm *props.Manager,
	lm *level.Manager,
	log *zap.Logger,
) (*Manager, error) {
	mgr := &Manager{
		mc:  mc,
		rc:  rc,
		dm:  dm,
		ug:  ug,
		fm:  fm,
		fg:  fg,
		imm: imm,
		dsm: dsm,
		pm:  pm,
		lm:  lm,
		log: log,
	}

	return mgr, nil
}

type Manager struct {
	mc  *db.MongoClient
	rc  *redi.Client
	dm  *redi.Mutex
	ug  user.Getter
	fm  *fund.Manager
	fg  fund.Getter
	imm *im.Manager
	dsm *dress.Manager
	pm  *props.Manager
	lm  *level.Manager
	log *zap.Logger
}
