package christmas

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	ErrRoundNotFound = errors.New("round not found")
	ErrRoundUpdated  = errors.New("round updated")
)

func (m *Manager) roundLoop() error {
	ctx := context.Background()

	rr, err := m.roundRecord(ctx)

	if err != nil {
		if err != ErrRoundNotFound {
			return err
		}

		// 没有轮次
		rr, err = m.createRound(ctx)

		if err != nil {
			return err
		}
	}

	p := m.phase(ctx, rr)

	set := bson.M{}
	var newRound bool

	switch p.Name {
	case RoundPhaseJoin:
		// 已经满员
		if len(rr.UserIds) == rr.TotalSeat {
			// 进入开奖
			set["phase"] = RoundPhaseDraw
		} else {
			if p.Countdown == 0 {
				// 流局，并更新轮次状态为流局
				if err := m.nullify(ctx, rr); err != nil {
					return err
				}
				// 创建新一局
				newRound = true
			}
		}
	case RoundPhaseDraw:
		if p.Countdown == 0 {
			// 开奖，并更新阶段到展示结果阶段
			if err := m.draw(ctx, rr); err != nil {
				return err
			}
		}
	case RoundPhaseShow:
		if p.Countdown == 0 {
			newRound = true
		}
	}

	if len(set) > 0 {
		set["updatedAt"] = time.Now()
		set["phaseChangedAt"] = time.Now()
		// 更新当前轮次
		m.mc.Collection(RoundRecordCollectionName()).UpdateOne(
			ctx,
			bson.M{"_id": rr.Id, "phase": p.Name},
			bson.M{"$set": set},
		)
	}

	if newRound {
		// 新的轮次
		_, err := m.createRound(ctx)

		if err != nil {
			return err
		}
	}

	return nil
}

func (m *Manager) roundRecord(ctx context.Context) (*RoundRecord, error) {
	var r RoundRecord

	err := m.mc.Collection(RoundRecordCollectionName()).FindOne(
		ctx,
		bson.M{},
		&options.FindOneOptions{
			Sort: bson.M{"_id": -1},
		},
	).Decode(&r)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrRoundNotFound
		}

		return nil, err
	}

	return &r, nil
}

func (m *Manager) round(ctx context.Context) (*Round, error) {
	rr, err := m.roundRecord(ctx)

	if err != nil {
		return nil, err
	}

	return m.mixerRound(ctx, rr), nil
}

func (m *Manager) roundById(ctx context.Context, roundId string) (*Round, error) {
	rr, err := m.roundRecordById(ctx, roundId)

	if err != nil {
		return nil, err
	}

	return m.mixerRound(ctx, rr), nil
}

func (m *Manager) mixerRound(ctx context.Context, rr *RoundRecord) *Round {
	var winUser *types.User

	if rr.WinUserId != "" {
		acc, err := m.ug.Account(ctx, rr.WinUserId)

		if err == nil {
			winUser = mixer.User(ctx, acc)
		}
	}

	return &Round{
		RoundId:   rr.Id.Hex(),
		Phase:     m.phase(ctx, rr),
		Seats:     m.seats(ctx, rr),
		TotalSeat: rr.TotalSeat,
		Joined:    len(rr.Seats),
		WinSeatNo: rr.WinSeatNo,
		WinUser:   winUser,
		WinGiftId: rr.WinGiftId,
	}
}

func (m *Manager) phase(_ context.Context, rr *RoundRecord) Phase {
	var dst time.Time
	switch rr.Phase {
	case RoundPhaseJoin:
		dst = rr.PhaseChangedAt.Add(durationPhaseJoin)
	case RoundPhaseDraw:
		dst = rr.PhaseChangedAt.Add(durationPhaseDraw)
	case RoundPhaseShow:
		dst = rr.PhaseChangedAt.Add(durationPhaseShow)
	}

	countdown := time.Until(dst).Milliseconds()

	if countdown < 0 {
		countdown = 0
	}

	return Phase{
		Name:      rr.Phase,
		Countdown: countdown,
	}
}

func (m *Manager) seats(ctx context.Context, rr *RoundRecord) map[int]Seat {
	seats := make(map[int]Seat, 0)

	for i := 1; i <= rr.TotalSeat; i++ {
		var u *types.User

		// 参与成功的用户
		userId, ok := rr.Seats[i]

		if ok {
			if acc, err := m.ug.Account(ctx, userId); err == nil {
				u = mixer.User(ctx, acc)
			}
		}

		seats[i] = Seat{User: u}
	}

	return seats
}

func (m *Manager) createRound(ctx context.Context) (*RoundRecord, error) {
	if closed() {
		return nil, errors.New("activity closed")
	}

	_, err := m.mc.Collection(RoundRecordCollectionName()).InsertOne(
		ctx,
		RoundRecord{
			Id:             primitive.NewObjectID(),
			TotalSeat:      totalSeat,
			Seats:          make(map[int]string),
			UserIds:        make([]string, 0),
			Phase:          RoundPhaseJoin,
			Status:         RoundStatusRunning,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
			PhaseChangedAt: time.Now(),
		},
	)

	if err != nil {
		return nil, err
	}

	return m.roundRecord(ctx)
}
