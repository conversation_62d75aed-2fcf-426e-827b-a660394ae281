package christmas

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (m *Manager) Records(ctx context.Context, num int64) []Record {
	var rr []RoundRecord

	cursor, err := m.mc.Collection(RoundRecordCollectionName()).Find(
		ctx,
		bson.M{"status": RoundStatusEnd}, // 正常开奖的10轮，不包括流局的
		options.Find().SetSort(bson.M{"createdAt": -1}).SetLimit(num),
	)

	if err != nil {
		return nil
	}

	defer cursor.Close(ctx)

	err = cursor.All(ctx, &rr)

	if err != nil {
		return nil
	}

	var ret []Record

	for _, r := range rr {
		if r.WinUserId == "" {
			continue
		}

		acc, err := m.ug.Account(ctx, r.WinUserId)

		if err != nil {
			continue
		}

		ret = append(ret, Record{
			RoundId:    r.Id.Hex(),
			User:       mixer.User(ctx, acc),
			WinGiftId:  r.WinGiftId,
			SeatNo:     r.WinSeatNo,
			UpdateTime: r.UpdatedAt.Unix(),
		})
	}

	return ret
}

func (m *Manager) roundRecordById(ctx context.Context, roundId string) (*RoundRecord, error) {
	id, err := primitive.ObjectIDFromHex(roundId)

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "invalid round id")
	}

	var ret RoundRecord

	if err := m.mc.Collection(RoundRecordCollectionName()).FindOne(ctx, bson.M{"_id": id}).Decode(&ret); err != nil {
		return nil, err
	}

	return &ret, nil
}

func (m *Manager) userRecordById(ctx context.Context, userId, roundId string) (*UserRecord, error) {
	var ur UserRecord

	if err := m.mc.Collection(UserRecordCollectionName()).FindOne(ctx, bson.M{"userId": userId, "roundId": roundId}).Decode(&ur); err != nil {
		return nil, err
	}

	return &ur, nil
}
