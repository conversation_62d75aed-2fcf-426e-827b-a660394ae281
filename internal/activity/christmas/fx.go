package christmas

import (
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	mc *db.MongoClient,
	rc *redi.Client,
	dm *redi.Mutex,
	ug user.Getter,
	fm *fund.Manager,
	fg fund.Getter,
	imm *im.Manager,
	dsm *dress.Manager,
	pm *props.Manager,
	lm *level.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	createUserRecordIndexer(mc)

	return newManager(mc, rc, dm, ug, fm, fg, imm, dsm, pm, lm, vnd.Scope("christmas.mgr"))
}

func InvokeInAPI(sch *cron.Scheduler, evb ev.Bus, mgr *Manager) {
	if !env.APIServer() {
		return
	}

	// 活动下线
	// {
	// 	job := sch.Exclusive("act.christmas.round.loop", func(ctx context.Context) error {
	// 		return mgr.roundLoop()
	// 	})

	// 	sch.Periodic(time.Second, job)
	// }

	// {
	// 	evb.Watch(evt.GiftSendAdvanced, "act.christmas.gift", ev.NewWatcher(mgr.onGiftSentAdvanced), ev.WithAsync())
	// }

	// {
	// 	task := sch.Exclusive("STR:M:MUTEX:ACT:CHRISTMAS:RANK", func(ctx context.Context) error {
	// 		return mgr.rankAward()
	// 	})

	// 	// UTC每日凌晨3点 0分(巴西时间每天0点10分)
	// 	sch.Cron("10 3 * * *").Do(task)
	// }
}
