package christmas

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/sourcegraph/conc/pool"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type Table struct {
	Rows []Row
}

type Row struct {
	GiftId      int
	Name        string
	Diamond     int64
	Probability float64
}

func (t *Table) Pick() (*Row, error) {
	n, _ := rand.Int(rand.Reader, randomBase.BigInt())
	nd := decimal.NewFromBigInt(n, 0)

	var success bool
	var ret Row

	for _, row := range t.Rows {
		v := decimal.NewFromFloat(row.Probability).Mul(randomBase)
		if nd.Cmp(v) == -1 {
			success = true
			ret = row
			break
		}
		nd = nd.Sub(v)
	}

	if success {
		return &ret, nil
	}

	return nil, errors.New("pick failed")
}

func (m *Manager) draw(ctx context.Context, rr *RoundRecord) error {
	userIds := lo.Shuffle(rr.UserIds)

	logger := m.log.With(
		zap.String("roundId", rr.Id.Hex()),
		zap.Strings("userIds", rr.UserIds),
	)

	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(userIds))))

	if err != nil {
		logger.With(zap.Error(err)).Error("开奖失败")
		return err
	}

	// 中奖用户
	userId := userIds[n.Int64()]

	// 中奖礼物
	row, err := rt.Pick()

	if err != nil {
		logger.With(zap.Error(err)).Error("开奖失败")
		return err
	}

	winGiftId := row.GiftId

	winSeatNo := 0

	for seatNo, v := range rr.Seats {
		if v == userId {
			winSeatNo = seatNo
			break
		}
	}

	diamond := giftDiamond(winGiftId)

	logger = logger.With(
		zap.Int("winGiftId", winGiftId),
		zap.Int("winSeatNo", winSeatNo),
		zap.String("winUserId", userId),
		zap.Int64("diamond", diamond),
	)

	err = m.mc.TryTxn(ctx, func(ctx context.Context) error {
		// 更新用户记录
		ur1, err := m.mc.Collection(UserRecordCollectionName()).UpdateOne(
			ctx,
			bson.M{"roundId": rr.Id.Hex(), "userId": userId},
			bson.M{"$set": bson.M{"giftId": winGiftId}},
		)

		if err != nil {
			return err
		}

		if ur1.MatchedCount == 0 || ur1.ModifiedCount == 0 {
			return ErrRoundUpdated
		}

		ur, err := m.mc.Collection(RoundRecordCollectionName()).UpdateOne(
			ctx,
			bson.M{"_id": rr.Id, "phase": RoundPhaseDraw},
			bson.M{
				"$set": bson.M{
					"winUserId":      userId,
					"winGiftId":      winGiftId,
					"winSeatNo":      winSeatNo,
					"phase":          RoundPhaseShow,
					"status":         RoundStatusEnd,
					"updatedAt":      time.Now(),
					"phaseChangedAt": time.Now(),
				},
			},
		)

		if err != nil {
			return err
		}

		if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
			return ErrRoundUpdated
		}

		return nil
	})

	if err != nil {
		logger.With(zap.Error(err)).Error("开奖失败")

		return err
	}

	logger.Info("开奖成功")

	if err := m.pm.AddItem(ctx, rr.Id.Timestamp(),
		userId, propc.GPropId(winGiftId), 1,
		props.WithTradeNo(rr.Id.Hex()),
	); err != nil {
		logger.Warn("发放礼物失败", zap.Error(err))
	}

	p := pool.New().WithMaxGoroutines(rr.TotalSeat)
	for _, userId2 := range userIds {
		if userId2 != userId {
			p.Go(func() {
				if _, err := m.lm.AddExp(ctx, userId2, int(costDiamond*10)); err != nil {
					logger.Warn("增加用户经验失败", zap.Error(err))
				}
			})
		}

		m.setUnreadResult(ctx, userId2, rr.Id.Hex())
	}
	p.Wait()

	// 记录中奖用户的礼物统计
	day := time.Now().In(ctz.Brazil).Format("20060102")
	res := m.rc.ZIncrBy(ctx, m.getRankUserKey(day), float64(diamond), userId).Val()

	// 首次增加增加时间权重
	if res == float64(diamond) {
		m.rc.ZIncrBy(ctx, m.getRankUserKey(day), 1-float64(time.Now().Unix()/1e10), userId)
	}
	m.rc.Expire(ctx, m.getRankUserKey(day), time.Hour*24*15)
	m.rc.HIncrBy(ctx, m.getUserKey(day, userId), fmt.Sprintf("%d", winGiftId), 1)
	m.rc.Expire(ctx, m.getUserKey(day, userId), time.Hour*24*15)

	if ur, err := m.userRecordById(ctx, userId, rr.Id.Hex()); err == nil {
		msg := fmt.Sprintf(
			// 圣诞快乐！你在【x月x日 xx点xx分】参与的【圣诞幸运星】活动，幸运获得【礼物名称】*1，已放入你的礼物背包，快去直播间使用吧
			"Feliz Natal! Você participou do evento [Natal Sortudo] em [%s] e ganhou [%s]*1. A recompensa já foi colocada em sua mochila de presentes. Tente usá-la na sala de live! ",
			ur.CreatedAt.In(ctz.Brazil).Format("15:04 do 02/01"),
			giftName(winGiftId),
		)

		m.imm.SendSystemNoticeTextToUser(
			ctx,
			userId,
			msg,
		)

		logger.With(zap.String("content", msg)).Info("圣诞活动开奖发送消息")
	}

	return nil
}
