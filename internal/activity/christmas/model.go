package christmas

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserRecord struct {
	Id        primitive.ObjectID `bson:"_id"`       // 记录ID
	UserId    string             `bson:"userId"`    // 用户ID
	RoundId   string             `bson:"roundId"`   // 轮次ID
	Diamond   int64              `bson:"diamond"`   // 报名费
	GiftId    int                `bson:"giftId"`    // 礼物ID
	CreatedAt time.Time          `bson:"createdAt"` // 创建时间
}

// 用户参与记录
func UserRecordCollectionName() string {
	return "act.christmas.user.record"
}

func createUserRecordIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(UserRecordCollectionName(), 1,
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}},
		db.Indexer{Name: "createdAt", Keys: bson.D{
			{Key: "createdAt", Value: -1},
		}},
		db.Indexer{Name: "roundId", Keys: bson.D{
			{Key: "roundId", Value: 1},
		}},
	)
}

// 轮次记录
type RoundRecord struct {
	Id             primitive.ObjectID `bson:"_id"`            // 轮次ID
	TotalSeat      int                `json:"totalSeat"`      // 座位总数
	Seats          map[int]string     `bson:"seats"`          // 座位用户
	UserIds        []string           `bson:"userIds"`        // 用户列表
	WinUserId      string             `bson:"winUserId"`      // 获奖用户ID
	WinGiftId      int                `json:"winGiftId"`      // 获奖礼物ID
	WinSeatNo      int                `json:"winSeatNo"`      // 获奖座位号
	Phase          string             `bson:"phase"`          // 阶段
	Status         int                `bson:"status"`         // 状态
	CreatedAt      time.Time          `bson:"createdAt"`      // 创建时间
	UpdatedAt      time.Time          `bson:"updatedAt"`      // 更新时间
	PhaseChangedAt time.Time          `bson:"phaseChangedAt"` // 阶段变更时间
}

func RoundRecordCollectionName() string {
	return "act.christmas.round.record"
}
