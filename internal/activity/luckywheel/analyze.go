package luckywheel

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func dateOf(t time.Time) string {
	return t.In(tz).Format("20060102")
}

func (m *Manager) getTaskRecv(ctx context.Context, at time.Time, userId string, taskId taskId) (int, error) {
	var data Data
	if err := m.mc.Collection(dataDB).FindOne(ctx, bson.M{"date": dateOf(at), "userId": userId},
		options.FindOne().SetProjection(bson.M{fmt.Sprintf("recvTasks.%s", taskId): 1}),
	).Decode(&data); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return 0, err
		}
	}
	return data.RecvTasks[string(taskId)], nil
}

func (m *Manager) incData(ctx context.Context, at time.Time, userId string, field string, val int) {
	if err := m.mc.NoTxn(ctx, func(ctx context.Context) error {
		_, err := m.mc.Collection(dataDB).UpdateOne(
			ctx, bson.M{"date": dateOf(at), "userId": userId},
			bson.M{"$inc": bson.M{field: val}},
			options.Update().SetUpsert(true),
		)
		return err
	}); err != nil {
		m.log.Warn("update data failed", zap.Error(err))
	}
}
