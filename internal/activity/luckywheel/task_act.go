package luckywheel

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

var (
	ErrInvalidTaskStatus = biz.NewError(biz.ErrBusiness, "invalid task status")
)

type TaskInfo struct {
	Id       taskId `json:"id"`       // 任务ID
	Progress int    `json:"progress"` // 进度值
	Status   int    `json:"status"`   // 0: 未完成, 1: 已完成, 2: 已领取
	Got      int    `json:"got"`      // 已领取的数量
}

func (m *Manager) TaskList(ctx context.Context, at time.Time, userId string) ([]TaskInfo, error) {
	keys := lo.Keys(tasks)
	slices.Sort(keys)
	ts2 := make([]TaskInfo, 0, len(keys))
	for _, tid := range keys {
		task, err := m.taskInfo(ctx, at, userId, tid)
		if err != nil {
			return nil, err
		}
		ts2 = append(ts2, task)
	}
	return ts2, nil
}

func (m *Manager) taskInfo(ctx context.Context, at time.Time, userId string, taskId taskId) (TaskInfo, error) {
	task := TaskInfo{Id: taskId}
	if !Open(at) {
		return task, nil
	}

	task.Status, _ = m.rc.Get(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), taskId, userId)).Int()
	if tasks[taskId].multiple() {
		task.Got, _ = m.getTaskRecv(ctx, at, userId, taskId)
	}

	switch taskId {
	case taskSignIn:
		if task.Status == taskWait {
			if err := m.markTaskDone(ctx, at, userId, taskSignIn); err != nil {
				return task, err
			}
			task.Status = taskDone
		}
	case taskWatch:
		if task.Status != taskWait {
			break
		}
		done, dur, err := m.taskWatch(ctx, at, userId)
		if err != nil {
			return task, err
		}
		task.Progress = int(dur.Minutes())
		if done {
			task.Status = taskDone
		}
	}

	return task, nil
}

func (m *Manager) TaskInfo(ctx context.Context, at time.Time, userId string, tid string) (TaskInfo, error) {
	return m.taskInfo(ctx, at, userId, taskId(tid))
}

func (m *Manager) TaskRecv(ctx context.Context, at time.Time, userId string, tid string) error {
	if !Open(at) {
		return ErrActivityClosed
	}
	if status, err := m.rc.Get(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), tid, userId)).Int(); err != nil {
		if errors.Is(err, redis.Nil) {
			return ErrInvalidTaskStatus
		}
		return err
	} else if status != taskDone {
		return ErrInvalidTaskStatus
	}
	chance, err := m.markTaskOver(ctx, at, userId, taskId(tid))
	if err != nil {
		return err
	}
	m.incData(ctx, at, userId, fmt.Sprintf("recvTasks.%s", tid), chance)
	return nil
}
