package luckywheel

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

var startEndTimes = [][2]time.Time{
	{time.Date(2025, 03, 10, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 03, 16, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 03, 17, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 03, 23, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 03, 24, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 03, 30, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 03, 31, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 04, 06, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 04, 14, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 04, 20, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 04, 21, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 04, 27, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 05, 05, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 05, 11, 23, 59, 59, 0, ctz.Brazil)},
	{time.Date(2025, 05, 26, 0, 0, 0, 0, ctz.Brazil), time.Date(2025, 06, 01, 23, 59, 59, 0, ctz.Brazil)},
}

func stage(at time.Time) int {
	for stage, times := range startEndTimes {
		if at.After(times[0]) && at.Before(times[1]) {
			return stage
		}
	}
	return 0
}

func Time(at time.Time) (startAt time.Time, endAt time.Time) {
	for _, times := range startEndTimes {
		startAt, endAt = times[0], times[1]
		if at.After(startAt) && at.Before(endAt) {
			return
		}
	}
	return
}

func Open(t time.Time) bool {
	if dbg.Ing() {
		return true
	}
	startTime, endTime := Time(t)
	return t.After(startTime) && t.Before(endTime)
}

func closed(t time.Time) bool {
	if dbg.Ing() {
		return false
	}
	_, endTime := Time(t)
	return t.After(endTime)
}

func PrevStage(at time.Time) int {
	if ps := stage(at); ps > 0 {
		return ps - 1
	}
	return len(startEndTimes) - 1
}

func StageTime(stage int) (startAt, endAt time.Time) {
	return startEndTimes[stage][0], startEndTimes[stage][1]
}
