package luckywheel

import (
	"context"
	"slices"
	"time"

	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
)

func SetupAPI(mgr *Manager, r *api.Router, ml *mux.Locker) {
	{
		s := &apis{m: mgr}
		g := r.WithAuth().Group("/activity/luckywheel", ml.Middleware(mux.WithPOST))
		g.GET("/info", api.Generic(s.info))
		g.GET("/ranking", api.Generic(s.ranking))
		g.GET("/task/list", api.Generic(s.taskList))
		g.POST("/task/recv", api.Generic(s.taskRecv))
		g.POST("/draw", api.Generic(s.draw))
	}
}

type apis struct{ m *Manager }

func (s *apis) myBalance(ctx context.Context, at time.Time, userId string) (balance int64) {
	if chance, _ := s.m.TakeChance(ctx, at, userId); chance != nil {
		balance = chance.Balance()
	}
	return
}

type infoResp struct {
	StartTime int64 `json:"startTime"` // 开始时间：unix秒
	EndTime   int64 `json:"endTime"`   // 结束时间：unix秒
	Chances   int64 `json:"chances"`   // 剩余抽奖机会
	RefreshAt int64 `json:"refreshAt"` // 任务重置时间：unix秒
}

// @Tags 活动
// @Summary 幸运转盘活动-信息
// @Description 幸运转盘活动-信息
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=infoResp}
// @Router /api/v1/activity/luckywheel/info [get]
func (s *apis) info(ctx *api.Context, _ api.EmptyReq) (*infoResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	at := time.Now()
	startTime, endTime := Time(at)

	resp := infoResp{
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
		Chances:   s.myBalance(ctx, at, uac.UserId),
		RefreshAt: now.With(at.In(tz)).BeginningOfDay().AddDate(0, 0, 1).Unix(),
	}

	return &resp, nil
}

type rankUser struct {
	Rank  int         `json:"rank"`  // 排名（0代表未上榜）
	Value int64       `json:"value"` // 获得抽奖券
	User  *types.User `json:"user"`  // 用户信息
}

type rankingResp struct {
	Ranks  []rankUser `json:"ranks"`
	MyRank rankUser   `json:"myRank"` // 榜单为空时，我的rank也是0
}

// @Tags 活动
// @Summary 幸运转盘活动-榜单
// @Description 幸运转盘活动-榜单
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=rankingResp}
// @Router /api/v1/activity/luckywheel/ranking [get]
func (s *apis) ranking(ctx *api.Context, _ api.EmptyReq) (*rankingResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	at := time.Now()
	ranks, err := s.m.Ranking(at)
	if err != nil {
		return nil, err
	}

	var resp rankingResp

	resp.Ranks = lo.Map(lo.Subset(ranks, 0, 10), func(r Rank, i int) rankUser {
		ru := rankUser{
			Rank:  i + 1,
			Value: r.Gain,
			User:  mixer.User(ctx, mixer.NoErr(s.m.ug.Account(ctx, r.UserId))),
		}
		if r.UserId == uac.UserId {
			resp.MyRank = ru
		}
		return ru
	})

	if resp.MyRank.Rank == 0 {
		chance, err := s.m.TakeChance(ctx, at, uac.UserId)
		if err != nil {
			return nil, err
		}
		resp.MyRank = rankUser{
			Rank:  slices.IndexFunc(ranks, func(r Rank) bool { return r.UserId == uac.UserId }) + 1,
			Value: chance.Gain,
			User:  mixer.User(ctx, uac),
		}
	}

	return &resp, nil
}

type taskListResp struct {
	Tasks []TaskInfo `json:"tasks"`
}

// @Tags 活动
// @Summary 幸运转盘活动-任务列表
// @Description 幸运转盘活动-任务列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=taskListResp}
// @Router /api/v1/activity/luckywheel/task/list [get]
func (s *apis) taskList(ctx *api.Context, _ api.EmptyReq) (*taskListResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	tasks, err := s.m.TaskList(ctx, time.Now(), uac.UserId)
	if err != nil {
		return nil, err
	}

	return &taskListResp{Tasks: tasks}, nil
}

type taskRecvReq struct {
	Id string `json:"id"`
}

type taskRecvResp struct {
	Chances int64    `json:"chances"` // 剩余抽奖机会
	Updated TaskInfo `json:"updated"` // 更新后任务信息
}

// @Tags 活动
// @Summary 幸运转盘活动-任务领取
// @Description 幸运转盘活动-任务领取
// @Produce json
// @Security HeaderAuth
// @Param param body taskRecvReq true "请求参数"
// @Success 200 {object} codec.Response{data=taskRecvResp}
// @Router /api/v1/activity/luckywheel/task/recv [post]
func (s *apis) taskRecv(ctx *api.Context, req taskRecvReq) (*taskRecvResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	at := time.Now()
	if err := s.m.TaskRecv(ctx, at, uac.UserId, req.Id); err != nil {
		return nil, err
	}

	var resp taskRecvResp
	resp.Chances = s.myBalance(ctx, at, uac.UserId)
	resp.Updated, _ = s.m.TaskInfo(ctx, at, uac.UserId, req.Id)

	return &resp, nil
}

type drawReq struct {
	Count int `json:"count"`
}

type drawResp struct {
	Chances int64        `json:"chances"` // 剩余抽奖机会
	Result  []DrawResult `json:"result"`  // 抽奖结果
}

// @Tags 活动
// @Summary 幸运转盘活动-抽奖
// @Description 幸运转盘活动-抽奖
// @Produce json
// @Security HeaderAuth
// @Param param body drawReq true "请求参数"
// @Success 200 {object} codec.Response{data=drawResp}
// @Router /api/v1/activity/luckywheel/draw [post]
func (s *apis) draw(ctx *api.Context, req drawReq) (*drawResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	at := time.Now()
	results, err := s.m.Draw(ctx, at, uac.UserId, req.Count)
	if err != nil {
		return nil, err
	}

	return &drawResp{
		Chances: s.myBalance(ctx, at, uac.UserId),
		Result:  results,
	}, nil
}
