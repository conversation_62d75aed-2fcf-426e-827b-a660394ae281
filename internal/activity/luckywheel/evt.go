package luckywheel

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

func (m *Manager) onPKGift(ctx context.Context, evd *evt.UserSendGift) error {
	if !Open(evd.At) {
		return nil
	}
	return errors.Join(
		m.taskPKGift(ctx, evd),
	)
}

func (m *Manager) onRecharge(ctx context.Context, evd *evt.UserRechargeData) error {
	if !Open(evd.At) {
		return nil
	}
	return errors.Join(
		m.taskRecharge(ctx, evd.At, evd.UserId, evd.Amount),
	)
}

func (m *Manager) onGameWin(ctx context.Context, evd *evt.WinGame) error {
	if !Open(evd.At) {
		return nil
	}
	return errors.Join(
		m.taskGameWin(ctx, evd),
	)
}

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if !Open(evd.At) {
		return nil
	}
	return errors.Join(
		m.taskLuckWin(ctx, evd),
		m.taskBlindBox(ctx, evd),
	)
}

func (m *Manager) onRedpacket(ctx context.Context, evd *evt.UserSendRedpacket) error {
	if !Open(evd.At) {
		return nil
	}
	return errors.Join(
		m.taskRedPacket(ctx, evd),
	)
}
