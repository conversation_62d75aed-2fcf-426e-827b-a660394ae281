package luckywheel

import (
	"context"
	"slices"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

const (
	taskSignIn    taskId = "task1" // 每日签到
	taskWatch     taskId = "task2" // 观看直播
	taskLuckWin   taskId = "task3" // 幸运礼物中奖
	taskPKGift    taskId = "task4" // PK送礼
	taskRecharge  taskId = "task5" // 购买金币
	taskBlindBox  taskId = "task6" // 送盲盒
	taskRedPacket taskId = "task7" // 送红包
	taskGameWin1  taskId = "task8" // 捕鱼游戏
	taskGameWin2  taskId = "task9" // Slot游戏
)

var tasks = map[taskId]taskInfo{
	taskSignIn:    {},
	taskWatch:     {},
	taskLuckWin:   {},
	taskPKGift:    {},
	taskRecharge:  {stepUnit: 10000, dayMax: 20},
	taskBlindBox:  {dayMax: 20},
	taskRedPacket: {},
	taskGameWin1:  {},
	taskGameWin2:  {},
}

func (m *Manager) taskWatch(ctx context.Context, at time.Time, userId string) (bool, time.Duration, error) {
	if can, err := m.canExecTask(ctx, at, userId, taskWatch); err != nil {
		return false, 0, err
	} else if !can {
		return true, 0, nil
	}
	dur, err := m.otm.UserOnlineTime(ctx, userId, now.New(at).BeginningOfDay(), now.New(at).EndOfDay())
	if err != nil {
		return false, 0, err
	} else if dur < 15*time.Minute {
		return false, dur, nil
	}
	if err := m.markTaskDone(ctx, at, userId, taskWatch); err != nil {
		return false, dur, err
	}
	return true, dur, nil
}

func (m *Manager) taskLuckWin(ctx context.Context, evd *evt.SendGift) error {
	if !slices.ContainsFunc(evd.Prizes, func(p int) bool { return p >= 500 }) {
		return nil
	}
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskLuckWin); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskLuckWin)
}

func (m *Manager) taskPKGift(ctx context.Context, evd *evt.UserSendGift) error {
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskPKGift); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskPKGift)
}

func (m *Manager) taskRecharge(ctx context.Context, at time.Time, userId string, amount int) error {
	if _, err := m.incTaskStep(ctx, at, userId, taskRecharge, amount); err != nil {
		return err
	}
	if can, err := m.canExecTask(ctx, at, userId, taskRecharge); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, at, userId, taskRecharge)
}

func (m *Manager) taskBlindBox(ctx context.Context, evd *evt.SendGift) error {
	if evd.BlindBox == 0 {
		return nil
	}
	if _, err := m.incTaskStep(ctx, evd.At, evd.UserId, taskBlindBox, 1); err != nil {
		return err
	}
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskBlindBox); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskBlindBox)
}

func (m *Manager) taskRedPacket(ctx context.Context, evd *evt.UserSendRedpacket) error {
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskRedPacket); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskRedPacket)
}

var (
	fishGameIds = []string{"1022", "1034", "1037", "1052"}
	slotGameIds = []string{"1023", "1042", "1053", "1074", "1096", "1098"}
)

func (m *Manager) taskGameWin(ctx context.Context, evd *evt.WinGame) error {
	var gameTask taskId
	if slices.Contains(fishGameIds, evd.GameId) {
		gameTask = taskGameWin1
	} else if slices.Contains(slotGameIds, evd.GameId) {
		gameTask = taskGameWin2
	}
	if gameTask == "" {
		return nil
	}
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, gameTask); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, gameTask)
}
