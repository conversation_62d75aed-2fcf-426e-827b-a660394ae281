package luckywheel

import (
	"time"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(mc *db.MongoClient, rc *redi.Client, ug user.Getter, fm *fund.Manager, lm *level.Manager, pm *props.Manager, dsm *dress.Manager, otm *view.OnlineTimeManager, vnd log.Vendor) *Manager {
	mc.SyncSchema(dataDB, 1, dataIdx...)
	mc.SyncSchema(chanceDB, 1, chanceIdx...)
	mgr := &Manager{mc: mc, rc: rc, ug: ug, fm: fm, lm: lm, pm: pm, dsm: dsm, otm: otm, log: vnd.Scope("luckywheel.mgr")}
	mgr.initRankCache()
	return mgr
}

func Invoke(evb ev.Bus, mgr *Manager) {
	if closed(time.Now()) {
		return
	}
	evb.Watch(evt.EvPkUserSendGift, "luckywheel.task", ev.NewWatcher(mgr.onPKGift), ev.WithAsync())
	evb.Watch(evt.UserRecharge, "luckywheel.task", ev.NewWatcher(mgr.onRecharge), ev.WithAsync())
	evb.Watch(evt.GiftSend, "luckywheel.task", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())
	evb.Watch(evt.EvSendRedpacket, "luckywheel.task", ev.NewWatcher(mgr.onRedpacket), ev.WithAsync())
	evb.Watch(evt.EvGameWin, "luckywheel.task", ev.NewWatcher(mgr.onGameWin), ev.WithAsync())
}
