package luckywheel

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (c *Chance) Balance() int64 {
	return c.Gain - c.Used
}

func (m *Manager) TakeChance(ctx context.Context, at time.Time, userId string) (*Chance, error) {
	var chance Chance
	if err := m.mc.Collection(chanceDB).FindOne(ctx, bson.M{"stage": stage(at), "userId": userId}).Decode(&chance); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
	}
	return &chance, nil
}

func (m *Manager) addChance(ctx context.Context, at time.Time, userId string, amount int) error {
	if _, err := m.mc.Collection(chanceDB).UpdateOne(ctx,
		bson.M{"stage": stage(at), "userId": userId},
		bson.M{"$inc": bson.M{"gain": amount}, "$set": bson.M{"gainAt": at}},
		options.Update().SetUpsert(true),
	); err != nil {
		return err
	}
	return nil
}

func (m *Manager) useChance(ctx context.Context, at time.Time, userId string, amount int) error {
	if _, err := m.mc.Collection(chanceDB).UpdateOne(ctx,
		bson.M{"stage": stage(at), "userId": userId},
		bson.M{"$inc": bson.M{"used": amount}, "$set": bson.M{"usedAt": at}},
	); err != nil {
		return err
	}
	return nil
}
