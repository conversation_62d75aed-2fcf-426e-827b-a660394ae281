package luckywheel

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

var tz = ctz.Brazil

var (
	ErrActivityClosed = biz.NewError(biz.ErrBusiness, "activity closed")
)

type Manager struct {
	mc  *db.MongoClient
	rc  *redi.Client
	ug  user.Getter
	fm  *fund.Manager
	lm  *level.Manager
	pm  *props.Manager
	dsm *dress.Manager
	otm *view.OnlineTimeManager
	log *zap.Logger
	rankC
}
