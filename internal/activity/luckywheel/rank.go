package luckywheel

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Rank struct {
	UserId string
	Gain   int64
}

type rankC struct {
	rcc cc.Cache[int64, []Rank]
}

func (m *Manager) initRankCache() {
	m.rcc = cc.New[int64, []Rank](2, cc.LRU, cc.Expiration(3*time.Second), cc.LoaderFunc(func(ts int64) ([]Rank, error) {
		var (
			ctx = context.Background()
			at  = time.Unix(ts, 0).In(tz)
		)
		raw, err := db.DecodeAll[Chance](ctx)(m.mc.Collection(chanceDB).Find(ctx, bson.M{"stage": stage(at)},
			options.Find().SetSort(bson.D{{"gain", -1}, {"gainAt", 1}}).SetLimit(99)),
		)
		if err != nil {
			return nil, err
		}
		return lo.Map(raw, func(c Chance, _ int) Rank { return Rank{UserId: c.UserId, Gain: c.Gain} }), nil
	}))
}

func (m *Manager) Ranking(at time.Time) ([]Rank, error) {
	return m.rcc.Get(at.Unix())
}
