package luckywheel

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

var chanceIdx = []db.Indexer{
	{
		Name: "stage_userId",
		Uniq: lo.<PERSON>(true),
		Keys: bson.D{
			{Key: "stage", Value: 1},
			{Key: "userId", Value: 1},
		},
	},
	{
		Name: "stage_gain_at",
		Keys: bson.D{
			{Key: "stage", Value: 1},
			{Key: "gain", Value: -1},
			{Key: "gainAt", Value: 1},
		},
	},
}

type Chance struct {
	Stage  int       `bson:"stage"`
	UserId string    `bson:"userId"`
	Gain   int64     `bson:"gain"`
	GainAt time.Time `bson:"gainAt"`
	Used   int64     `bson:"used"`
	UsedAt time.Time `bson:"usedAt"`
}

const chanceDB = "act.luckywheel.chance.v2"

var dataIdx = []db.Indexer{
	{
		Name: "date_userId",
		Uniq: lo.<PERSON>(true),
		Keys: bson.D{
			{Key: "date", Value: 1},
			{Key: "userId", Value: 1},
		},
	},
}

type Data struct {
	Date        string         `bson:"date"` // 20060102
	UserId      string         `bson:"userId"`
	Draws       int64          `bson:"draws"`
	RewardExp   int64          `bson:"rewardExp"`
	RewardCoins int64          `bson:"rewardCoins"`
	RewardGifts map[int]int    `bson:"rewardGifts"`
	RecvTasks   map[string]int `bson:"recvTasks"`
}

const dataDB = "act.luckywheel.data.v2"
