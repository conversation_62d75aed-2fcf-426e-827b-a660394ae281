package luckywheel

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress/avatar"
)

const (
	rwdExp    = "exp"    // 经验
	rwdCoin   = "coin"   // 金币
	rwdAvatar = "avatar" // 头像框
	rwdGift1  = "gift1"  // 礼物1
	rwdGift2  = "gift2"  // 礼物2
	rwdGift3  = "gift3"  // 礼物3
	rwdGift4  = "gift4"  // 礼物4
	rwdGift5  = "gift5"  // 礼物5
)

var giftMap = map[string]int{
	rwdGift1: 1,     // Amor（1金币幸运礼物）
	rwdGift2: 19,    // VIVA BRASIL!（5金币幸运礼物）
	rwdGift3: 10034, // hi（1000金币特效礼物）
	rwdGift4: 10038, // Corneta（3000金币特效礼物）【高级】
	rwdGift5: 10032, // Racing（10w金币互动礼物）【稀有】
}

type reward struct {
	id    string
	count int // 金币数量/经验值/头像框天数/礼物数量
}

var (
	rwdList = []reward{
		{rwdCoin, 10},
		{rwdCoin, 20},
		{rwdCoin, 60},
		{rwdGift1, 20},
		{rwdGift1, 40},
		{rwdGift1, 100},
		{rwdGift2, 5},
		{rwdGift2, 15},
		{rwdGift2, 30},
		{rwdGift3, 1},
		{rwdGift4, 1},
		{rwdGift5, 1},
		{rwdAvatar, 3},
		{rwdAvatar, 7},
		{rwdExp, 10},
		{rwdExp, 50},
	}
	rwdRatio = map[int][]float64{
		0: { // B级：≤5级
			32.000,
			8.000,
			2.000,
			22.000,
			4.000,
			1.000,
			6.000,
			2.000,
			1.000,
			0.100,
			0.050,
			0.000,
			5.000,
			3.000,
			9.850,
			4.000,
		},
		1: { // A级：(5-20）级
			20.000,
			16.000,
			4.000,
			12.000,
			8.000,
			2.000,
			10.000,
			4.000,
			2.000,
			0.500,
			0.210,
			0.005,
			5.000,
			3.000,
			9.285,
			4.000,
		},
		2: { // S级：≥20级
			16.000,
			16.000,
			6.000,
			10.000,
			8.000,
			3.000,
			10.000,
			6.000,
			3.000,
			0.990,
			0.400,
			0.010,
			5.000,
			3.000,
			8.600,
			4.000,
		},
	}
	rwdPool = map[int][]*rng.Ratio[reward]{}
)

func init() {
	for lv, rates := range rwdRatio {
		rwdPool[lv] = make([]*rng.Ratio[reward], len(rates))
		for i, rate := range rates {
			rwdPool[lv][i] = rng.NewRatio(rate, rwdList[i])
		}
	}
}

func lvGear(lv int) int {
	if lv <= 5 {
		return 0
	} else if lv < 20 {
		return 1
	} else {
		return 2
	}
}

type DrawResult struct {
	Id    string `json:"id"`    // 奖品ID
	Count int    `json:"count"` // 金币数量/经验值/头像框天数/礼物数量
}

func (m *Manager) Draw(ctx context.Context, at time.Time, userId string, count int) ([]DrawResult, error) {
	if closed(at) {
		return nil, ErrActivityClosed
	}

	chance, err := m.TakeChance(ctx, at, userId)
	if err != nil {
		return nil, err
	} else if chance.Balance() < int64(count) {
		return nil, fund.ErrBalanceNotEnough
	}

	uac, err := m.ug.Account(ctx, userId)
	if err != nil {
		return nil, err
	}
	gear := lvGear(uac.Level)

	if err := m.useChance(ctx, at, userId, count); err != nil {
		return nil, err
	}

	m.incData(ctx, at, userId, "draws", count)

	var (
		exp     int                            // total exp
		coins   int                            // total coins
		border  int                            // max border days
		gifts   = make(map[int]int)            // giftId -> count
		results = make([]DrawResult, 0, count) // raw results
	)

	for i := 0; i < count; i++ {
		got := rng.Pick(rwdPool[gear]...)
		switch got.id {
		case rwdExp:
			exp += got.count
		case rwdCoin:
			coins += got.count
		case rwdAvatar:
			border = max(border, got.count)
		case rwdGift1, rwdGift2, rwdGift3, rwdGift4, rwdGift5:
			gifts[giftMap[got.id]] += got.count
		}
		results = append(results, DrawResult{Id: got.id, Count: got.count})
	}

	var errs []error
	if exp > 0 {
		if _, err := m.lm.AddExp(ctx, userId, exp); err != nil {
			errs = append(errs, err)
		} else {
			m.incData(ctx, at, userId, "rewardExp", exp)
		}
	}
	if coins > 0 {
		if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, coins); err != nil {
			errs = append(errs, err)
		} else {
			m.incData(ctx, at, userId, "rewardCoins", coins)
		}
	}
	if border > 0 {
		errs = append(errs, m.dsm.SetAvatarBorder(ctx, userId, avatar.BLuckyWheel, at.AddDate(0, 0, border)))
	}
	if len(gifts) > 0 {
		for giftId, giftCount := range gifts {
			if err := m.pm.AddItem(ctx, at, userId, propc.GPropId(giftId), giftCount); err != nil {
				errs = append(errs, err)
			} else {
				m.incData(ctx, at, userId, fmt.Sprintf("rewardGifts.%d", giftId), giftCount)
			}
		}
	}

	return results, errors.Join(errs...)
}
