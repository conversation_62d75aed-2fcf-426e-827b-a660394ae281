package payment_vehicle

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
)

func Provide(
	dm *dress.Manager,
	rc *redi.Client,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(dm, rc.Cluster("rank"), vnd.Scope("activity_vehicle.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
) {
	// 监听充值
	evb.Watch(evt.UserRecharge, "payment.vehicle.activity", ev.<PERSON>atcher(mgr.onRecharge), ev.WithAs<PERSON>())
}
