package payment_vehicle

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
	"time"
)

type Manager struct {
	dm  *dress.Manager
	rc  *redi.Client
	log *zap.Logger
}

func newManager(dm *dress.Manager, rc *redi.Client, log *zap.Logger) *Manager {
	m := &Manager{
		dm:  dm,
		rc:  rc,
		log: log,
	}

	return m
}

type StartEndAndStage struct {
	StartTime time.Time
	EndTime   time.Time
	Stage     string
}

var startEndTime = []StartEndAndStage{
	{
		StartTime: time.Date(2025, 03, 10, 0, 0, 0, 0, ctz.Brazil),
		EndTime:   time.Date(2025, 03, 16, 23, 59, 59, 0, ctz.Brazil),
		Stage:     "",
	},
	{
		StartTime: time.Date(2025, 03, 17, 0, 0, 0, 0, ctz.Brazil),
		EndTime:   time.Date(2025, 03, 23, 23, 59, 59, 0, ctz.Brazil),
		Stage:     "20250317",
	},
}

func (m *Manager) InPeriod(t time.Time) bool {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return true
		}
	}
	return false
}

func (m *Manager) GetPeriod(t time.Time) StartEndAndStage {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return ses
		}
	}
	return StartEndAndStage{}
}

func (m *Manager) getKey(t time.Time, userId string) string {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) && ses.Stage != "" {
			return fmt.Sprintf(newKeyPaymentVehicleUser, ses.Stage, userId)
		}
	}
	return fmt.Sprintf(keyPaymentVehicleUser, userId)
}

func (m *Manager) UserValueInfo(ctx context.Context, userId string) int64 {
	value, _ := m.rc.Get(ctx, m.getKey(time.Now(), userId)).Int64()
	return value
}
