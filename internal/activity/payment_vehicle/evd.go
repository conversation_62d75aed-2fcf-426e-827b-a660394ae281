package payment_vehicle

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

func (m *Manager) onRecharge(ctx context.Context, evd *evt.UserRechargeData) error {
	if !m.InPeriod(evd.At.In(ctz.Brazil)) {
		return nil
	}

	amount := int64(evd.Amount)

	key := m.getKey(evd.At.In(ctz.Brazil), evd.UserId)
	value := m.rc.IncrBy(ctx, key, amount).Val()
	m.rc.Expire(ctx, key, ttlPaymentVehicleUser)

	if value >= 1500000 && value < 3000000 && value-amount < 1500000 {
		// 座驾1
		if err := m.dm.SetVehicle(ctx, evd.UserId, "vehicle_01", time.Now().AddDate(0, 0, 7)); err != nil {
			return err
		}
		m.log.Info("获取座驾1成功", zap.String("userId", evd.UserId), zap.Int64("value", value))
	} else if value >= 3000000 && value < 5000000 && value-amount < 3000000 {
		// 座驾2
		if err := m.dm.SetVehicle(ctx, evd.UserId, "vehicle_02", time.Now().AddDate(0, 0, 7)); err != nil {
			return err
		}
		m.log.Info("获取座驾2成功", zap.String("userId", evd.UserId), zap.Int64("value", value))
	} else if value >= 5000000 && value-amount < 5000000 {
		// 座驾3
		if err := m.dm.SetVehicle(ctx, evd.UserId, "vehicle_03", time.Now().AddDate(0, 0, 7)); err != nil {
			return err
		}
		m.log.Info("获取座驾3成功", zap.String("userId", evd.UserId), zap.Int64("value", value))
	}

	return nil
}
