package foolsday

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress/vehicle"
	"go.uber.org/zap"
	"time"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if !lo.Contains(foolsDayGift, evd.GiftId) || evd.Count <= 0 {
		return nil
	}

	// 活动时间判断
	ses := m.GetSES(evd.At)
	if ses.Stage == "" {
		return nil
	}

	var value int64
	if evd.Gift.Lucky {
		value = int64(evd.Diamond)
	} else {
		value = int64(evd.Diamond) * 10
	}

	key := fmt.Sprintf(keyFoolsDayUser, ses.Stage, evd.UserId)
	v := m.rc.IncrBy(ctx, key, value).Val()
	m.rc.Expire(ctx, key, ttlFoolsDayUser)

	// 分数达到之后发放座驾
	if v >= TargetValue && v-value < TargetValue {
		// 记录已获得
		userGetKey := fmt.Sprintf(keyUserGetVehicles, evd.UserId)
		m.rc.HMSet(ctx, userGetKey, vehicle.JokerCar, 1)
		m.rc.Expire(ctx, userGetKey, ttlUserGetVehicles)

		// 查询用户当前座驾，如果已经
		p, err := m.dm.Take(ctx, evd.UserId)
		if err == nil && p != nil {
			if p.Vehicle == nil || (p.Vehicle != nil && p.Vehicle.Id != vehicle.JokerCar && p.Vehicle.Id != vehicle.PartyCar) {
				if err := m.dm.SetVehicle(ctx, evd.UserId, vehicle.JokerCar, time.Now().AddDate(0, 0, 3)); err != nil {
					return err
				}
				m.log.Info("获取小丑座驾1成功", zap.String("userId", evd.UserId), zap.Int64("value", v))
			} else if p.Vehicle != nil {
				m.log.Info("已拥有小丑座驾1", zap.String("userId", evd.UserId), zap.Int64("value", v), zap.String("id", p.Vehicle.Id))
			}
		}
	}

	return nil
}

// 统计礼物
func (m *Manager) onRocketSend(ctx context.Context, evd *evt.RocketSendData) error {
	if evd.GiftId != rocket.FoolsDayGiftId || evd.UserId == "" {
		return nil
	}

	// 记录已获得
	userGetKey := fmt.Sprintf(keyUserGetVehicles, evd.UserId)
	m.rc.HMSet(ctx, userGetKey, vehicle.PartyCar, 1)
	m.rc.Expire(ctx, userGetKey, ttlUserGetVehicles)

	// 查询用户当前座驾
	p, err := m.dm.Take(ctx, evd.UserId)
	if err == nil && p != nil {
		if p.Vehicle == nil || (p.Vehicle != nil && p.Vehicle.Id != vehicle.PartyCar) {
			if err := m.dm.SetVehicle(ctx, evd.UserId, vehicle.PartyCar, time.Now().AddDate(0, 0, 3)); err != nil {
				return err
			}
			m.log.Info("获取小丑座驾2成功", zap.String("userId", evd.UserId))
		} else {
			m.log.Info("已拥有小丑座驾2", zap.String("userId", evd.UserId), zap.String("id", p.Vehicle.Id))
		}
	}

	return nil
}
