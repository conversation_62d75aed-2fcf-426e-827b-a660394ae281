package foolsday

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
)

func Provide(
	rc *redi.Client,
	dm *dress.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(dm, rc.Cluster("rank"), vnd.Scope("foolsday.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
) {
	// 监听送礼
	evb.Watch(evt.GiftSend, "foolsday.activity.gift", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())
	// 监听火箭礼物
	evb.Watch(evt.RocketSend, "foolsday.rocket.send", ev.<PERSON>(mgr.onRocketSend), ev.With<PERSON>ync())
}
