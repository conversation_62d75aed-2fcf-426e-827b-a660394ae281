package foolsday

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
	"time"
)

type Manager struct {
	dm  *dress.Manager
	rc  *redi.Client
	log *zap.Logger
}

func newManager(dm *dress.Manager, rc *redi.Client, log *zap.Logger) *Manager {
	m := &Manager{
		dm:  dm,
		rc:  rc,
		log: log,
	}

	return m
}

type StartEndAndStage struct {
	StartTime time.Time
	EndTime   time.Time
	Stage     string
}

var startEndTime = []StartEndAndStage{
	{
		StartTime: time.Date(2025, 04, 01, 0, 0, 0, 0, ctz.Brazil),
		EndTime:   time.Date(2025, 04, 01, 23, 59, 59, 0, ctz.Brazil),
		Stage:     "20250401",
	},
}

func (m *Manager) GetSES(t time.Time) StartEndAndStage {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return ses
		}
	}
	return StartEndAndStage{}
}

func (m *Manager) UserValueInfo(ctx context.Context, userId string) int64 {
	ses := m.GetSES(time.Now().In(ctz.Brazil))
	if ses.Stage == "" {
		return 0
	}
	key := fmt.Sprintf(keyFoolsDayUser, ses.Stage, userId)
	value, _ := m.rc.Get(ctx, key).Int64()
	return value
}

func (m *Manager) UserGetVehicle(ctx context.Context, userId string) []string {
	var res []string
	key := fmt.Sprintf(keyUserGetVehicles, userId)
	g, err := m.rc.HGetAll(ctx, key).Result()
	if err != nil {
		return res
	}
	for k, _ := range g {
		res = append(res, k)
	}

	return res
}
