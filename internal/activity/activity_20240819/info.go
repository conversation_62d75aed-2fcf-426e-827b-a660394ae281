package activity_20240819

import (
	"github.com/jinzhu/now"
)

type Info struct {
	Status    int   // 活动状态 0：未开始 1：进行中 2：已结束
	StartTime int64 // 活动开始时间，毫秒时间戳
	EndTime   int64 // 活动结束时间，毫秒时间戳
	Day       int   // 活动第几天
	TotalDays int   // 活动天数
	Issue     int   // 活动期数
}

func (m *Manager) Info(nn *now.Now) *Info {
	var day int

	status := StatusNotStart

	st := getStartTime(nn.Time)
	et := getEndTime(nn.Time)

	if nn.After(st) && nn.Before(et) {
		status = StatusRunning
	} else if nn.After(et) {
		status = StatusEnd
	}

	if nn.After(st) {
		day = int(nn.Time.Unix()-st.Unix())/86400 + 1
	}

	return &Info{
		Status:    status,
		StartTime: st.UnixMilli(),
		EndTime:   et.UnixMilli(),
		Day:       day,
		TotalDays: TotalDays(nn),
		Issue:     12,
	}
}
