package activity_20240819

import (
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

const (
	StatusNotStart = iota
	StatusRunning
	StatusEnd
)

var (
	// startTime = time.Date(2024, 8, 19, 0, 0, 0, 0, ctz.Brazil)
	// endTime   = time.Date(2024, 8, 25, 23, 59, 59, 59, ctz.Brazil)

	// startTime2 = time.Date(2024, 8, 26, 0, 0, 0, 0, ctz.Brazil)
	// endTime2   = time.Date(2024, 9, 1, 23, 59, 59, 59, ctz.Brazil)

	// startTime3 = time.Date(2024, 9, 7, 0, 0, 0, 0, ctz.Brazil)
	// endTime3   = time.Date(2024, 9, 13, 23, 59, 59, 59, ctz.Brazil)

	// startTime4 = time.Date(2024, 9, 16, 0, 0, 0, 0, ctz.Brazil)
	// endTime4   = time.Date(2024, 9, 22, 23, 59, 59, 59, ctz.Brazil)

	// startTime5 = time.Date(2024, 9, 24, 0, 0, 0, 0, ctz.Brazil)
	// endTime5   = time.Date(2024, 9, 30, 23, 59, 59, 59, ctz.Brazil)

	// startTime6 = time.Date(2024, 10, 1, 0, 0, 0, 0, ctz.Brazil)
	// endTime6   = time.Date(2024, 10, 7, 23, 59, 59, 59, ctz.Brazil)

	// startTime7 = time.Date(2024, 10, 28, 0, 0, 0, 0, ctz.Brazil)
	// endTime7   = time.Date(2024, 11, 3, 23, 59, 59, 59, ctz.Brazil)

	// startTime8 = time.Date(2024, 11, 4, 0, 0, 0, 0, ctz.Brazil)
	// endTime8   = time.Date(2024, 11, 10, 23, 59, 59, 59, ctz.Brazil)

	// startTime9 = time.Date(2024, 11, 11, 0, 0, 0, 0, ctz.Brazil)
	// endTime9   = time.Date(2024, 11, 17, 23, 59, 59, 59, ctz.Brazil)

	// startTime10 = time.Date(2024, 11, 18, 0, 0, 0, 0, ctz.Brazil)
	// endTime10   = time.Date(2024, 11, 24, 23, 59, 59, 59, ctz.Brazil)

	// startTime11 = time.Date(2024, 11, 25, 0, 0, 0, 0, ctz.Brazil)
	// endTime11   = time.Date(2024, 12, 1, 23, 59, 59, 59, ctz.Brazil)

	newStartTime = time.Date(2024, 12, 3, 0, 0, 0, 0, ctz.Brazil)
)

func IsInPeriod(t time.Time) bool {
	st := getStartTime(t)
	et := getEndTime(t)

	return t.After(st) && t.Before(et)
}

func TotalDays(nn *now.Now) int {
	st := getStartTime(nn.Time)
	et := getEndTime(nn.Time)

	return int(et.Sub(st).Hours()/24) + 1
}

func getStartTime(t time.Time) time.Time {
	startTime := now.With(t).BeginningOfWeek()

	if startTime.Before(newStartTime) {
		return newStartTime
	}

	return startTime
}

func getEndTime(_ time.Time) time.Time {
	return time.Date(2025, 4, 25, 23, 59, 59, 59, ctz.Brazil)
}
