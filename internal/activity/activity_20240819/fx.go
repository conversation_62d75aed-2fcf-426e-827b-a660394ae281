package activity_20240819

import (
	"context"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Provide(
	rc *redi.Client,
	fm *fund.Manager,
	am *agency.Manager,
	sm *salary.Manager,
	im *im.Manager,
	psm *profitsharing.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	return newManager(rc, fm, am, sm, im, psm, vnd.Scope("activity.mgr"))
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
) error {
	if !env.APIServer() {
		return nil
	}

	evb.Watch(evt.GiftSend, "actitivy.20240819", ev.NewWatcher(mgr.onGiftSent), ev.WithAsync())

	{
		task := sch.Exclusive("STR:M:MUTEX:ACT:240819:AWARD", func(ctx context.Context) error {
			return mgr.Award(ctx, GetNowNow())
		})

		// UTC每日凌晨4点 5分(巴西时间每天1点5分)
		sch.Cron("5 4 * * *").Do(task)
	}

	return nil
}
