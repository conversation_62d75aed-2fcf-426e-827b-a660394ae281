package activity_20240819

import (
	"context"
	"strconv"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
)

func (m *Manager) onGiftSent(ctx context.Context, evd *evt.SendGift) error {
	if !evd.Gift.Lucky {
		return nil
	}

	if evd.Diamond <= 0 {
		return nil
	}

	nn := GetNowNow()

	// 活动未开始或已结束
	if !IsInPeriod(nn.Time) {
		return nil
	}

	anchorId := evd.AnchorId
	sessionId := evd.Session
	diamond := int64(evd.Diamond)

	// 增加主播排行榜
	m.incrDiamond(ctx, nn, anchorId, diamond, RankRoleAnchor)

	ps, err := m.psm.Get(anchorId, sessionId)

	if err == nil {
		if ps.AgencyId > 0 {
			m.incrDiamond(ctx, nn, strconv.FormatInt(ps.AgencyId, 10), diamond, RankRoleAgency)
		}
	} else {
		m.log.Error(
			"活动240819",
			zap.Error(err),
			zap.Any("evd", evd),
			zap.String("sessionId", sessionId),
		)
	}

	m.log.Info(
		"活动240819",
		zap.String("userId", evd.UserId),
		zap.String("anchorId", anchorId),
		zap.Int64("diamond", diamond),
	)

	return nil
}
