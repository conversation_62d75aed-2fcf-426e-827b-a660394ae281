package activity_20240819

import (
	"context"
	"fmt"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"go.uber.org/zap"
)

func newManager(
	rc *redi.Client,
	fm *fund.Manager,
	am *agency.Manager,
	sm *salary.Manager,
	im *im.Manager,
	psm *profitsharing.Manager,
	log *zap.Logger,
) (*Manager, error) {
	if err := rc.ScriptLoad(context.TODO(), luaScript).Err(); err != nil {
		return nil, fmt.Errorf("load lua script failed: %w", err)
	}

	return &Manager{
		rc:  rc,
		fm:  fm,
		am:  am,
		sm:  sm,
		im:  im,
		psm: psm,
		log: log,
	}, nil
}

type Manager struct {
	rc  *redi.Client
	fm  *fund.Manager
	am  *agency.Manager
	sm  *salary.Manager
	im  *im.Manager
	psm *profitsharing.Manager
	log *zap.Logger
}
