package rebate

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// ActivityRebateReceive 奖励领取记录
type ActivityRebateReceive struct {
	Id          primitive.ObjectID `bson:"_id"`
	UserId      string             `bson:"userId"`
	Date        string             `bson:"date"`
	RebateAward int                `bson:"rebateAward"`
	RankAward   int                `bson:"rankAward"`
	TotalAward  int                `bson:"totalAward"`
	IsReceive   bool               `bson:"isReceive"`
	CreateAt    time.Time          `bson:"createAt"` // 创建时间 领取时间
}

func ActivityRebateReceiveCollectionName() string {
	return "activity.rebate.receive"
}
