package rebate

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(
	pm *props.Manager,
	ug user.Getter,
	imm *im.Manager,
	fm *fund.Manager,
	rc *redi.Client,
	dm *redi.Mutex,
	dbmc *db.MongoClient,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(ActivityRebateReceiveCollectionName(), 1,
		db.Indexer{Name: "userId_receiveDate", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "date", Value: -1},
		}, Uniq: lo.ToPtr(true)},
	)

	m := newManager(ug, rc.Cluster("rank"), dm, pm, imm, fm, dbmc, vnd.Scope("rebate.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
) {
	// 监听送礼
	evb.Watch(evt.GiftSend, "rebate.activity.gift", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())
	// 监听游戏消费
	//evb.Watch(evt.EvGamePaid, "rebate.activity.game", ev.NewWatcher(mgr.onGamePaid), ev.WithAsync())
	// 巴西时间每天00:01发送奖励：UTC 03:01 = Sao_Paulo 00:01 生成记录
	sch.CronWithSeconds("0 1 3 * * *").Do(sch.Exclusive("activity.rebate.record", func(ctx context.Context) error {
		return mgr.genRecord(ctx, time.Now().In(ctz.Brazil).AddDate(0, 0, -1))
	}))
}
