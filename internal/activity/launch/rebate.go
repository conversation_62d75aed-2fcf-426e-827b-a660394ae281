package launch

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var (
	keyRebateReceive = "STR:ACT:L:REBATE:R:%s:%s" // userId:day
	ttlRebateReceive = time.Hour * 24 * 7
)

func (m *Manager) GetRebateAward(diamond int64) int64 {
	return int64(float64(diamond) * 0.01)
}

// 领取返利奖励
func (m *Manager) ReceiveRebateAward(ctx context.Context, userId string, nn *now.Now) (int64, error) {
	if !IsInActivityLaunchPeriod(nn.AddDate(0, 0, -1)) {
		return 0, biz.NewError(biz.ErrBusiness, "not in the activity period")
	}

	rank, err := m.getUserRank(ctx, m.getYesterdayRankKey(nn), userId)

	if err != nil {
		m.log.Error(
			"领取送礼活动奖励失败",
			zap.Error(err),
			zap.String("userId", userId),
			zap.String("award", "rebate"),
		)

		return 0, err
	}

	diamond := m.GetRebateAward(rank.Diamond)

	if diamond == 0 {
		return 0, biz.NewError(biz.ErrBusiness, "You are not eligible for the rebate award")
	}

	// 是否已领取
	key := m.getRebateReceiveKey(userId, nn.AddDate(0, 0, -1).Format("20060102"))

	if !m.rc.SetNX(ctx, key, 1, ttlRebateReceive).Val() {
		return 0, biz.NewError(biz.ErrBusiness, "You have received the rebate award")
	}

	tradeId := primitive.NewObjectID().Hex()

	err = m.fm.Income(
		ctx,
		userId,
		fund.JTypeRecharge,
		fund.PTypeDiamond,
		diamond,
		fund.WithTrade(tradeId),
		fund.WithTime(nn.Time),
	)

	if err != nil {
		m.log.Error(
			"领取送礼活动奖励失败",
			zap.Error(err),
			zap.String("userId", userId),
			zap.Int64("diamond", diamond),
			zap.String("tradeId", tradeId),
			zap.String("award", "rebate"),
		)

		return 0, err
	}

	m.log.Info(
		"领取送礼活动奖励成功",
		zap.String("userId", userId),
		zap.Int64("diamond", diamond),
		zap.String("tradeId", tradeId),
		zap.String("award", "rebate"),
	)

	return diamond, nil
}

func (m *Manager) GetRebateReceiveStatus(ctx context.Context, userId string, nn *now.Now) int {
	if m.rc.Exists(ctx, m.getRebateReceiveKey(userId, nn.AddDate(0, 0, -1).Format("20060102"))).Val() == 1 {
		return StatusReceiveFinish
	}

	return StatusReceiveAccept
}

func (m *Manager) getRebateReceiveKey(userId string, day string) string {
	return fmt.Sprintf(keyRebateReceive, userId, day)
}
