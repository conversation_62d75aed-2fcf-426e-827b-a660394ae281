package launch

import (
	"context"
	"fmt"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"go.uber.org/zap"
)

func newManager(
	rc *redi.Client,
	fm *fund.Manager,
	log *zap.Logger,
) (*Manager, error) {
	if err := rc.ScriptLoad(context.TODO(), luaScript).Err(); err != nil {
		return nil, fmt.Errorf("load lua script failed: %w", err)
	}

	return &Manager{rc: rc, fm: fm, log: log}, nil
}

type Manager struct {
	rc  *redi.Client
	fm  *fund.Manager
	log *zap.Logger
}
