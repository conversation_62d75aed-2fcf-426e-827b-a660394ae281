package launch

import "github.com/jinzhu/now"

type Info struct {
	Status    int   // 活动状态 0：未开始 1：进行中 2：已结束仍能领取奖励 3：已结束
	StartTime int64 // 活动开始时间，毫秒时间戳
	EndTime   int64 // 活动结束时间，毫秒时间戳
	Day       int   // 活动第几天
}

func (m *Manager) Info(nn *now.Now) *Info {
	var day int

	status := StatusNotStart

	if nn.After(startTime) && nn.Before(endTime) {
		status = StatusRunning

		day = int(nn.Time.Unix()-startTime.Unix())/86400 + 1
	} else if nn.After(endTime) && nn.Before(endTime.AddDate(0, 0, 1)) {
		status = StatusEndCanReceive
	} else if nn.After(endTime.AddDate(0, 0, 1)) {
		status = StatusEnd
	}

	return &Info{
		Status:    status,
		StartTime: startTime.UnixMilli(),
		EndTime:   endTime.UnixMilli(),
		Day:       day,
	}
}
