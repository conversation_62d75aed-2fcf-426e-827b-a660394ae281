package launch

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

const (
	StatusNotStart = iota
	StatusRunning
	StatusEndCanReceive
	StatusEnd
)

const (
	StatusReceiveReject = iota // 不可领取
	StatusReceiveAccept        // 可领取
	StatusReceiveFinish        // 已领取
)

var (
	startTime = time.Date(2024, 8, 1, 0, 0, 0, 0, ctz.Brazil)
	endTime   = time.Date(2024, 8, 7, 23, 59, 59, 59, ctz.Brazil)
)

func IsInActivityLaunchPeriod(t time.Time) bool {
	return t.After(startTime) && t.Before(endTime)
}
