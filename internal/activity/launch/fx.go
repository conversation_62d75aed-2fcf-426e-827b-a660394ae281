package launch

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
)

func Provide(
	rc *redi.Client,
	fm *fund.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	return newManager(rc, fm, vnd.Scope("activity.launch.mgr"))
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
) error {
	evb.Watch(evt.GiftSend, "actitivy.launch", ev.<PERSON>(mgr.onGiftSent), ev.WithAsync())

	return nil
}
