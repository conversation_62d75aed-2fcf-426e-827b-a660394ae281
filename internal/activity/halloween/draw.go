package halloween

import (
	"context"
	"errors"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
)

type reward struct {
	coins  int
	border bool
	empty  bool
}

var rewards = []*rng.Ratio[reward]{
	rng.NewRatio(60, reward{coins: 100}),
	rng.NewRatio(15, reward{coins: 300}),
	rng.NewRatio(3.5, reward{coins: 1000}),
	rng.NewRatio(0.5, reward{coins: 3000}),
	rng.NewRatio(11, reward{border: true}),
	rng.NewRatio(10, reward{empty: true}),
}

var Rewards = lo.Map(rewards[:len(rewards)-1], func(r *rng.Ratio[reward], _ int) DrawResult {
	return DrawResult{Coins: r.Data.coins, Border: r.Data.border}
})

type DrawResult struct {
	Coins  int  `json:"coins,omitempty"`  // 获得金币
	Border bool `json:"border,omitempty"` // 获得头像框
}

func (m *Manager) Draw(ctx context.Context, at time.Time, userId string, count int) ([]DrawResult, error) {
	chance, err := m.TakeChance(ctx, userId)
	if err != nil {
		return nil, err
	} else if chance.Balance() < int64(count) {
		return nil, fund.ErrBalanceNotEnough
	}

	if err := m.useChance(ctx, userId, count); err != nil {
		return nil, err
	}

	m.incData(ctx, at, userId, "draws", count)

	var (
		coins   int
		border  bool
		results []DrawResult
	)

	for i := 0; i < count; i++ {
		got := rng.Pick(rewards...)
		if got.coins > 0 {
			coins += got.coins
			results = append(results, DrawResult{Coins: got.coins})
		} else if got.border {
			border = true
			results = append(results, DrawResult{Border: true})
		}
	}

	var errs []error
	if coins > 0 {
		if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, coins); err != nil {
			errs = append(errs, err)
		} else {
			m.incData(ctx, at, userId, "rewardCoins", coins)
			m.drawCoins(ctx, at, userId, coins)
		}
	}
	if border {
		errs = append(errs, m.dsm.SetAvatarBorder(ctx, userId, avatarBorderUrl, at.AddDate(0, 0, 3)))
	}

	return results, errors.Join(errs...)
}
