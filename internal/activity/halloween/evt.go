package halloween

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

func (m *Manager) onPKWin(ctx context.Context, evd *evt.UserWinPk) error {
	if !Open() {
		return nil
	}
	return errors.Join(
		m.taskPKWin(ctx, evd),
	)
}

func (m *Manager) onRecharge(ctx context.Context, evd *evt.UserRechargeData) error {
	if !Open() {
		return nil
	}
	m.incData(ctx, evd.At, evd.UserId, "recharge", evd.Amount)
	return errors.Join(
		m.checkRebate(ctx, evd),
		m.taskRecharge(ctx, evd),
	)
}

func (m *Manager) onGamePlay(ctx context.Context, evd *evt.PlayGame) error {
	if !Open() {
		return nil
	}
	return errors.Join(
		m.taskGame(ctx, evd),
	)
}

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if !Open() {
		return nil
	}
	return errors.Join(
		m.taskLuckWin(ctx, evd),
		m.taskGifting(ctx, evd),
	)
}

func (m *Manager) onFollowing(ctx context.Context, evd *evt.FollowedUser) error {
	if !Open() {
		return nil
	}
	return errors.Join(
		m.taskFans(ctx, time.Now(), evd),
	)
}

func (m *Manager) onLevelUp(ctx context.Context, evd *evt.LevelUpgrade) error {
	if !Open() {
		return nil
	}
	return errors.Join(
		m.taskLevel(ctx, time.Now(), evd),
	)
}
