package halloween

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

var tz = ctz.Brazil

func newManager(db *db.Client, mc *db.MongoClient, rc *redi.Client, dm *redi.Mutex, ug user.Getter, fm *fund.Manager, dsm *dress.Manager, otm *view.OnlineTimeManager, imm *im.Manager, log *zap.Logger) *Manager {
	m := &Manager{
		db:  db,
		mc:  mc,
		rc:  rc,
		dm:  dm,
		ug:  ug,
		fm:  fm,
		dsm: dsm,
		otm: otm,
		imm: imm,
		log: log,
	}
	return m
}

type Manager struct {
	db  *db.Client
	mc  *db.MongoClient
	rc  *redi.Client
	dm  *redi.Mutex
	ug  user.Getter
	fm  *fund.Manager
	dsm *dress.Manager
	otm *view.OnlineTimeManager
	imm *im.Manager
	log *zap.Logger
}
