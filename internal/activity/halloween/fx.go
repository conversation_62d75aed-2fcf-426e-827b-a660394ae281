package halloween

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.Client, mc *db.MongoClient, rc *redi.Client, dm *redi.Mutex, ug user.Getter, fm *fund.Manager, dsm *dress.Manager, otm *view.OnlineTimeManager, imm *im.Manager, vnd log.Vendor) *Manager {
	mc.SyncSchema(dataDB, 1, dataIdx...)
	mc.SyncSchema(inviteDB, 1, inviteIdx...)
	mc.SyncSchema(rebateDB, 1)
	mc.SyncSchema(chanceDB, 1)
	return newManager(db, mc, rc, dm, ug, fm, dsm, otm, imm, vnd.Scope("halloween.mgr"))
}

func Invoke(evb ev.Bus, mgr *Manager) {
	if closed() {
		return
	}
	if env.Scheduler() {
		evb.Watch(evt.EvPKWin, "halloween.task", ev.NewWatcher(mgr.onPKWin), ev.WithAsync())
	}
	if env.APIServer() {
		evb.Watch(evt.EvGamePlay, "halloween.task", ev.NewWatcher(mgr.onGamePlay), ev.WithAsync())
		evb.Watch(evt.GiftSend, "halloween.task", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())
		evb.Watch(evt.UserFollowed, "halloween.task", ev.NewWatcher(mgr.onFollowing), ev.WithAsync())
		evb.Watch(evt.UserLevelUpgrade, "halloween.task", ev.NewWatcher(mgr.onLevelUp), ev.WithAsync())
	}
	evb.Watch(evt.UserRecharge, "halloween.task", ev.NewWatcher(mgr.onRecharge), ev.WithAsync())
}
