package halloween

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

var (
	StartTime = time.Date(2024, 10, 28, 0, 0, 0, 0, ctz.Brazil)
	EndTime   = time.Date(2024, 11, 03, 23, 59, 59, 0, ctz.Brazil)
)

const (
	avatarBorderUrl = "https://godzilla-live-oss.kako.live/res/avatar/b2.png"
)

func Open() bool {
	if dbg.Ing() {
		return true
	}
	now := time.Now()
	return now.After(StartTime) && now.Before(EndTime)
}

func closed() bool {
	if dbg.Ing() {
		return false
	}
	return time.Now().After(EndTime)
}
