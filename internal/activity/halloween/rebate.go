package halloween

import (
	"context"
	"errors"
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var rebatePoints = [][2]int64{
	{100000, 10000},
	{300000, 30000},
	{1000000, 100000},
	{5000000, 500000},
	{10000000, 800000},
	{30000000, 3000000},
}

func (r *Rebate) Reward() int64 {
	var total int64
	for i := 0; i < len(rebatePoints); i++ {
		point, reward := rebatePoints[i][0], rebatePoints[i][1]
		if r.Receive >= point {
			total += reward
		}
	}
	return total
}

func (m *Manager) RebateInfo(ctx context.Context, userId string) (*Rebate, error) {
	var rebate Rebate
	if err := m.mc.Collection(rebateDB).FindOne(ctx, bson.M{"_id": userId}).Decode(&rebate); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
	}
	return &rebate, nil
}

func (m *Manager) checkRebate(ctx context.Context, evd *evt.UserRechargeData) error {
	referrer, err := m.getReferrer(ctx, evd.UserId)
	if err != nil {
		return err
	} else if referrer == "" {
		return nil
	}

	var rebate Rebate
	if err := m.mc.Collection(rebateDB).FindOneAndUpdate(ctx,
		bson.M{"_id": referrer},
		bson.M{"$inc": bson.M{"charged": fund.New(evd.Amount)}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	).Decode(&rebate); err != nil {
		return err
	}

	for i := len(rebatePoints) - 1; i >= 0; i-- {
		point, reward := rebatePoints[i][0], rebatePoints[i][1]
		if rebate.Receive == point {
			break
		}
		if rebate.Charged.IntPart() >= point {
			if err := m.fm.Income(ctx, referrer, fund.JTypeRewards, fund.PTypeDiamond, reward); err != nil {
				m.log.Warn("send rebate rewards failed", zap.String("userId", referrer))
			} else if _, err := m.mc.Collection(rebateDB).UpdateByID(ctx, referrer, bson.M{"$set": bson.M{"receive": point}}); err != nil {
				m.log.Warn("update rebate point failed", zap.String("userId", referrer))
			}
			_ = m.imm.SendSystemNoticeTextToUser(ctx, referrer,
				fmt.Sprintf(`Parabéns! Você completou a tarefa do evento "Convide novos usuários para recarregar" e ganhou %d coins como recompensa. A recompensa foi enviada para sua conta, por favor, verifique!`, reward),
			)
			break
		}
	}

	return nil
}
