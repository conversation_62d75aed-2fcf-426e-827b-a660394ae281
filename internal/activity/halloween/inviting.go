package halloween

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (m *Manager) InviteCount(ctx context.Context, userId string) (int64, error) {
	return m.mc.Collection(inviteDB).CountDocuments(ctx, bson.M{"userId": userId})
}

func (m *Manager) invitedBy(ctx context.Context, userId string, newUserId string) error {
	var (
		now = time.Now()
	)

	m.incData(ctx, now, userId, "inviteUsers", 1)

	if _, err := m.mc.Collection(inviteDB).InsertOne(ctx, bson.M{"userId": userId, "newUserId": newUserId, "createdAt": now}); err != nil {
		m.log.Warn("write invite record failed", zap.Error(err))
	}

	_ = m.taskInvite(ctx, now, userId)

	return nil
}

func (m *Manager) getReferrer(ctx context.Context, userId string) (string, error) {
	var invite Invite
	if err := m.mc.Collection(inviteDB).FindOne(ctx, bson.M{"newUserId": userId}).Decode(&invite); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return "", nil
		}
		return "", err
	}
	return invite.UserId, nil
}

func (m *Manager) ReferrerChk(ctx context.Context, deviceId, userId string, refer string) error {
	if !strings.HasPrefix(refer, "HW") {
		return nil
	}

	if !Open() {
		return nil
	}

	key1 := fmt.Sprintf(keyInviteDevFlag, deviceId)
	if m.rc.Exists(ctx, key1).Val() > 0 && !dbg.Ing() {
		return nil
	}

	newUser, err := m.ug.Account(ctx, userId)
	if err != nil {
		return err
	} else if time.Since(newUser.CreatedAt) > ttlInviteUsrFlag {
		return nil
	}

	key2 := fmt.Sprintf(keyInviteUsrFlag, userId)
	if m.rc.Exists(ctx, key2).Val() > 0 {
		return nil
	}

	refUID, _ := strconv.ParseInt(refer[2:], 10, 64)

	referrer, err := m.ug.GetByNumId(ctx, refUID)
	if err != nil {
		return nil
	}

	if err := m.invitedBy(ctx, referrer.UserId, userId); err != nil {
		return err
	}

	txp := m.rc.Pipeline()
	txp.Set(ctx, key1, 1, ttlInviteDevFlag)
	txp.Set(ctx, key2, 1, ttlInviteUsrFlag)
	if _, err := txp.Exec(ctx); err != nil {
		return err
	}

	return nil
}

func ReferCode(acc *user.Account) string {
	return fmt.Sprintf("HW%d", acc.NumId)
}
