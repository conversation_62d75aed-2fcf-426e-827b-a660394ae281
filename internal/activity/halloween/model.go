package halloween

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

type Chance struct {
	Id   string `bson:"_id"` // userId
	Gain int64  `bson:"gain"`
	Used int64  `bson:"used"`
}

const chanceDB = "halloween.chance"

type Rebate struct {
	Id      string       `bson:"_id"` // userId
	Charged fund.Decimal `bson:"charged"`
	Receive int64        `bson:"receive"`
}

const rebateDB = "halloween.rebate"

var inviteIdx = []db.Indexer{
	{
		Name: "userId_createdAt",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},
	{
		Name: "newUserId",
		Keys: bson.D{
			{Key: "newUserId", Value: 1},
		},
	},
}

type Invite struct {
	UserId    string    `bson:"userId"`
	NewUserId string    `bson:"newUserId"`
	CreatedAt time.Time `bson:"createdAt"`
}

const inviteDB = "halloween.invite"

var dataIdx = []db.Indexer{
	{
		Name: "date_userId",
		Uniq: lo.ToPtr(true),
		Keys: bson.D{
			{Key: "date", Value: 1},
			{Key: "userId", Value: 1},
		},
	},
}

type TaskCount map[taskId]int64

type Data struct {
	Date        string    `bson:"date"` // 20060102
	UserId      string    `bson:"userId"`
	Draws       int64     `bson:"draws"`
	Recharge    int64     `bson:"recharge"`
	RewardCoins int64     `bson:"rewardCoins"`
	InviteUsers int64     `bson:"inviteUsers"`
	RecvTasks   TaskCount `bson:"recvTasks"`
}

const dataDB = "halloween.data"
