package halloween

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

type taskId string

const (
	taskWatch    taskId = "task1"
	taskPKWin    taskId = "task2"
	taskRecharge taskId = "task3"
	taskGame     taskId = "task4"
	taskLuckWin  taskId = "task5"
	taskInvite   taskId = "task6"
	taskFans     taskId = "task7"
	taskGifting  taskId = "task8"
	taskLevel    taskId = "task9"
)

var (
	normalTasks = []taskId{taskWatch, taskPKWin, taskRecharge, taskGame, taskLuckWin, taskInvite}
	newbieTasks = []taskId{taskFans, taskGifting, taskLevel}
	stagedTasks = []taskId{taskGame, taskFans, taskGifting}
)

var tasks = map[taskId]taskInfo{
	taskWatch:    {1, 0},
	taskPKWin:    {1, 0},
	taskRecharge: {3, 0},
	taskGame:     {1, 0},
	taskLuckWin:  {1, 0},
	taskInvite:   {5, 2},
	taskFans:     {1, 1},
	taskGifting:  {3, 1},
	taskLevel:    {5, 1},
}

type taskInfo struct {
	chance int
	limit  int // 0 按天 1 一次性 2 无上限
}

func (m *Manager) taskWatch(ctx context.Context, at time.Time, userId string) (bool, time.Duration, error) {
	if can, err := m.canExecTask(ctx, at, userId, taskWatch); err != nil {
		return false, 0, err
	} else if !can {
		return true, 0, nil
	}
	dur, err := m.otm.UserOnlineTime(ctx, userId, now.New(at).BeginningOfDay(), now.New(at).EndOfDay())
	if err != nil {
		return false, 0, err
	} else if dur < 30*time.Minute {
		return false, dur, nil
	}
	if err := m.markTaskDone(ctx, at, userId, taskWatch); err != nil {
		return false, dur, err
	}
	return true, dur, nil
}

func (m *Manager) taskPKWin(ctx context.Context, evd *evt.UserWinPk) error {
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskPKWin); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskPKWin)
}

func (m *Manager) taskRecharge(ctx context.Context, evd *evt.UserRechargeData) error {
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskRecharge); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskRecharge)
}

func (m *Manager) taskGame(ctx context.Context, evd *evt.PlayGame) error {
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskGame); err != nil {
		return err
	} else if !can {
		return nil
	}
	if uniq, err := m.addTaskItem(ctx, evd.At, evd.UserId, taskGame, fmt.Sprintf("%s:%s", evd.Platform, evd.GameId)); err != nil {
		return err
	} else if !uniq {
		return nil
	}
	if cnt, err := m.incTaskStep(ctx, evd.At, evd.UserId, taskGame); err != nil {
		return err
	} else if cnt == 5 {
		return m.markTaskDone(ctx, evd.At, evd.UserId, taskGame)
	}
	return nil
}

func (m *Manager) taskLuckWin(ctx context.Context, evd *evt.SendGift) error {
	if !slices.Contains([]int{20, 21}, evd.GiftId) {
		return nil
	}
	if !slices.ContainsFunc(evd.Prizes, func(p int) bool { return p >= 500 }) {
		return nil
	}
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskLuckWin); err != nil {
		return err
	} else if !can {
		return nil
	}
	return m.markTaskDone(ctx, evd.At, evd.UserId, taskLuckWin)
}

func (m *Manager) taskInvite(ctx context.Context, at time.Time, userId string) error {
	return m.markTaskOver(ctx, at, userId, taskInvite)
}

func (m *Manager) taskFans(ctx context.Context, at time.Time, evd *evt.FollowedUser) error {
	if is, err := m.isNewUser(ctx, evd.To); err != nil {
		return err
	} else if !is {
		return nil
	}
	if can, err := m.canExecTask(ctx, at, evd.To, taskFans); err != nil {
		return err
	} else if !can {
		return nil
	}
	if cnt, err := m.incTaskStep(ctx, at, evd.To, taskFans); err != nil {
		return err
	} else if cnt == 5 {
		return m.markTaskDone(ctx, at, evd.To, taskFans)
	}
	return nil
}

func (m *Manager) taskGifting(ctx context.Context, evd *evt.SendGift) error {
	if is, err := m.isNewUser(ctx, evd.UserId); err != nil {
		return err
	} else if !is {
		return nil
	}
	if can, err := m.canExecTask(ctx, evd.At, evd.UserId, taskGifting); err != nil {
		return err
	} else if !can {
		return nil
	}
	if uniq, err := m.addTaskItem(ctx, evd.At, evd.UserId, taskGifting, evd.AnchorId); err != nil {
		return err
	} else if !uniq {
		return nil
	}
	if cnt, err := m.incTaskStep(ctx, evd.At, evd.UserId, taskGifting); err != nil {
		return err
	} else if cnt == 3 {
		return m.markTaskDone(ctx, evd.At, evd.UserId, taskGifting)
	}
	return nil
}

func (m *Manager) taskLevel(ctx context.Context, at time.Time, evd *evt.LevelUpgrade) error {
	if evd.Level < 10 {
		return nil
	}
	if is, err := m.isNewUser(ctx, evd.UserId); err != nil {
		return err
	} else if !is {
		return nil
	}
	return m.markTaskDone(ctx, at, evd.UserId, taskLevel)
}

func (m *Manager) isNewUser(ctx context.Context, userId string) (bool, error) {
	uac, err := m.ug.Account(ctx, userId)
	if err != nil {
		return false, err
	}
	if uac.CreatedAt.Before(StartTime) {
		return false, nil
	}
	return true, nil
}

func (m *Manager) canExecTask(ctx context.Context, at time.Time, userId string, taskId taskId) (bool, error) {
	key := fmt.Sprintf(keyTaskDone, dateOf(at), taskId, userId)
	if cnt, err := m.rc.Exists(ctx, key).Result(); err != nil {
		return false, err
	} else if cnt > 0 {
		return false, nil
	}
	return true, nil
}

func (m *Manager) markTaskDone(ctx context.Context, at time.Time, userId string, taskId taskId) error {
	return m.markTaskStatus(ctx, at, userId, taskId, taskDone)
}

func (m *Manager) markTaskOver(ctx context.Context, at time.Time, userId string, taskId taskId) error {
	if err := m.markTaskStatus(ctx, at, userId, taskId, taskOver); err != nil {
		return err
	}
	return m.addChance(ctx, userId, tasks[taskId].chance)
}

func (m *Manager) markTaskStatus(ctx context.Context, at time.Time, userId string, taskId taskId, status int) error {
	return m.rc.Set(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), taskId, userId), status, taskTTL(taskId, at)).Err()
}

func (m *Manager) incTaskStep(ctx context.Context, at time.Time, userId string, taskId taskId) (int64, error) {
	key := fmt.Sprintf(keyTaskStep, dateOf(at), taskId, userId)
	txp := m.rc.Pipeline()
	ret1 := txp.Incr(ctx, key)
	txp.Expire(ctx, key, taskTTL(taskId, at))
	if _, err := txp.Exec(ctx); err != nil {
		return 0, err
	}
	return ret1.Val(), nil
}

func (m *Manager) addTaskItem(ctx context.Context, at time.Time, userId string, taskId taskId, refer string) (bool, error) {
	key := fmt.Sprintf(keyTaskUniq, dateOf(at), taskId, userId)
	txp := m.rc.Pipeline()
	ret1 := txp.SAdd(ctx, key, refer)
	txp.Expire(ctx, key, taskTTL(taskId, at))
	if _, err := txp.Exec(ctx); err != nil {
		return false, err
	} else if ret1.Val() == 0 {
		return false, nil
	}
	return true, nil
}

func taskTTL(taskId taskId, at time.Time) time.Duration {
	switch tasks[taskId].limit {
	case 0:
		return dayTTL(at)
	case 1:
		return EndTime.Sub(at)
	default:
		return 24 * time.Hour
	}
}

func dayTTL(t time.Time) time.Duration {
	t = t.In(tz)
	return now.New(t).EndOfDay().Sub(t).Truncate(time.Second) + time.Second
}
