package halloween

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

var (
	ErrInvalidTaskStatus = biz.NewError(biz.ErrBusiness, "invalid task status")
)

type TaskInfo struct {
	Id       taskId `json:"id"`       // 任务ID
	Progress int    `json:"progress"` // 进度值
	Status   int    `json:"status"`   // 0: 未完成, 1: 已完成, 2: 已领取
}

func (m *Manager) TaskList(ctx context.Context, at time.Time, userId string) ([]TaskInfo, error) {
	uac, err := m.ug.Account(ctx, userId)
	if err != nil {
		return nil, err
	}
	isNew := uac.CreatedAt.After(StartTime)

	ts1 := make([]TaskInfo, 0, 3)
	if isNew {
		for _, tid := range newbieTasks {
			task, err := m.taskInfo(ctx, at, userId, tid)
			if err != nil {
				return nil, err
			}
			switch task.Id {
			case taskLevel:
				if task.Status == 0 {
					task.Progress = uac.Level
				}
			}
			ts1 = append(ts1, task)
		}
	}

	ts2 := make([]TaskInfo, 0, 6)
	for _, tid := range normalTasks {
		task, err := m.taskInfo(ctx, at, userId, tid)
		if err != nil {
			return nil, err
		}
		switch task.Id {
		case taskWatch:
			if task.Status != 0 {
				break
			}
			done, dur, err := m.taskWatch(ctx, at, userId)
			if err != nil {
				return nil, err
			}
			task.Progress = int(dur.Minutes())
			if done {
				task.Status = taskDone
			}
		case taskInvite:
			if cnt, err := m.InviteCount(ctx, userId); err != nil {
				return nil, err
			} else {
				task.Progress = int(cnt) * tasks[taskInvite].chance
			}
		}
		ts2 = append(ts2, task)
	}

	if isNew && slices.ContainsFunc(ts1, func(t TaskInfo) bool { return t.Status != taskOver }) {
		return append(ts1, ts2...), nil
	} else {
		return append(ts2, ts1...), nil
	}
}

func (m *Manager) taskInfo(ctx context.Context, at time.Time, userId string, taskId taskId) (TaskInfo, error) {
	ret := TaskInfo{Id: taskId}
	ret.Status, _ = m.rc.Get(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), taskId, userId)).Int()
	if ret.Status == 0 && slices.Contains(stagedTasks, taskId) {
		ret.Progress, _ = m.rc.Get(ctx, fmt.Sprintf(keyTaskStep, dateOf(at), taskId, userId)).Int()
	}
	return ret, nil
}

func (m *Manager) TaskRecv(ctx context.Context, at time.Time, userId string, tid string) error {
	if status, err := m.rc.Get(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), tid, userId)).Int(); err != nil {
		if errors.Is(err, redis.Nil) {
			return ErrInvalidTaskStatus
		}
		return err
	} else if status != taskDone {
		return ErrInvalidTaskStatus
	}
	if err := m.markTaskOver(ctx, at, userId, taskId(tid)); err != nil {
		return err
	}
	m.incData(ctx, at, userId, fmt.Sprintf("recvTasks.%s", tid), 1)
	return nil
}
