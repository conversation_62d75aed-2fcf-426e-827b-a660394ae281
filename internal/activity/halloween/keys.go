package halloween

import "time"

const (
	taskDone = 1
	taskOver = 2
)

const (
	keyTaskDone = "HALLOW:TASK:DONE:%s:%s:%s" // date, taskId, userId
	keyTaskStep = "HALLOW:TASK:STEP:%s:%s:%s" // date, taskId, userId
	keyTaskUniq = "HALLOW:TASK:UNIQ:%s:%s:%s" // date, taskId, userId
)

const (
	keyInviteDevFlag = "HALLOW:INVITE:FLAG:DEV:%s" // deviceId
	ttlInviteDevFlag = 7 * 24 * time.Hour

	keyInviteUsrFlag = "HALLOW:INVITE:FLAG:USR:%s" // userId
	ttlInviteUsrFlag = time.Hour
)
