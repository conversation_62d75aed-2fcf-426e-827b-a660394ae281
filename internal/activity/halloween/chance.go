package halloween

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (c *Chance) Balance() int64 {
	return c.Gain - c.Used
}

func (m *Manager) TakeChance(ctx context.Context, userId string) (*Chance, error) {
	var chance Chance
	if err := m.mc.Collection(chanceDB).FindOne(ctx, bson.M{"_id": userId}).Decode(&chance); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
	}
	return &chance, nil
}

func (m *Manager) addChance(ctx context.Context, userId string, amount int) error {
	if _, err := m.mc.Collection(chanceDB).UpdateByID(ctx, userId, bson.M{"$inc": bson.M{"gain": amount}},
		options.Update().SetUpsert(true),
	); err != nil {
		return err
	}
	return nil
}

func (m *Manager) useChance(ctx context.Context, userId string, amount int) error {
	if _, err := m.mc.Collection(chanceDB).UpdateByID(ctx, userId, bson.M{"$inc": bson.M{"used": amount}}); err != nil {
		return err
	}
	return nil
}
