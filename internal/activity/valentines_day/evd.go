package valentines_day

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if evd.GiftId == 0 || evd.Count <= 0 || evd.AnchorId == "" {
		return nil
	}

	// 活动时间判断
	if !m.InPeriod(evd.At) {
		return nil
	}

	var value = 0
	if evd.GiftId == flowerGiftId {
		value = 2 * evd.Count
	}
	if evd.GiftId == perfumeGiftId {
		value = 20 * evd.Count
	}
	if value == 0 {
		return nil
	}

	rankKey := fmt.Sprintf(keyValentinesActivityRank, evd.At.In(ctz.Brazil).Format("2006010215"))
	err := m.rc.ZIncrBy(ctx, rankKey, float64(value), evd.AnchorId).Err()
	if err != nil {
		m.log.Error("valentines onSendGift zincrBy score err", zap.Error(err), zap.Any("evd", evd))
		return err
	}
	m.rc.Expire(ctx, rankKey, ttlValentinesActivityRank)

	// 记录用户分数最后更新时间
	updateKey := fmt.Sprintf(keyValentinesUpdateTime, evd.At.In(ctz.Brazil).Format("2006010215"))
	m.rc.HSet(ctx, updateKey, evd.AnchorId, evd.At.Unix())
	m.rc.Expire(ctx, updateKey, ttlValentinesUpdateTime)

	return nil
}
