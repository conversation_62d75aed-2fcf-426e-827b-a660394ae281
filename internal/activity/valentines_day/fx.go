package valentines_day

import (
	"context"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"time"
)

func Provide(
	pm *props.Manager,
	ug user.Getter,
	imm *im.Manager,
	rc *redi.Client,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(ug, rc.Cluster("rank"), pm, imm, vnd.Scope("valentines.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
) {
	// 监听送礼
	evb.Watch(evt.GiftSend, "valentines.activity.gift", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())

	// 巴西时间每个小时的0分10秒发送奖励：UTC 03:01 = Sao_Paulo 00:01
	sch.CronWithSeconds("10 0 * * * *").Do(sch.Exclusive("activity.valentines.reward", func(ctx context.Context) error {
		return mgr.SendReward(ctx, time.Now().In(ctz.Brazil).Add(-time.Hour))
	}))
}
