package coin_grab

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// ActivityCoinGrabReceive 奖励领取记录
type ActivityCoinGrabReceive struct {
	Id       primitive.ObjectID `bson:"_id"`
	UserId   string             `bson:"userId"`
	Date     string             `bson:"date"`
	Rank     int                `bson:"rank"`
	CoinPool int                `bson:"coinPool"`
	Percent  float64            `bson:"percent"`
	Reward   int64              `bson:"reward"`
	CreateAt time.Time          `bson:"createAt"` // 创建时间 领取时间
}

func ActivityCoinGrabReceiveCollectionName() string {
	return "activity.coingrab.receive"
}
