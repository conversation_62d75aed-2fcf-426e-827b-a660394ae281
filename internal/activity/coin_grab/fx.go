package coin_grab

import (
	"context"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(
	pm *props.Manager,
	ug user.Getter,
	imm *im.Manager,
	fm *fund.Manager,
	rc *redi.Client,
	dbmc *db.MongoClient,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(ActivityCoinGrabReceiveCollectionName(), 1,
		db.Indexer{Name: "userId_receiveDate", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "date", Value: -1},
		}, Uniq: lo.ToPtr(true)},
	)

	m := newManager(ug, rc.Cluster("rank"), pm, imm, fm, dbmc, vnd.Scope("coingrab.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
) {
	// 监听游戏收益
	evb.Watch(evt.EvGameWin, "coingrab.activity.game", ev.NewWatcher(mgr.onWinGame), ev.WithAsync())

	// 巴西时间每小时尝试生成第二天奖池
	sch.CronWithSeconds("0 0 * * * *").Do(sch.Exclusive("activity.coingrab.init", func(ctx context.Context) error {
		return mgr.initCoinPool(ctx)
	}))

	// 每天结榜后发送私信
	sch.CronWithSeconds("0 1 3 * * *").Do(sch.Exclusive("activity.coingrab.notify", func(ctx context.Context) error {
		return mgr.imNotify(ctx)
	}))
}
