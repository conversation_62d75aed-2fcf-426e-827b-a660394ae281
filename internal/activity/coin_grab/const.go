package coin_grab

import "time"

const (
	keyCoinGrabActivityRank = "ZSET:COIN:GRAB:%s" // 日期
	ttlCoinGrabActivityRank = time.Hour * 24 * 7

	keyCoinGrabUpdateTime = "HASH:COIN:GRAB:TIME:%s" // 日期
	ttlCoinGrabUpdateTime = time.Hour * 24 * 7

	keyCoingrabLeftover = "STR:COINGRAB:LEFTOVER:%s:%s" // 日期：用户id
	ttlCoingrabLeftover = time.Hour * 24 * 2

	keyCoinPool = "STR:COIN:GRAB:POOL:%s" // 日期
	ttlCoinPool = time.Hour * 24 * 7

	PreValueIncrCoin = 3
	InitPoolCoin     = 500000
)
