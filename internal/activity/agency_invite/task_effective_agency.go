package agency_invite

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"time"
)

func (m *Manager) EffectiveAgencyFlag(ctx context.Context) error {
	m.log.Info("effectiveAgencyFlag start")

	cursor, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).Find(ctx, bson.M{})
	if err != nil {
		m.log.Error("effectiveAgencyFlag err", zap.Error(err))
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var record AgencyInviteCreate
		err := cursor.Decode(&record)
		if err != nil {
			m.log.Error("effectiveAgencyFlag decode err", zap.Error(err))
			continue
		}

		// 已经标记为活跃公会
		thisMonths := time.Now().AddDate(0, 0, -1).Format("200601")
		if record.LastEffectiveAgency == thisMonths {
			continue
		}

		data, err := m.lsm.AgencyPeriod(
			ctx,
			int64(record.UserAgencyId),
			"",
			time.Now().AddDate(0, 0, -7),
			time.Now().AddDate(0, 0, -1),
		)
		if err != nil {
			m.log.Error("effectiveAgencyFlag get data err", zap.Error(err))
			continue
		}

		if data.ValidDayTimeCount >= effectiveAgencyValidDayTime && data.LuckDiamond >= effectiveAgencyLuckDiamond {
			if _, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).UpdateOne(ctx, bson.M{
				"inviteUserId": record.InviteUserId,
				"userAgencyId": record.UserAgencyId,
			}, bson.M{
				"$set": bson.M{
					"lastEffectiveAgency": thisMonths,
				},
			}); err != nil {
				return err
			}
		}
	}

	m.log.Info("effectiveAgencyFlag end")

	return nil
}

func (m *Manager) EffectiveAgencyTask(ctx context.Context) error {
	m.log.Info("effectiveAgencyTask start")

	lastMonths := time.Now().AddDate(0, -1, 0).Format("200601")

	cursor, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).Find(ctx, bson.M{
		"lastEffectiveAgency": lastMonths,
	})
	if err != nil {
		m.log.Error("effectiveAgencyTask err", zap.Error(err))
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var record AgencyInviteCreate
		err := cursor.Decode(&record)
		if err != nil {
			m.log.Error("effectiveAgencyFlag decode err", zap.Error(err))
			continue
		}

		m.dbmc.TryTxn(ctx, func(ctx context.Context) error {
			// 记录任务完成
			if _, err := m.dbmc.Collection(AgencyInviteTaskCollectionName()).UpdateOne(ctx, bson.M{
				"userId":   record.UserId,
				"taskType": TaskTypeEffectiveAgency,
				"date":     lastMonths,
			}, bson.M{
				"inviteUserId":   record.InviteUserId,
				"inviteAgencyId": record.InviteAgencyId,
				"inviteCode":     record.InviteCode,
				"createTime":     time.Now(),
			}, options.Update().SetUpsert(true)); err != nil {
				return err
			}

			return nil
		})
	}

	m.log.Info("effectiveAgencyTask end")

	return nil
}
