package agency_invite

import (
	"context"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(
	dbmc *db.MongoClient,
	am *agency.Manager,
	lsm *ls.Manager,
	rc *redi.Client,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(AgencyInviteInfoCollectionName(), 1,
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}},
		db.Indexer{Name: "inviteCode", Keys: bson.D{
			{Key: "inviteCode", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)

	dbmc.SyncSchema(AgencyInviteCreateCollectionName(), 1,
		db.Indexer{Name: "inviteUserId", Keys: bson.D{
			{Key: "inviteUserId", Value: 1},
		}},
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)

	dbmc.SyncSchema(AgencyInviteTaskCollectionName(), 1,
		db.Indexer{Name: "inviteUserId", Keys: bson.D{
			{Key: "inviteUserId", Value: 1},
		}},
		db.Indexer{Name: "userId_taskType", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "taskType", Value: 1},
			{Key: "date", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)

	return newManager(dbmc, am, lsm, rc, vnd.Scope("activity.agency_invite.mgr"))
}

func Invoke(
	sch *cron.Scheduler,
	evb ev.Bus,
	mgr *Manager,
) error {
	// 公会申请
	evb.Watch(evt.AgencyApply, "activity.agency.apply", ev.NewWatcher(mgr.onAgencyApply), ev.WithAsync())
	// 公会审核
	evb.Watch(evt.AgencyVerify, "activity.agency.verify", ev.NewWatcher(mgr.onAgencyVerify), ev.WithAsync())
	// 设置新主播
	evb.Watch(evt.NewAnchorFlagSet, "activity.invite.task.newAnchor", ev.NewWatcher(mgr.onSetNewAnchor), ev.WithAsync())

	// 定时处理公会任务
	// task_effective_live任务需要每周一02:00:00统计一次,utc-3=巴西时间
	if _, err := sch.CronWithSeconds("0 0 5 * * 1").Do(sch.Exclusive("activity.agency.task2", func(context.Context) error {
		return mgr.EffectiveLiveTask(context.Background())
	})); err != nil {
		return err
	}

	// task_effective_agency——flag任务每周一02:00:00标记有效公会，utc-3=巴西时间
	if _, err := sch.CronWithSeconds("0 0 5 * * 1").Do(sch.Exclusive("activity.agency.task3", func(context.Context) error {
		return mgr.EffectiveAgencyFlag(context.Background())
	})); err != nil {
		return err
	}

	// task_effective_agency任务需要每月1号02:00:00统计一次，utc-3=巴西时间
	if _, err := sch.CronWithSeconds("0 0 5 1 * *").Do(sch.Exclusive("activity.agency.task4", func(context.Context) error {
		return mgr.EffectiveAgencyTask(context.Background())
	})); err != nil {
		return err
	}

	return nil
}
