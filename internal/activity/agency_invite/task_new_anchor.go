package agency_invite

import (
	"context"
	"fmt"
	"time"

	ap "gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	// 一周内某个公会newAnchor人数
	keyAgencyNewAnchorNumByWeek = "STR:AGENCY:NEWANCHOR:NUM:%d:%d" // 周:公会id
	ttlAgencyNewAnchorNumByWeek = 7 * 24 * time.Hour
)

func (m *Manager) onSetNewAnchor(
	ctx context.Context,
	evd *evt.SetNewAnchorFlag,
) error {
	if !evd.Flag {
		return nil
	}

	agency, err := m.am.GetAgencyByMember(evd.UserId)
	if err != nil {
		if err == ap.ErrNotJoinAgency {
			return nil
		}

		return err
	}

	// 是否是被邀请的公会
	inviteInfo, err := m.GetInviteAgencyInfo(ctx, int(agency.ID))
	if err == nil && !inviteInfo.Id.IsZero() && !inviteInfo.IsCompleteNewAnchor {
		// 记录缓存
		year, ISOWeek := time.Now().ISOWeek()
		key := fmt.Sprintf(keyAgencyNewAnchorNumByWeek, ISOWeek, inviteInfo.UserAgencyId)
		curr := m.rc.Incr(ctx, key).Val()
		m.rc.Expire(ctx, key, ttlAgencyNewAnchorNumByWeek)
		if curr >= 5 {
			m.dbmc.TryTxn(ctx, func(ctx context.Context) error {
				// 记录任务完成
				if _, err := m.dbmc.Collection(AgencyInviteTaskCollectionName()).InsertOne(ctx, bson.M{
					"inviteUserId":   inviteInfo.InviteUserId,
					"inviteAgencyId": inviteInfo.InviteAgencyId,
					"inviteCode":     inviteInfo.InviteCode,
					"userId":         inviteInfo.UserId,
					"userAgencyId":   inviteInfo.UserAgencyId,
					"taskType":       TaskTypeNewAnchor,
					"date":           fmt.Sprintf("%d%d", year, ISOWeek),
					"createTime":     time.Now(),
				}); err != nil {
					return err
				}

				if _, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).UpdateOne(ctx, bson.M{
					"inviteUserId":        inviteInfo.InviteUserId,
					"userAgencyId":        inviteInfo.UserAgencyId,
					"isCompleteNewAnchor": false,
				}, bson.M{
					"$set": bson.M{
						"isCompleteNewAnchor": true,
					},
				}); err != nil {
					return err
				}

				return nil
			})
		}
	}

	return nil
}
