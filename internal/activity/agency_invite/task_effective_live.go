package agency_invite

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"time"
)

const (
	effectiveAgencyValidDayTime = 30
	effectiveAgencyLuckDiamond  = 30000000
)

// EffectiveLiveTask 遍历被邀请的公会，每周结束后检查是否达到任务要求
func (m *Manager) EffectiveLiveTask(ctx context.Context) error {
	m.log.Info("effectiveLiveTask start")

	cursor, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).Find(ctx, bson.M{
		"isCompleteEffectiveLive": false,
	})
	if err != nil {
		m.log.Error("effectiveLiveTask err", zap.Error(err))
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var record AgencyInviteCreate
		err := cursor.Decode(&record)
		if err != nil {
			m.log.Error("effectiveLiveTask decode err", zap.Error(err))
			continue
		}

		data, err := m.lsm.AgencyPeriod(
			ctx,
			int64(record.UserAgencyId),
			"",
			time.Now().AddDate(0, 0, -7),
			time.Now().AddDate(0, 0, -1),
		)
		if err != nil {
			m.log.Error("effectiveLiveTask get data err", zap.Error(err))
			continue
		}

		if data.ValidDayTimeCount >= effectiveAgencyValidDayTime && data.LuckDiamond >= effectiveAgencyLuckDiamond {
			m.dbmc.TryTxn(ctx, func(ctx context.Context) error {
				year, ISOWeek := time.Now().ISOWeek()

				// 记录任务完成
				if _, err := m.dbmc.Collection(AgencyInviteTaskCollectionName()).InsertOne(ctx, bson.M{
					"inviteUserId":   record.InviteUserId,
					"inviteAgencyId": record.InviteAgencyId,
					"inviteCode":     record.InviteCode,
					"userId":         record.UserId,
					"userAgencyId":   record.UserAgencyId,
					"taskType":       TaskTypeEffectiveLive,
					"date":           fmt.Sprintf("%d%d", year, ISOWeek),
					"createTime":     time.Now(),
				}); err != nil {
					return err
				}

				if _, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).UpdateOne(ctx, bson.M{
					"inviteUserId":            record.InviteUserId,
					"userAgencyId":            record.UserAgencyId,
					"isCompleteEffectiveLive": false,
				}, bson.M{
					"$set": bson.M{
						"isCompleteEffectiveLive": true,
					},
				}); err != nil {
					return err
				}

				return nil
			})
		}
	}

	m.log.Info("effectiveLiveTask end")

	return nil
}
