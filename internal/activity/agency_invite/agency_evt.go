package agency_invite

import (
	"context"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

func (m *Manager) onAgencyApply(
	ctx context.Context,
	evd *evt.AgencyApplyEvt,
) error {
	if evd.InviteCode != "" {
		inviteInfo, err := m.GetInviteInfoByCode(ctx, evd.InviteCode)
		if err == nil && !inviteInfo.Id.IsZero() {
			m.IncrInviteApply(ctx, inviteInfo.UserId, inviteInfo.AgencyId, 1)
		}
	}
	return nil
}

func (m *Manager) onAgencyVerify(
	ctx context.Context,
	evd *evt.AgencyVerifyEvt,
) error {
	if evd.InviteCode != "" {
		inviteInfo, err := m.GetInviteInfoByCode(ctx, evd.InviteCode)
		if err == nil && !inviteInfo.Id.IsZero() {
			if evd.VerifyStatus == 1 {
				m.IncrInviteCreate(ctx, inviteInfo.UserId, inviteInfo.AgencyId, evd.Manage)
				m.InsertInviteAgency(ctx, &AgencyInviteCreate{
					Id:             primitive.NewObjectID(),
					InviteUserId:   inviteInfo.UserId,
					InviteAgencyId: inviteInfo.AgencyId,
					InviteCode:     inviteInfo.InviteCode,
					UserId:         evd.UserId,
					UserAgencyId:   evd.AgencyId,
					CreateAt:       time.Now(),
				})
			}

			if evd.VerifyStatus == -1 {
				m.IncrInviteApply(ctx, inviteInfo.UserId, inviteInfo.AgencyId, -1)
			}
		}
	}

	return nil
}
