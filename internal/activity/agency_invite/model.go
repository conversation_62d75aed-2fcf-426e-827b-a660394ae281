package agency_invite

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// AgencyInviteInfo 邀请人信息
type AgencyInviteInfo struct {
	Id           primitive.ObjectID `bson:"_id"`
	UserId       string             `bson:"userId"`       // 邀请人id
	AgencyId     int                `bson:"agencyId"`     // 邀请人公会id
	InviteCode   string             `bson:"inviteCode"`   // 邀请码
	InviteApply  int                `bson:"inviteApply"`  // 申请中
	InviteCreate int                `bson:"inviteCreate"` // 已入驻
	CreateAt     time.Time          `bson:"createAt"`     // 创建时间
}

func AgencyInviteInfoCollectionName() string {
	return "activity.agency.invite.info"
}

// AgencyInviteCreate 受邀创建的公会信息
type AgencyInviteCreate struct {
	Id                      primitive.ObjectID `bson:"_id"`
	InviteUserId            string             `bson:"inviteUserId"`            // 邀请人id
	InviteAgencyId          int                `bson:"inviteAgencyId"`          // 邀请人公会id
	InviteCode              string             `bson:"inviteCode"`              // 邀请码
	UserId                  string             `bson:"userId"`                  // 受邀请人id
	UserAgencyId            int                `bson:"userAgencyId"`            // 受邀请人的公会id
	IsCompleteNewAnchor     bool               `bson:"isCompleteNewAnchor"`     // 是否已完成newAnchor任务
	IsCompleteEffectiveLive bool               `bson:"isCompleteEffectiveLive"` // 是否已完成effectiveLive任务
	LastEffectiveAgency     string             `bson:"lastEffectiveAgency"`     // 最后一次有效公会的月份 yyyyMM
	CreateAt                time.Time          `bson:"createAt"`                // 创建时间
}

func AgencyInviteCreateCollectionName() string {
	return "activity.agency.invite.create"
}

const (
	_ = iota
	TaskTypeNewAnchor
	TaskTypeEffectiveLive
	TaskTypeEffectiveAgency
)

// AgencyInviteTask 受邀公会的任务完成记录
type AgencyInviteTask struct {
	Id             primitive.ObjectID `bson:"_id"`
	InviteUserId   string             `bson:"inviteUserId"`   // 邀请人id
	InviteAgencyId int                `bson:"inviteAgencyId"` // 邀请人公会id
	InviteCode     string             `bson:"inviteCode"`     // 邀请码
	UserId         string             `bson:"userId"`         // 受邀请人id
	UserAgencyId   int                `bson:"userAgencyId"`   // 受邀请人的公会id
	TaskType       int                `bson:"taskType"`       // 任务类型
	Date           string             `bson:"date"`           // 任务完成的周/月
	CreateAt       time.Time          `bson:"createAt"`       // 创建时间
}

func AgencyInviteTaskCollectionName() string {
	return "activity.agency.invite.task"
}
