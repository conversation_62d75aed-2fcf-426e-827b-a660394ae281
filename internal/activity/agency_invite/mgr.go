package agency_invite

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

func newManager(
	dbmc *db.MongoClient,
	am *agency.Manager,
	lsm *ls.Manager,
	rc *redi.Client,
	log *zap.Logger,
) (*Manager, error) {
	return &Manager{
		dbmc: dbmc,
		am:   am,
		lsm:  lsm,
		rc:   rc,
		log:  log,
	}, nil
}

type Manager struct {
	dbmc *db.MongoClient
	am   *agency.Manager
	lsm  *ls.Manager
	rc   *redi.Client
	log  *zap.Logger
}
