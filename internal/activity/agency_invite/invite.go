package agency_invite

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

func (m *Manager) GetInviteInfo(ctx context.Context, userId string, agencyId uint, agencyShowId string) (*AgencyInviteInfo, error) {
	var info AgencyInviteInfo
	err := m.dbmc.Collection(AgencyInviteInfoCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId":   userId,
			"agencyId": agencyId,
		},
	).Decode(&info)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			info = AgencyInviteInfo{
				Id:           primitive.NewObjectID(),
				UserId:       userId,
				AgencyId:     int(agencyId),
				InviteCode:   agencyShowId,
				InviteApply:  0,
				InviteCreate: 0,
				CreateAt:     time.Now(),
			}
			if _, err := m.dbmc.Collection(AgencyInviteInfoCollectionName()).InsertOne(ctx, &info); err != nil {
				return nil, err
			}

		} else {
			return nil, err
		}
	}

	return &info, nil
}

func (m *Manager) GetInviteInfoByCode(ctx context.Context, inviteCode string) (*AgencyInviteInfo, error) {
	var info AgencyInviteInfo
	err := m.dbmc.Collection(AgencyInviteInfoCollectionName()).FindOne(
		ctx,
		bson.M{
			"inviteCode": inviteCode,
		},
	).Decode(&info)

	if err != nil {
		return nil, err
	}

	return &info, nil
}

func (m *Manager) InsertInviteAgency(ctx context.Context, info *AgencyInviteCreate) error {
	if _, err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).InsertOne(ctx, &info); err != nil {
		return err
	}

	return nil
}

func (m *Manager) GetInviteAgencyInfo(ctx context.Context, userAgencyId int) (*AgencyInviteCreate, error) {
	var info AgencyInviteCreate
	if err := m.dbmc.Collection(AgencyInviteCreateCollectionName()).FindOne(ctx, bson.M{
		"userAgencyId": userAgencyId,
	}).Decode(&info); err != nil {
		return nil, err
	}

	return &info, nil
}

func (m *Manager) IncrInviteApply(ctx context.Context, userId string, agencyId int, by int) error {
	if _, err := m.dbmc.Collection(AgencyInviteInfoCollectionName()).UpdateOne(ctx, bson.M{
		"userId":   userId,
		"agencyId": agencyId,
	}, bson.M{
		"$inc": bson.M{
			"inviteApply": by,
		},
	}); err != nil {
		return err
	}

	return nil
}

func (m *Manager) IncrInviteCreate(ctx context.Context, userId string, agencyId int, manage bool) error {
	if manage {
		if _, err := m.dbmc.Collection(AgencyInviteInfoCollectionName()).UpdateOne(ctx, bson.M{
			"userId":   userId,
			"agencyId": agencyId,
		}, bson.M{
			"$inc": bson.M{
				"inviteCreate": 1,
			},
		}); err != nil {
			return err
		}
	} else {
		if _, err := m.dbmc.Collection(AgencyInviteInfoCollectionName()).UpdateOne(ctx, bson.M{
			"userId":      userId,
			"agencyId":    agencyId,
			"inviteApply": bson.M{"$gte": 1},
		}, bson.M{
			"$inc": bson.M{
				"inviteApply":  -1,
				"inviteCreate": 1,
			},
		}); err != nil {
			return err
		}
	}

	return nil
}
