package rlaa

import (
	"context"
	"strconv"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
)

func (m *Manager) onGiftSent(ctx context.Context, evd *evt.SendGift) error {
	if evd.Diamond <= 0 {
		return nil
	}

	nn := GetNowNow()

	// 活动未开始或已结束
	if !IsInPeriod(nn.Time) {
		return nil
	}

	anchorId := evd.AnchorId
	sessionId := evd.Session
	diamond := int64(evd.Diamond)

	var score int64

	if evd.Gift.Lucky {
		score = diamond
	} else {
		score = diamond * 10
	}

	// 增加主播排行榜
	if err := m.incrScore(ctx, nn, anchorId, score, RankRoleAnchor); err != nil {
		m.log.Error(
			"活动RLAA",
			zap.Error(err),
			zap.Any("evd", evd),
			zap.String("anchorId", anchorId),
			zap.String("sessionId", sessionId),
			zap.Int64("diamond", diamond),
		)
		return nil
	}

	ps, err := m.psm.Get(anchorId, sessionId)

	if err == nil {
		if ps.AgencyId > 0 {
			if err := m.incrScore(ctx, nn, strconv.FormatInt(ps.AgencyId, 10), score, RankRoleAgency); err != nil {
				m.log.Error(
					"活动RLAA",
					zap.Error(err),
					zap.Any("evd", evd),
					zap.String("anchorId", anchorId),
					zap.String("sessionId", sessionId),
					zap.Int64("diamond", diamond),
					zap.String("agencyId", strconv.FormatInt(ps.AgencyId, 10)),
				)
			}
		}
	} else {
		m.log.Error(
			"活动RLAA",
			zap.Error(err),
			zap.Any("evd", evd),
			zap.String("anchorId", anchorId),
			zap.String("sessionId", sessionId),
			zap.Int64("diamond", diamond),
		)
	}

	return nil
}
