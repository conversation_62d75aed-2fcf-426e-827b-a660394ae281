package rlaa

import (
	"github.com/jinzhu/now"
)

type Info struct {
	Status    int   // 活动状态 0：未开始 1：进行中 2：已结束
	StartTime int64 // 活动开始时间，秒时间戳
	EndTime   int64 // 活动结束时间，秒时间戳
}

func (m *Manager) Info(nn *now.Now) *Info {
	status := StatusNotStart

	st := getStartTime()
	et := getEndTime()

	if IsInPeriod(nn.Time) {
		status = StatusRunning
	} else if nn.After(et) {
		status = StatusEnd
	}

	return &Info{
		Status:    status,
		StartTime: st.Unix(),
		EndTime:   et.Unix(),
	}
}
