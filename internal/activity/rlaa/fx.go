package rlaa

import (
	"context"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Provide(
	rc *redi.Client,
	fm *fund.Manager,
	am *agency.Manager,
	im *im.Manager,
	psm *profitsharing.Manager,
	lm *live.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	return newManager(rc, fm, am, im, psm, lm, vnd.Scope("rlaa.mgr"))
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
) error {
	if !env.APIServer() {
		return nil
	}

	// 监听送礼事件
	evb.Watch(evt.GiftSend, "rlaa.gift.send", ev.NewWatcher(mgr.onGiftSent), ev.WithAsync())

	{
		task := sch.Exclusive("STR:M:MUTEX:ACT:RLAA:AWARD", func(ctx context.Context) error {
			return mgr.Award(ctx, GetNowNow())
		})

		// UTC每日凌晨3点 10分(巴西时间每天0点10分)
		sch.Cron("10 3 * * *").Do(task)
	}

	{
		task := sch.Exclusive("STR:ACT:RLAA:RANK:SNAPSHOT", func(ctx context.Context) error {
			return mgr.snapshotRank(ctx, GetNowNow())
		})

		// 每5分钟保存榜单数据用于比对排名变化
		sch.Cron("*/5 * * * *").Do(task)
	}

	return nil
}
