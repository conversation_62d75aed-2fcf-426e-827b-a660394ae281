package rlaa

import "github.com/redis/go-redis/v9"

const (
	luaScript = `
	local key = KEYS[1]
	local field = ARGV[1]
	local increment = tonumber(ARGV[2])
	local timeFactor = tonumber(ARGV[3])
	local ttl = tonumber(ARGV[4])

	local currentScore = redis.call("ZSCORE", key, field)
	if not currentScore then
		currentScore = 0
	else
		currentScore = tonumber(currentScore)
	end

	local newScore = math.floor(currentScore) + increment + timeFactor
	redis.call("ZADD", key, newScore, field)
	redis.call("EXPIRE", key, ttl)
	return newScore
`
)

var ls = redis.NewScript(luaScript)
