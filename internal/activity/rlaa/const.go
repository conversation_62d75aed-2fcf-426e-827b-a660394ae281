package rlaa

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

const (
	StatusNotStart = iota
	StatusRunning
	StatusEnd
)

var (
	startTime = time.Date(2025, 4, 26, 0, 0, 0, 0, ctz.Brazil)
	endTime   = time.Date(2045, 4, 26, 0, 0, 0, 0, ctz.Brazil)
)

func IsInPeriod(t time.Time) bool {
	st := getStartTime()
	et := getEndTime()

	return t.After(st) && t.Before(et)
}

func getStartTime() time.Time {
	return startTime
}

func getEndTime() time.Time {
	return endTime
}
