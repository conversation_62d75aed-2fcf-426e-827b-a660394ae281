package rlaa

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/now"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type RankRole string

const (
	RankRoleAnchor RankRole = "anchor"
	RankRoleAgency RankRole = "agency"
)

const (
	RankAnchorNum = 50 // 主播排名人数
	RankAgencyNum = 15 // 公会排名人数
)

var (
	keyRank = "ZSET:ACT:RLAA:%s:%s" // anchor/agency:day
	ttlRank = time.Hour * 24 * 15

	keyRankAward = "STR:ACT:RLAA:%s:%s:%s" // 奖励已发放记录 role:userId:day
	ttlRankAward = time.Hour * 24 * 7

	keyRankAwardUser = "SET:ACT:RLAA:AU:%s:%s" // 发放了奖励的用户 role:day
	ttlRankAwardUser = time.Hour * 24 * 7
)

type Rank struct {
	No      int
	UserId  string
	Diamond int64
	Award   int64
	Change  int
	Issued  bool // 发放了奖励
}

func (m *Manager) getRankKey(t time.Time, role RankRole) string {
	return fmt.Sprintf(keyRank, role, t.Format("20060102"))
}

func (m *Manager) getRankAwardKey(userId string, day string, role RankRole) string {
	return fmt.Sprintf(keyRankAward, role, userId, day)
}

func (m *Manager) getRankAwardUserKey(role RankRole, day string) string {
	return fmt.Sprintf(keyRankAwardUser, role, day)
}

func (m *Manager) incrScore(ctx context.Context, nn *now.Now, userId string, score int64, role RankRole) error {
	key := m.getRankKey(nn.Time, role)

	leftSeconds, err := strconv.ParseFloat(fmt.Sprintf("0.%d", int(nn.EndOfDay().Sub(nn.Time).Seconds())), 64)

	if err != nil {
		return err
	}

	_, err = m.rc.EvalSha(ctx, ls.Hash(), []string{key}, userId, score, leftSeconds, ttlRank).Result()

	if err != nil {
		return err
	}

	return nil
}

// 获取排名
func (m *Manager) getRank(ctx context.Context, key string, role RankRole) ([]Rank, error) {
	var stop int64

	if role == RankRoleAnchor {
		stop = RankAnchorNum - 1
	} else {
		stop = RankAgencyNum - 1
	}

	res, err := m.rc.ZRevRangeWithScores(ctx, key, 0, stop).Result()

	if err != nil {
		return nil, err
	}

	rank := make([]Rank, len(res))

	for i, v := range res {
		rank[i] = Rank{
			No:      i + 1,
			UserId:  v.Member.(string),
			Diamond: int64(v.Score),
		}
	}

	return rank, nil
}

func (m *Manager) getRank2(ctx context.Context, key string, stop int64) ([]Rank, error) {
	res, err := m.rc.ZRevRangeWithScores(ctx, key, 0, stop).Result()

	if err != nil {
		return nil, err
	}

	rank := make([]Rank, len(res))

	for i, v := range res {
		rank[i] = Rank{
			No:      i + 1,
			UserId:  v.Member.(string),
			Diamond: int64(v.Score),
		}
	}

	return rank, nil
}

// 获取某个用户的排名
func (m *Manager) getUserRank(ctx context.Context, key, userId string) (*Rank, error) {
	score, err := m.rc.ZScore(ctx, key, userId).Result()

	if err != nil {
		if err != redis.Nil {
			return &Rank{}, err
		}
	}

	no, err := m.rc.ZRevRank(ctx, key, userId).Result()

	if err != nil {
		if err != redis.Nil {
			return &Rank{}, err
		}

		no = 99
	}

	rank := &Rank{
		No:      int(no) + 1,
		UserId:  userId,
		Diamond: int64(score),
	}

	return rank, nil
}

func (m *Manager) GetTodayRank(ctx context.Context, nn *now.Now, role RankRole) ([]Rank, error) {
	res, err := m.getRank(ctx, m.getRankKey(nn.Time, role), role)

	if err != nil {
		return nil, err
	}

	sr, err := m.getSnapshotRankMap(ctx, role)

	if err == nil {
		for i, v := range res {
			lastNo := 100

			if no, ok := sr[v.UserId]; ok {
				if no < 100 {
					lastNo = no
				}
			}

			res[i].Change = lastNo - v.No
		}
	}

	return res, nil
}

func (m *Manager) GetYesterdayRank(ctx context.Context, nn *now.Now, role RankRole) ([]Rank, error) {
	res, err := m.getRank(ctx, m.getRankKey(nn.AddDate(0, 0, -1), role), role)

	if err != nil {
		return nil, err
	}

	userIds := m.rc.SMembers(ctx, m.getRankAwardUserKey(role, nn.AddDate(0, 0, -1).Format("20060102"))).Val()

	for i, v := range res {
		if lo.Contains(userIds, v.UserId) {
			res[i].Issued = true
		}
	}

	return res, nil
}

func (m *Manager) GetUserTodayRank(ctx context.Context, nn *now.Now, userId string, role RankRole) (*Rank, error) {
	res, err := m.getUserRank(ctx, m.getRankKey(nn.Time, role), userId)

	if err != nil {
		return nil, err
	}

	if role == RankRoleAnchor {
		res.Award = m.getAnchorReward(res.No)
	} else {
		res.Award = m.getAgencyReward(res.No)
	}

	return res, nil
}

func (m *Manager) GetUserYesterdayRank(ctx context.Context, nn *now.Now, userId string, role RankRole) (*Rank, error) {
	return m.getUserRank(ctx, m.getRankKey(nn.AddDate(0, 0, -1), role), userId)
}

// 发放排名奖励
func (m *Manager) Award(ctx context.Context, nn *now.Now) error {
	if !IsInPeriod(nn.AddDate(0, 0, -1)) {
		return biz.NewError(biz.ErrBusiness, "not in the activity period")
	}

	startTime := nn.BeginningOfDay().AddDate(0, 0, -1)
	endTime := nn.EndOfDay().AddDate(0, 0, -1)

	awardDayTime := nn.AddDate(0, 0, -1)

	logger := m.log.With(
		zap.String("from", awardDayTime.Format("20060102")),
	)

	roles := []RankRole{RankRoleAnchor, RankRoleAgency}

	for _, role := range roles {
		// 昨日排名
		ranks, err := m.getRank(ctx, m.getRankKey(awardDayTime, role), role)

		if err != nil {
			logger.Error(
				"发放活动奖励RLAA",
				zap.Error(err),
				zap.String("name", string(role)),
			)

			continue
		}

		for _, rank := range ranks {
			// 1：之前已发放过 2：有奖励处理成功
			var status int

			userId := rank.UserId

			// 昨日是否已发放
			key := m.getRankAwardKey(userId, awardDayTime.Format("20060102"), role)

			if m.rc.Exists(ctx, key).Val() == 1 {
				status = 1

				logger.Info(
					"发放活动奖励RLAA",
					zap.String("name", string(role)),
					zap.String("userId", userId),
					zap.Int("status", status),
				)

				continue
			}

			// 奖励
			var diamond int64
			var fruits int64
			// 主播直播时长
			var duration int64
			// 公会长ID
			var chiefId string
			// 直播时长不够
			var durationNotEnough bool

			if role == RankRoleAnchor {
				// 直播有效时长，秒
				duration, err = m.lm.GetLiveValidDuration(ctx, userId, startTime, endTime)

				if err != nil {
					logger.Error(
						"发放活动奖励RLAA",
						zap.Error(err),
						zap.String("name", string(role)),
						zap.String("userId", userId),
						zap.String("from", "GetLiveValidDuration"),
					)
				}

				// 直播时长大于等于2小时，发放奖励
				if duration/60 >= 120 {
					fruits = m.getAnchorReward(rank.No)
				} else {
					durationNotEnough = true
				}
			} else if role == RankRoleAgency {
				diamond = m.getAgencyReward(rank.No)
			}

			tradeId := primitive.NewObjectID().Hex()

			if fruits > 0 { // 给主播的奖励
				err = m.fm.Income(
					ctx,
					userId,
					fund.JTypeRewards,
					fund.PTypeFruits,
					fruits,
					fund.WithTrade(tradeId),
				)

				if err != nil {
					logger.Error(
						"发放活动奖励RLAA",
						zap.Error(err),
						zap.String("name", string(role)),
						zap.String("userId", userId),
						zap.Int64("fruits", fruits),
						zap.String("tradeId", tradeId),
					)

					continue
				}

				m.im.SendSystemNoticeTextToUser(
					ctx,
					userId,
					fmt.Sprintf(
						"Parabéns! Você ficou em %dº lugar (host) no evento \"Torneio de Estrela\" de ontem. Como prêmio, você recebeu %d cristais. O prêmio de ranking foi enviado para sua conta, por favor, verifique.",
						rank.No,
						fruits,
					),
				)

				status = 2
			} else if diamond > 0 { // 给公会长的奖励
				// userId是公会ID，查询公会长
				agencyId, err := strconv.ParseInt(userId, 10, 64)

				if err == nil {
					agency, err := m.am.GetAgencyById(ctx, agencyId)
					if err == nil {
						chiefId = agency.ChiefId
					} else {
						logger.Error(
							"发放活动奖励RLAA",
							zap.Error(err),
							zap.String("name", string(role)),
							zap.String("userId", userId),
							zap.String("chiefId", chiefId),
							zap.Int64("diamond", diamond),
							zap.String("tradeId", tradeId),
						)
					}
				} else {
					logger.Error(
						"发放活动奖励RLAA",
						zap.Error(err),
						zap.String("name", string(role)),
						zap.String("userId", userId),
						zap.String("chiefId", chiefId),
						zap.Int64("diamond", diamond),
						zap.String("tradeId", tradeId),
					)
				}

				if chiefId != "" {
					err = m.fm.Income(
						ctx,
						chiefId,
						fund.JTypeRewards,
						fund.PTypeDiamond,
						diamond,
						fund.WithTrade(tradeId),
					)

					if err != nil {
						logger.Error(
							"发放活动奖励RLAA",
							zap.Error(err),
							zap.String("name", string(role)),
							zap.String("userId", userId),
							zap.String("chiefId", chiefId),
							zap.Int64("diamond", diamond),
							zap.String("tradeId", tradeId),
						)

						continue
					}

					m.im.SendSystemNoticeTextToUser(
						ctx,
						chiefId, // 公会长
						fmt.Sprintf(
							"Parabéns! Sua agência ficou em %dº lugar (agência) no evento \"Torneio de Estrela\" de ontem. Você receberá %d coins. O prêmio de ranking foi enviado para sua conta, por favor, verifique.",
							rank.No,
							diamond,
						),
					)

					status = 2
				}
			} else if durationNotEnough {
				// 直播时长不够，发送私信
				m.im.SendSystemNoticeTextToUser(
					ctx,
					userId,
					"O seu tempo de transmissão ao vivo para o evento 'Torneio de Estrela' de ontem foi menos de 2 horas, e não atendeu aos requisitos para receber a recompensa. Por favor, garanta as 2 horas de duração de transmissão nas próximas participações, para que possa concorrer aos prêmios!",
				)
			}

			// 用户获得奖励，记录已发放，用于返回给客户端在顶部显示奖励已发放的用户
			if fruits > 0 || diamond > 0 {
				rauKey := m.getRankAwardUserKey(role, awardDayTime.Format("20060102"))
				m.rc.SAdd(ctx, rauKey, userId)
				m.rc.Expire(ctx, rauKey, ttlRankAwardUser)
			}

			// 记录已发放
			m.rc.Set(ctx, key, 1, ttlRankAward)

			logger.Info(
				"发放活动奖励RLAA",
				zap.String("name", string(role)),
				zap.String("userId", userId),
				zap.String("chiefId", chiefId),
				zap.Int64("duration", duration),
				zap.Int64("diamond", diamond),
				zap.Int64("fruits", fruits),
				zap.String("tradeId", tradeId),
				zap.Int("status", status),
			)
		}
	}

	return nil
}

// 获取所有排名
func (m *Manager) GetRankList(ctx context.Context, nn *now.Now, role RankRole) ([]Rank, error) {
	key := m.getRankKey(nn.Time, role)

	res, err := m.rc.ZRevRangeWithScores(ctx, key, 0, -1).Result()

	if err != nil {
		return nil, err
	}

	rank := make([]Rank, len(res))

	for i, v := range res {
		rank[i] = Rank{
			No:      i + 1,
			UserId:  v.Member.(string),
			Diamond: int64(v.Score),
		}
	}

	return rank, nil
}

func (m *Manager) GetAnchorReward(rank int) int64 {
	return m.getAnchorReward(rank)
}

func (m *Manager) getAnchorReward(rank int) int64 {
	if rank == 1 {
		return 500000
	} else if rank == 2 {
		return 300000
	} else if rank == 3 {
		return 150000
	} else if rank == 4 {
		return 100000
	} else if rank == 5 {
		return 80000
	} else if rank >= 6 && rank <= 10 {
		return 50000
	} else if rank >= 11 && rank <= 20 {
		return 30000
	} else if rank >= 21 && rank <= 30 {
		return 20000
	} else if rank >= 31 && rank <= 50 {
		return 10000
	}

	return 0
}

func (m *Manager) GetAgencyReward(rank int) int64 {
	return m.getAgencyReward(rank)
}

func (m *Manager) getAgencyReward(rank int) int64 {
	if rank == 1 {
		return 500000
	} else if rank == 2 {
		return 300000
	} else if rank == 3 {
		return 200000
	} else if rank == 4 {
		return 100000
	} else if rank == 5 {
		return 80000
	} else if rank >= 6 && rank <= 10 {
		return 50000
	} else if rank >= 11 && rank <= 15 {
		return 30000
	}

	return 0
}
