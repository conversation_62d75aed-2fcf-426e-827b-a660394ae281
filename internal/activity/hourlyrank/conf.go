package hourlyrank

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"time"
)

func IsOpen(t time.Time) bool {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return true
		}
	}

	return false
}

func InPeriod(t time.Time) bool {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			if ses.Connection == false &&
				t.Month() == ses.StartTime.Month() &&
				t.Day() == ses.StartTime.Day() &&
				t.Hour() == 0 {
				// 排除第一天的0点
				return false
			}
			return lo.Contains(ses.Period, t.Hour())
		}
	}
	return false
}
