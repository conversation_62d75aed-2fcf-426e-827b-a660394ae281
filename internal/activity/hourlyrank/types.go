package hourlyrank

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"time"
)

type DayRank struct {
	Day        time.Time    `json:"day"`        // Unix时间戳
	PeriodRank []PeriodRank `json:"periodRank"` // 时段榜单
}

type PeriodRank struct {
	StartTime time.Time
	EndTime   time.Time
	Top3      []UserRank
}

type UserRank struct {
	Rank    int           // 排名
	Value   int64         // 榜单分数
	Gap     int64         // 分差
	Reward  int           // 奖励水晶
	IsValid bool          // 是否符合发奖条件
	User    *user.Account // 用户信息
}
