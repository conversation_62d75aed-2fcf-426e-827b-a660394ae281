package hourlyrank

import (
	"context"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/ranklist"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"time"
)

func Provide(
	fm *fund.Manager,
	ug user.Getter,
	imm *im.Manager,
	rm *ranklist.Manager,
	lm *live.Manager,
	rc *redi.Client,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(fm, ug, rc.Cluster("rank"), imm, lm, rm, vnd.Scope("hourlyrank.mgr"))

	return m, nil
}

func Invoke(
	mgr *Manager,
	sch *cron.Scheduler,
) {
	// 每小时第10秒钟进行奖励发放
	sch.CronWithSeconds("10 0 * * * *").Do(sch.Exclusive("activity.hourlyrank.reward", func(ctx context.Context) error {
		return mgr.SendReward(ctx, time.Now().In(ctz.Brazil).Add(-time.Hour))
	}))
}
