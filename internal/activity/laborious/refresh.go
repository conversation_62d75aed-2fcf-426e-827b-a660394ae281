package laborious

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *Manager) Refresh(ctx context.Context, weekId string, date time.Time) error {
	collection := m.dbmc.Collection(DayRecordCollectionName())

	curs, err := collection.Find(
		ctx,
		bson.M{
			"weekId": weekId,
			"date":   date,
		},
	)

	if err != nil {
		return err
	}

	defer curs.Close(ctx)

	logger := m.log.With(
		zap.String("weekId", weekId),
		zap.Time("date", date),
	)

	for curs.Next(ctx) {
		var dr DayRecord

		if err := curs.Decode(&dr); err != nil {
			logger.Error("decode day record", zap.Error(err))

			return err
		}

		luckDiamond, err := m.lsm.GetLuckDiamond(ctx, dr.UserId, dr.Date.In(ctz.Brazil))

		if err != nil {
			logger.Error("get luck diamond", zap.Error(err))

			return err
		}

		if dr.<PERSON>Diamond < luckDiamond {
			ur, err := collection.UpdateOne(
				ctx,
				bson.M{"_id": dr.Id},
				bson.M{
					"$set": bson.M{
						"immutable":   false,
						"luckDiamond": luckDiamond,
					},
				},
			)

			if err != nil {
				logger.Error("update luck diamond", zap.Error(err))

				return err
			}

			if ur.ModifiedCount != 1 || ur.MatchedCount != 1 {
				logger.Error("update luck diamond", zap.Any("result", ur))
			}

			logger.Info(
				"刷新勤劳主播幸运礼物流水",
				zap.String("userId", dr.UserId),
				zap.Int64("duration", dr.Duration),
				zap.Int64("count", dr.LuckDiamond),
				zap.Int64("diamond", luckDiamond),
			)
		}
	}

	return nil
}
