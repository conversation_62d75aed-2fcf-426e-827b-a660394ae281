package laborious

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
)

const (
	ReceiveStatusNotTime  = 1
	ReceiveStatusCan      = 2
	ReceiveStatusDone     = 3
	ReceiveStatusNoReward = 4
)

type Act struct {
	StartTime   int64  `json:"startTime"`   // 活动开始时间：unix秒
	EndTime     int64  `json:"endTime"`     // 活动结束时间：unix秒
	CurrentDate string `json:"currentDate"` // 当前日期字符串
	Weeks       []Week `json:"weeks"`       // 每周信息
}

type Week struct {
	WeekId             string    `json:"weekId"`             // 周开始时间 20250120000000
	StartTime          time.Time `json:"-"`                  // 周开始时间
	EndTime            time.Time `json:"-"`                  // 周结束时间
	Tasks              []Task    `json:"tasks"`              // 任务列表
	Today              int       `json:"today"`              // 今天是第几天
	TotalScore         float64   `json:"totalScore"`         // 总获得积分
	EstimateTotalScore float64   `json:"estimateTotalScore"` // 预计总获得积分
	Factor             float64   `json:"factor"`             // 积分系数
	EstimateFactor     float64   `json:"estimateFactor"`     // 预计积分系数
	Cristais           int64     `json:"cristais"`           // 可领取水晶
	EstimateCristais   int64     `json:"estimateCristais"`   // 预计可领取水晶
	ReceiveTime        int64     `json:"receiveTime"`        // 可领取时间，毫秒时间戳
	Status             int       `json:"status"`             // 1未到领取时间 2可领取 3已领取 4没有可领取的奖励
}

type Task struct {
	Date        string    `json:"date"`        // 日期字符串
	DateTime    time.Time `json:"-"`           // 日期格式
	Day         int       `json:"day"`         // 第几天
	Score       float64   `json:"score"`       // 获得积分
	Duraiton    int64     `json:"duration"`    // 直播时长，单位秒
	LuckDiamond int64     `json:"luckDiamond"` // 幸运礼物流水，金币
	IsToday     bool      `json:"isToday"`     // 是否是今天
}

type Step struct {
	Duration    time.Duration `json:"duration"`    // 直播时长
	LuckDiamond int64         `json:"luckDiamond"` // 幸运礼物流水，金币
	Score       float64       `json:"score"`       // 积分
}

// 积分系数
type ScoreFactor map[int]float64

func (f ScoreFactor) GetFactor(days int) float64 {
	if v, ok := f[days]; ok {
		return v
	}

	return 0
}

var (
	startTime = time.Date(2025, 4, 28, 0, 0, 0, 0, ctz.Brazil)
	endTime   = time.Date(2025, 5, 4, 23, 59, 59, 0, ctz.Brazil)

	unitFruit int64 = 2000

	scoreFactor = ScoreFactor{
		4: 1.0,
		5: 1.5,
		6: 1.7,
		7: 2.0,
	}

	steps = []Step{
		{Duration: time.Minute * 120, LuckDiamond: 0, Score: 0.3},
		{Duration: time.Minute * 120, LuckDiamond: 150000, Score: 0.4},
		{Duration: time.Minute * 120, LuckDiamond: 300000, Score: 0.5},
		{Duration: time.Minute * 120, LuckDiamond: 600000, Score: 1.0},
		{Duration: time.Minute * 120, LuckDiamond: 1000000, Score: 2.0},
		{Duration: time.Minute * 120, LuckDiamond: 1500000, Score: 3.0},
		{Duration: time.Minute * 120, LuckDiamond: 3000000, Score: 4.0},
		{Duration: time.Minute * 120, LuckDiamond: 5000000, Score: 5.0},
		{Duration: time.Minute * 120, LuckDiamond: 8000000, Score: 6.0},
		{Duration: time.Minute * 120, LuckDiamond: 12000000, Score: 7.0},
		{Duration: time.Minute * 120, LuckDiamond: 20000000, Score: 8.0},
		{Duration: time.Minute * 120, LuckDiamond: 30000000, Score: 9.0},
		{Duration: time.Minute * 120, LuckDiamond: 50000000, Score: 10.0},
		{Duration: time.Minute * 120, LuckDiamond: 100000000, Score: 11.0},
	}
)
