package laborious

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (m *Manager) onGiftSentAdvanced(ctx context.Context, evd *evt.SendGiftAdvanced) error {
	t := evd.At.In(ctz.Brazil)

	if !m.isOpen(t) {
		return nil
	}

	if !evd.Gift.Lucky {
		return nil
	}

	tn := now.New(t)

	anchorId := evd.AnchorId
	date := tn.BeginningOfDay()
	weekId := tn.BeginningOfWeek().Format("20060102150405")

	logger := m.log.With(
		zap.String("anchorId", anchorId),
		zap.Int("diamond", evd.Diamond),
		zap.String("weekId", weekId),
		zap.Time("date", date),
		zap.String("content", weekId),
		zap.String("to", date.Format("2006-01-02")),
	)

	// 锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:ACT:LABORIOUS:MUTEX:EVB:%s", anchorId))
	if err != nil {
		logger.Error("统计勤劳主播幸运礼物流水获取锁失败", zap.Error(err))

		return err
	}
	defer l.MustUnlock()

	_, err = m.dbmc.Collection(DayRecordCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"userId":    anchorId,
			"weekId":    weekId,
			"date":      date,
			"immutable": false,
		},
		bson.M{
			"$inc": bson.M{
				"luckDiamond": evd.Diamond,
			},
			"$setOnInsert": bson.M{
				"createdAt": time.Now(),
			},
			"$set": bson.M{
				"updatedAt": time.Now(),
			},
		},
		options.Update().SetUpsert(true),
	)

	if err != nil {
		logger.Error("统计勤劳主播幸运礼物流水错误", zap.Error(err))

		return err
	}

	logger.Info("统计勤劳主播幸运礼物流水")

	return nil
}
