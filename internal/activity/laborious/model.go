package laborious

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type WeekRecord struct {
	Id         primitive.ObjectID `bson:"_id" json:"id"`                // ID
	UserId     string             `bson:"userId" json:"userId"`         // 主播ID
	WeekId     string             `bson:"weekId" json:"weekId"`         // 周ID
	Received   bool               `bson:"received" json:"received"`     // 是否已领取奖励
	ReceivedAt time.Time          `bson:"receivedAt" json:"receivedAt"` // 领取时间
	Cristais   int64              `bson:"cristais" json:"cristais"`     // 领取水晶
	Factor     float64            `bson:"factor" json:"factor"`         // 积分系数
	TotalScore float64            `bson:"totalScore" json:"totalScore"` // 总积分
	Immutable  bool               `bson:"immutable" json:"immutable"`   // 数据更新完毕，不再变更
	CreatedAt  time.Time          `bson:"createdAt" json:"createdAt"`   // 创建时间
	UpdatedAt  time.Time          `bson:"updatedAt" json:"updatedAt"`   // 更新时间
}

func WeekRecordCollectionName() string {
	return "act.laborious.week.record"
}

func createWeekRecordIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(WeekRecordCollectionName(), 1,
		db.Indexer{Name: "userId_weekId", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "weekId", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)
}

// 用户每天记录
type DayRecord struct {
	Id          primitive.ObjectID `bson:"_id" json:"id"`                  // ID
	UserId      string             `bson:"userId" json:"userId"`           // 主播ID
	WeekId      string             `bson:"weekId" json:"weekId"`           // 周ID
	Date        time.Time          `bson:"date" json:"date"`               // 日期
	Duration    int64              `bson:"duration" json:"duration"`       // 直播时长，单位秒
	LuckDiamond int64              `bson:"luckDiamond" json:"luckDiamond"` // 幸运礼物流水，金币
	Score       float64            `bson:"score" json:"score"`             // 积分
	Immutable   bool               `bson:"immutable" json:"immutable"`     // 数据更新完毕，不再变更
	CreatedAt   time.Time          `bson:"createdAt" json:"createdAt"`     // 创建时间
	UpdatedAt   time.Time          `bson:"updatedAt" json:"updatedAt"`     // 更新时间
}

func DayRecordCollectionName() string {
	return "act.laborious.day.record"
}

func createDayRecordIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(DayRecordCollectionName(), 1,
		db.Indexer{Name: "userId_date", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "date", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)
}
