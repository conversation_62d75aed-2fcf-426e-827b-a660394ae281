package laborious

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Provide(
	dbmc *db.MongoClient,
	dm *redi.Mutex,
	fm *fund.Manager,
	lm *live.Manager,
	lsm *ls.Manager,
	imm *im.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	createWeekRecordIndexer(dbmc)
	createDayRecordIndexer(dbmc)

	return newManager(dbmc, dm, fm, lm, lsm, imm, vnd.Scope("laborious.mgr"))
}

func InvokeInAPI(
	evq mq.Queue,
	mgr *Manager,
) {
	if !env.APIServer() {
		return
	}

	evt.Watch(evq, evt.GiftSendAdvanced, "laborious.mgr.gift", mgr.onGiftSentAdvanced,
		mq.MaxConcurrency(32), mq.LogCost("evt.gift.send.advanced"),
	)
}
