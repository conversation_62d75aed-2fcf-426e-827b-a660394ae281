package laborious

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *Manager) Receive(ctx context.Context, userId string, weekId string) error {
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:LABORIOUS:%s", userId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	nn := now.New(time.Now().In(ctz.Brazil))

	logger := m.log.With(
		zap.String("userId", userId),
		zap.String("weekId", weekId),
		zap.Time("date", nn.BeginningOfDay()),
	)

	weeks, err := m.weeks(ctx, userId)

	if err != nil {
		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return err
	}

	var w *Week

	for _, v := range weeks {
		if v.WeekId == weekId {
			w = &v

			break
		}
	}

	if w == nil {
		err = fmt.Errorf("week not found")

		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return err
	}

	if w.Cristais <= 0 {
		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return fmt.Errorf("no reward")
	}

	// 根据状态领取
	if w.Status != ReceiveStatusCan {
		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return fmt.Errorf("status error")
	}

	var wr WeekRecord

	err = m.dbmc.Collection(WeekRecordCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
			"weekId": weekId,
		},
	).Decode(&wr)

	if err != nil {
		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return err
	}

	ur, err := m.dbmc.Collection(WeekRecordCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"userId": userId,
			"weekId": weekId,
		},
		bson.M{
			"$set": bson.M{
				"received":   true,
				"receivedAt": time.Now(),
				"cristais":   w.Cristais,
			},
		},
	)

	if err != nil {
		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return err
	}

	if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
		logger.Error("领取勤劳主播奖励错误", zap.Error(err))

		return fmt.Errorf("update error")
	}

	// 发放奖励
	err = m.fm.Income(
		ctx,
		userId,
		fund.JTypeRewards,
		fund.PTypeFruits,
		w.Cristais,
		fund.WithTrade(wr.Id.Hex()),
		fund.WithDetail("Trabalhou, ganhou."),
	)

	if err != nil {
		logger.Error(
			"领取奖励发放失败",
			zap.Error(err),
		)
	} else {
		// 发送IM通知
		m.imm.SendSystemNoticeTextToUser(
			ctx,
			userId,
			fmt.Sprintf(
				// 恭喜您在勤劳主播活动中表现出色，共获得 xx 水晶，奖励已发放至账户
				`Parabéns pelo seu excelente desempenho no evento "Trabalhou, ganhou". Você recebeu um total de %d cristais. As recompensas foram enviadas para sua conta.`,
				w.Cristais,
			),
		)
	}

	logger.Info(
		"领取勤劳主播奖励",
		zap.Int64("fruits", w.Cristais),
		zap.Float64("score", w.TotalScore),
		zap.Float64("factor", w.Factor),
	)

	return nil
}
