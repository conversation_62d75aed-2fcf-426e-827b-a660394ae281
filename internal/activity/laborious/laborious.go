package laborious

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (m *Manager) Info(ctx context.Context, userId string) (*Act, error) {
	// 和领取使用相同的锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:LABORIOUS:%s", userId))
	if err != nil {
		return nil, err
	}
	defer l.MustUnlock()

	wks, err := m.weeks(ctx, userId)

	if err != nil {
		return nil, err
	}

	return &Act{
		StartTime:   m.getStartTime().Unix(),
		EndTime:     m.getEndTime().Unix(),
		CurrentDate: now.New(time.Now().In(ctz.Brazil)).BeginningOfDay().Format("2006-01-02"),
		Weeks:       wks,
	}, nil
}

// 周数据列表
func (m *Manager) weeks(ctx context.Context, userId string) ([]Week, error) {
	nn := now.New(time.Now().In(ctz.Brazil))
	todayDate := nn.BeginningOfDay()
	actStartTime := m.getStartTime()
	actEndTime := m.getEndTime()

	sn := now.New(actStartTime)
	en := now.New(actEndTime)

	// 活动开始周起始时间
	weekStartDate := sn.BeginningOfWeek()

	logger := m.log.With(
		zap.String("userId", userId),
		zap.Time("todayDate", todayDate),
		zap.Time("weekStartDate", weekStartDate),
	)

	// 查询用户已有的日数据
	cursor, err := m.dbmc.Collection(DayRecordCollectionName()).Find(
		ctx,
		bson.M{
			"userId": userId,
			"date": bson.M{
				"$gte": sn.BeginningOfDay(),
				"$lte": en.BeginningOfDay(),
			},
		},
	)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	recordMap := make(map[time.Time]DayRecord)

	for cursor.Next(ctx) {
		var record DayRecord

		if err := cursor.Decode(&record); err != nil {
			return nil, err
		}

		recordMap[record.Date.In(ctz.Brazil)] = record
	}

	weeks := make([]Week, 0)

	for weekStartDate.Before(actEndTime) {
		weekId := weekStartDate.Format("20060102150405")
		var today int
		var totalScore float64
		var todayScore float64
		var todayFinish int
		// 已经出勤的天数
		var finishDays int
		// 剩余天数
		var remainDays int
		tasks := make([]Task, 0)

		// 下周开始时间
		nextWeekStartDate := weekStartDate.AddDate(0, 0, 7)

		// 每日状态
		for i := 0; i < 7; i++ {
			var duration int64
			var luckDiamond int64
			var score float64

			dateTime := weekStartDate.AddDate(0, 0, i)

			isToday := dateTime.Equal(todayDate)

			if isToday {
				today = i + 1
			}

			// 是否读取最新的直播时长数据
			var readLatest bool

			record, ok := recordMap[dateTime]

			if ok {
				// 流水直接读取表数据
				luckDiamond = record.LuckDiamond

				if record.Immutable {
					duration = record.Duration
					score = record.Score
				} else {
					readLatest = true
				}
			} else {
				readLatest = true
			}

			if readLatest {
				nDateTime := now.New(dateTime)
				duration, err = m.lm.GetLiveValidDuration(ctx, userId, nDateTime.BeginningOfDay(), nDateTime.EndOfDay())

				if err != nil {
					return nil, err
				}

				score = m.getScore(duration, luckDiamond)
			}

			// 算做出勤
			if score > 0 {
				finishDays++
			}

			if dateTime.Before(todayDate) {
				// 今日之前的数据，更新数据，设置为不可修改
				if !record.Immutable {
					ur, err := m.dbmc.Collection(DayRecordCollectionName()).UpdateOne(
						ctx,
						bson.M{
							"userId":    userId,
							"weekId":    weekId,
							"date":      dateTime,
							"immutable": false,
						},
						bson.M{
							"$set": bson.M{
								"duration":  duration,
								"score":     score,
								"immutable": true,
								"updatedAt": time.Now(),
							},
							"$setOnInsert": bson.M{
								"createdAt": time.Now(),
							},
						},
						options.Update().SetUpsert(true),
					)

					logger.Info(
						"勤劳主播更新日数据",
						zap.String("weekId", weekId),
						zap.Time("date", dateTime),
						zap.Int64("duration", duration),
						zap.Int64("luckDiamond", luckDiamond),
						zap.Float64("score", score),
						zap.Any("response", ur),
						zap.Error(err),
					)
				}
			} else {
				remainDays++
			}

			tasks = append(tasks, Task{
				Date:        dateTime.Format("2006-01-02"),
				DateTime:    dateTime,
				Day:         i + 1,
				Score:       score,
				Duraiton:    duration,
				LuckDiamond: luckDiamond,
				IsToday:     isToday,
			})

			// 已出勤天数总积分
			totalScore += score

			if isToday {
				todayScore = score
				if score > 0 {
					todayFinish = 1
				}
			}
		}

		// 领取时间，下周一九点
		receiveTime := weekStartDate.AddDate(0, 0, 7).Add(9 * time.Hour)
		var isReceived bool

		var canUpdate bool

		// 只更新当前时间之前已开始的周数据
		if nn.Time.After(weekStartDate) {
			canUpdate = true
		}

		var wr WeekRecord

		err := m.dbmc.Collection(WeekRecordCollectionName()).FindOne(
			ctx,
			bson.M{
				"userId": userId,
				"weekId": weekId,
			},
		).Decode(&wr)

		if err != nil {
			if err != mongo.ErrNoDocuments {
				return nil, err
			}
		} else {
			isReceived = wr.Received

			if wr.Immutable {
				canUpdate = false
			}
		}

		// 根据出勤天数计算系数
		factor := m.getFactor(finishDays)
		// 根据总积分计算可获得水晶
		cristais := int64(totalScore*factor) * unitFruit

		status := ReceiveStatusNotTime

		if nn.Time.After(receiveTime) {
			if isReceived {
				status = ReceiveStatusDone
			} else {
				if cristais > 0 {
					status = ReceiveStatusCan
				} else {
					status = ReceiveStatusNoReward
				}
			}
		}

		var estimateTotalScore, estimateFactor float64
		var estimateCristais int64
		var weekImmutable bool
		// 周已经结束的不需要估算数据，使用真实数据
		if nn.Time.After(nextWeekStartDate) {
			estimateTotalScore = totalScore
			estimateFactor = factor
			estimateCristais = cristais
			weekImmutable = true
		} else {
			// 加上剩余天数的总分数
			estimateTotalScore = totalScore - todayScore + float64(remainDays)*m.getMaxScore()
			// 算上剩余天数后计算系数
			estimateFactor = m.getFactor(finishDays - todayFinish + remainDays)
			// 估算水晶
			estimateCristais = int64(estimateTotalScore*estimateFactor) * unitFruit
		}

		// 更新
		if canUpdate {
			wur, err := m.dbmc.Collection(WeekRecordCollectionName()).UpdateOne(
				ctx,
				bson.M{
					"userId":    userId,
					"weekId":    weekId,
					"immutable": false,
				},
				bson.M{
					"$set": bson.M{
						"totalScore": totalScore,
						"factor":     factor,
						"cristais":   cristais,
						"immutable":  weekImmutable,
						"updatedAt":  time.Now(),
					},
					"$setOnInsert": bson.M{
						"received":   false,
						"receivedAt": time.Time{},
						"createdAt":  time.Now(),
					},
				},
				options.Update().SetUpsert(true),
			)

			logger.Info(
				"勤劳主播更新周数据",
				zap.String("weekId", weekId),
				zap.Float64("totalScore", totalScore),
				zap.Float64("factor", factor),
				zap.Int64("cristais", cristais),
				zap.Bool("immutable", weekImmutable),
				zap.Any("response", wur),
				zap.Error(err),
			)
		}

		weeks = append(weeks, Week{
			WeekId:             weekId,
			StartTime:          weekStartDate,
			EndTime:            now.New(weekStartDate).EndOfWeek(),
			Tasks:              tasks,
			Today:              today,
			TotalScore:         math.Round(totalScore*10) / 10,
			EstimateTotalScore: estimateTotalScore,
			Factor:             factor,
			EstimateFactor:     estimateFactor,
			Cristais:           cristais,
			EstimateCristais:   estimateCristais,
			ReceiveTime:        receiveTime.UnixMilli(),
			Status:             status,
		})

		weekStartDate = weekStartDate.AddDate(0, 0, 7)
	}

	return weeks, nil
}

func (m *Manager) getScore(duration, luckDiamond int64) float64 {
	td := time.Duration(duration) * time.Second

	var ret float64

	for _, step := range steps {
		if td >= step.Duration && luckDiamond >= step.LuckDiamond {
			ret = step.Score
		}
	}

	return ret
}

func (m *Manager) getFactor(weekDays int) float64 {
	var ret float64

	v, ok := scoreFactor[weekDays]

	if ok {
		ret = v
	}

	return ret
}

func (m *Manager) getMaxScore() float64 {
	return steps[len(steps)-1].Score
}

func (m *Manager) getStartTime() time.Time {
	return startTime
}

func (m *Manager) getEndTime() time.Time {
	return endTime
}

func (m *Manager) isOpen(t time.Time) bool {
	return t.After(m.getStartTime()) && t.Before(m.getEndTime())
}

func (m *Manager) TestChangeDate(ctx context.Context, userId string, startDate time.Time) error {
	oldStartTime := m.getStartTime()

	// 改时间
	startTime = startDate
	endTime = startDate.AddDate(0, 0, 13).Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	diff := m.getStartTime().Sub(oldStartTime)

	cur1, err := m.dbmc.Collection(WeekRecordCollectionName()).Find(
		ctx,
		bson.M{
			"userId": userId,
		},
	)

	if err != nil {
		return err
	}

	defer cur1.Close(ctx)

	for cur1.Next(ctx) {
		var wr WeekRecord

		if err := cur1.Decode(&wr); err != nil {
			return err
		}

		weekId2Time, err := time.ParseInLocation("20060102150405", wr.WeekId, ctz.Brazil)

		if err != nil {
			return err
		}

		newWeekId := weekId2Time.Add(diff).Format("20060102150405")

		_, err = m.dbmc.Collection(WeekRecordCollectionName()).UpdateOne(
			ctx,
			bson.M{"_id": wr.Id},
			bson.M{
				"$set": bson.M{
					"weekId": newWeekId,
				},
			},
		)

		if err != nil {
			return err
		}
	}

	cur2, err := m.dbmc.Collection(DayRecordCollectionName()).Find(
		ctx,
		bson.M{
			"userId": userId,
		},
	)

	if err != nil {
		return err
	}

	defer cur2.Close(ctx)

	for cur2.Next(ctx) {
		var dr DayRecord

		if err := cur2.Decode(&dr); err != nil {
			return err
		}

		weekId2Time, err := time.ParseInLocation("20060102150405", dr.WeekId, ctz.Brazil)

		if err != nil {
			return err
		}

		newWeekId := weekId2Time.Add(diff).Format("20060102150405")

		newDate := dr.Date.Add(diff)

		_, err = m.dbmc.Collection(DayRecordCollectionName()).UpdateOne(
			ctx,
			bson.M{"_id": dr.Id},
			bson.M{
				"$set": bson.M{
					"weekId": newWeekId,
					"date":   newDate,
				},
			},
		)

		if err != nil {
			return err
		}
	}

	return nil
}
