package laborious

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

func newManager(
	dbmc *db.MongoClient,
	dm *redi.Mutex,
	fm *fund.Manager,
	lm *live.Manager,
	lsm *ls.Manager,
	imm *im.Manager,
	log *zap.Logger,
) (*Manager, error) {
	return &Manager{
		dbmc: dbmc,
		dm:   dm,
		fm:   fm,
		lm:   lm,
		lsm:  lsm,
		imm:  imm,
		log:  log,
	}, nil
}

type Manager struct {
	dbmc *db.MongoClient
	dm   *redi.Mutex
	fm   *fund.Manager
	lm   *live.Manager
	lsm  *ls.Manager
	imm  *im.Manager
	log  *zap.Logger
}
