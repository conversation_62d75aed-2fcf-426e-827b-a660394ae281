package anchor

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

// 修改扶持日期
func (m *Manager) ChangeSupportDate(
	ctx context.Context,
	userId string,
	startDate time.Time,
) error {
	var aes AnchorEvaluationSupport

	err := m.db.Collection(AnchorEvaluationSupportCollectionName()).FindOne(
		ctx,
		bson.M{
			"_id": userId,
		},
	).Decode(&aes)

	if err != nil {
		return err
	}

	diff := startDate.Sub(aes.StartDate)

	set := bson.M{
		"startDate": startDate,
		"endTime":   aes.EndTime.Add(diff),
		"createdAt": aes.CreatedAt.Add(diff),
		"updatedAt": aes.UpdatedAt.Add(diff),
	}

	if !aes.PauseDate.IsZero() {
		set["pauseDate"] = aes.PauseDate.Add(diff)
	}

	if !aes.PauseTime.IsZero() {
		set["pauseTime"] = aes.PauseTime.Add(diff)
	}

	return m.db.TryTxn(ctx, func(ctx context.Context) error {
		_, err = m.db.Collection(AnchorEvaluationSupportCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id": userId,
			},
			bson.M{
				"$set": set,
			},
		)

		if err != nil {
			return err
		}

		cursor, err := m.db.Collection(AnchorEvaluationSupportDayCollectionName()).Find(
			ctx,
			bson.M{
				"userId": userId,
			},
		)

		if err != nil {
			return err
		}

		defer cursor.Close(ctx)

		var aesd []AnchorEvaluationSupportDay

		err = cursor.All(ctx, &aesd)

		if err != nil {
			return err
		}

		for _, v := range aesd {
			_, err = m.db.Collection(AnchorEvaluationSupportDayCollectionName()).UpdateOne(
				ctx,
				bson.M{
					"_id": v.Id,
				},
				bson.M{
					"$set": bson.M{
						"date": v.Date.Add(diff),
					},
				},
			)

			if err != nil {
				return err
			}
		}

		return nil
	})
}

// 清空扶持数据
func (m *Manager) ClearSupportData(
	ctx context.Context,
	userId string,
) error {
	err := m.db.TryTxn(ctx, func(ctx context.Context) error {
		_, err := m.db.Collection(AnchorEvaluationSupportCollectionName()).DeleteOne(
			ctx,
			bson.M{
				"_id": userId,
			},
		)

		if err != nil {
			return err
		}

		_, err = m.db.Collection(AnchorEvaluationSupportDayCollectionName()).DeleteMany(
			ctx,
			bson.M{
				"userId": userId,
			},
		)

		return err
	})

	if err != nil {
		return err
	}

	m.rc.Del(ctx, fmt.Sprintf(keySupportPending, userId))

	return nil
}
