package anchor

import (
	"context"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(
	dbmc *db.MongoClient,
	rc *redi.Client,
	dm *redi.Mutex,
	lm *live.Manager,
	fm *fund.Manager,
	imm *im.Manager,
	ev ev.Bus,
	syn cc.Sync,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(AnchorEvaluationCollectionName(), 1,
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}},
		db.Indexer{Name: "userIp", Keys: bson.D{
			{Key: "userIp", Value: 1},
		}},
		db.Indexer{Name: "userDevice", Keys: bson.D{
			{Key: "userDevice", Value: 1},
		}},
	)

	createAnchorEvaluationSupport(dbmc)
	createAnchorEvaluationSupportDayIndexer(dbmc)
	createAnchorEvaluationFund(dbmc)

	return newManager(dbmc, rc, dm, lm, fm, imm, ev, syn, vnd.Scope("anchor.mgr")), nil
}

func Invoke(
	sch *cron.Scheduler,
	evb ev.Bus,
	mgr *Manager,
) {
	// 开播事件
	evb.Watch(evt.LiveStart, "anchor.mgr", ev.NewWatcher(mgr.onLiveStart))
	// 设置直播间flag
	evb.Watch(evt.LiveFlagSet, "anchor.mgr.live.flag", ev.NewWatcher(mgr.onLiveFlagSet))
}

func InvokeInAPI(sch *cron.Scheduler, evq mq.Queue, mgr *Manager) {
	if !env.APIServer() {
		return
	}

	// 送礼事件
	evt.Watch(evq, evt.GiftSendAdvanced, "anchor.mgr.gift", mgr.onGiftSentAdvanced,
		mq.MaxConcurrency(32), mq.LogCost("evt.gift.send.advanced"),
	)

	// 流水考核检查
	{
		task := sch.Exclusive("anchor.mgr.fund.inspect", func(context.Context) error {
			mgr.inspectFund()

			return nil
		})

		// 每分钟检查
		sch.Cron("*/1 * * * *").Do(task)
	}
	// 扶持任务检查
	{
		task := sch.Exclusive("anchor.mgr.support.inspect", func(context.Context) error {
			mgr.inspectSupport()

			return nil
		})

		// 每分钟检查
		sch.Cron("*/1 * * * *").Do(task)
	}
}
