package anchor

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *Manager) onLiveStart(ctx context.Context, evd *evt.StartLive) error {
	af, err := m.GetAnchorFlags(evd.UserId)

	if err != nil {
		return err
	}

	if af.IsNewAnchorFlag() {
		return m.lm.AddFlag(ctx, evd.RoomId, live.RoomFlagNewAnchor)
	}

	return m.lm.RemoveFlag(ctx, evd.RoomId, live.RoomFlagNewAnchor)
}

func (m *Manager) onGiftSentAdvanced(ctx context.Context, evd *evt.SendGiftAdvanced) error {
	if !evd.Gift.Lucky {
		return nil
	}

	anchorId := evd.AnchorId

	logger := m.log.With(
		zap.String("anchorId", anchorId),
		zap.Int("diamond", evd.Diamond),
	)

	// 流水考核主播
	if m.rc.Exists(ctx, fmt.Sprintf(keyFundExamine, anchorId)).Val() == 1 {
		m.db.Collection(AnchorEvaluationFundCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id":    anchorId,
				"status": AnchorEvaluationStatusPending,
			},
			bson.M{
				"$inc": bson.M{
					"luckDiamond": evd.Diamond,
				},
			},
		)

		logger.Info("新主播流水考核")
	}

	// 扶持政策主播
	if m.rc.Exists(ctx, fmt.Sprintf(keySupportPending, anchorId)).Val() == 1 {
		todayDate := now.With(time.Now().In(ctz.Brazil)).BeginningOfDay()

		// 当日的幸运礼物流水值
		m.db.Collection(AnchorEvaluationSupportDayCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"userId": anchorId,
				"date":   todayDate,
			},
			bson.M{
				"$inc": bson.M{
					"luckDiamond": evd.Diamond,
				},
			},
		)

		logger.Info("新主播扶持")
	}

	return nil
}

func (m *Manager) onLiveFlagSet(ctx context.Context, evd *evt.SetLiveFlag) error {
	if evd.Flag != int(live.RoomFlagQuality) {
		return nil
	}

	room, err := m.lm.Room2(evd.RoomId)

	if err != nil {
		m.log.Error("取消新主播扶持失败", zap.Error(err), zap.String("roomId", evd.RoomId))

		return err
	}

	return m.TerminateSupport(ctx, room.UserId, "标记为优质主播", TerminateReasonTypeQuality, "")
}
