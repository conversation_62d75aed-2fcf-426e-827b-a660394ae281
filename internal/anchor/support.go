package anchor

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type Support struct {
	Status int                      `json:"status"` // -1终止 1进行中 2已暂停 3已结束
	Day    int                      `json:"day"`    // 考核第几天
	List   []SupportDay             `json:"list"`   // 每日任务列表
	Aes    *AnchorEvaluationSupport `json:"-"`      // 扶持信息
}

type SupportDay struct {
	Day         int       `json:"day"`         // 第几天
	Date        time.Time `json:"date"`        // 日期
	IsToday     bool      `json:"isToday"`     // 是否今日
	Paused      bool      `json:"paused"`      // 暂停
	Duration    int64     `json:"duration"`    // 直播时长，单位秒
	LuckDiamond int64     `json:"luckDiamond"` // 幸运礼物流水，金币
	Status      int       `json:"status"`      // 1未开始 2未完成 3待领取 4已领取
}

type SupportAward struct {
	LuckDiamond int64 `json:"luckDiamond" bson:"luckDiamond"` // 幸运礼物流水，金币
	Duration    int64 `json:"duration" bson:"duration"`       // 直播时长，单位秒
	Award       int64 `json:"award" bson:"award"`             // 奖励，水晶
}

var (
	// 前5日奖励
	SupportAwardsB5 = []SupportAward{
		{LuckDiamond: 0, Duration: 3600 * 2, Award: 20000},
	}
	// 5日后奖励
	SupportAwardsA5 = []SupportAward{
		{LuckDiamond: 300000, Duration: 3600 * 2, Award: 10000},
		{LuckDiamond: 600000, Duration: 3600 * 2, Award: 15000},
		{LuckDiamond: 1000000, Duration: 3600 * 2, Award: 20000},
	}
	// 扶持天数
	SupportDays = 15
	// 扶持未开启
	ErrSupportNotStart = biz.NewError(biz.ErrBusiness, "support not start")
	ErrSupportNotAllow = biz.NewError(biz.ErrBusiness, "you can't start support")
)

const (
	keySupportPending = "STR:ANCHOR:ES:PENDING:%s" // 主播userId
)

const (
	DurationSupportStartConfirm = 3 * time.Hour
	DurationSupportStartDeny    = 2 * time.Hour
)

// 新主播扶持
func (m *Manager) StatusSupport(
	ctx context.Context,
	userId string,
) (*Support, error) {
	var aes *AnchorEvaluationSupport

	err := m.db.Collection(AnchorEvaluationSupportCollectionName()).FindOne(
		ctx,
		bson.M{"_id": userId},
	).Decode(&aes)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrSupportNotStart
		}

		return nil, err
	}

	cursor, err := m.db.Collection(AnchorEvaluationSupportDayCollectionName()).Find(
		ctx,
		bson.M{"userId": userId},
		options.Find().SetSort(bson.M{"day": 1}),
	)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	var res []AnchorEvaluationSupportDay

	if err := cursor.All(ctx, &res); err != nil {
		return nil, err
	}

	today := now.With(time.Now().In(ctz.Brazil)).BeginningOfDay()

	var day int
	list := []SupportDay{}

	for _, v := range res {
		isToday := today.Equal(v.Date)

		list = append(list, SupportDay{
			Day:         v.Day,
			Date:        v.Date.In(ctz.Brazil),
			IsToday:     isToday,
			Paused:      aes.PauseDate.Equal(v.Date),
			Duration:    v.Duration,
			LuckDiamond: v.LuckDiamond,
			Status:      v.Status(),
		})

		if isToday {
			day = v.Day
		}
	}

	return &Support{
		Status: aes.Status,
		Day:    day,
		List:   list,
		Aes:    aes,
	}, nil
}

func (m *Manager) StartSupport(
	ctx context.Context,
	userId string,
) error {
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:AES:START:%s", userId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	// 前置条件判断
	nt := time.Now()
	startDate := now.With(nt.In(ctz.Brazil)).BeginningOfDay()

	var ae AnchorEvaluation

	err = m.db.Collection(AnchorEvaluationCollectionName()).FindOne(
		ctx,
		bson.M{"userId": userId},
	).Decode(&ae)

	if err != nil {
		return err
	}

	if ae.Phase == AnchorEvaluationPhaseSupport {
		return nil
	}

	// 资料考核未通过
	if ae.Phase == AnchorEvaluationPhaseDocument && ae.Status != AnchorEvaluationStatusPass {
		return ErrSupportNotAllow
	}

	// 流水考核未通过
	if ae.Phase == AnchorEvaluationPhaseFund {
		var aef AnchorEvaluationFund

		err := m.db.Collection(AnchorEvaluationFundCollectionName()).FindOne(
			ctx,
			bson.M{
				"_id": userId,
			},
		).Decode(&aef)

		if err != nil {
			return err
		}

		if aef.Status != AnchorEvaluationFundStatusPass {
			return ErrSupportNotAllow
		}
	}

	return m.db.TryTxn(ctx, func(ctx context.Context) error {
		ur1, err := m.db.Collection(AnchorEvaluationCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"userId": userId,
				"phase":  bson.M{"$ne": AnchorEvaluationPhaseSupport},
			},
			bson.M{
				"$set": bson.M{
					"phase": AnchorEvaluationPhaseSupport,
				},
			},
		)

		if err != nil {
			return err
		}

		if ur1.MatchedCount == 0 || ur1.ModifiedCount == 0 {
			return errors.New("start support failed")
		}

		ur, err := m.db.Collection(AnchorEvaluationSupportCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id":    userId,
				"status": AnchorEvaluationSupportStatusNotStart,
			},
			bson.M{
				"$set": bson.M{
					"startDate": startDate,
					"status":    AnchorEvaluationSupportStatusPending,
					"endTime":   startDate.AddDate(0, 0, SupportDays),
					"updatedAt": nt,
				},
			},
		)

		if err != nil {
			return err
		}

		if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
			return errors.New("start support failed, please try again")
		}

		for i := 1; i <= SupportDays; i++ {
			var awards []SupportAward
			if i <= 5 {
				awards = SupportAwardsB5
			} else {
				awards = SupportAwardsA5
			}

			_, err := m.db.Collection(AnchorEvaluationSupportDayCollectionName()).InsertOne(
				ctx,
				&AnchorEvaluationSupportDay{
					Id:          primitive.NewObjectID(),
					UserId:      userId,
					Date:        startDate.AddDate(0, 0, i-1),
					Day:         i,
					Duration:    0,
					LuckDiamond: 0,
					Awards:      awards,
				},
			)

			if err != nil {
				return err
			}
		}

		m.rc.Set(ctx, fmt.Sprintf(keySupportPending, userId), 1, time.Until(startDate.AddDate(0, 0, SupportDays)))

		return nil
	})
}

// 领取奖励
func (m *Manager) ReceiveSupportAward(
	ctx context.Context,
	userId string,
	date time.Time,
) error {
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:AES:%s", userId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	nn := now.New(time.Now().In(ctz.Brazil))
	todayTime := nn.BeginningOfDay()

	if date.After(todayTime.AddDate(0, 0, 1)) {
		return biz.NewError(biz.ErrBusiness, "can't receive award after today")
	}

	logger := m.log.With(
		zap.String("userId", userId),
		zap.Time("date", date),
		zap.Any("country", date.Format(time.RFC3339)),
	)

	var aes *AnchorEvaluationSupport

	err = m.db.Collection(AnchorEvaluationSupportCollectionName()).FindOne(
		ctx,
		bson.M{"_id": userId},
	).Decode(&aes)

	if err != nil {
		logger.Error("领取新主播扶持奖励失败", zap.Error(err))

		return err
	}

	if !aes.CanReceive() {
		return biz.NewError(biz.ErrBusiness, "can't receive award")
	}

	var aesd AnchorEvaluationSupportDay

	err = m.db.Collection(AnchorEvaluationSupportDayCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
			"date":   date,
		},
	).Decode(&aesd)

	if err != nil {
		logger.Error("领取新主播扶持奖励失败", zap.Error(err))

		return err
	}

	if aesd.Received {
		return biz.NewError(biz.ErrBusiness, "award has received")
	}

	if aesd.Status() != SupportDayStatusCanReceive {
		return biz.NewError(biz.ErrBusiness, "can't receive award")
	}

	_, award := aesd.Award()

	if award == nil || award.Award == 0 {
		return biz.NewError(biz.ErrBusiness, "award not found")
	}

	logger = logger.With(
		zap.Int64("diamond", award.LuckDiamond),
		zap.Int64("duration", award.Duration),
		zap.Int64("fruits", award.Award),
		zap.String("tradeNo", aesd.Id.Hex()),
		zap.String("content", fmt.Sprintf("%d", aesd.Day)),
	)

	ur, err := m.db.Collection(AnchorEvaluationSupportDayCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"_id":      aesd.Id,
			"received": false,
		},
		bson.M{
			"$set": bson.M{
				"received":      true,
				"receivedAward": award,
				"receivedAt":    nn.Time,
				"immutable":     true,
			},
		},
	)

	if err != nil {
		logger.Error("领取新主播扶持奖励失败", zap.Error(err))

		return err
	}

	if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
		logger.Error("领取新主播扶持奖励失败", zap.Any("response", ur))

		return biz.NewError(biz.ErrBusiness, "award has received")
	}

	// 发放奖励
	if award.Award > 0 {
		err = m.fm.Income(
			ctx,
			userId,
			fund.JTypeRewards,
			fund.PTypeFruits,
			award.Award,
			fund.WithTrade(aesd.Id.Hex()),
			fund.WithDetail("Suporte do Novo Host"),
		)

		if err != nil {
			logger.Error(
				"领取奖励发放失败",
				zap.Error(err),
			)
		} else {
			// 发送IM通知
			m.imm.SendSystemNoticeTextToUser(
				ctx,
				userId,
				fmt.Sprintf(
					"Parabéns por completar a tarefa de apoio de novo host e ganhou %d cristais adicionais. A recompensa foi enviada para sua conta, por favor verifique!",
					award.Award,
				),
			)
		}
	}

	logger.Info("领取新主播扶持奖励")

	return nil
}

func (m *Manager) inspectSupport() {
	ctx := context.Background()
	collection1 := m.db.Collection(AnchorEvaluationSupportCollectionName())
	collection2 := m.db.Collection(AnchorEvaluationSupportDayCollectionName())

	cursor1, err := collection1.Find(
		ctx,
		bson.M{
			"status": AnchorEvaluationSupportStatusPending,
		},
	)

	if err != nil {
		return
	}

	defer cursor1.Close(ctx)

	var res1 []AnchorEvaluationSupport

	if err := cursor1.All(ctx, &res1); err != nil {
		return
	}

	logger := m.log.With(
		zap.Int("count", len(res1)),
	)

	logger.Info("开始新主播扶持检查")

	nn := now.With(time.Now().In(ctz.Brazil))

	for _, v := range res1 {
		cur, err := collection2.Find(
			ctx,
			bson.M{
				"userId":    v.Id,
				"date":      bson.M{"$lte": nn.Time},
				"immutable": false,
			},
			options.Find().SetSort(bson.M{"day": 1}),
		)

		if err != nil {
			logger.Error(
				"新主播扶持检查失败",
				zap.String("userId", v.Id),
				zap.String("req", "1"),
				zap.Error(err),
			)
			continue
		}

		defer cur.Close(ctx)

		var res2 []AnchorEvaluationSupportDay

		if err := cur.All(ctx, &res2); err != nil {
			logger.Error(
				"新主播扶持检查失败",
				zap.String("userId", v.Id),
				zap.String("req", "2"),
				zap.Error(err),
			)
			continue
		}

		for _, v := range res2 {
			dateTime := now.New(v.Date.In(ctz.Brazil))

			// !下播30分钟以后的不用再检查

			// 当日直播时长
			duration, err := m.lm.GetLiveValidDuration(ctx, v.UserId, dateTime.BeginningOfDay(), dateTime.EndOfDay())

			if err != nil {
				logger.Error(
					"新主播扶持检查失败",
					zap.String("userId", v.UserId),
					zap.String("req", "3"),
					zap.Time("from", dateTime.BeginningOfDay()),
					zap.Time("to", dateTime.EndOfDay()),
					zap.Error(err),
				)
				continue
			}

			set := bson.M{
				"duration": duration,
			}

			// 今天之前的数据，更新完不再变更
			if dateTime.Time.Before(nn.BeginningOfDay()) {
				set["immutable"] = true
			}

			_, err = collection2.UpdateOne(
				ctx,
				bson.M{
					"_id": v.Id,
				},
				bson.M{
					"$set": set,
				},
			)

			logger.Info(
				"新主播扶持数据更新",
				zap.String("userId", v.UserId),
				zap.Time("date", v.Date),
				zap.Int64("duration", duration),
				zap.Int64("luckDiamond", v.LuckDiamond),
				zap.Any("request", set),
				zap.Error(err),
			)

			if err != nil {
				logger.Error(
					"新主播扶持检查失败",
					zap.String("userId", v.UserId),
					zap.String("req", "4"),
					zap.Any("request", set),
					zap.Error(err),
				)
				continue
			}

			// 判断任务是否完成
			v.Duration = duration
			_, award := v.Award()
			if award != nil {
				if m.rc.SetNX(ctx, fmt.Sprintf("STR:ANCHOR:SUPPORT:EVT:%s:%s", v.UserId, dateTime.Format("20060102")), 1, time.Hour*24).Val() {
					// 任务完成事件
					m.ev.Emit(ctx, evt.NewAnchorSupportTask, &evt.SupportNewAnchorTask{
						UserId: v.UserId,
						Date:   dateTime.Time,
					})

					logger.Debug(
						"新主播扶持任务完成EVT",
						zap.String("userId", v.UserId),
						zap.Time("date", v.Date),
						zap.Int64("duration", v.Duration),
						zap.Int64("luckDiamond", v.LuckDiamond),
					)
				}
			}
		}

		// 已经结束的
		if v.IsEnd() {
			_, err := collection1.UpdateOne(
				ctx,
				bson.M{
					"_id": v.Id,
				},
				bson.M{
					"$set": bson.M{
						"status":    AnchorEvaluationSupportStatusEnd,
						"updatedAt": nn.Time,
					},
				},
			)

			if err != nil {
				logger.Error(
					"新主播扶持检查失败",
					zap.String("userId", v.Id),
					zap.String("req", "5"),
					zap.Error(err),
				)
			}

			m.delSupoprtKey(ctx, v.Id)
		} else {
			m.rebuildSupportKey(ctx, &v)
		}
	}
}

func (m *Manager) initSupport(ctx context.Context, anchorId, source string) error {
	// 初始化待开启的扶持
	_, err := m.db.Collection(AnchorEvaluationSupportCollectionName()).InsertOne(
		ctx,
		&AnchorEvaluationSupport{
			Id:        anchorId,
			Status:    AnchorEvaluationSupportStatusNotStart,
			Source:    source,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	)

	if err != nil {
		return err
	}

	// 如果是优质直播间，直接终止
	if isQualityRoom, _ := m.IsQualityRoom(ctx, anchorId); isQualityRoom {
		m.TerminateSupport(ctx, anchorId, "标记为优质主播", TerminateReasonTypeQuality, "")
	}

	return err
}

func (m *Manager) IsQualityRoom(ctx context.Context, anchorId string) (bool, error) {
	room, err := m.lm.RoomByUserId2(ctx, anchorId)

	if err != nil {
		return false, err
	}

	return room.IsQualityRoom(), nil
}

// 某天是否是新主播扶持阶段
func (m *Manager) IsSupportingDate(ctx context.Context, anchorId string, date time.Time) (bool, error) {
	// 获取扶持信息
	sp, err := m.StatusSupport(ctx, anchorId)

	if err != nil {
		if err == ErrSupportNotStart {
			return false, nil
		}

		return false, err
	}

	if sp.Aes == nil || sp.Aes.Status == AnchorEvaluationSupportStatusNotStart {
		return false, nil
	}

	if len(sp.List) == 0 {
		return false, nil
	}

	// 早于第一天日期
	if date.Before(sp.List[0].Date) {
		return false, nil
	}

	var terminateDate, pauseDate time.Time
	var isTerminate, isPause bool

	// 处理两个特殊状态
	switch sp.Status {
	case AnchorEvaluationSupportStatusTerminate:
		terminateDate = now.New(sp.Aes.TerminateTime.In(ctz.Brazil)).BeginningOfDay()
		isTerminate = true
	case AnchorEvaluationSupportStatusPause:
		pauseDate = now.New(sp.Aes.PauseDate.In(ctz.Brazil)).BeginningOfDay()
		isPause = true
	}

	for _, v := range sp.List {
		// 在要查询的日期之后，不再判断
		if v.Date.After(date) {
			return false, nil
		}

		// 终止
		if isTerminate {
			// 因是优质主播终止
			if sp.Aes.ReasonType == TerminateReasonTypeQuality {
				// 终止当天也算做扶持期
				if date.After(terminateDate) {
					return false, nil
				}
			} else {
				// 非优质主播终止当天不算做扶持期
				if date.After(terminateDate) || date.Equal(terminateDate) {
					return false, nil
				}
			}
		}

		// 暂停
		if isPause {
			// 暂停日期之后不算扶持（暂停当天算扶持，会消耗一天的扶持）
			if date.After(pauseDate) {
				return false, nil
			}
		}

		// 不算扶持的情况判断完后，要查询的日期匹配到，即为扶持中
		if v.Date.Equal(date) {
			return true, nil
		}
	}

	return false, nil
}

func (m *Manager) GetSupport(ctx context.Context, anchorId string) (*AnchorEvaluationSupport, error) {
	var aes AnchorEvaluationSupport

	err := m.db.Collection(AnchorEvaluationSupportCollectionName()).FindOne(
		ctx,
		bson.M{
			"_id": anchorId,
		},
	).Decode(&aes)

	if err != nil {
		return nil, err
	}

	return &aes, nil
}

func (m *Manager) rebuildSupportKey(ctx context.Context, aes *AnchorEvaluationSupport) {
	if aes.IsPending() {
		pendingKey := fmt.Sprintf(keySupportPending, aes.Id)
		if m.rc.Exists(ctx, pendingKey).Val() != 1 {
			// TODO 优化：准确的结束时间
			m.rc.Set(ctx, pendingKey, 1, time.Duration(SupportDays)*time.Hour*24)
		}
	}
}

func (m *Manager) delSupoprtKey(ctx context.Context, userId string) {
	m.rc.Del(ctx, fmt.Sprintf(keySupportPending, userId))
}
