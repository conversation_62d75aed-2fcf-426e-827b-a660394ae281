package anchor

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// 流水审核通过后，考核结果也变为通过

type FundExamine struct {
	Status      int   `json:"status"`      // 1进行中 2考核通过 3考核不通过
	RemainDay   int   `json:"remainDay"`   // 剩余天数
	LuckDiamond int64 `json:"luckDiamond"` // 流水，金币
	Target      int64 `json:"target"`      // 流水目标，金币
}

const (
	TargetLuckDiamond = 5000000                         // 任务目标流水
	keyFundExamine    = "STR:ANCHOR:EVALUATION:FUND:%s" // 主播流水考核
)

var (
	ErrFundNotAllow = biz.NewError(biz.ErrBusiness, "you can't start fund examine")
)

// 流水考核信息
func (m *Manager) StatusFund(ctx context.Context, userId string) (*FundExamine, error) {
	var aef AnchorEvaluationFund

	err := m.db.Collection(AnchorEvaluationFundCollectionName()).FindOne(
		ctx,
		bson.M{
			"_id": userId,
		},
	).Decode(&aef)

	if err != nil {
		return nil, err
	}

	return &FundExamine{
		Status:      aef.Status,
		RemainDay:   aef.RemainDay(),
		LuckDiamond: aef.LuckDiamond,
		Target:      aef.Target,
	}, nil
}

// 开始流水考核
func (m *Manager) StartFund(
	ctx context.Context,
	userId string,
) error {
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:AEF:START:%s", userId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	var ae AnchorEvaluation

	err = m.db.Collection(AnchorEvaluationCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
		},
	).Decode(&ae)

	if err != nil {
		return err
	}

	if ae.Phase == AnchorEvaluationPhaseFund {
		return nil
	}

	// 只有在文档阶段，且状态为不允许时，才能开启流水考核
	if ae.Phase != AnchorEvaluationPhaseDocument || ae.Status != AnchorEvaluationStatusNotAllow {
		return ErrFundNotAllow
	}

	return m.db.TryTxn(ctx, func(ctx context.Context) error {
		_, err := m.db.Collection(AnchorEvaluationCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"userId": userId,
				"phase":  AnchorEvaluationPhaseDocument,
				"status": AnchorEvaluationStatusNotAllow,
			},
			bson.M{
				"$set": bson.M{
					"phase":     AnchorEvaluationPhaseFund,
					"updatedAt": time.Now(),
				},
			},
		)

		if err != nil {
			return err
		}

		nt := time.Now().In(ctz.Brazil)
		nn := now.New(nt)
		startTime := nn.BeginningOfDay()
		endTime := startTime.AddDate(0, 0, 5)

		ur, err := m.db.Collection(AnchorEvaluationFundCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id":    userId,
				"status": AnchorEvaluationFundStatusNotStart,
			},
			bson.M{
				"$set": bson.M{
					"status":      AnchorEvaluationFundStatusPending,
					"startTime":   startTime,
					"endTime":     endTime,
					"luckDiamond": 0,
					"target":      TargetLuckDiamond,
					"createdAt":   nt,
					"updatedAt":   nt,
				},
			},
		)

		if err != nil {
			return err
		}

		if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
			return fmt.Errorf("update fund failed")
		}

		return m.rc.Set(ctx, fmt.Sprintf(keyFundExamine, userId), 1, time.Until(endTime)).Err()
	})
}

func (m *Manager) inspectFund() {
	ctx := context.Background()
	collection := m.db.Collection(AnchorEvaluationFundCollectionName())

	cursor, err := collection.Find(
		ctx,
		bson.M{
			"status": AnchorEvaluationFundStatusPending,
		},
	)

	if err != nil {
		return
	}

	defer cursor.Close(ctx)

	var res []AnchorEvaluationFund

	if err := cursor.All(ctx, &res); err != nil {
		return
	}

	m.log.Info("开始新主播流水检查", zap.Int("count", len(res)))

	nt := time.Now().In(ctz.Brazil)

	for _, v := range res {
		status := v.Status

		// 完成考核
		if v.LuckDiamond >= v.Target {
			status = AnchorEvaluationFundStatusPass
		} else {
			if nt.After(v.EndTime) {
				status = AnchorEvaluationFundStatusReject
			}
		}

		if status != v.Status {
			_, err := collection.UpdateOne(
				ctx,
				bson.M{
					"_id": v.Id,
				},
				bson.M{
					"$set": bson.M{
						"status": status,
					},
				},
			)

			if err != nil {
				m.log.Error(
					"更新新主播流水考核状态失败",
					zap.String("userId", v.Id),
					zap.Int("status", status),
					zap.Error(err),
				)

				continue
			}

			m.log.Info(
				"新主播流水检查",
				zap.String("userId", v.Id),
				zap.Int("status", status),
			)
		}

		switch status {
		case AnchorEvaluationFundStatusPending:
			if m.rc.Exists(ctx, fmt.Sprintf(keyFundExamine, v.Id)).Val() != 1 && time.Until(v.EndTime) > 0 {
				m.rc.Set(ctx, fmt.Sprintf(keyFundExamine, v.Id), 1, time.Until(v.EndTime))
			}
		case AnchorEvaluationFundStatusPass:
			// 流水考核通过，考核结果也变为通过
			m.AuditEvaluationByFundExamine(ctx, v.Id)
			fallthrough
		case AnchorEvaluationFundStatusReject:
			m.rc.Del(ctx, fmt.Sprintf(keyFundExamine, v.Id))
		}
	}
}

func (m *Manager) initFund(ctx context.Context, anchorId string) error {
	_, err := m.db.Collection(AnchorEvaluationFundCollectionName()).InsertOne(
		ctx,
		AnchorEvaluationFund{
			Id:        anchorId,
			Status:    AnchorEvaluationFundStatusNotStart,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	)

	return err
}
