package anchor

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	V2StartTime = time.Date(2025, 1, 16, 4, 30, 0, 0, ctz.Brazil)
	BannerTitle = "新主播扶持"
)

func IsV2(t time.Time) bool {
	return t.After(V2StartTime)
}

func (m *Manager) IsShowEvaluationBanner(ctx context.Context, title, userId string) bool {
	if title != BannerTitle {
		return true
	}

	nt := time.Now()

	if !IsV2(nt) {
		return false
	}

	// 是否有扶持记录
	var sp *AnchorEvaluationSupport
	var err error

	sp, err = m.GetSupport(ctx, userId)

	if err != nil {
		if err != mongo.ErrNoDocuments {
			return false
		}
	}

	// 有记录：已完成扶持超一星期后不显示，其他的显示
	if sp != nil {
		if sp.IsEnd() && nt.Sub(sp.EndTime) > 7*24*time.Hour {
			return false
		}

		return true
	}

	// 是否有新主播考核申请记录
	var ae *AnchorEvaluation

	ae, err = m.GetEvaluation(ctx, userId)

	if err != nil {
		if err != ErrEvaluationNotFound {
			return false
		}
	}

	// 没有有新主播考核申请记录
	if ae == nil {
		return true
	}

	// 有记录的
	// 允许重新提交的才显示 || 更新时间在V2之后的显示
	if ae.Status == AnchorEvaluationStatusReject || IsV2(ae.UpdatedAt) {
		return true
	}

	return false
}
