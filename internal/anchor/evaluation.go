package anchor

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var (
	ErrEvaluationNotFound = errors.New("evaluation not found")
)

func (m *Manager) GetEvaluation(ctx context.Context, userId string) (*AnchorEvaluation, error) {
	var ret AnchorEvaluation

	err := m.db.Collection(AnchorEvaluationCollectionName()).FindOne(
		ctx,
		bson.M{"userId": userId},
	).Decode(&ret)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrEvaluationNotFound
		}

		return nil, err
	}

	return &ret, nil
}

func (m *Manager) ApplyEvaluation(
	ctx context.Context,
	userId string,
	userIp string,
	userDevice string,
	screenshot string,
	video string,
	attachment string,
) error {
	res, err := m.db.Collection(AnchorEvaluationCollectionName()).UpdateOne(
		ctx,
		bson.M{"userId": userId},
		bson.M{
			"$set": bson.M{
				"phase":      AnchorEvaluationPhaseDocument,
				"userIp":     userIp,
				"userDevice": userDevice,
				"screenshot": screenshot,
				"video":      video,
				"attachment": attachment,
				"status":     AnchorEvaluationStatusPending,
				"updatedAt":  time.Now(),
			},
			"$setOnInsert": bson.M{
				"createdAt": time.Now(),
			},
		},
		options.Update().SetUpsert(true),
	)

	if err != nil {
		return err
	}

	if res.ModifiedCount > 0 || res.UpsertedCount > 0 {
		m.ev.Emit(ctx, evt.EvaluationApply, &evt.ApplyEvaluation{UserId: userId})
	}

	return nil
}

func (m *Manager) GetCheatInfo(ctx context.Context, userId string) (*AnchorCheatInfo, error) {
	var (
		ret     AnchorCheatInfo
		userEva AnchorEvaluation
	)
	err := m.db.Collection(AnchorEvaluationCollectionName()).FindOne(
		ctx,
		bson.M{"userId": userId},
	).Decode(&userEva)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrEvaluationNotFound
		}
		return nil, err
	}

	// 查询相同ip
	if userEva.UserIp != "" {
		var sameIps []AnchorEvaluation
		cur, err := m.db.Collection(AnchorEvaluationCollectionName()).Find(
			ctx,
			bson.M{"userIp": userEva.UserIp},
		)
		if err != nil {
			return nil, err
		}

		if err := cur.All(ctx, &sameIps); err != nil {
			return nil, err
		}
		ret.Ip = userEva.UserId
		ret.SameIpList = sameIps
	}

	// 查询相同设备id
	if userEva.UserDevice != "" {
		var sameDevices []AnchorEvaluation
		cur, err := m.db.Collection(AnchorEvaluationCollectionName()).Find(
			ctx,
			bson.M{"userDevice": userEva.UserDevice},
		)
		if err != nil {
			return nil, err
		}

		if err := cur.All(ctx, &sameDevices); err != nil {
			return nil, err
		}
		ret.Device = userEva.UserDevice
		ret.SameDeviceList = sameDevices
	}

	return &ret, nil
}

func (m *Manager) AuditEvaluation(
	ctx context.Context,
	auditUserId string,
	userId string,
	status int,
	reason string,
) error {
	res, err := m.db.Collection(AnchorEvaluationCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"userId": userId,
			"status": bson.M{
				"$ne": AnchorEvaluationStatusPass, // 不是审核通过状态
			},
		},
		bson.M{
			"$set": bson.M{
				"status":      status,
				"reason":      reason,
				"auditUserId": auditUserId,
				"auditedAt":   time.Now(),
				"updatedAt":   time.Now(),
			},
		},
	)

	if err != nil {
		return err
	}

	if res.MatchedCount > 0 {
		if status == AnchorEvaluationStatusPass {
			err := m.SetNewAnchorFlag(ctx, userId)

			if err != nil {
				m.log.Error("SetNewAnchorFlag", zap.Error(err), zap.String("userId", userId))
			}

			// 初始化一个扶持数据
			if IsV2(time.Now()) {
				err := m.initSupport(ctx, userId, AnchorEvaluationSupportSourceDocument)

				m.log.Info("初始化新主播扶持记录", zap.Error(err), zap.String("userId", userId))
			}
		} else if status == AnchorEvaluationStatusNotAllow {
			// 初始化一个流水考核数据
			if IsV2(time.Now()) {
				err := m.initFund(ctx, userId)

				m.log.Info("初始化新主播流水考核记录", zap.Error(err), zap.String("userId", userId))
			}
		}
	}

	return nil
}

// 流水考核通过
func (m *Manager) AuditEvaluationByFundExamine(ctx context.Context, userId string) error {
	res, err := m.db.Collection(AnchorEvaluationCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"userId": userId,
			"status": bson.M{
				"$ne": AnchorEvaluationStatusPass, // 不是审核通过状态
			},
		},
		bson.M{
			"$set": bson.M{
				"status":   AnchorEvaluationStatusPass,
				"fromFund": true,
			},
		},
	)

	if err != nil {
		return err
	}

	if res.MatchedCount > 0 {
		err := m.SetNewAnchorFlag(ctx, userId)

		if err != nil {
			m.log.Error("SetNewAnchorFlag", zap.Error(err), zap.String("userId", userId))
		}

		err = m.initSupport(ctx, userId, AnchorEvaluationSupportSourceFund)

		m.log.Info("初始化新主播扶持记录", zap.Error(err), zap.String("userId", userId))
	}

	return nil
}

func (m *Manager) ManageEvaluationList(
	ctx context.Context,
	anchorIds []string,
	status int,
	page int64,
	pageSize int64,
) ([]AnchorEvaluation, int64, error) {
	var ret []AnchorEvaluation

	filter := bson.M{}

	if status != 0 {
		filter["status"] = status
	}

	if len(anchorIds) == 1 {
		filter["userId"] = anchorIds[0]
	} else if len(anchorIds) > 1 {
		filter["userId"] = bson.M{"$in": anchorIds}
	}

	cur, err := m.db.Collection(AnchorEvaluationCollectionName()).Find(
		ctx,
		filter,
		options.Find().SetSort(bson.M{"createdAt": -1}).SetSkip((page-1)*pageSize).SetLimit(pageSize),
	)

	if err != nil {
		return nil, 0, err
	}

	defer cur.Close(ctx)

	err = cur.All(ctx, &ret)

	if err != nil {
		return nil, 0, err
	}

	count, _ := m.db.Collection(AnchorEvaluationCollectionName()).CountDocuments(ctx, filter)

	return ret, count, err
}

func (m *Manager) SetEvaluationIpDevice(ctx context.Context, userId, ip, device string) error {
	if userId == "" || ip == "" || device == "" {
		return nil
	}
	_, err := m.db.Collection(AnchorEvaluationCollectionName()).UpdateOne(
		ctx,
		bson.M{"userId": userId},
		bson.M{
			"$set": bson.M{
				"userIp":     ip,
				"userDevice": device,
			},
		})

	return err
}
