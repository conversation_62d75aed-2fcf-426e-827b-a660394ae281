package anchor

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (m *Manager) SetNewAnchorFlag(ctx context.Context, userId string) error {
	_, err := m.db.Collection(AnchorFlagsCollectionName()).InsertOne(
		ctx,
		&AnchorFlags{
			Id:               primitive.NewObjectID(),
			UserId:           userId,
			IsNewAnchor:      true,
			NewAnchorEndTime: time.Now().Add(15 * 24 * time.Hour),
			CreateAt:         time.Now(),
		})

	if err != nil {
		return err
	}

	// 清理缓存
	m.flagCache.Remove(userId)

	m.ev.Emit(ctx, evt.NewAnchorFlagSet, &evt.SetNewAnchorFlag{
		UserId: userId,
		Flag:   true,
		Time:   time.Now(),
	})

	return m.lm.AddFlagByUserId(ctx, userId, live.RoomFlagNewAnchor)
}

func (m *Manager) GetAnchorFlags(userId string) (*AnchorFlags, error) {
	return m.flagCache.Get(userId)
}

func (m *Manager) getAnchorFlagsModel(userId string) (*AnchorFlags, error) {
	var res AnchorFlags
	err := m.db.Collection(AnchorFlagsCollectionName()).FindOne(
		context.Background(),
		bson.M{
			"userId": userId,
		}, &options.FindOneOptions{Sort: bson.M{"_id": -1}}).Decode(&res)

	if err != nil && err != mongo.ErrNoDocuments {
		return &res, err
	}

	return &res, nil
}

func (m *Manager) ManageAnchorFlagsList(ctx context.Context) ([]*AnchorFlags, error) {
	cursor, err := m.db.Collection(AnchorFlagsCollectionName()).Find(
		ctx,
		bson.M{},
	)

	if err != nil {
		return nil, err
	}

	var res []*AnchorFlags
	err = cursor.All(ctx, &res)

	if err != nil {
		return nil, err
	}

	return res, nil
}
