package anchor

import "golang.org/x/text/language"

type AnchorCheatInfo struct {
	Ip             string `json:"ip"`
	SameIpList     []AnchorEvaluation
	Device         string `json:"device"`
	SameDeviceList []AnchorEvaluation
}

type NoticeDescription string

type Notice struct {
	Description NoticeDescription `json:"description"`
	Content     map[string]string `json:"content"`
}

type NoticeList struct {
	List []Notice `json:"list"`
}

func (m *NoticeList) GetContent(description NoticeDescription, lng string) string {
	for _, item := range m.List {
		if item.Description == description {
			if content, ok := item.Content[lng]; ok {
				return content
			}
			return item.Content[language.Portuguese.String()]
		}
	}

	return ""
}

var (
	CancelNoticeList = NoticeList{
		List: []Notice{
			{
				Description: "您已违反平台直播规则，您的新主播扶持资格已被取消。",
				Content: map[string]string{
					language.Portuguese.String(): "Devido a violação de regra da plataforma , seu suporte de novo host foi desqualificado.",
				},
			},
		},
	}

	PauseNoticeList = NoticeList{
		List: []Notice{
			{
				Description: "您已违反平台直播规则，您的新主播扶持资格已被暂停。",
				Content: map[string]string{
					language.Portuguese.String(): "Devido a violação de regra da plataforma , seu suporte de novo host foi cancelado temporariamente.",
				},
			},
			{
				Description: "您的新主播扶持资格已恢复。",
				Content: map[string]string{
					language.Portuguese.String(): "Seu apoio de novo host foi reativado, por favor segue as diretrizes da plataforma!",
				},
			},
		},
	}
)
