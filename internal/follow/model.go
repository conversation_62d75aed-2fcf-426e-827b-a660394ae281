package follow

import (
	"time"
)

type Following struct {
	ID        uint      `gorm:"primaryKey"`
	UserId    string    `gorm:"not null;size:32;uniqueIndex:followed"` // 用户Id
	Target    string    `gorm:"not null;size:32;uniqueIndex:followed"` // 目标Id
	Remark    string    `gorm:"not null;size:20;default:''"`           // 备注信息
	Duplex    bool      `gorm:"not null;default:false"`                // 双向关注
	Chummy    bool      `gorm:"not null;default:false"`                // 是否密友
	Focus     bool      `gorm:"not null;default:false"`                // 特别关注
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (f *Following) TableName() string {
	return "followings"
}
