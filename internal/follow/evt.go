package follow

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

func (s *Manager) onAddBlackList(ctx context.Context, evd evt.UserGrantUpdate) error {
	if evd.Kind == evt.GrantKindBlacklist && evd.OP == evt.GrantOpTypeAdd {
		ctx = WaitRefresh(ctx)
		if err := s.Unfollow(ctx, evd.UserId, evd.TargetId); err != nil {
			if !errors.Is(err, ErrNoFollowing) {
				return err
			}
		}
		if err := s.Remove(ctx, evd.UserId, evd.TargetId); err != nil {
			if !errors.Is(err, ErrNoFollowing) {
				return err
			}
		}
		return nil
	}
	return nil
}
