package follow

import (
	"context"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/es/query"
)

type Listing interface {
	TargetIds(ctx context.Context, userId string) ([]string, error)
	FriendIds(ctx context.Context, userId string) ([]string, error)
}

func (s *Manager) TargetIds(ctx context.Context, userId string) ([]string, error) {
	return s.getIds(ctx, query.MustTerm("userId", userId), query.SortBy("time", query.DESC))
}

func (s *Manager) FriendIds(ctx context.Context, userId string) ([]string, error) {
	return s.getIds(ctx, query.MustTerm("userId", userId), query.MustTerm("duplex", true))
}

func (s *Manager) getIds(ctx context.Context, opts ...query.Option) ([]string, error) {
	resp, err := s.ec.Search(ctx, idxName, append([]query.Option{query.NoSource(), query.Limit(maxFollows)}, opts...)...)
	if err != nil {
		return nil, err
	}
	return lo.Map(resp.DocIds(), func(docId string, _ int) string { return docId[32:] }), nil
}

// Friends 朋友列表
func (s *Manager) Friends(ctx context.Context, userId string, match string, cursor string) (*query.Pagination, Follows, error) {
	return s.getList(ctx, cursor,
		query.MustTerm("userId", userId), query.MustTerm("duplex", true),
		query.ShouldMatch("remark", match), query.ShouldContain("p2Id", match), query.ShouldMatch("p2Name", match),
		query.SortBy("chummy", query.DESC), query.SortBy("time", query.DESC),
	)
}

// Followings 关注列表
func (s *Manager) Followings(ctx context.Context, userId string, match string, cursor string) (*query.Pagination, Follows, error) {
	return s.getList(ctx, cursor,
		query.MustTerm("userId", userId),
		query.ShouldMatch("remark", match), query.ShouldContain("p2Id", match), query.ShouldMatch("p2Name", match),
		query.SortBy("focus", query.DESC), query.SortBy("time", query.DESC),
	)
}

// Followers 粉丝列表
func (s *Manager) Followers(ctx context.Context, userId string, match string, cursor string) (*query.Pagination, Follows, error) {
	return s.getList(ctx, cursor,
		query.MustTerm("target", userId),
		query.ShouldContain("p1Id", match), query.ShouldMatch("p1Name", match),
		query.SortBy("time", query.DESC),
	)
}

// Export 关注列表
func (s *Manager) Export(ctx context.Context, userId string) (*query.Pagination, Follows, error) {
	return s.getList(ctx, "", query.MustTerm("userId", userId), query.Limit(maxFollows),
		query.Select("target", "remark", "duplex"),
	)
}

func (s *Manager) getList(ctx context.Context, cursor string, opts ...query.Option) (*query.Pagination, Follows, error) {
	resp, err := s.ec.Search(ctx, idxName, append([]query.Option{query.Cursor(cursor), query.Limit(maxListing)}, opts...)...)
	if err != nil {
		return nil, nil, err
	}
	out := make(Follows, 0, resp.Count())
	if err := resp.Decode(&out); err != nil {
		return nil, nil, err
	}
	return resp.PageInfo(), out, nil
}
