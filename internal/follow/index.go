package follow

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/es"
	"gitlab.sskjz.com/go/es/index"
	"gitlab.sskjz.com/go/es/query"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const (
	idxName = "osl-follows"
)

var idxFields = map[string]index.Field{
	"userId": index.Keyword,
	"target": index.Keyword,
	"remark": index.SimpleText,
	"duplex": index.Boolean,
	"chummy": index.Boolean,
	"focus":  index.Boolean,
	"time":   index.Date,
	// extension
	"p1Id":   index.Wildcard,
	"p1Name": index.SimpleText,
	"p2Id":   index.Wildcard,
	"p2Name": index.SimpleText,
}

func newIndexer(m *Manager) *Indexer {
	return &Indexer{m: m}
}

type Indexer struct {
	m *Manager
}

type esApi func(ctx context.Context, idxName string, docId string, data any) error

type dataFn func(*Following) map[string]any

func (s *Indexer) updateFollow(ctx context.Context, from, to string, call esApi, data dataFn) error {
	link, err := s.m.takeInfo(ctx, from, to)
	if err != nil {
		if errors.Is(err, ErrNoFollowing) {
			return nil
		}
		return err
	}
	if err := call(ctx, idxName, followId(from, to), data(link)); err != nil {
		return err
	}
	return nil
}

func (s *Indexer) onUserFollowed(ctx context.Context, evd *evt.FollowedUser) error {
	if err := s.updateFollow(ctx, evd.From, evd.To, func(ctx context.Context, idxName string, docId string, data any) error {
		return s.m.ec.Create(ctx, idxName, docId, data, esOptions(ctx)...)
	}, func(link *Following) map[string]any {
		v := map[string]any{
			"userId": link.UserId,
			"target": link.Target,
			"duplex": link.Duplex,
			"time":   link.CreatedAt,
		}
		if p1, _ := s.m.ug.Account(ctx, link.UserId); p1 != nil {
			v["p1Id"] = p1.ShowId
			v["p1Name"] = p1.Nickname
		}
		if p2, _ := s.m.ug.Account(ctx, link.Target); p2 != nil {
			v["p2Id"] = p2.ShowId
			v["p2Name"] = p2.Nickname
		}
		return v
	}); err != nil {
		return err
	}
	if !evd.Duplex {
		return nil
	}
	return s.onFollowUpdate(ctx, &evt.UpdateFollow{From: evd.To, To: evd.From})
}

func (s *Indexer) onUserUnfollow(ctx context.Context, evd *evt.UnfollowUser) error {
	if err := s.m.ec.Delete(ctx, idxName, followId(evd.From, evd.To), esOptions(ctx)...); err != nil {
		return err
	}
	if !evd.Duplex {
		return nil
	}
	return s.onFollowUpdate(ctx, &evt.UpdateFollow{From: evd.To, To: evd.From})
}

func esOptions(ctx context.Context) []es.UpdateOpt {
	var opts []es.UpdateOpt
	if waitRefresh(ctx) {
		opts = append(opts, es.WaitRefresh())
	}
	return opts
}

func (s *Indexer) onFollowUpdate(ctx context.Context, evd *evt.UpdateFollow) error {
	return s.updateFollow(ctx, evd.From, evd.To, func(ctx context.Context, idxName string, docId string, data any) error {
		return s.m.ec.Update(ctx, idxName, docId, data, es.WithUpsert())
	}, func(link *Following) map[string]any {
		return map[string]any{
			"remark": link.Remark,
			"duplex": link.Duplex,
			"chummy": link.Chummy,
			"focus":  link.Focus,
		}
	})
}

func followId(userId, target string) string {
	return userId + target
}

func (s *Indexer) onUserUpdate(ctx context.Context, evd *user.EvUpdating) error {
	old, acc := evd.Old, evd.New
	up1 := make(map[string]any) // by userId
	up2 := make(map[string]any) // by target
	if old.ShowId != acc.ShowId {
		up1["p1Id"] = acc.ShowId
		up2["p2Id"] = acc.ShowId
	}
	if old.Nickname != acc.Nickname {
		up1["p1Name"] = acc.Nickname
		up2["p2Name"] = acc.Nickname
	}
	return errors.Join(
		s.m.ec.Updates(ctx, idxName, []query.Option{query.MustTerm("userId", acc.UserId)}, up1),
		s.m.ec.Updates(ctx, idxName, []query.Option{query.MustTerm("target", acc.UserId)}, up2),
	)
}
