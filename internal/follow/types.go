package follow

import (
	"time"
)

type Stats struct {
	Friends   int // 朋友人数
	Following int // 关注人数
	Followers int // 粉丝人数
}

type Follow struct {
	UserId string    `json:"userId"` // 用户Id
	Target string    `json:"target"` // 目标用户
	Remark string    `json:"remark"` // 备注信息
	Duplex bool      `json:"duplex"` // 双向关注
	Chummy bool      `json:"chummy"` // 是否密友
	Focus  bool      `json:"focus"`  // 特别关注
	Time   time.Time `json:"time"`   // 关注时间
}

type Follows []*Follow
