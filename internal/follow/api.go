package follow

import (
	"context"
	"errors"
	"fmt"
)

const (
	// TODO use internal lock key
	keyMutex = "HTTP:MUX:%s" // userId
)

func (s *Manager) TryFollow(ctx context.Context, userId, target string, withLock bool) (bool, error) {
	if userId == target {
		return false, nil
	}

	if followed, err := s.Following(ctx, userId, target); err != nil {
		return false, err
	} else if followed {
		return false, nil
	}

	if withLock {
		if l, err := s.dm.Lock(ctx, fmt.Sprintf(keyMutex, userId)); err != nil {
			return false, err
		} else {
			defer l.MustUnlock()
		}
	}

	if err := s.Follow(ctx, userId, target); err != nil {
		if !errors.Is(err, ErrIsFollowing) {
			return false, err
		}
		return false, nil
	}

	return true, nil
}
