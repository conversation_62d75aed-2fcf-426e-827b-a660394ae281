package follow

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/es"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var (
	ErrInvalidTarget  = errors.New("invalid target")
	ErrLimitedFollows = errors.New("reach max follows")
)

func newManager(db *db.Client, dm *redi.Mutex, ug user.Getter, ev ev.Bus, ec *es.Client, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Following{})
	return &Manager{
		db:  db,
		dm:  dm,
		ug:  ug,
		ev:  ev,
		ec:  ec,
		log: log,
	}
}

type Manager struct {
	db  *db.Client
	dm  *redi.Mutex
	ug  user.Getter
	ev  ev.Bus
	ec  *es.Client
	log *zap.Logger
	getCache
}

func (s *Manager) Follow(ctx context.Context, userId, target string) error {
	if userId == target {
		return ErrInvalidTarget
	}

	if _, err := s.ug.Account(ctx, target); err != nil {
		return err
	}

	if followed, err := s.followed(ctx, userId, target); err != nil {
		return err
	} else if followed {
		return ErrIsFollowing
	}

	if follows, err := s.follows(ctx, userId); err != nil {
		return err
	} else if follows >= maxFollows {
		return ErrLimitedFollows
	}

	var link Following
	if followed, err := s.followed(ctx, target, userId); err != nil {
		return err
	} else if followed {
		link.Duplex = true
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if link.Duplex {
			if err := s.updateColumn(ctx, target, userId, "duplex", true); err != nil {
				return err
			}
		}
		return tx.Create(&Following{UserId: userId, Target: target, Duplex: link.Duplex}).Error
	}); err != nil {
		return err
	}

	s.onFollowing(userId, target, link.Duplex)

	s.log.Debug("user follow", zap.String("p1", userId), zap.String("p2", target))

	s.ev.Emit(ctx, evt.UserFollowed, &evt.FollowedUser{From: userId, To: target, Duplex: link.Duplex})

	return nil
}

func (s *Manager) Unfollow(ctx context.Context, userId, target string) error {
	if userId == target {
		return ErrInvalidTarget
	}

	link, err := s.takeInfo(ctx, userId, target)
	if err != nil {
		return err
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if link.Duplex {
			if err := s.updateColumn(ctx, target, userId, "duplex", false); err != nil {
				return err
			}
		}
		return tx.Where("user_id = ? AND target = ?", userId, target).Delete(&Following{}).Error
	}); err != nil {
		return err
	}

	s.onUnfollowing(userId, target, link.Duplex)

	s.log.Debug("user unfollow", zap.String("p1", userId), zap.String("p2", target))

	s.ev.Emit(ctx, evt.UserUnfollow, &evt.UnfollowUser{From: userId, To: target, Duplex: link.Duplex})

	return nil
}

func (s *Manager) Remove(ctx context.Context, userId, target string) error {
	return s.Unfollow(ctx, target, userId)
}

func (s *Manager) SetRemark(ctx context.Context, userId, target string, remark string) error {
	return s.updateWrapper(ctx, userId, target, false, "remark", remark)
}

func (s *Manager) SetChummy(ctx context.Context, userId, target string, chummy bool) error {
	return s.updateWrapper(ctx, userId, target, true, "chummy", chummy)
}

func (s *Manager) SetFocus(ctx context.Context, userId, target string, focus bool) error {
	return s.updateWrapper(ctx, userId, target, false, "focus", focus)
}

func (s *Manager) updateWrapper(ctx context.Context, userId, target string, duplex bool, field string, val any) error {
	link, err := s.takeInfo(ctx, userId, target)
	if err != nil {
		return err
	}

	if duplex && !link.Duplex {
		return ErrNotFriends
	}

	if err := s.updateColumn(ctx, userId, target, field, val); err != nil {
		return err
	}

	s.ev.Emit(ctx, evt.FollowUpdate, &evt.UpdateFollow{From: userId, To: target})

	return nil
}

func (s *Manager) updateColumn(ctx context.Context, userId, target string, field string, val any) error {
	return db.UseTx(ctx, s.db).Model(&Following{}).Where("user_id = ? AND target = ?", userId, target).Update(field, val).Error
}
