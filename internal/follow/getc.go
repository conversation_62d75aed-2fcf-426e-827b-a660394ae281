package follow

import (
	"context"
	"errors"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/go/cc"
)

func (s *Manager) initCache(syn cc.Sync) {
	s.fc = cc.New[followingKey, *followCache](
		100000, cc.LRU,
		cc.LoaderFunc(func(key followingKey) (*followCache, error) {
			link, err := s.takeInfo(context.TODO(), key.UserId, key.Target)
			if err != nil {
				if errors.Is(err, ErrNoFollowing) {
					return &followCache{}, nil
				}
				return nil, err
			}
			return newCacheItem(link), nil
		}),
		cc.Expiration(time.Hour),
		cc.ExportStats("follow.links"),
		cc.WithSync(syn, "follow.links"),
	)
	s.sc = cc.New[string, *Stats](
		100000, cc.LRU,
		cc.LoaderFunc(func(userId string) (*Stats, error) {
			return s.takeStats(context.TODO(), userId)
		}),
		cc.Expiration(time.Hour),
		cc.ExportStats("follow.stats"),
		cc.WithSync(syn, "follow.stats"),
	)
}

func newCacheItem(from *Following) *followCache {
	return &followCache{
		ID:     from.ID,
		Duplex: from.Duplex,
		Chummy: from.Chummy,
		Focus:  from.Focus,
		Time:   from.CreatedAt,
	}
}

type followCache struct {
	ID     uint
	Duplex bool
	Chummy bool
	Focus  bool
	Time   time.Time
}

func (c *followCache) valid() bool {
	return c.ID > 0
}

func (c *followCache) export() *Follow {
	return &Follow{
		Duplex: c.Duplex,
		Chummy: c.Chummy,
		Focus:  c.Focus,
		Time:   c.Time,
	}
}

type followingKey struct{ UserId, Target string }

func (k followingKey) MarshalBinary() ([]byte, error) {
	return sonic.Marshal(k)
}

func (k *followingKey) UnmarshalBinary(data []byte) error {
	return sonic.Unmarshal(data, k)
}

type getCache struct {
	fc cc.Cache[followingKey, *followCache]
	sc cc.Cache[string, *Stats]
}

func (s *getCache) invalidCache(userId, target string, duplex bool) {
	s.fc.Remove(followingKey{UserId: userId, Target: target})
	if duplex {
		s.fc.Remove(followingKey{UserId: target, Target: userId})
	}
	s.sc.Remove(userId)
	s.sc.Remove(target)
}

func (s *getCache) onFollowing(userId, target string, duplex bool) {
	s.invalidCache(userId, target, duplex)
}

func (s *getCache) onUnfollowing(userId, target string, duplex bool) {
	s.invalidCache(userId, target, duplex)
}
