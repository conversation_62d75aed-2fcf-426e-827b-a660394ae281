package follow

import (
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/es"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.Client, dm *redi.Mutex, ug user.Getter, ev ev.Bus, ec *es.Client, syn cc.Sync, vnd log.Vendor) (*Manager, Getter) {
	ec.ApplyIndex(idxName, idxFields)
	mgr := newManager(db, dm, ug, ev, ec, vnd.Scope("follow.mgr"))
	mgr.initCache(syn)
	return mgr, mgr
}

func Invoke(evb ev.Bus, mgr *Manager) {
	idx := newIndexer(mgr)
	evb.Watch(evt.UserFollowed, "follow.indexing", ev.NewWatcher(idx.onUserFollowed), ev.WithAsync(), ev.WithAwait(time.Second))
	evb.Watch(evt.UserUnfollow, "follow.indexing", ev.NewWatcher(idx.onUserUnfollow), ev.WithAsync(), ev.WithAwait(time.Second))
	evb.Watch(evt.FollowUpdate, "follow.indexing", ev.NewWatcher(idx.onFollowUpdate), ev.WithAsync(), ev.WithAwait(time.Second))
	evb.Watch(user.EvUserUpdated, "follow.indexing", ev.NewWatcher(idx.onUserUpdate), ev.WithAsync())
	evb.Watch(evt.EvUserGrantUpdate, "follow.process", ev.NewWatcher(mgr.onAddBlackList))
}
