package follow

import (
	"context"
	"errors"

	"github.com/sourcegraph/conc/pool"
	"gitlab.sskjz.com/go/es/query"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

var (
	ErrIsFollowing = errors.New("is following")
	ErrNoFollowing = errors.New("no following")
	ErrNotFriends  = errors.New("not friends")
)

type Getter interface {
	Following(ctx context.Context, userId, target string) (bool, error)
	IsFriend(ctx context.Context, userId, target string) (bool, error)
	Take(ctx context.Context, userId, target string) (*Follow, error)
	Stats(ctx context.Context, userId string) (*Stats, error)
	Listing
}

func (s *Manager) Following(ctx context.Context, userId, target string) (bool, error) {
	if userId == target {
		return false, nil
	}
	link, err := s.fc.Get(followingKey{UserId: userId, Target: target})
	if err != nil {
		return false, err
	}
	return link.valid(), nil
}

func (s *Manager) IsFriend(ctx context.Context, userId, target string) (bool, error) {
	if userId == target {
		return false, nil
	}
	link, err := s.fc.Get(followingKey{UserId: userId, Target: target})
	if err != nil {
		return false, err
	}
	return link.Duplex, nil
}

func (s *Manager) Take(ctx context.Context, userId, target string) (*Follow, error) {
	if userId == target {
		return nil, ErrNoFollowing
	}
	link, err := s.fc.Get(followingKey{UserId: userId, Target: target})
	if err != nil {
		return nil, err
	} else if !link.valid() {
		return nil, ErrNoFollowing
	}
	return link.export(), nil
}

func (s *Manager) Stats(ctx context.Context, userId string) (*Stats, error) {
	return s.sc.Get(userId)
}

func (s *Manager) followed(ctx context.Context, userId, target string) (bool, error) {
	var cnt int64
	if err := db.UseTx(ctx, s.db).Model(&Following{}).Where("user_id = ? AND target = ?", userId, target).Count(&cnt).Error; err != nil {
		return false, err
	}
	return cnt > 0, nil
}

func (s *Manager) follows(ctx context.Context, userId string) (int, error) {
	var cnt int64
	if err := db.UseTx(ctx, s.db).Model(&Following{}).Where("user_id = ?", userId).Count(&cnt).Error; err != nil {
		return 0, err
	}
	return int(cnt), nil
}

func (s *Manager) takeInfo(ctx context.Context, userId, target string) (*Following, error) {
	var link Following
	if err := db.UseTx(ctx, s.db).Where("user_id = ? AND target = ?", userId, target).Take(&link).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNoFollowing
		}
		return nil, err
	}
	return &link, nil
}

func (s *Manager) takeStats(ctx context.Context, userId string) (*Stats, error) {
	var stats Stats

	p := pool.New().WithErrors().WithContext(ctx)

	p.Go(func(ctx context.Context) error {
		count, err := s.ec.Count(ctx, idxName, query.MustTerm("userId", userId), query.MustTerm("duplex", true))
		if err != nil {
			return err
		}
		stats.Friends = count
		return nil
	})

	p.Go(func(ctx context.Context) error {
		count, err := s.ec.Count(ctx, idxName, query.MustTerm("userId", userId))
		if err != nil {
			return err
		}
		stats.Following = count
		return nil
	})

	p.Go(func(ctx context.Context) error {
		count, err := s.ec.Count(ctx, idxName, query.MustTerm("target", userId))
		if err != nil {
			return err
		}
		stats.Followers = count
		return nil
	})

	return &stats, p.Wait()
}
