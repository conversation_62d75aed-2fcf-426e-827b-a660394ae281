package feed

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/contrib"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

type Manager struct {
	log  *zap.Logger
	dbmc *db.MongoClient
	fg   follow.Getter
	lm   *live.Manager
	sm   *rsd.Stats
	cm   *contrib.Manager
	fc   fclub.Getter
	rc   *redi.Client
	ev   ev.Bus
	lkm  *link.Manager
}

func newManager(
	dbmc *db.MongoClient,
	fg follow.Getter,
	lm *live.Manager,
	sm *rsd.Stats,
	cm *contrib.Manager,
	fc fclub.Getter,
	rc *redi.Client,
	ev ev.Bus,
	lkm *link.Manager,
	log *zap.Logger,
) *Manager {
	m := &Manager{
		log:  log,
		dbmc: dbmc,
		fg:   fg,
		lm:   lm,
		sm:   sm,
		cm:   cm,
		fc:   fc,
		rc:   rc,
		ev:   ev,
		lkm:  lkm,
	}

	return m
}
