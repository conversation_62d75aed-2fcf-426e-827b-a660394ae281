package feed

import (
	"context"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/contrib"
)

// 与关注主播的亲密度
func (m *Manager) getIntimacy(ctx context.Context, userId, toUserId string) int64 {
	var score float64

	c, err := m.cm.GetContrib(ctx, userId, toUserId, contrib.ContribIn30)

	if err == nil {
		for _, v := range []int{contrib.ContribIn7, contrib.ContribIn30} {
			data, ok := c.Gift[v]
			if !ok {
				continue
			}

			var x float64

			switch v {
			case contrib.ContribIn7:
				x = 1
			case contrib.ContribIn30:
				x = 0.5
			}

			score += float64(data.LuckDiamond+data.NormalDiamond*10) * x
		}
	}

	// 守护
	if false {
		score += 1000
	}

	// 粉丝团
	if lv, _ := m.fc.ALevel(ctx, toUserId, userId); lv > 0 {
		score += 500
	}

	// 关注天数
	fi, err := m.fg.Take(ctx, userId, toUserId)
	if err == nil {
		followDays := now.New(time.Now()).BeginningOfDay().Sub(now.New(fi.Time).BeginningOfDay()).Hours()/24 + 1
		score += (30 - min(followDays, 30)) * 500
	}

	return int64(score)
}
