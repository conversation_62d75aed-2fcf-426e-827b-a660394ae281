package binance

import (
	"context"
	"crypto/rsa"

	"github.com/go-resty/resty/v2"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/redi"
	proxy "gitlab.sskjz.com/overseas/live/osl/internal/binance/proxy/pkg"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

func newManager(cfg conf.Binance, dm *redi.Mutex, ps *pay.Service, log *zap.Logger) (*Manager, error) {
	mgr := &Manager{
		sig: newSigner(cfg.ApiKey, cfg.SecretKey),
		cfg: cfg,
		dm:  dm,
		ps:  ps,
		log: log,
	}
	{
		cli := resty.New()
		if cfg.Proxy != "" {
			cli.OnBeforeRequest(proxy.Hijack(cfg.Proxy)).OnAfterResponse(proxy.Unpack())
		}
		mgr.cli = cli
	}
	mgr.initNotify()
	return mgr, nil
}

type Manager struct {
	sig *signer
	cfg conf.Binance
	dm  *redi.Mutex
	ps  *pay.Service
	log *zap.Logger
	cli *resty.Client
	// task
	chk1 dq.Queue[*pendingOrder]
	// setting
	notifyUrl string
	notifyKey cc.Cache[string, *rsa.PublicKey]
}

func (s *Manager) Methods(ctx context.Context, userId string, country pay.Country, scene, sku string) ([]payermax.Method, error) {
	payments := countryPayments[country]
	if len(payments) == 0 {
		return nil, nil
	}

	submit, err := s.ps.Forms(ctx, scene, sku, payments[0].Cur)
	if err != nil {
		return nil, err
	}

	out := make([]payermax.Method, 0, len(payments))
	for _, payment := range payments {
		price, err := s.adjPrice(scene, submit.Price, payment)
		if err != nil {
			return nil, err
		}
		out = append(out, payermax.Method{
			Payment: payment,
			Price:   price,
		})
	}

	return out, nil
}
