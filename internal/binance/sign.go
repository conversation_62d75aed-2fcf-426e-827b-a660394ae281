package binance

import (
	"bytes"
	"crypto"
	"crypto/hmac"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.sskjz.com/go/cc"
)

func newSigner(apiKey, secretKey string) *signer {
	return &signer{apiKey: apiKey, secretKey: secretKey}
}

type signer struct {
	apiKey    string
	secretKey string
}

func (c *signer) Sign(body []byte) (timestamp, nonce, signature string) {
	timestamp = strconv.FormatInt(time.Now().UnixMilli(), 10)
	nonce = strings.ReplaceAll(uuid.NewString(), "-", "")

	var sb bytes.Buffer
	sb.WriteString(timestamp)
	sb.WriteRune('\n')
	sb.WriteString(nonce)
	sb.WriteRune('\n')
	sb.Write(body)
	sb.WriteRune('\n')

	h := hmac.New(sha512.New, []byte(c.secretKey))
	h.Write(sb.Bytes())
	signature = strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	return
}

func (c *signer) Verify(timestamp, nonce, signature string, body []byte, kSerial string, kLoader cc.Cache[string, *rsa.PublicKey]) bool {
	var sb bytes.Buffer
	sb.WriteString(timestamp)
	sb.WriteRune('\n')
	sb.WriteString(nonce)
	sb.WriteRune('\n')
	sb.Write(body)
	sb.WriteRune('\n')

	pubKey, err := kLoader.Get(kSerial)
	if err != nil {
		return false
	}

	hashed := sha256.Sum256(sb.Bytes())
	signBytes, _ := base64.StdEncoding.DecodeString(signature)

	if err := rsa.VerifyPKCS1v15(pubKey, crypto.SHA256, hashed[:], signBytes); err != nil {
		return false
	}

	return true
}
