package binance

import (
	"fmt"

	"github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

const (
	statusSuccess = "SUCCESS"
)

const (
	hdrCertSN    = "BinancePay-Certificate-SN"
	hdrTimestamp = "BinancePay-Timestamp"
	hdrNonce     = "BinancePay-Nonce"
	hdrSign      = "BinancePay-Signature"
)

func newApiError(code, msg string) *apiError {
	return &apiError{Code: code, Msg: msg}
}

type apiError struct {
	Code string
	Msg  string
}

func (e *apiError) Error() string {
	return fmt.Sprintf("api error: %s: %s", e.Code, e.Msg)
}

const (
	apiCreateOrder  = "/binancepay/openapi/v3/order"
	apiQueryOrder   = "/binancepay/openapi/v2/order/query"
	apiCertificates = "/binancepay/openapi/certificates"
)

func endpoint(cfg conf.Binance, uri string) string {
	return cfg.Endpoint + uri
}

func doRequest[resp any](sig *signer, log *zap.Logger, cli *resty.Client, path string, req any) (resp, error) {
	var void resp
	bs, err := sonic.Marshal(req)
	if err != nil {
		return void, err
	}

	timestamp, nonce, sign := sig.Sign(bs)

	hReq := cli.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetHeader(hdrCertSN, sig.apiKey).
		SetHeader(hdrTimestamp, timestamp).
		SetHeader(hdrNonce, nonce).
		SetHeader(hdrSign, sign).
		SetBody(bs)

	ff := make([]zap.Field, 0, 6)
	ff = append(ff, zap.String("path", path), zap.Any("reqHeader", hReq.Header), zap.ByteString("reqData", bs))

	hResp, err := hReq.Post(path)
	if hResp != nil {
		ff = append(ff, zap.Int("respStatus", hResp.StatusCode()), zap.Any("respHeader", hResp.Header()), zap.String("respBody", hResp.String()))
	}

	if err != nil {
		log.With(zap.Error(err)).Warn("request api", ff...)
		return void, err
	}

	log.Debug("request api", ff...)

	if hResp.StatusCode() != 200 {
		return void, fmt.Errorf("request failed: %s", hResp.Status())
	}

	var ret apiResponse[resp]
	if err := sonic.Unmarshal(hResp.Body(), &ret); err != nil {
		return void, fmt.Errorf("unmarshal response failed: %w", err)
	}

	if ret.Status != statusSuccess {
		return void, newApiError(ret.Code, ret.Msg)
	}

	return ret.Body, nil
}

type apiResponse[data any] struct {
	Status string `json:"status"`
	Code   string `json:"code"`
	Msg    string `json:"errorMessage"`
	Body   data   `json:"data"`
}

// CreateOrderRequest to /binancepay/openapi/v3/order
type CreateOrderRequest struct {
	Env                 Env                  `json:"env"`
	MerchantTradeNo     string               `json:"merchantTradeNo"`
	OrderAmount         string               `json:"orderAmount,omitempty"`
	Currency            pay.Currency         `json:"currency,omitempty"`
	FiatAmount          string               `json:"fiatAmount,omitempty"`
	FiatCurrency        pay.Currency         `json:"fiatCurrency,omitempty"`
	GoodsDetails        []Goods              `json:"goodsDetails"`
	Shipping            *Shipping            `json:"shipping,omitempty"`
	Buyer               *Buyer               `json:"buyer,omitempty"`
	ReturnUrl           string               `json:"returnUrl,omitempty"`
	CancelUrl           string               `json:"cancelUrl,omitempty"`
	OrderExpireTime     int64                `json:"orderExpireTime,omitempty"`
	SupportPayCurrency  string               `json:"supportPayCurrency,omitempty"`
	AppId               string               `json:"appId,omitempty"`
	UniversalUrlAttach  string               `json:"universalUrlAttach,omitempty"`
	PassThroughInfo     string               `json:"passThroughInfo,omitempty"`
	WebhookUrl          string               `json:"webhookUrl,omitempty"`
	DirectDebitContract *DirectDebitContract `json:"directDebitContract,omitempty"`
	OrderTags           *OrderTags           `json:"orderTags,omitempty"`
	Description         string               `json:"description"`
	VoucherCode         string               `json:"voucherCode,omitempty"`
}

type Merchant struct {
	SubMerchantId string `json:"subMerchantId,omitempty"`
}

type Env struct {
	TerminalType  string `json:"terminalType"`
	OsType        string `json:"osType,omitempty"`
	OrderClientIp string `json:"orderClientIp,omitempty"`
	CookieId      string `json:"cookieId,omitempty"`
}

type Goods struct {
	GoodsType        string  `json:"goodsType"`
	GoodsCategory    string  `json:"goodsCategory"`
	ReferenceGoodsId string  `json:"referenceGoodsId"`
	GoodsName        string  `json:"goodsName"`
	GoodsDetail      string  `json:"goodsDetail,omitempty"`
	GoodsUnitAmount  *Amount `json:"goodsUnitAmount,omitempty"`
}

type Amount struct {
	Currency string `json:"currency"`
	Amount   string `json:"amount"`
}

type Shipping struct {
	ShippingName    *Name    `json:"shippingName,omitempty"`
	ShippingAddress *Address `json:"shippingAddress,omitempty"`
	ShippingPhoneNo string   `json:"shippingPhoneNo,omitempty"`
}

type Name struct {
	FirstName  string `json:"firstName"`
	MiddleName string `json:"middleName,omitempty"`
	LastName   string `json:"lastName"`
}

type Address struct {
	Region              string `json:"region"`
	State               string `json:"state,omitempty"`
	City                string `json:"city,omitempty"`
	Address             string `json:"address,omitempty"`
	ZipCode             string `json:"zipCode,omitempty"`
	ShippingAddressType string `json:"shippingAddressType,omitempty"`
}

type Buyer struct {
	ReferenceBuyerId      string `json:"referenceBuyerId,omitempty"`
	BuyerName             *Name  `json:"buyerName,omitempty"`
	BuyerPhoneCountryCode string `json:"buyerPhoneCountryCode,omitempty"`
	BuyerPhoneNo          string `json:"buyerPhoneNo,omitempty"`
	BuyerEmail            string `json:"buyerEmail,omitempty"`
	BuyerRegistrationTime int64  `json:"buyerRegistrationTime,omitempty"`
	BuyerBrowserLanguage  string `json:"buyerBrowserLanguage,omitempty"`
}

type DirectDebitContract struct {
	MerchantContractCode string `json:"merchantContractCode"`
	ServiceName          string `json:"serviceName"`
	ScenarioCode         string `json:"scenarioCode"`
	SingleUpperLimit     string `json:"singleUpperLimit"`
	Periodic             bool   `json:"periodic"`
	CycleDebitFixed      bool   `json:"cycleDebitFixed,omitempty"`
	CycleType            string `json:"cycleType,omitempty"`
	CycleValue           int    `json:"cycleValue,omitempty"`
	FirstDeductTime      int64  `json:"firstDeductTime,omitempty"`
	MerchantAccountNo    string `json:"merchantAccountNo,omitempty"`
	ContractEndTime      int64  `json:"contractEndTime,omitempty"`
}

type OrderTags struct {
	IfProfitSharing bool `json:"ifProfitSharing,omitempty"`
}

// CreateOrderResult from /binancepay/openapi/v3/order
type CreateOrderResult struct {
	PrepayId     string `json:"prepayId"`
	TerminalType string `json:"terminalType"`
	ExpireTime   int64  `json:"expireTime"`
	QrcodeLink   string `json:"qrcodeLink"`
	QrContent    string `json:"qrContent"`
	CheckoutUrl  string `json:"checkoutUrl"`
	Deeplink     string `json:"deeplink"`
	UniversalUrl string `json:"universalUrl"`
	Currency     string `json:"currency"`
	TotalFee     string `json:"totalFee"`
	FiatCurrency string `json:"fiatCurrency,omitempty"`
	FiatAmount   string `json:"fiatAmount,omitempty"`
}

// OrderQueryRequest to /binancepay/openapi/v2/order/query
type OrderQueryRequest struct {
	PrepayId        string `json:"prepayId,omitempty"`
	MerchantTradeNo string `json:"merchantTradeNo,omitempty"`
}

// QueryOrderResult from /binancepay/openapi/v2/order/query
type QueryOrderResult struct {
	MerchantId      int64                `json:"merchantId"`                // The merchant account id
	PrepayId        string               `json:"prepayId"`                  // Unique id generated by binance
	TransactionId   string               `json:"transactionId,omitempty"`   // Issued once the payment is successful
	MerchantTradeNo string               `json:"merchantTradeNo"`           // The order id
	Status          OrderStatus          `json:"status"`                    // Order status
	Currency        pay.Currency         `json:"currency"`                  // Order currency
	OrderAmount     string               `json:"orderAmount"`               // Order amount (as string)
	OpenUserId      string               `json:"openUserId,omitempty"`      // Consumer unique id
	PassThroughInfo string               `json:"passThroughInfo,omitempty"` // Pass through info
	TransactTime    int64                `json:"transactTime,omitempty"`    // Timestamp when transaction happened
	CreateTime      int64                `json:"createTime"`                // Timestamp when order was created
	PaymentInfo     *PaymentInfo[string] `json:"paymentInfo,omitempty"`     // Payment information
}

// OrderStatus represents the status of an order
type OrderStatus string

// Define order status constants
const (
	OrderStatusInitial      OrderStatus = "INITIAL"       // 订单初始化状态
	OrderStatusPending      OrderStatus = "PENDING"       // 订单待支付状态
	OrderStatusPaid         OrderStatus = "PAID"          // 订单已支付状态
	OrderStatusCanceled     OrderStatus = "CANCELED"      // 订单已取消状态
	OrderStatusError        OrderStatus = "ERROR"         // 订单错误状态
	OrderStatusRefunding    OrderStatus = "REFUNDING"     // 订单退款中状态
	OrderStatusRefunded     OrderStatus = "REFUNDED"      // 订单部分退款状态
	OrderStatusFullRefunded OrderStatus = "FULL_REFUNDED" // 订单全额退款状态
	OrderStatusExpired      OrderStatus = "EXPIRED"       // 订单已过期状态
)

type amt interface {
	~string | ~float64
}

// PaymentInfo Data
type PaymentInfo[T amt] struct {
	PayerId             string                  `json:"payerId"`               // Payer pay id
	PayMethod           string                  `json:"payMethod"`             // Pay method
	PaymentInstructions []PaymentInstruction[T] `json:"paymentInstructions"`   // Payment instruction list
	Channel             string                  `json:"channel,omitempty"`     // Channel
	SubChannel          string                  `json:"subChannel,omitempty"`  // Sub channel
	PayerDetail         string                  `json:"payerDetail,omitempty"` // encrypted payer identity information
}

// PaymentInstruction Data
type PaymentInstruction[T amt] struct {
	Currency string `json:"currency"` // Payment currency
	Amount   T      `json:"amount"`   // Currency amount (as T)
	Price    T      `json:"price"`    // Currency price (as T)
}

// OrderNotification from webhook notify
type OrderNotification struct {
	MerchantTradeNo string                `json:"merchantTradeNo"`           // The order id, Unique identifier for the request
	ProductType     string                `json:"productType"`               // product type
	ProductName     string                `json:"productName"`               // product name
	TransactTime    int64                 `json:"transactTime"`              // transaction time
	TradeType       string                `json:"tradeType"`                 // "WEB", "APP", "WAP", "MINI_PROGRAM", "PAYMENT_LINK", "OTHERS"
	TotalFee        float64               `json:"totalFee"`                  // order amount
	Currency        string                `json:"currency"`                  // order currency
	TransactionId   string                `json:"transactionId,omitempty"`   // debit transaction id, omitempty because it's optional
	OpenUserId      string                `json:"openUserId,omitempty"`      // payer unique id, omitempty because it's optional
	PassThroughInfo string                `json:"passThroughInfo,omitempty"` // pass through info, from the create order api, omitempty because it's optional
	Commission      float64               `json:"commission"`                // Transaction fees charged
	PaymentInfo     *PaymentInfo[float64] `json:"paymentInfo,omitempty"`     // payment information, omitempty because it's optional
}

type CertificatesResult struct {
	Serial string `json:"certSerial"`
	Public string `json:"certPublic"`
}
