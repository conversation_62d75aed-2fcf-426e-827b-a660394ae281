package binance

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"io"

	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/cc"
)

type notifyReq struct {
	BizType   string `json:"bizType"`   // "PAY"
	BizId     int64  `json:"bizId"`     // Prepay order id
	BizIdStr  string `json:"bizIdStr"`  // biz id as string
	BizStatus string `json:"bizStatus"` // "PAY_SUCCESS","PAY_CLOSED"
	Data      string `json:"data"`      // JSON string, data details refer to NotificationData
}

func (s *Manager) SetNotify(url string) {
	s.notifyUrl = url
}

func (s *Manager) initNotify() {
	s.notifyKey = cc.New[string, *rsa.PublicKey](3, cc.LRU, cc.LoaderFunc(func(sn string) (*rsa.<PERSON>, error) {
		resp, err := doRequest[[]CertificatesResult](s.sig, s.log, s.cli, endpoint(s.cfg, apiCertificates), nil)
		if err != nil {
			return nil, err
		}
		for _, cert := range resp {
			if cert.Serial == sn {
				block, _ := pem.Decode([]byte(cert.Public))
				if block == nil {
					return nil, errors.New("invalid public key")
				}
				pubKey, err := x509.ParsePKIXPublicKey(block.Bytes)
				if err != nil {
					return nil, err
				}
				return pubKey.(*rsa.PublicKey), nil
			}
		}
		return nil, errors.New("certificate not found")
	}))
}

func (s *Manager) RecvNotify(ctx *gin.Context) error {
	bs, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		return err
	}

	if !s.sig.Verify(ctx.GetHeader(hdrTimestamp), ctx.GetHeader(hdrNonce), ctx.GetHeader(hdrSign), bs, ctx.GetHeader(hdrCertSN), s.notifyKey) {
		return errors.New("invalid signature")
	}

	var req notifyReq
	_ = sonic.Unmarshal(bs, &req)

	switch req.BizType {
	case "PAY":
		var status OrderStatus
		switch req.BizStatus {
		case "PAY_SUCCESS":
			status = OrderStatusPaid
		case "PAY_CLOSED":
			status = OrderStatusExpired
		default:
			return errors.New("invalid status")
		}
		var data OrderNotification
		_ = sonic.UnmarshalString(req.Data, &data)
		if _, err := s.updateOrder(ctx, data.MerchantTradeNo, req.BizIdStr, status); err != nil {
			return err
		}
	}

	ctx.JSON(200, map[string]any{"returnCode": "SUCCESS", "returnMessage": nil})
	return nil
}
