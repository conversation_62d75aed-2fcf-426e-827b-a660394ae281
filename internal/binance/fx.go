package binance

import (
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
)

func Provide(desc *conf.Setting, si sto.Instance, dm *redi.Mutex, ps *pay.Service, gws *pay.Gateways, qm *dq.Master, vnd log.Vendor) (*Manager, error) {
	payermax.InitLogo(si.Conf("default"), countryPayments)
	mgr, err := newManager(desc.Binance, dm, ps, vnd.Scope("binance.mgr"))
	if err != nil {
		return nil, err
	}
	mgr.initCheck(qm)
	gws.Register("binance", mgr)
	return mgr, nil
}

func Invoke(mgr *Manager, h *api.Host) {
	if h.Has() {
		mgr.SetNotify(h.URL("/binance/notify"))
	}
	if env.Scheduler() {
		mgr.startCheck()
	}
}
