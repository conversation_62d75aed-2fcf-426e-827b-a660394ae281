package binance

import (
	"context"

	"gitlab.sskjz.com/go/dq"
)

type pendingOrder struct {
	TradeNo string `json:"tradeNo"`
}

func (s *Manager) initCheck(dm *dq.Master) {
	s.chk1 = dq.NewWith[*pendingOrder](dm, "binance.orders")
}

func (s *Manager) startCheck() {
	s.chk1.Register(func(ctx context.Context, msg *pendingOrder) error {
		_, err := s.Query(ctx, msg.TradeNo)
		return err
	}, dq.Channel("status-checker"))
}
