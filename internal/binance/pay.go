package binance

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"go.uber.org/zap"
)

func (s *Manager) Create(ctx context.Context, order *pay.Order) (*pay.Order, pay.Extras, error) {
	goods, err := s.ps.TakeGoods(ctx, order)
	if err != nil {
		return nil, nil, err
	}

	payment, has := paymentOf(order.Country)
	if !has {
		return nil, nil, errors.New("payment not found")
	}

	order.Price, err = s.adjPrice(order.Scene, order.Price, payment)
	if err != nil {
		return nil, nil, err
	}

	resp, err := doRequest[CreateOrderResult](s.sig, s.log, s.cli, endpoint(s.cfg, apiCreateOrder), CreateOrderRequest{
		Env: Env{
			TerminalType: "APP",
		},
		MerchantTradeNo: order.TradeNo,
		FiatCurrency:    order.Currency,
		FiatAmount:      fmt.Sprintf("%.2f", order.Price),
		GoodsDetails: []Goods{{
			GoodsType:        "02",
			GoodsCategory:    "6000",
			ReferenceGoodsId: fmt.Sprintf("%s_%s", order.Scene, order.SKU),
			GoodsName:        goods.Name,
		}},
		ReturnUrl:       s.cfg.CallbackUrl,
		CancelUrl:       s.cfg.CallbackUrl,
		OrderExpireTime: order.CreatedAt.Add(time.Hour).UnixMilli(),
		WebhookUrl:      s.notifyUrl,
		Description:     goods.Name,
	})
	if err != nil {
		return nil, nil, err
	}

	if err := s.chk1.Submit(ctx, 60*time.Minute, &pendingOrder{TradeNo: order.TradeNo}); err != nil {
		s.log.Warn("submit order check task failed", zap.String("tradeNo", order.TradeNo), zap.Error(err))
	}

	order.OrderId = resp.PrepayId
	order.Status = pay.OStatusBusy
	return order, pay.Extras{
		"redirectUrl": resp.CheckoutUrl,
	}, nil
}

func (s *Manager) Query(ctx context.Context, tradeNo string) (*pay.Order, error) {
	order, err := s.ps.Take(ctx, tradeNo)
	if err != nil {
		return nil, err
	} else if order.Status.Final() {
		return order, nil
	}

	resp, err := doRequest[QueryOrderResult](s.sig, s.log, s.cli, endpoint(s.cfg, apiQueryOrder), OrderQueryRequest{
		MerchantTradeNo: order.TradeNo,
	})
	if err != nil {
		return nil, err
	}

	if resp.Status == OrderStatusInitial || resp.Status == OrderStatusPending {
		return order, nil
	}

	return s.updateOrder(ctx, tradeNo, resp.TransactionId, resp.Status)
}

const (
	keyOrderMutex = "BINANCE:MUTEX:%s" // tradeNo
)

func (s *Manager) updateOrder(ctx context.Context, tradeNo string, orderId string, status OrderStatus) (*pay.Order, error) {
	if l, err := s.dm.Lock(ctx, fmt.Sprintf(keyOrderMutex, tradeNo)); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}

	var (
		order *pay.Order
		err   error
	)

	switch status {
	case OrderStatusPaid:
		order, err = s.ps.MakePaid(ctx, tradeNo, pay.WithOrderId(orderId))
	case OrderStatusError:
		order, err = s.ps.MakeFailed(ctx, tradeNo, pay.WithOrderId(orderId))
	case OrderStatusCanceled, OrderStatusExpired:
		order, err = s.ps.MakeClosed(ctx, tradeNo)
	}

	if err != nil {
		s.log.Warn("make status update failed", zap.String("tradeNo", tradeNo), zap.String("status", string(status)), zap.Error(err))
	} else {
		s.log.Debug("make status update success", zap.String("tradeNo", tradeNo), zap.String("status", string(status)))
	}

	return order, err
}
