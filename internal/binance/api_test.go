package binance

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/go-resty/resty/v2"
	proxy "gitlab.sskjz.com/overseas/live/osl/internal/binance/proxy/pkg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

func newClient(t *testing.T) (conf.Binance, *signer, *zap.Logger, *resty.Client) {
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	cfg := desc.Binance
	sign := newSigner(cfg.ApiKey, cfg.SecretKey)
	log, _ := zap.NewDevelopment()
	cli := resty.New()
	if cfg.Proxy != "" {
		cli.OnBeforeRequest(proxy.Hijack(cfg.Proxy)).OnAfterResponse(proxy.Unpack())
	}
	return cfg, sign, log, cli
}

func TestCreateOrder(t *testing.T) {
	cfg, sign, log, cli := newClient(t)
	resp, err := doRequest[CreateOrderResult](sign, log, cli, endpoint(cfg, apiCreateOrder), CreateOrderRequest{
		Env: Env{
			TerminalType: "APP",
		},
		MerchantTradeNo: fmt.Sprintf("TEST%d", time.Now().Unix()),
		FiatCurrency:    "USD",
		FiatAmount:      "0.01",
		GoodsDetails: []Goods{
			{
				GoodsType:        "02",
				GoodsCategory:    "6000",
				ReferenceGoodsId: "recharge_1",
				GoodsName:        "1000 coins",
			},
		},
		OrderExpireTime: time.Now().Add(time.Hour).UnixMilli(),
		WebhookUrl:      "https://godzilla-api-test.sskjz.com/api/v1/binance/notify",
		Description:     "1000 coins",
	})
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestQueryOrder(t *testing.T) {
	cfg, sign, log, cli := newClient(t)
	resp, err := doRequest[QueryOrderResult](sign, log, cli, endpoint(cfg, apiQueryOrder), OrderQueryRequest{
		MerchantTradeNo: "20250317091521448026",
	})
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestQueryOrder2(t *testing.T) {
	cfg, sign, log, cli := newClient(t)
	var wg sync.WaitGroup
	wg.Add(2)
	queryOrder := func(tradeNo string) {
		defer wg.Done()
		resp, err := doRequest[QueryOrderResult](sign, log, cli, endpoint(cfg, apiQueryOrder), OrderQueryRequest{
			MerchantTradeNo: tradeNo,
		})
		if err != nil {
			t.Logf("Error: %v", err)
		}
		spew.Dump(resp)
	}
	go queryOrder("20250314165057294037")
	go queryOrder("20250317091521448026")
	wg.Wait()
}
