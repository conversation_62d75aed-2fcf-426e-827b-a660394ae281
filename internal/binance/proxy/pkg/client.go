package proxy

import (
	"errors"
	"net/http"

	"github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/est"
)

type Request struct {
	Url     string      `json:"url"`
	Headers http.Header `json:"headers"`
	Method  string      `json:"method"`
	Body    []byte      `json:"body"`
}

type Response struct {
	Status     string      `json:"status"`
	StatusCode int         `json:"statusCode"`
	Headers    http.Header `json:"headers"`
	Body       []byte      `json:"body"`
}

func Hijack(url string) func(*resty.Client, *resty.Request) error {
	return func(c *resty.Client, req *resty.Request) error {
		cp := Request{
			Url:     req.URL,
			Headers: req.Header,
			Method:  req.Method,
			Body:    req.Body.([]byte),
		}
		bs, err := sonic.MarshalString(cp)
		if err != nil {
			return err
		}
		req.URL = url
		req.Header = make(http.Header)
		req.Method = http.MethodPost
		req.Body = est.New(bs).String()
		return nil
	}
}

var (
	r = resty.New()
)

func Forward(raw string) (string, error) {
	state := est.Parse(raw)
	if !state.Valid() {
		return "", errors.New("invalid request")
	}
	var req Request
	if err := sonic.UnmarshalString(state.Data, &req); err != nil {
		return "", err
	}
	resp, err := r.R().SetBody(req.Body).SetHeaderMultiValues(req.Headers).Execute(req.Method, req.Url)
	if err != nil {
		return "", err
	}
	out := Response{
		Status:     resp.Status(),
		StatusCode: resp.StatusCode(),
		Headers:    resp.Header(),
		Body:       resp.Body(),
	}
	bs, err := sonic.MarshalString(out)
	if err != nil {
		return "", err
	}
	return est.New(bs).String(), nil
}

func Unpack() func(*resty.Client, *resty.Response) error {
	return func(c *resty.Client, resp *resty.Response) error {
		state := est.Parse(string(resp.Body()))
		if !state.Valid() {
			return errors.New("invalid response")
		}
		var out Response
		if err := sonic.UnmarshalString(state.Data, &out); err != nil {
			return err
		}
		resp.RawResponse.Status = out.Status
		resp.RawResponse.StatusCode = out.StatusCode
		resp.RawResponse.Header = out.Headers
		resp.SetBody(out.Body)
		return nil
	}
}
