package binance

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
)

var binancePay = payermax.Payment{
	Name: "Binance",
	Logo: "{oss}/payermax/Rectangular_432x216/Global/Binance.png",
	Type: "BINANCE",
	Cur:  pay.USD,
	Fee:  payermax.Fee{Rate: 0.01},
}

var countryPayments = map[pay.Country][]payermax.Payment{
	pay.BR: {
		binancePay,
	},
	pay.US: {
		binancePay,
	},
}

func paymentOf(c pay.Country) (payermax.Payment, bool) {
	return payermax.FindPayment(countryPayments, c, binancePay.Type, "")
}
