package exhibition

import (
	"context"
	"time"

	"github.com/samber/lo"
)

func (s *Manager) Items(ctx context.Context, userId string, at time.Time) ([]*Item, error) {
	schema, err := s.LoadSchema(ctx)
	if err != nil {
		return nil, err
	}

	profile, err := s.getProfile2(ctx, userId, at)
	if err != nil {
		return nil, err
	}

	items := make([]*Item, 0, len(schema.Gifts))

	for _, gift := range schema.Gifts {
		active := profile.Gifts[gift.GiftId]
		items = append(items, &Item{
			GiftId:    gift.GiftId,
			Threshold: gift.LightNeed,
			Received:  active.Received,
			Task:      makeTask(gift, active),
		})
	}

	return items, nil
}

func makeTask(schema SchemaGift, active ActiveGift) *Task {
	if active.Received < schema.LightNeed {
		return nil
	}

	base := schema.LightNeed

	var idx, grade int
	for idx, grade = range schema.StarNeeds {
		if active.Received > base {
			base += grade
			continue
		}
	}

	return &Task{
		StageId:   idx + 1,
		Threshold: schema.StarNeeds[idx],
		Received:  active.Received - base,
		Rewards:   schema.StarAwards[idx],
		Ceiling:   lo.Sum(schema.StarAwards),
	}
}
