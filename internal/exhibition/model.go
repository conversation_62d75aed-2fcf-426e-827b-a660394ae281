package exhibition

import (
	"fmt"
	"time"

	"github.com/samber/lo"
)

type SchemaGift struct {
	GiftId     int   `bson:"giftId"`     // 礼物ID
	LightNeed  int   `bson:"lightNeed"`  // 点亮需要数量
	StarNeeds  []int `bson:"starNeeds"`  // 集星阶段需求数量
	StarAwards []int `bson:"starAwards"` // 集星阶段获得数量
}

type Schema struct {
	Id    string       `bson:"_id"`   // 版本号
	Gifts []SchemaGift `bson:"gifts"` // 礼物配置
	// temp data
	giftIds []int
	giftMax map[int]int
}

func (s *Schema) prepare() {
	s.giftIds = lo.Map(s.Gifts, func(g SchemaGift, _ int) int { return g.GiftId })
	s.giftMax = lo.Associate(s.Gifts, func(g SchemaGift) (int, int) {
		return g.GiftId, g.LightNeed + lo.Sum(g.<PERSON>Needs)
	})
}

const schemaDB = "exhibition.schema"

type ActiveGift struct {
	Received int `bson:"received"` // 总收数量
}

type Profile struct {
	Id     string             `bson:"_id"`    // userId + weekId
	Schema string             `bson:"schema"` // 版本号
	Gifts  map[int]ActiveGift `bson:"gifts"`  // 礼物列表
}

func profileId(userId string, t time.Time) string {
	_, week := t.ISOWeek()
	return fmt.Sprintf("%s+%02d", userId, week)
}

func profileDB(t time.Time) string {
	t = beginOfWeek(t)
	return "exhibition.profile." + t.Format("200601")
}
