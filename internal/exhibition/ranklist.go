package exhibition

import (
	"context"
	"fmt"
	"time"
)

const (
	keyRank = "EXHIBITION:RANK:%s:%d" // profileId, giftId, zset(member=userId,score=count)
)

func (s *Manager) upRank(ctx context.Context, profileId, userId string, giftId, count int, at time.Time) error {
	key := fmt.Sprintf(keyRank, profileId, giftId)
	txp := s.rc.Pipeline()
	txp.ZIncrBy(ctx, key, float64(count), userId)
	txp.ExpireAt(ctx, key, endOfWeek(at).Add(time.Hour))
	_, err := txp.Exec(ctx)
	return err
}
