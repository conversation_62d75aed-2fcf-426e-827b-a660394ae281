package exhibition

import (
	"context"
	"fmt"
	"slices"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	keyFinished = "EXHIBITION:FIN:%s" // profileId, set(member=giftId)
)

func (s *Manager) isFinished(ctx context.Context, profileId string, giftId int) (bool, error) {
	return s.rc.SIsMember(ctx, fmt.Sprintf(keyFinished, profileId), giftId).Result()
}

func (s *Manager) markFinished(ctx context.Context, profileId string, giftId int) error {
	key := fmt.Sprintf(keyFinished, profileId)
	txp := s.rc.Pipeline()
	txp.SAdd(ctx, key, giftId)
	txp.ExpireAt(ctx, key, endOfWeek(time.Now()).Add(time.Hour))
	_, err := txp.Exec(ctx)
	return err
}

const (
	keyMutex = "EXHIBITION:MUTEX:%s:%d" // profileId, giftId
)

func (s *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	schema, err := s.LoadSchema(ctx)
	if err != nil {
		return err
	}

	var (
		anchorId = evd.AnchorId
		userId   = evd.UserId
		giftId   = evd.GiftId
		count    = evd.Count
		now      = time.Now()
	)

	if !slices.Contains(schema.giftIds, giftId) {
		return nil
	}

	pid := profileId(anchorId, now)
	if done, err := s.isFinished(ctx, pid, giftId); err != nil {
		return err
	} else if done {
		return nil
	}

	l, err := s.dm.Lock(ctx, fmt.Sprintf(keyMutex, pid, giftId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	profile, err := s.getProfile2(ctx, anchorId, now)
	if err != nil {
		return err
	}

	if profile.Gifts[giftId].Received >= schema.giftMax[giftId] {
		return s.markFinished(ctx, pid, giftId)
	}

	if _, err := s.db.Collection(profileDB(now)).UpdateOne(ctx, bson.M{"_id": pid}, bson.M{
		"$setOnInsert": bson.M{
			"schema": schema.Id,
		},
		"$inc": bson.M{
			fmt.Sprintf("gifts.%d.received", giftId): count,
		},
	}, options.Update().SetUpsert(true)); err != nil {
		return err
	}

	if err := s.upRank(ctx, pid, userId, giftId, count, now); err != nil {
		return err
	}

	if profile.Gifts[giftId].Received+count >= schema.giftMax[giftId] {
		return s.markFinished(ctx, pid, giftId)
	}

	return nil
}
