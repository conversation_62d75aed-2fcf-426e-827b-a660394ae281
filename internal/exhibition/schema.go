package exhibition

var (
	currentSchema = "v1"
	defaultSchema = &Schema{
		Id: "v1",
		Gifts: []SchemaGift{
			{
				GiftId:     1,
				LightNeed:  300,
				StarNeeds:  []int{300, 300, 300},
				StarAwards: []int{1, 1, 1},
			},
			{
				GiftId:     2,
				LightNeed:  300,
				StarNeeds:  []int{300, 600, 900},
				StarAwards: []int{1, 2, 3},
			},
			{
				GiftId:     6,
				LightNeed:  10,
				StarNeeds:  []int{10, 10, 10},
				StarAwards: []int{3, 3, 3},
			},
			{
				GiftId:     11,
				LightNeed:  5,
				StarNeeds:  []int{5, 6, 7},
				StarAwards: []int{3, 3, 3},
			},
			{
				GiftId:     15,
				LightNeed:  1,
				StarNeeds:  []int{1},
				StarAwards: []int{100},
			},
			{
				GiftId:     16,
				LightNeed:  1,
				StarNeeds:  []int{1},
				StarAwards: []int{200},
			},
		},
	}
)
