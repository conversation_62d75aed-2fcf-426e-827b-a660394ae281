package exhibition

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var (
	ErrProfileNotExists = errors.New("profile not exists")
)

func newManager(rc *redi.Client, dm *redi.Mutex, db *db.MongoClient, log *zap.Logger) *Manager {
	return &Manager{
		rc:  rc,
		dm:  dm,
		db:  db,
		log: log,
	}
}

type Manager struct {
	rc  *redi.Client
	dm  *redi.Mutex
	db  *db.MongoClient
	log *zap.Logger
}

func (s *Manager) init(ctx context.Context) error {
	_, err := s.db.Collection(schemaDB).UpdateOne(ctx,
		bson.M{"_id": currentSchema}, bson.M{"$set": defaultSchema},
		options.Update().SetUpsert(true),
	)
	return err
}

func (s *Manager) LoadSchema(ctx context.Context) (*Schema, error) {
	var schema Schema
	if err := s.db.Collection(schemaDB).FindOne(ctx, bson.M{"_id": currentSchema}).Decode(&schema); err != nil {
		return nil, err
	}
	schema.prepare()
	return &schema, nil
}

func (s *Manager) getProfile(ctx context.Context, userId string, at time.Time) (*Profile, error) {
	var profile Profile
	if err := s.db.Collection(profileDB(at)).FindOne(ctx, bson.M{"_id": profileId(userId, at)}).Decode(&profile); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrProfileNotExists
		}
		return nil, err
	}
	return &profile, nil
}

func (s *Manager) getProfile2(ctx context.Context, userId string, at time.Time) (*Profile, error) {
	profile, err := s.getProfile(ctx, userId, at)
	if err != nil {
		if !errors.Is(err, ErrProfileNotExists) {
			return nil, err
		}
	}
	if profile == nil {
		profile = &Profile{}
	}
	return profile, nil
}
