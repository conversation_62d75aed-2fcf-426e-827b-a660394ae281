package exhibition

import (
	"context"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(rc *redi.Client, dm *redi.Mutex, db *db.MongoClient, vnd log.Vendor) *Manager {
	db.SyncSchema(schemaDB, 1)
	db.SyncSchema(profileDB, 2)
	return newManager(rc, dm, db, vnd.Scope("exhibition.mgr"))
}

func Invoke(evb ev.Bus, mgr *Manager) error {
	if err := mgr.init(context.Background()); err != nil {
		return err
	}
	evb.Watch(evt.GiftSend, "exhibition.gift.process", ev.<PERSON>(mgr.onSendGift), ev.<PERSON><PERSON><PERSON>())
	return nil
}
