package asset

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	sc sto.Conf
)

func Provide(dbmc *db.MongoClient, desc *conf.Setting, si sto.Instance, vnd log.Vendor) (*Manager, error) {
	dbmc.SyncSchema(LabelCollectionName(), 1,
		db.Indexer{Name: "key", Keys: bson.D{
			{Key: "key", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)

	sc = si.Conf("default")

	return newManager(dbmc, vnd.Scope("asset.mgr")), nil
}
