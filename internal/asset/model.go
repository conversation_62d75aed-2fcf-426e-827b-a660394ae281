package asset

import (
	"context"
	"fmt"

	"gitlab.sskjz.com/go/i3n"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Effect struct{}

// 热门Top1
func LabelHotTopKey(n int) string {
	return fmt.Sprintf("live_hot_top%d", n)
}

// Top1
func LabelTopKey(n int) string {
	return fmt.Sprintf("live_top%d", n)
}

const (
	DefaultLng = "id"
)

const (
	LabelEvento = "evento"
)

// 标签资源
type Label struct {
	Id   primitive.ObjectID `bson:"_id"`
	Key  string             `bson:"key"`  // 标签key
	Name string             `bson:"name"` // 标签名称
	Urls map[string]string  `bson:"urls"` // 标签资源，key为语言版本
}

func LabelCollectionName() string {
	return "label"
}

func (l Label) Url(ctx context.Context) string {
	lng := i3n.UnWarp(ctx)

	var path string

	if url, ok := l.Urls[lng]; ok {
		path = url
	}

	if path == "" {
		if url, ok := l.Urls[DefaultLng]; ok {
			path = url
		}
	}

	return sc.ExternalURL(path)
}
