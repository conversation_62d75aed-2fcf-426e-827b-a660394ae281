package asset

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 根据key获取标签资源
func (m *Manager) GetLabel(ctx context.Context, key string) (*Label, error) {
	var record Label

	err := m.dbmc.Collection(LabelCollectionName()).FindOne(
		ctx,
		bson.M{
			"key": key,
		},
	).Decode(&record)

	if err != nil {
		return nil, err
	}

	return &record, nil
}

func (m *Manager) GetLabelUrl(ctx context.Context, key string) (string, error) {
	label, err := m.GetLabel(ctx, key)
	if err != nil {
		return "", err
	}

	return label.Url(ctx), nil
}

// 标签初始化
func (m *Manager) initSystemLable() {
	ctx := context.Background()

	labels := make([]Label, 0)

	urlTemplate := "/label/%s.png"

	for i := 1; i <= 4; i++ {
		key := LabelHotTopKey(i)
		url := fmt.Sprintf(urlTemplate, key)

		labels = append(labels, Label{
			Id:   primitive.NewObjectID(),
			Key:  key,
			Name: key,
			Urls: map[string]string{
				"id": url,
			},
		})
	}

	for i := 1; i <= 4; i++ {
		key := LabelTopKey(i)
		url := fmt.Sprintf(urlTemplate, key)

		labels = append(labels, Label{
			Id:   primitive.NewObjectID(),
			Key:  key,
			Name: key,
			Urls: map[string]string{
				"id": url,
			},
		})
	}

	for _, v := range labels {
		m.dbmc.Collection(LabelCollectionName()).InsertOne(ctx, &v)
	}
}
