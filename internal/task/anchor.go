package task

import (
	"context"
	"errors"
	"time"

	"github.com/jinzhu/now"
)

// 主播任务

type AnchorTask struct {
	Key        string            `json:"key"`     // 任务key
	Current    int64             `json:"current"` // 总进度值
	Stages     []AnchorTaskStage `json:"stages"`  // 档位列表
	AwardStage AnchorTaskStage   `json:"-"`       // 奖励档位
}

type AnchorTaskStage struct {
	Start       int64  `json:"start"`                          // 当前档位开始值
	Target      int64  `json:"target"`                         // 当前档位目标值
	Capacity    int64  `json:"capacity"`                       // 当前档位容量值
	Type        string `json:"type"`                           // 当前档位类型 normal:普通, additional:额外叠加
	AnchorAward int64  `json:"anchorAward" bson:"anchorAward"` // 当前档位主播奖励
	AgencyAward int64  `json:"agencyAward" bson:"agencyAward"` // 当前档位公会奖励
	Is          bool   `json:"is"`                             // 是否在当前档位（可能完成可能未完成）
	No          int    `json:"no"`                             // 当前档位序号，从0开始计数
}

const (
	stageTypeNormal     = "normal"
	stageTypeAdditional = "additional"
)

const (
	AnchorTaskKeyDuration    = "duration"    // 今日直播时长任务
	AnchorTaskKeyLuckDiamond = "luckDiamond" // 幸运礼物音符任务
)

// 主播任务列表
func (m *Manager) AnchorTaskList(ctx context.Context, userId string, day time.Time) ([]AnchorTask, error) {
	n := now.New(day)
	startTime := n.BeginningOfDay()
	endTime := n.EndOfDay()

	current, err := m.lsm.GetLuckDiamond(ctx, userId, day)

	if err != nil {
		return nil, err
	}

	tasks := m.getAnchorTaskList(current)

	ret := make([]AnchorTask, 0, len(tasks))

	for _, v := range tasks {
		switch v.Key {
		case AnchorTaskKeyDuration:
			duration, err := m.lm.GetLiveValidDuration(ctx, userId, startTime, endTime)

			if err != nil {
				return nil, err
			}

			// 转成分钟
			v.Current = int64(duration / 60)
		}

		ret = append(ret, v)
	}

	return ret, nil
}

func (m *Manager) GetLuckDiamondTask(ctx context.Context, luckDiamond int64) (*AnchorTask, error) {
	var task *AnchorTask

	tasks := m.getAnchorTaskList(luckDiamond)

	for _, v := range tasks {
		if v.Key == AnchorTaskKeyLuckDiamond {
			task = &v
			break
		}
	}

	if task == nil {
		return nil, errors.New("task not found")
	}

	return task, nil
}
