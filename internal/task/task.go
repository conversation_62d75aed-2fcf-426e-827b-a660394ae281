package task

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
)

const (
	additionalCapacity    = 1000000
	additionalAnchorAward = 15000
	additionalAgencyAward = 3000
)

// 获取任务列表
// 音符任务没有上限，必须根据当前音符数量来获取任务列表，仅返回当前阶段和下一阶段的任务数据
func (m *Manager) getAnchorTaskList(currentLuckDiamond int64) []AnchorTask {
	ret := []AnchorTask{
		{
			Key:     AnchorTaskKeyDuration, // 今日直播时长
			Current: 0,                     // 当前直播时长
			Stages: []AnchorTaskStage{
				{
					Start:  0,                                    // 当前档位任务开始值0分钟
					Target: int64(ls.ValidDayDuration.Minutes()), // 任务目标，分钟
				},
			},
		},
		{
			Key:     AnchorTaskKeyLuckDiamond, // 幸运礼物音符任务
			Current: 0,                        // 当前音符
			Stages: []AnchorTaskStage{
				{
					Start:       0,      // 当前档位任务开始值
					Target:      150000, // 当前档位目标值
					AnchorAward: 10000,
					AgencyAward: 500,
				},
				{
					Start:       150001,
					Target:      300000,
					AnchorAward: 13000,
					AgencyAward: 800,
				},
				{
					Start:       300001,
					Target:      600000,
					AnchorAward: 18000,
					AgencyAward: 1500,
				},
				{
					Start:       600001,
					Target:      1000000,
					AnchorAward: 22000,
					AgencyAward: 2500,
				},
				{
					Start:       1000001,
					Target:      1500000,
					AnchorAward: 28000,
					AgencyAward: 3000,
				},
				{
					Start:       1500001,
					Target:      3000000,
					AnchorAward: 40000,
					AgencyAward: 5000,
				},
				{
					Start:       3000001,
					Target:      5000000,
					AnchorAward: 60000,
					AgencyAward: 8000,
				},
				{
					Start:       5000001,
					Target:      8000000,
					AnchorAward: 100000,
					AgencyAward: 15000,
				},
				{
					Start:       8000001,
					Target:      12000000,
					AnchorAward: 160000,
					AgencyAward: 20000,
				},
				{
					Start:       12000001,
					Target:      20000000,
					AnchorAward: 250000,
					AgencyAward: 50000,
				},
				{
					Start:       20000001,
					Target:      30000000,
					AnchorAward: 400000,
					AgencyAward: 70000,
				},
				{
					Start:       30000001,
					Target:      50000000,
					AnchorAward: 800000,
					AgencyAward: 100000,
				},
				{
					Start:       50000001,
					Target:      100000000,
					AnchorAward: 2000000,
					AgencyAward: 200000,
				},
			},
		},
	}

	for i, task := range ret {
		if task.Key == AnchorTaskKeyLuckDiamond {
			task.Current = currentLuckDiamond

			var no int
			var lastStage AnchorTaskStage
			var awardStage AnchorTaskStage
			ss := make([]AnchorTaskStage, 0)
			for _, stage := range task.Stages {
				no++
				// 当前阶段的序号
				stage.No = no
				// 计算当前阶段的容量
				stage.Capacity = stage.Target - lastStage.Target
				// 正常阶段
				stage.Type = stageTypeNormal

				if currentLuckDiamond >= stage.Start && currentLuckDiamond <= stage.Target {
					stage.Is = true
				}

				lastStage = stage

				ss = append(ss, stage)

				// 只需要显示当前进度往后一个档位数据
				if currentLuckDiamond < stage.Start {
					break
				}

				if currentLuckDiamond >= stage.Target {
					awardStage = stage
				}
			}

			task.Stages = ss

			// 额外叠加阶段
			for currentLuckDiamond >= lastStage.Start {
				no++
				lastStage = AnchorTaskStage{
					Start:       lastStage.Target + 1,
					Target:      lastStage.Target + additionalCapacity,
					Capacity:    additionalCapacity,
					Type:        stageTypeAdditional,
					AnchorAward: lastStage.AnchorAward + additionalAnchorAward,
					AgencyAward: lastStage.AgencyAward + additionalAgencyAward,
					No:          no,
				}

				if currentLuckDiamond >= lastStage.Start && currentLuckDiamond <= lastStage.Target {
					lastStage.Is = true
				}

				task.Stages = append(task.Stages, lastStage)

				if currentLuckDiamond >= lastStage.Target {
					awardStage = lastStage
				}
			}

			// task.Stages最多返回2个
			if len(task.Stages) > 2 {
				task.Stages = task.Stages[len(task.Stages)-2:]
			}

			task.AwardStage = awardStage

			ret[i] = task
		}
	}

	return ret
}
