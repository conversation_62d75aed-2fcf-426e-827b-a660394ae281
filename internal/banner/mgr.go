package banner

import (
	"context"
	"strconv"
	"time"

	"github.com/hashicorp/go-version"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.uber.org/zap"
)

func newManager(
	log *zap.Logger,
	db *db.Client,
	am *agency.Manager,
	anm *anchor.Manager,
	syn cc.Sync,
) *Manager {
	db.ApplyMigrate(&Banner{})

	mgr := &Manager{
		log: log,
		db:  db,
		am:  am,
		anm: anm,
	}

	mgr.identityCache = cc.New[string, BitFlag](
		1024, cc.LRU,
		cc.LoaderFunc(func(userId string) (BitFlag, error) {
			return mgr.userIdentity(userId), nil
		}),
		cc.Expiration(5*time.Minute),
		cc.ExportStats("banner.identity"),
		cc.WithSync(syn, "banner.identity"),
	)

	return mgr
}

type Manager struct {
	am            *agency.Manager
	anm           *anchor.Manager
	log           *zap.Logger
	db            *db.Client
	identityCache cc.Cache[string, BitFlag]
}

func (m *Manager) Create(banner *Banner) error {
	return m.db.Create(banner).Error
}

// 获取指定场景的banner列表
func (m *Manager) List(ctx context.Context, deviceType, scene, userId string) ([]Banner, error) {
	var (
		banners         []Banner
		err             error
		t               = time.Now()
		userIdentity, _ = m.identityCache.Get(userId)
		userDevice      = DeviceTypeIOS
	)

	tx := m.db.Where("status <> ? and switch = ?", StatusDeleted, SwitchOpen).Where("start_at <= ? and end_at >= ?", t, t)

	targetSql := ""
	targetSqlArgs := make([]interface{}, 0)
	for _, identity := range []BitFlag{TargetAll, TargetVerifiedAnchor, TargetAgencyChief} {
		if userIdentity.Has(identity) {
			targetSql += "target&? = ? or "
			targetSqlArgs = append(targetSqlArgs, identity, identity)
		}
	}

	if targetSql != "" && len(targetSqlArgs) > 0 {
		tx = tx.Where(targetSql[:len(targetSql)-4], targetSqlArgs...)
	}

	// 根据设备类型进行筛选
	switch deviceType {
	case "ios":
		userDevice = DeviceTypeIOS
	case "android":
		userDevice = DeviceTypeAndroid
	}
	tx = tx.Where("device&? = ?", userDevice, userDevice)

	if scene == "" {
		err = tx.Order("sort DESC").Find(&banners).Error
	} else {
		err = tx.Where("scene_bit&? = ?", sceneToBig[scene], sceneToBig[scene]).Order("sort DESC").Find(&banners).Error
	}

	var inReview bool

	if g := api.Unwrap(ctx); g != nil {
		inReview = app.InReview(app.Version(g))
	}

	if inReview {
		ret := make([]Banner, 0)

		for _, v := range banners {
			if v.ID == 13 {
				continue
			}

			ret = append(ret, v)
		}

		return ret, err
	}

	ret2 := make([]Banner, 0)

	g := api.Unwrap(ctx)

	for _, v := range banners {
		if v.ID == 17 {
			if app.IsAppStore(g) {
				// 苹果小于34屏蔽
				bId := app.BuildId(g)

				buildId, err := strconv.Atoi(bId)

				if err != nil || buildId < 34 {
					continue
				}
			} else {
				// 安卓小于1.4.9版本需屏蔽
				if app.Version(g).LessThan(version.Must(version.NewVersion("1.4.9"))) {
					continue
				}
			}
		}

		// 新主播政策扶持banner是否显示给用户
		if !m.anm.IsShowEvaluationBanner(ctx, v.Title, userId) {
			continue
		}

		ret2 = append(ret2, v)
	}

	return ret2, err
}

// userIdentity
func (m *Manager) userIdentity(userId string) BitFlag {
	var identity = TargetAll

	// 是否是审核通过的主播
	if flag, err := m.anm.GetAnchorFlags(userId); err == nil && flag.HasBeenEvaluated() {
		identity = identity.Set(TargetVerifiedAnchor)
	}

	// 是否是会长
	if _, err := m.am.MyOwnAgency(context.Background(), userId); err == nil {
		identity = identity.Set(TargetAgencyChief)
	}

	return identity
}

type Scene struct {
	Scene  string `json:"scene"`
	Desc   string `json:"desc"`
	Hidden bool   `json:"hidden"`
}

func (m *Manager) SceneList() ([]Scene, error) {
	scenes := []Scene{
		{Scene: SceneLiveRoom, Desc: "直播间右上长触区", Hidden: false},
		{Scene: SceneHomeHot, Desc: "热门页签顶部", Hidden: false},
		{Scene: SceneLiveBeginning, Desc: "直播开始前", Hidden: false},
		{Scene: SceneLiveEnd, Desc: "直播结束后", Hidden: false},
	}
	return scenes, nil
}

func (m *Manager) Add(ctx context.Context, banner *Banner) error {
	return m.db.Create(banner).Error
}

func (m *Manager) Get(ctx context.Context, id uint) (*Banner, error) {
	var b Banner
	if err := m.db.Where("id = ?", id).First(&b).Error; err != nil {
		return nil, err
	}

	return &b, nil
}

func (m *Manager) Update(ctx context.Context, id uint, b *Banner) error {
	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Model(&Banner{ID: id}).Updates(b).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (m *Manager) Delete(ctx context.Context, id uint) error {
	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Model(&Banner{ID: id}).Update("status", StatusDeleted).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (m *Manager) ManageList(scene string) ([]Banner, error) {
	var banners []Banner
	var err error
	tx := m.db.Where("status <> ?", StatusDeleted)
	if scene == "" {
		err = tx.Order("switch asc, sort DESC").Find(&banners).Error
	} else {
		err = tx.Where("scene_bit&? = ?", sceneToBig[scene], sceneToBig[scene]).Order("switch asc, sort DESC").Find(&banners).Error
	}

	return banners, err
}
