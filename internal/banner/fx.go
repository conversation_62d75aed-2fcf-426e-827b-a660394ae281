package banner

import (
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(
	vnd log.Vendor,
	db *db.Client,
	am *agency.Manager,
	anm *anchor.Manager,
	syn cc.Sync,
) (*Manager, error) {
	return newManager(vnd.Scope("banner.mgr"), db, am, anm, syn), nil
}
