package banner

import "time"

const (
	SceneLiveRoom      = "live_room"      // 直播间右上长触区
	SceneHomeHot       = "home_hot"       // 热门页签顶部
	SceneLiveBeginning = "live_beginning" // 直播开始前
	SceneLiveEnd       = "live_end"       // 直播结束后
)

const (
	StatusDeleted = -1
)

const (
	SwitchOpen  = 1
	SwitchClose = 2
)

type BitFlag uint32

func (s BitFlag) Has(cmp BitFlag) bool {
	return s&cmp == cmp
}

func (s BitFlag) Set(in ...BitFlag) BitFlag {
	n := s
	for _, add := range in {
		n |= add
	}
	return n
}

func (s BitFlag) Unset(in ...BitFlag) BitFlag {
	n := s
	for _, del := range in {
		n &= ^del
	}
	return n
}

const (
	SceneBitRoom          BitFlag = 1 << iota // 直播间右上长触区
	SceneBitHomeHot                           // 热门页签顶部
	SceneBitLiveBeginning                     // 直播开始前
	SceneBitLiveEnd                           // 直播结束后
)

const (
	TargetAll            BitFlag = 1 << iota // 所有人
	TargetVerifiedAnchor                     // 过审主播
	TargetAgencyChief                        // 公会长
)

const (
	DeviceTypeIOS     BitFlag = 1 << iota // ios设备
	DeviceTypeAndroid                     // 安卓设备
)

type Banner struct {
	ID        uint      `gorm:"primaryKey"`
	SceneBit  BitFlag   `gorm:"not null;size:32;default:0;index:scene_bit"` // 场景位，支持多场景
	Scene     string    `gorm:"not null;size:32;index:scene"`               // 场景
	Title     string    `gorm:"not null;size:255"`                          // 标题
	ImageUrl  string    `gorm:"not null;size:255"`                          // 图片地址
	LinkUrl   string    `gorm:"not null;size:255"`                          // 跳转地址
	Sort      uint      `gorm:"not null;size:32;default:0"`                 // 排序
	Target    BitFlag   `gorm:"not null;size:32;default:0;index:target"`    // 生效人群，支持多选
	Device    BitFlag   `gorm:"not null;size:32;default:3"`                 // 支持设备，支持多选
	Switch    int       `gorm:"not null;size:8;default:1"`                  // 开关：1开启 2关闭
	Status    int       `gorm:"not null;size:8;default:0"`                  // 状态 -1:已删除 0:正常
	StartAt   time.Time `gorm:"index:idx_start_end"`                        // 开始时间
	EndAt     time.Time `gorm:"index:idx_start_end"`                        // 结束时间
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (b *Banner) TableName() string {
	return "banners"
}
