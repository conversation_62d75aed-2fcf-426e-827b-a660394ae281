package contrib

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
)

const (
	keyContribCons = "HASH:CONTRIB:%s:%s" // {A用户}对{B用户}消费的金额，按天记录
	ttlContribCons = time.Hour * 24 * 30
)

const (
	ContribIn7  = 7  // 近7天（自然天，包括今天）
	ContribIn30 = 30 // 近30天（自然天，包括今天）
)

const (
	TypeGiftNormal = "gn"
	TypeGiftLuck   = "gl"
)

type Contrib struct {
	Gift map[int]ContribGift
}

type ContribGift struct {
	NormalDiamond int64 // 非幸运礼物
	LuckDiamond   int64 // 幸运礼物
}

func (m *Manager) onGiftSent(ctx context.Context, evd *evt.SendGift) error {
	userId := evd.UserId
	toUserId := evd.AnchorId
	diamond := int64(evd.Diamond)
	luckGift := evd.Gift.Lucky

	key := m.getKeyContribCons(userId, toUserId)

	// 记录用户对用户的消费
	now := time.Now()
	cType := TypeGiftNormal

	if luckGift {
		cType = TypeGiftLuck
	}

	field := m.getField(now, cType)

	_, err := m.rc.HIncrBy(ctx, key, field, diamond).Result()

	if err != nil {
		return fmt.Errorf("incr contrib cons failed: %w", err)
	}

	// 设置过期时间
	m.rc.Expire(ctx, key, ttlContribCons)

	return nil
}

// 获取用户对用户的贡献
func (m *Manager) GetContrib(ctx context.Context, userId string, toUserId string, inDay int) (*Contrib, error) {
	key := m.getKeyContribCons(userId, toUserId)

	// 获取用户对用户的消费记录
	cons, err := m.rc.HGetAll(ctx, key).Result()

	if err != nil {
		return nil, fmt.Errorf("get contrib cons failed: %w", err)
	}

	if inDay <= 0 || inDay > ContribIn30 {
		inDay = ContribIn30
	}

	ret := &Contrib{
		Gift: make(map[int]ContribGift),
	}

	days := make([]int, 0)

	for _, v := range []int{ContribIn7, ContribIn30} {
		if v <= inDay {
			days = append(days, v)

			ret.Gift[v] = ContribGift{
				NormalDiamond: 0,
				LuckDiamond:   0,
			}
		}
	}

	// 获取最近{inDay}天的消费记录
	today := now.New(time.Now()).BeginningOfDay()

	// 开始统计的时间
	start := today.AddDate(0, 0, -inDay+1)
	// 最小时间
	minStart := today.AddDate(0, 0, -ContribIn30+1)

	for t, v := range cons {
		tt, cType, err := m.parseField(t)

		if err != nil {
			m.log.Error("parse field failed", zap.Error(err))
			continue
		}

		if tt.Before(minStart) {
			// !异步任务处理过期的记录
			m.rc.HDel(ctx, key, t)
			continue
		}

		if tt.Before(start) {
			continue
		}

		diamond, err := strconv.ParseInt(v, 10, 64)

		if err != nil {
			m.log.Error("get diamond failed", zap.Error(err))
			continue
		}

		for _, day := range days {
			if tt.Before(today.AddDate(0, 0, -day+1)) {
				continue
			}

			data := ret.Gift[day]

			switch cType {
			case TypeGiftNormal:
				data.NormalDiamond += diamond
			case TypeGiftLuck:
				data.LuckDiamond += diamond
			}

			ret.Gift[day] = data
		}
	}

	return ret, nil
}

func (m *Manager) getKeyContribCons(userId string, toUserId string) string {
	return fmt.Sprintf(keyContribCons, userId, toUserId)
}

func (m *Manager) getField(t time.Time, cType string) string {
	return fmt.Sprintf("%s:%s", t.Format("20060102"), cType)
}

func (m *Manager) parseField(field string) (time.Time, string, error) {
	strs := strings.Split(field, ":")

	if len(strs) != 2 {
		return time.Time{}, "", fmt.Errorf("invalid field: %s", field)
	}

	t, err := time.Parse("20060102", strs[0])

	if err != nil {
		return time.Time{}, "", fmt.Errorf("invalid field: %s", field)
	}

	return t, strs[1], nil
}
