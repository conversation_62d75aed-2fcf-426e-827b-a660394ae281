package contrib

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(
	dbmc *db.MongoClient,
	rc *redi.Client,
	ev ev.Bus,
	vnd log.Vendor,
) (*Manager, error) {
	mgr := newManager(dbmc, rc, ev, vnd.Scope("contrib.mgr"))

	return mgr, nil
}

func Invoke(evb ev.Bus, mgr *Manager) {
	evb.Watch(evt.GiftSend, "contrib.consume.gift", ev.<PERSON>er(mgr.onGiftSent), ev.WithAs<PERSON>())
}
