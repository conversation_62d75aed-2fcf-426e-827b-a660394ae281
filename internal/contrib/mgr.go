package contrib

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

type Manager struct {
	log  *zap.Logger
	dbmc *db.MongoClient
	rc   *redi.Client
	ev   ev.Bus
}

func newManager(dbmc *db.MongoClient, rc *redi.Client, ev ev.Bus, log *zap.Logger) *Manager {
	m := &Manager{
		log:  log,
		dbmc: dbmc,
		rc:   rc,
		ev:   ev,
	}

	return m
}
