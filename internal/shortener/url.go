package shortener

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ErrorInvalidShortId = biz.NewError(biz.ErrInvalidParam, "shortId is invalid")
)

func (m *Manager) ShortenUrl(ctx context.Context, url string) (string, error) {
	result, err := transform(url)

	if err != nil {
		return "", err
	}

	l := len(result)

	if l <= 0 {
		return "", errors.New("url is empty")
	}

	tn := time.Now()

	shortId := fmt.Sprintf("%s%s", big.NewInt(tn.Unix()).Text(62), result[0])

	_, err = m.dbmc.Collection(ShortenerCollectionName(tn)).InsertOne(
		ctx,
		Shortener{
			Id:      primitive.NewObjectIDFromTimestamp(tn),
			ShortId: shortId,
			Long:    url,
		},
	)

	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%s/%s", m.cs.Host, shortId), nil
}

func (m *Manager) ResolveShortUrl(ctx context.Context, shortId string) (string, error) {
	if len(shortId) <= 6 {
		return "", ErrorInvalidShortId
	}

	var i big.Int
	ts, ok := i.SetString(shortId[:6], 62)

	if !ok {
		return "", ErrorInvalidShortId
	}

	collection := ShortenerCollectionName(time.Unix(ts.Int64(), 0))

	var st Shortener

	err := m.dbmc.Collection(collection).FindOne(
		ctx,
		bson.M{"shortId": shortId},
	).Decode(&st)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return "", ErrorInvalidShortId
		}

		return "", err
	}

	return st.Long, nil
}
