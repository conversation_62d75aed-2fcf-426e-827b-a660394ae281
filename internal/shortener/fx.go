package shortener

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

// 短链接
func Provide(
	dbmc *db.MongoClient,
	desc *conf.Setting,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(ShortenerCollectionName, 2,
		db.Indexer{Name: "shortId", Keys: bson.D{
			{Key: "shortId", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)

	return newManager(dbmc, desc.Shortener, vnd.Scope("shortener.mgr")), nil
}
