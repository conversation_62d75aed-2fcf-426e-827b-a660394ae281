package shortener

import (
	"fmt"
	"math/big"
	"testing"
	"time"
)

func TestXxx(t *testing.T) {
	incr := time.Now().Unix()

	result, err := transform("https://www.baidu.com?id=1&type=live")
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(result)

	var i big.Int

	i.SetInt64(incr)

	c := i.Text(62)

	fmt.Println(incr, c)

	var m big.Int
	bi, ok := m.SetString(c, 62)

	fmt.Println(bi, ok)
}
