package withdraw

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sms"
	"gorm.io/gorm"
)

var (
	ErrPayeeNotFound = biz.NewError(biz.ErrPayeeNotFound, "payee not found")
)

func (s *Manager) TakePayee(ctx context.Context, userId string) (*Payee, error) {
	var payee Payee
	if err := db.UseTx(ctx, s.db).Where("user_id = ?", userId).Take(&payee).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPayeeNotFound
		}
		return nil, err
	}
	return &payee, nil
}

func (s *Manager) SavePayee(ctx context.Context, userId string, country pay.Country, bankId, fullName, account, identity, phone string) error {
	if phone != "" {
		var err error
		_, phone, err = sms.TrimPhone(ctx, phone)
		if err != nil {
			return err
		}
	}

	payee := &Payee{
		Country:  country,
		Currency: pay.CurrencyOf(country),
		Params:   pay.Params(bankId),
		FullName: trimStr(fullName),
		Account:  trimStr(account),
		Identity: identity,
		Phone:    phone,
	}

	payee2 := payee.Export()
	if err := payermax.ValidatePayee(ctx, country, payee.Params.Get("o"), payee2); err != nil {
		return err
	}
	payee.Syncing(payee2)

	curr, err := s.TakePayee(ctx, userId)
	if err != nil && !errors.Is(err, ErrPayeeNotFound) {
		return err
	}

	// update
	if curr != nil {
		return db.UseTx(ctx, s.db).Model(&curr).Updates(payee).Error
	}

	// create
	payee.UserId = userId
	return db.UseTx(ctx, s.db).Create(payee).Error
}
