package withdraw

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"github.com/redis/go-redis/v9"
)

const (
	keyLimit = "WITHDRAW:LIMIT:DAY:%s:%s" // userId, 20060102
)

func (s *Manager) dLimitGet(ctx context.Context, userId string, at time.Time) (int, error) {
	v, err := s.rc.Get(ctx, fmt.Sprintf(keyLimit, userId, at.Format("20060102"))).Int()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	}
	return v, nil
}

func (s *Manager) dLimitInc(ctx context.Context, userId string, amount int, at time.Time) error {
	end := now.With(at).EndOfDay()
	if time.Now().After(end) {
		return nil
	}
	key := fmt.Sprintf(keyLimit, userId, at.Format("20060102"))
	txp := s.rc.Pipeline()
	txp.IncrBy(ctx, key, int64(amount))
	txp.Expire(ctx, key, end.Sub(at))
	_, err := txp.Exec(ctx)
	return err
}
