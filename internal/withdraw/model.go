package withdraw

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
)

type Payee struct {
	ID        uint         `gorm:"primaryKey"`
	UserId    string       `gorm:"not null;size:32;unique"` // 用户ID
	Country   pay.Country  `gorm:"not null;size:8"`         // 国家代码
	Currency  pay.Currency `gorm:"not null;size:8"`         // 标价币种
	Params    pay.Params   `gorm:"not null;size:128"`       // 平台参数
	FullName  string       `gorm:"not null;size:512"`       // 账户名
	Account   string       `gorm:"not null;size:128"`       // 账号
	Identity  string       `gorm:"not null;size:64"`        // 税号
	Phone     string       `gorm:"not null;size:64"`        // 手机号
	CreatedAt time.Time    `gorm:"not null"`
	UpdatedAt time.Time    `gorm:"not null"`
}

func (p *Payee) TableName() string {
	return "withdraw_payees"
}

type Status int

const (
	StateAuditing Status = iota // 审核中
	StateSuccess                // 提现完成
	StateReject                 // 提现失败
)

type Record struct {
	ID        uint          `gorm:"primaryKey"`
	TradeNo   string        `gorm:"not null;size:64;uniqueIndex:trade_no"`     // 流水号
	UserId    string        `gorm:"not null;size:32;index:user_view"`          // 用户ID
	Prop      fund.PropType `gorm:"not null;size:8;default:0;index:user_view"` // 货币类型
	Amount    int64         `gorm:"not null;type:decimal(10,0) unsigned"`      // 提现数额
	Money     float64       `gorm:"not null;type:decimal(10,2) unsigned"`      // 预估到手金额
	Fee       float64       `gorm:"not null;type:decimal(10,2) unsigned"`      // 预估手续费
	Payee     *PayeeInfo    `gorm:"serializer:json"`                           // 收款人
	Status    Status        `gorm:"not null;size:8;default:0"`                 // 处理状态
	Reason    string        `gorm:"not null;size:255;default:''"`              // 失败原因
	CreatedAt time.Time     `gorm:"not null"`
	UpdatedAt time.Time     `gorm:"not null"`
}

func (r *Record) TableName() string {
	return "withdraw_records"
}
