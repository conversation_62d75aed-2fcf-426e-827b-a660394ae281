package withdraw

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
)

const (
	unit     = 50000
	onceMin  = 50000
	onceMax  = ********
	dailyMax = ********
)

var (
	t2users = map[string]int{
		"7d430b6d111d46e49686d6fafb56b5ee": 20, // ******** fox
		"38640ac6d39e4a38896fc45cf5c2152a": 3,  // ******** elly
	}
)

var currencyRatio = map[pay.Currency]float64{
	pay.BRL: pay.FromUSD(pay.BRL, 0.0001),
}

func Ratio(c pay.Currency) float64 {
	return currencyRatio[c]
}

var remitDesc = map[pay.Country]map[string][]string{
	pay.BR: {
		"PIX":         {"Fee: 2% (min 0.4USD)", "IOF: 0.38%"},
		"PAGBANK":     {"Fee: 5.9% + 0.1BRL", "IOF: 0.38%"},
		"MERCADOPAGO": {"Fee: 0.5USD", "IOF: 0.38%"},
	},
}

func getDesc(c pay.Country, org string) []string {
	return remitDesc[c][org]
}
