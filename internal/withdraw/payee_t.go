package withdraw

import (
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
)

type PayeeBank struct {
	BankCode string `json:"bankCode"`
	BankName string `json:"bankName"`
	BankForm Form   `json:"bankForm"`
}

func makePayeeBank(c pay.Country, org string) PayeeBank {
	return PayeeBank{
		BankCode: payermax.BankCode(c, org),
		BankName: payermax.BankName(c, org),
		BankForm: makeForm(c, org),
	}
}

type PayeePub struct {
	PayeeInfo
	PayeeBank
	BankId   string `json:"bankId"`   // pub不返回
	BankForm Form   `json:"bankForm"` // pub不返回
	Identity string `json:"identity"` // pub不返回
	Phone    string `json:"phone"`    // pub不返回
}

func PubPayee(r *PayeeInfo) *PayeePub {
	org := r.BankId.Get("o")
	return &PayeePub{
		PayeeInfo: *r,
		PayeeBank: makePayeeBank(r.Country, org),
	}
}

type PayeePri struct {
	PayeeInfo
	PayeeBank
}

func PriPayee(r *PayeeInfo) *PayeePri {
	org := r.BankId.Get("o")
	return &PayeePri{
		PayeeInfo: *r,
		PayeeBank: makePayeeBank(r.Country, org),
	}
}

func trimStr(in string) string {
	in = strings.TrimSpace(in)
	out := make([]rune, 0, len(in))
	for _, char := range in {
		switch char {
		case '\r', '\n', '\t':
		default:
			out = append(out, char)
		}
	}
	return string(out)
}
