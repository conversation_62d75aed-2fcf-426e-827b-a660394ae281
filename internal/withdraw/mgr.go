package withdraw

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/go/rpc"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func newManager(ev ev.Bus, rc *redi.Client, db *db.Client, fm *fund.Manager, pm *payermax.Manager, id gid.Generator, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Payee{}, &Record{})
	return &Manager{
		ev:  ev,
		rc:  rc,
		db:  db,
		fm:  fm,
		pm:  pm,
		id:  id.New("withdraw", gid.WithSpan(24*time.Hour), gid.WithPads(4)),
		log: log,
	}
}

type Manager struct {
	ev  ev.Bus
	rc  *redi.Client
	db  *db.Client
	fm  *fund.Manager
	pm  *payermax.Manager
	id  gid.Generator
	rpc rpc.Client
	log *zap.Logger
}

func (s *Manager) Banks(ctx context.Context, userId string, country pay.Country) ([]*Bank, error) {
	payments := payermax.RemitPayments(country)
	out := make([]*Bank, 0, len(payments))
	for _, payment := range payments {
		out = append(out, &Bank{
			Id:   payment.Id(),
			Code: payermax.BankCode(country, payment.Org),
			Name: payment.Name,
			Form: makeForm(country, payment.Org),
			Logo: payment.Logo,
			Desc: getDesc(country, payment.Org),
		})
	}
	return out, nil
}

type Limit struct {
	Unit     int `json:"unit"`     // 最小提现单位
	OnceMin  int `json:"onceMin"`  // 单笔最小额度
	OnceMax  int `json:"onceMax"`  // 单笔最大额度
	DailyMax int `json:"dailyMax"` // 单日最大额度
}

func (s *Manager) Limit(ctx context.Context, userId string) (*Limit, error) {
	l := &Limit{Unit: unit, OnceMin: onceMin, OnceMax: onceMax, DailyMax: dailyMax}
	if t2multi := t2users[userId]; t2multi > 0 {
		l.OnceMax *= t2multi
		l.DailyMax *= t2multi
	}
	return l, nil
}

func (s *Manager) Records(ctx context.Context, tradeNos []string) ([]*Record, error) {
	var records []*Record
	if err := db.UseTx(ctx, s.db).Where("trade_no IN ?", tradeNos).Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

type tradeStatus struct {
	TradeNo string
	Status  Status
}

func (s *Manager) Status(ctx context.Context, tradeNos []string) (map[string]Status, error) {
	var ss []tradeStatus
	if err := db.UseTx(ctx, s.db).Model(&Record{}).Where("trade_no IN ?", tradeNos).Find(&ss).Error; err != nil {
		return nil, err
	}
	out := make(map[string]Status, len(ss))
	for _, v := range ss {
		out[v.TradeNo] = v.Status
	}
	return out, nil
}

func (s *Manager) LastFail(ctx context.Context, userId string) (*Record, error) {
	var rec Record
	if err := db.UseTx(ctx, s.db).Where("user_id = ? AND status = ?", userId, StateReject).Order("updated_at DESC").Take(&rec).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &rec, nil
}
