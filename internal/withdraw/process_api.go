package withdraw

import (
	"context"
	"errors"
	"strconv"
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
)

func (s *Manager) Remit(ctx context.Context, tradeNo string, attempts int) error {
	if attempts > 0 {
		if receipt, err := s.pm.GetReceipt(ctx, TradeNo(tradeNo, attempts-1)); err != nil {
			return err
		} else if receipt.Status != payermax.RemitFailed {
			return errors.New("receipt status not failed")
		}
	}
	return s.rpc.Call("DoRemit", RemitRequest{TradeNo: tradeNo, Attempts: attempts}, nil)
}

type RemitRequest struct {
	TradeNo  string
	Attempts int
}

type RemitResponse struct {
}

func (s *Manager) DoRemit(req RemitRequest, resp *RemitResponse) error {
	ctx := context.TODO()
	r, err := s.TakeRecord(ctx, req.TradeNo)
	if err != nil {
		return err
	} else if r.Status != StateAuditing {
		return ErrProcessedRecord
	}
	return s.pm.Remit(ctx, r.User<PERSON>d, "withdraw", TradeNo(r.TradeNo, req.Attempts), r.<PERSON>, r.<PERSON>)
}

func TradeNo(tradeNo string, attempts int) string {
	if attempts <= 0 {
		return tradeNo
	} else {
		return tradeNo + "_" + strconv.Itoa(attempts)
	}
}

func RawTradeNo(tradeNo string) string {
	if idx := strings.IndexRune(tradeNo, '_'); idx > 0 {
		return tradeNo[:idx]
	}
	return tradeNo
}
