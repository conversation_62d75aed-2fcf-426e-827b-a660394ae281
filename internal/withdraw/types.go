package withdraw

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
)

var symbols = map[pay.Currency]Currency{
	pay.BRL: {Code: pay.BRL, Symbol: "R$", Name: "Real"},
}

func Symbol(c pay.Currency) Currency {
	return symbols[c]
}

type Currency struct {
	Code   pay.Currency `json:"code"`   // 代码
	Symbol string       `json:"symbol"` // 符号
	Name   string       `json:"name"`   // 名称
}

type Bank struct {
	Id   string   `json:"id"`
	Code string   `json:"code"`
	Name string   `json:"name"`
	Form Form     `json:"form"`
	Logo string   `json:"logo"`
	Desc []string `json:"desc"`
}

type PayeeInfo = payermax.Payee

func (p *Payee) Export() *PayeeInfo {
	return &PayeeInfo{
		Country:  p.Country,
		Currency: p.Currency,
		BankId:   p.Params,
		FullName: p.FullName,
		Account:  p.Account,
		Identity: p.Identity,
		Phone:    p.Phone,
	}
}

func (p *Payee) Syncing(p2 *PayeeInfo) {
	if p2.Account != "" && p2.Account != p.Account {
		p.Account = p2.Account
	}
	if p2.Identity != "" && p2.Identity != p.Identity {
		p.Identity = p2.Identity
	}
}
