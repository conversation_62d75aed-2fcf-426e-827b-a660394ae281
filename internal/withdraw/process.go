package withdraw

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrRecordNotFound  = errors.New("record not found")
	ErrProcessedRecord = errors.New("processed record")
	ErrIllegalAmount   = biz.NewError(biz.ErrIllegalWithdraw, "illegal amount")
	ErrOverloadAmount  = biz.NewError(biz.ErrProhibitWithdraw, "overload amount")
)

func (s *Manager) TakeRecord(ctx context.Context, tradeNo string) (*Record, error) {
	var record Record
	if err := db.UseTx(ctx, s.db).Where("trade_no = ?", tradeNo).Take(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &record, nil
}

func (s *Manager) Money(ctx context.Context, userId string, amount int) (money, fee float64, err error) {
	payee, err := s.TakePayee(ctx, userId)
	if err != nil {
		return 0, 0, err
	}
	return s.moneyOf(payee, amount)
}

func (s *Manager) moneyOf(payee *Payee, amount int) (money, fee float64, err error) {
	p := payee.Params.Explode()
	typ, org := p["t"], p["o"]
	payment, has := payermax.RemitPayment(payee.Country, payermax.Type(typ), org)
	if !has {
		return 0, 0, errors.New("payment not found")
	}

	money = fund.New(amount).Mul(fund.New(Ratio(payee.Currency))).InexactFloat64()

	fee, err = s.pm.FeeOf(payment.Cur, money, payment.Fee, payermax.Payout)
	if err != nil {
		return
	}

	money = pay.OutPrice(money - fee)
	if money <= 0 {
		return 0, 0, ErrIllegalAmount
	}

	return
}

func (s *Manager) Submit(ctx context.Context, userId string, amount int) error {
	limit, err := s.Limit(ctx, userId)
	if err != nil {
		return err
	}

	if amount < limit.OnceMin || amount > limit.OnceMax || amount%limit.Unit != 0 {
		return ErrIllegalAmount
	}

	at := time.Now()
	if daily, err := s.dLimitGet(ctx, userId, at); err != nil {
		return err
	} else if daily+amount > limit.DailyMax {
		return ErrOverloadAmount
	}

	payee, err := s.TakePayee(ctx, userId)
	if err != nil {
		return err
	}

	if last, err := s.LastFail(ctx, userId); err != nil {
		return err
	} else if last != nil && payee.UpdatedAt.Before(last.CreatedAt) {
		return biz.Legacy(last.Reason)
	}

	fruit := fund.New(amount)
	money, fee, err := s.moneyOf(payee, amount)
	if err != nil {
		return err
	}

	tradeNo, err := s.id.Next()
	if err != nil {
		return err
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := s.fm.Expend(ctx, userId, fund.JTypeWithdraw, fund.PTypeFruits, fruit, fund.WithTime(at), fund.WithTrade(tradeNo)); err != nil {
			return err
		}
		return tx.Create(&Record{
			TradeNo:   tradeNo,
			UserId:    userId,
			Prop:      fund.PTypeFruits,
			Amount:    fruit.IntPart(),
			Money:     money,
			Fee:       fee,
			Payee:     payee.Export(),
			Status:    StateAuditing,
			CreatedAt: at,
		}).Error
	}); err != nil {
		return err
	}

	_ = s.dLimitInc(ctx, userId, amount, at)

	s.ev.Emit(ctx, evt.WithdrawInitial, &evt.ProcessWithdraw{UserId: userId, TradeNo: tradeNo})
	return nil
}

func (s *Manager) remitUpdate(ctx context.Context, tradeNo string, status payermax.RemitStatus, code, msg string) error {
	tradeNo = RawTradeNo(tradeNo)
	switch status {
	case payermax.RemitSuccess:
		return s.MarkSuccess(ctx, tradeNo)
	case payermax.RemitFailed:
		if reason := getFailReason(code); reason != "" {
			return s.MarkReject(ctx, tradeNo, reason)
		}
		s.log.Warn("unknown reason", zap.String("tradeNo", tradeNo), zap.String("message", msg))
		return nil
	case payermax.RemitReturn:
		s.log.Warn("remit rollback", zap.String("tradeNo", tradeNo))
		return nil
	default:
		return errors.New("unknown remit status")
	}
}

func (s *Manager) MarkReject(ctx context.Context, tradeNo string, reason string) error {
	r, err := s.TakeRecord(ctx, tradeNo)
	if err != nil {
		return err
	} else if r.Status != StateAuditing {
		return ErrProcessedRecord
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		updated := tx.Model(r).Where("status = ?", StateAuditing).Updates(map[string]any{"status": StateReject, "reason": reason})
		if err := updated.Error; err != nil {
			return err
		} else if updated.RowsAffected == 0 {
			return errors.New("raced to mark reject")
		}
		return s.fm.Income(ctx, r.UserId, fund.JTypeWithdraw, r.Prop, r.Amount, fund.WithTime(r.CreatedAt), fund.WithTrade(r.TradeNo))
	}); err != nil {
		return err
	}

	_ = s.dLimitInc(ctx, r.UserId, -int(r.Amount), r.CreatedAt)

	s.ev.Emit(ctx, evt.WithdrawFailure, &evt.ProcessWithdraw{UserId: r.UserId, TradeNo: r.TradeNo})
	return nil
}

func (s *Manager) MarkSuccess(ctx context.Context, tradeNo string) error {
	r, err := s.TakeRecord(ctx, tradeNo)
	if err != nil {
		return err
	} else if r.Status != StateAuditing {
		return ErrProcessedRecord
	}

	{
		updated := db.UseTx(ctx, s.db).Model(r).Where("status = ?", StateAuditing).Update("status", StateSuccess)
		if err := updated.Error; err != nil {
			return err
		} else if updated.RowsAffected == 0 {
			return errors.New("raced to mark success")
		}
	}

	s.ev.Emit(ctx, evt.WithdrawSuccess, &evt.ProcessWithdraw{UserId: r.UserId, TradeNo: r.TradeNo})
	return nil
}
