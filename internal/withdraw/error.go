package withdraw

var remitErrMsg = map[string]string{
	"INVALID_EMAIL":            "Endereço de e-mail inválido",
	"INVALID_PHONE_NO":         "Número de celular inválido",
	"INVALID_ACCOUNT":          "Conta inválida",
	"INVALID_ACCOUNT_FORMAT":   "Conta inválida",
	"INVALID_IDENTIFICATION":   "CPF/CNPJ inválido",
	"ACCOUNT_BLOCKED":          "Conta inválida",
	"PAYMENT_FAILED":           "Saque rejeitado, contate-nos",
	"PROVIDER_REFUSED_PROCESS": "Saque rejeitado, contate-nos",
}

func getFailReason(code string) string {
	return remitErrMsg[code]
}
