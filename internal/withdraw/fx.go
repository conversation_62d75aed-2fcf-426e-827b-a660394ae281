package withdraw

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/go/rpc"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gid"
)

func Provide(ev ev.Bus, rc *redi.Client, db *db.Client, fm *fund.Manager, pm *payermax.Manager, id gid.Generator, rpc rpc.Client, vnd log.Vendor) *Manager {
	mgr := newManager(ev, rc, db, fm, pm, id, vnd.Scope("withdraw.mgr"))
	pm.OnRemitUpdate(mgr.remitUpdate)
	{
		mgr.rpc = rpc.With("withdraw")
	}
	return mgr
}

func Invoke(rpc rpc.Server, mgr *Manager) {
	if env.Scheduler() {
		_ = rpc.Register("withdraw", mgr)
	}
}
