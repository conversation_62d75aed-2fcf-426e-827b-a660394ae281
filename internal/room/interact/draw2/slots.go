package draw2

import (
	"fmt"
	"sync"

	"gitlab.sskjz.com/go/rng"
)

// 预计算总使用量
func calculateTotalUsed(prizes []Prize) int {
	total := 0
	for _, p := range prizes {
		total += p.Avg + p.Random
	}
	return total
}

// 全局的slots池
var slotsPool = sync.Pool{
	New: func() interface{} {
		slots := make([]int, 0, 100000) // 预分配一个较大的容量
		return &slots
	},
}

func generateSlots(totalSlots int, prizes []Prize) map[int]Prize {
	// 预计算总使用量
	used := calculateTotalUsed(prizes)
	if used > totalSlots {
		panic(fmt.Sprintf("total slots %d is not enough for %d prizes", totalSlots, used))
	}

	// 预分配map
	out := make(map[int]Prize, used)

	// 从池中获取slots切片指针
	slotsPtr := slotsPool.Get().(*[]int)
	slots := *slotsPtr
	// 确保切片有足够的容量
	if cap(slots) < totalSlots {
		slots = make([]int, totalSlots)
	} else {
		slots = slots[:totalSlots]
	}

	// 初始化槽位 - 优化：使用更高效的初始化方式
	for i := 0; i < totalSlots; i++ {
		slots[i] = i
	}

	// 使用Fisher-Yates洗牌算法预打乱槽位
	for i := totalSlots - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		slots[i], slots[j] = slots[j], slots[i]
	}

	// 使用索引跟踪可用槽位，避免频繁的切片重新分配
	availableCount := totalSlots

	// 合并处理所有奖品分配，避免两次遍历
	for _, pc := range prizes {
		totalCount := pc.Avg + pc.Random

		// 一次性处理该奖品的所有分配
		for j := 0; j < totalCount && availableCount > 0; j++ {
			// 在剩余槽位中随机选择一个
			slotIndex := rng.Intn(availableCount)
			selectedSlot := slots[slotIndex]

			// 将选中的槽位与最后一个未使用的槽位交换
			slots[slotIndex] = slots[availableCount-1]
			availableCount--

			out[selectedSlot] = pc
		}
	}

	// 更新指针指向的切片，只保留未使用的部分
	*slotsPtr = slots[:availableCount]
	// 将slots指针放回池中
	slotsPool.Put(slotsPtr)

	return out
}
