package draw2

import (
	"fmt"
	"sync"

	"gitlab.sskjz.com/go/rng"
)

// 预计算总使用量
func calculateTotalUsed(prizes []Prize) int {
	total := 0
	for _, p := range prizes {
		total += p.Avg + p.Random
	}
	return total
}

// 全局的slots池
var slotsPool = sync.Pool{
	New: func() interface{} {
		slots := make([]int, 0, 100000) // 预分配一个较大的容量
		return &slots
	},
}

func generateSlots(totalSlots int, prizes []Prize) map[int]Prize {
	// 预计算总使用量
	used := calculateTotalUsed(prizes)
	if used > totalSlots {
		panic(fmt.Sprintf("total slots %d is not enough for %d prizes", totalSlots, used))
	}

	// 预分配map
	out := make(map[int]Prize, used)

	// 从池中获取slots切片指针
	slotsPtr := slotsPool.Get().(*[]int)
	slots := *slotsPtr
	// 确保切片有足够的容量
	if cap(slots) < totalSlots {
		slots = make([]int, totalSlots)
	} else {
		slots = slots[:totalSlots]
	}

	// 初始化槽位
	for i := range slots {
		slots[i] = i
	}

	// 使用Fisher-Yates洗牌算法预打乱槽位
	for i := len(slots) - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		slots[i], slots[j] = slots[j], slots[i]
	}

	// 处理平均分配的奖品
	remainingSlots := len(slots)
	for i, pc := range prizes {
		if pc.Avg == 0 {
			continue
		}

		// 计算每个奖品应该分配的槽位数量
		slotsPerPrize := remainingSlots / pc.Avg
		if slotsPerPrize == 0 {
			slotsPerPrize = 1
		}

		for j := 0; j < pc.Avg; j++ {
			if remainingSlots == 0 {
				prizes[i].Random++
				continue
			}

			// 在剩余槽位中随机选择一个
			slotIndex := rng.Intn(remainingSlots)
			selectedSlot := slots[slotIndex]

			// 将选中的槽位与最后一个未使用的槽位交换
			slots[slotIndex] = slots[remainingSlots-1]
			slots = slots[:remainingSlots-1]
			remainingSlots--

			out[selectedSlot] = pc
		}
	}

	// 处理随机分配的奖品
	for _, pc := range prizes {
		for i := 0; i < pc.Random; i++ {
			if len(slots) == 0 {
				break
			}

			slotIndex := rng.Intn(len(slots))
			selectedSlot := slots[slotIndex]

			// 将选中的槽位与最后一个未使用的槽位交换
			slots[slotIndex] = slots[len(slots)-1]
			slots = slots[:len(slots)-1]

			out[selectedSlot] = pc
		}
	}

	// 更新指针指向的切片
	*slotsPtr = slots
	// 将slots指针放回池中
	slotsPool.Put(slotsPtr)

	return out
}
