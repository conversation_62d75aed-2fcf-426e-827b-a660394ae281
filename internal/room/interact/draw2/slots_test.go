package draw2

import (
	"image"
	"image/color"
	"image/png"
	"os"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"gitlab.sskjz.com/go/rng"
)

func TestSlots_Walk(t *testing.T) {
	{
		slot := &Slots{
			Total: 2, // progree 0-9
		}

		cur := 0
		walked, _ := slot.Walk(0, 1)
		cur += walked

		t.Log("cur", cur, walked)

		walked, _ = slot.Walk(cur, 1)
		cur += walked

		t.Log("cur", cur, walked)

		walked, _ = slot.Walk(cur, 1)
		cur += walked

		t.Log("cur", cur, walked)
	}
}

func Render(total int, prizes map[int]Prize, path string) error {
	// 创建一个新的RGBA图像，大小为s.Total
	img := image.NewRGBA(image.Rect(0, 0, total, 1))

	for i := 0; i < total; i++ {
		// 设置像素颜色
		img.Set(i, 0, color.RGBA{0, 0, 0, 255})
	}

	// 遍历s.Prizes，根据奖品的数量和位置设置像素颜色
	for i := range prizes {
		// 简单的热力图效果：奖品数量越多，颜色越红
		col := color.White
		img.Set(i, 0, col)
	}

	// 创建文件
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	// 将图像保存到文件中
	err = png.Encode(file, img)
	if err != nil {
		return err
	}

	return nil
}

func Test_generateSlots(t *testing.T) {
	{
		total := 1000
		out := generateSlots(total, []Prize{
			{
				Avg:    6,
				Random: 5,
			},
		})

		t.Log(out)
		Render(total, out, "/tmp/1.png")
	}

	// {
	// 	out := generateSlots(100, []Prize{
	// 		{Mul: 10, Avg: 10, Random: 2},
	// 		{Mul: 50, Avg: 10, Random: 2},
	// 		{Mul: 500, Avg: 10, Random: 3},
	// 	})

	// 	t.Log(out)
	// }
}

func Test_generateSlots2(t *testing.T) {
	total := 43
	prizes := []Prize{
		{Avg: 2, Mul: 1, Random: 1},
		{Avg: 4, Mul: 2, Random: 10},
		{Avg: 6, Mul: 3, Random: 20},
	}

	for x := 0; x < 100000; x++ {

		out := generateSlots(total, prizes)

		// check prizes count of each Mul
		counts := make(map[int]int)
		for _, p := range out {
			counts[p.Mul]++
		}

		for _, p := range prizes {
			if counts[p.Mul] != p.Avg+p.Random {
				t.Errorf("expected %d, got %d", p.Avg+p.Random, counts[p.Mul])
			}
		}
	}
}

func TestReturnProbability(t *testing.T) {
	// Create a GiftConfig instance
	cfg := &PoolGroup{
		GiftId: 1,
		Pools: []Pool{
			{
				GiftId: 1,
				Enable: true,
				Total:  2000,
				Prizes: []Prize{
					{Mul: 2, Avg: 2, Random: 2 - 1},
					{Mul: 3, Avg: 2, Random: 3 - 1},
					{Mul: 4, Avg: 2, Random: 3 - 1},
					{Mul: 5, Avg: 2, Random: 3 - 1},
					{Mul: 6, Avg: 2, Random: 6 - 1},
				},
			},
			{
				GiftId: 1,
				Enable: true,
				Total:  1000,
				Prizes: []Prize{
					{Mul: 2, Avg: 1, Random: 2},
					{Mul: 3, Avg: 1, Random: 3},
					{Mul: 4, Avg: 1, Random: 3},
					{Mul: 5, Avg: 1, Random: 3},
					{Mul: 6, Avg: 1, Random: 6},
				},
			},
		},
	}

	slots, _ := cfg.GenSlots(time.Now())

	// Perform a large number of draws
	draws := 100000
	step := 0
	var totalMul decimal.Decimal
	for i := 0; i < draws; i++ {
		walked, prizes := slots.Walk(step%slots.Total, 1)
		for _, prize := range prizes {
			totalMul = totalMul.Add(decimal.NewFromInt(int64(prize.Mul)))
		}

		step += walked
	}

	actualReturnProbability := totalMul.Div(decimal.NewFromInt(int64(draws)))

	expectedReturnProbability := slots.CalcReturnRate()
	t.Logf("expected return probability: %s, actual return probability: %s", expectedReturnProbability, actualReturnProbability)
	if !actualReturnProbability.Equal(expectedReturnProbability) {
		t.Fail()
	}
}

// 生成测试数据
func generateTestData(totalSlots int) []Prize {
	return []Prize{
		{
			Avg:    totalSlots / 4,
			Random: totalSlots / 8,
		},
		{
			Avg:    totalSlots / 6,
			Random: totalSlots / 12,
		},
		{
			Avg:    totalSlots / 8,
			Random: totalSlots / 16,
		},
	}
}

// 原始版本的generateSlots函数
func generateSlotsOriginal(totalSlots int, prizes []Prize) map[int]Prize {
	var (
		out            = make(map[int]Prize)
		availableSlots = make([]int, 0)
		used           = 0
	)

	for _, p := range prizes {
		used += p.Avg + p.Random
	}

	if used > totalSlots {
		panic("total slots is not enough for prizes")
	}

	// render averaged prizes
	for i, pc := range prizes {
		if pc.Avg == 0 {
			continue
		}
		interval := totalSlots / pc.Avg
		for j := 0; j < pc.Avg; j++ {
			from, to := interval*j, interval*(j+1)

			availableSlots = availableSlots[:0]
			for pos := from; pos < to; pos++ {
				if _, occupied := out[pos]; !occupied {
					availableSlots = append(availableSlots, pos)
				}
			}

			if len(availableSlots) > 0 {
				selectedSlot := availableSlots[rng.Intn(len(availableSlots))]
				out[selectedSlot] = pc
			} else {
				prizes[i].Random++
			}
		}
	}

	// render random prizes
	for _, pc := range prizes {
		for i := 0; i < pc.Random; i++ {
			pos := rng.Intn(totalSlots)
			if _, ok := out[pos]; !ok {
				out[pos] = pc
			} else {
				i--
			}
		}
	}

	return out
}

func BenchmarkGenerateSlots(b *testing.B) {
	testCases := []struct {
		name       string
		totalSlots int
	}{
		{"Tiny", 50},
		{"Small", 100},
		{"Medium", 1000},
		{"Large", 10000},
		{"XLarge", 100000},
	}

	for _, tc := range testCases {
		prizes := generateTestData(tc.totalSlots)

		b.Run(tc.name+"_Original", func(b *testing.B) {
			b.ReportAllocs()
			for i := 0; i < b.N; i++ {
				generateSlotsOriginal(tc.totalSlots, prizes)
			}
		})

		b.Run(tc.name+"_Optimized", func(b *testing.B) {
			b.ReportAllocs()
			for i := 0; i < b.N; i++ {
				generateSlots(tc.totalSlots, prizes)
			}
		})
	}
}

// 测试内存分配
func BenchmarkGenerateSlotsMemory(b *testing.B) {
	testCases := []struct {
		name       string
		totalSlots int
	}{
		{"Small", 100},
		{"Medium", 1000},
		{"Large", 10000},
		{"XLarge", 100000},
	}

	for _, tc := range testCases {
		prizes := generateTestData(tc.totalSlots)

		b.Run(tc.name+"_Memory", func(b *testing.B) {
			b.ReportAllocs()
			for i := 0; i < b.N; i++ {
				_ = generateSlots(tc.totalSlots, prizes)
			}
		})
	}
}

// 测试并发场景下的内存分配
func BenchmarkGenerateSlotsParallelMemory(b *testing.B) {
	testCases := []struct {
		name       string
		totalSlots int
	}{
		{"Small", 100},
		{"Medium", 1000},
		{"Large", 10000},
	}

	for _, tc := range testCases {
		prizes := generateTestData(tc.totalSlots)

		b.Run(tc.name+"_ParallelMemory", func(b *testing.B) {
			b.ReportAllocs()
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					_ = generateSlots(tc.totalSlots, prizes)
				}
			})
		})
	}
}

// 测试结果验证
func TestGenerateSlots(t *testing.T) {
	testCases := []struct {
		name       string
		totalSlots int
	}{
		{"Small", 100},
		{"Medium", 1000},
		{"Large", 10000},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			prizes := generateTestData(tc.totalSlots)

			// 测试原始版本
			originalResult := generateSlotsOriginal(tc.totalSlots, prizes)
			if len(originalResult) == 0 {
				t.Errorf("Original version returned empty result")
			}

			// 测试优化版本
			optimizedResult := generateSlots(tc.totalSlots, prizes)
			if len(optimizedResult) == 0 {
				t.Errorf("Optimized version returned empty result")
			}

			// 验证两个版本的结果数量是否一致
			if len(originalResult) != len(optimizedResult) {
				t.Errorf("Result count mismatch: original=%d, optimized=%d",
					len(originalResult), len(optimizedResult))
			}
		})
	}
}
