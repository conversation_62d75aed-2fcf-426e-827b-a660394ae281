package redpacket

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var (
	ErrAlreadyGifting = biz.NewError(biz.ErrInvalidParam, "already gifting")
)

const (
	keySubGifting = "REDPACKET:GIFTING:%s" // prize id
	ttlSubGifting = time.Hour * 24 * 2
)

type GiftingTask struct {
	Id        string    `json:"id"`
	GiftId    int       `json:"giftId"`
	GiftCount int       `json:"giftCount"`
	SenderId  string    `json:"senderId"`
	SessionId string    `json:"sessionId"`
	At        time.Time `json:"at"`
}

func (m *Manager) SendPrize(ctx context.Context, id, userId string, at time.Time) error {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return biz.NewError(biz.ErrInvalidParam, "invalid prize id")
	}

	if oid.Timestamp().After(at) || oid.Timestamp().Before(at.Add(-time.Hour*24)) {
		return biz.NewError(biz.ErrInvalidParam, "prize not started or ended")
	}

	ok, err := m.rc.SetNX(ctx, fmt.Sprintf(keySubGifting, id), true, ttlSubGifting).Result()
	if err != nil {
		return fmt.Errorf("send prize: %w", err)
	}
	if !ok {
		return ErrAlreadyGifting
	}

	pz, err := m.getPrize(ctx, id)
	if err != nil {
		return err
	}

	if pz.Gifts.Count() == 0 || pz.UserId != userId {
		return biz.NewError(biz.ErrInvalidParam, "invalid prize")
	}

	pkt, err := m.GetRedPacket2(ctx, pz.PacketId)
	if err != nil {
		return err
	}

	for gid, count := range pz.Gifts {
		task := &GiftingTask{
			Id:        fmt.Sprintf("%s:%d", id, gid),
			GiftId:    gid,
			GiftCount: count,
			SenderId:  userId,
			SessionId: pkt.SessionId,
			At:        at,
		}
		if err := m.tasks.Submit(ctx, time.Millisecond*1500, task); err != nil {
			m.logger.Error("GiftingPrize: submit task failed",
				zap.Error(err),
				zap.String("id", id),
				zap.Int("giftId", gid),
				zap.Int("giftCount", count),
				zap.String("userId", pz.UserId),
				zap.String("sessionId", pkt.SessionId),
			)
		}
	}

	return nil
}
