package redpacket

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/flytext"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var (
	ErrRedPacketNotStarted    = biz.NewError(biz.ErrHongBaoNotStarted, "Wait to open")
	ErrRedPacketAlreadyClosed = biz.NewError(biz.ErrHongBaoAlreadyClosed, "Wait for the other one")
	ErrRedPacketAlreadyGrabed = biz.NewError(biz.ErrHongBaoAlreadyGrabed, "Already collected")
	ErrRedPacketGrabedOut     = biz.NewError(biz.ErrHongBaoGrabedOut, "Try next time")
)

type Manager struct {
	rc          *redi.Client
	dm          *redi.Mutex
	mc          *db.MongoClient
	rm          *room.Manager
	fm          *fund.Manager
	om          *order.Manager
	gm          *gift.Manager
	psm         *props.Manager
	imm         *im.Manager
	ug          user.Getter
	evb         ev.Bus
	tasks       dq.Queue[*GiftingTask]
	packets     cc.Cache[string, *RedPacket]
	sessPackets cc.Cache[string, []string]
	pool        co.Pool
	logger      *zap.Logger
}

type PacketConfig struct {
	RoomId       string    // 房间id
	SessionId    string    // 场次id
	Anchor       string    // 主播
	Sender       string    // 发红包的人
	GiftPacketId int       // 礼物红包id
	Coins        int       // 金币红包的金币
	Count        int       // 红包数量
	Countdown    int       // 倒计时秒数
	Condition    Condition // 条件
	GlobalNotify bool      // 是否全局通知
}

func (c *PacketConfig) Type() Type {
	if c.Coins > 0 {
		return TypeCoin
	}
	return TypeGift
}

func (m *Manager) CreateRedPacket(ctx context.Context, c *PacketConfig, at time.Time) (*RedPacket, error) {
	oid, err := primitive.ObjectIDFromHex(c.SessionId)
	if err != nil {
		return nil, err
	}

	var (
		cfg       = Preset()
		sessionAt = oid.Timestamp().Local()
		grabAt    = at.Add(time.Duration(c.Countdown) * time.Second).Round(time.Millisecond * 500)
	)

	packet := &RedPacket{
		Id:          primitive.NewObjectIDFromTimestamp(sessionAt),
		SessionId:   c.SessionId,
		Type:        c.Type(),
		Anchor:      c.Anchor,
		Sender:      c.Sender,
		Count:       c.Count,
		Condition:   c.Condition,
		Countdown:   c.Countdown,
		GrabStartAt: grabAt,
		GrabEndAt:   grabAt.Add(grabDuration),
		Status:      StatusInit,
		CreatedAt:   at,
		UpdatedAt:   at,
	}

	if packet.Type == TypeGift {
		gp := cfg.GiftPacketById(c.GiftPacketId)
		if gp == nil {
			return nil, errors.New("gift packet id not found")
		}

		packet.Gifts = gp.Gifts

		for _, item := range gp.Gifts {
			gft, err := m.gm.GiftById(item.GiftId)
			if err != nil {
				return nil, fmt.Errorf("gift by id %d: %w", item.GiftId, err)
			}

			packet.Value += gft.Diamond * item.Count
		}
	} else {
		cp := cfg.CoinPacketByCoin(c.Coins)
		if cp == nil {
			return nil, errors.New("coin packet not found")
		}

		if !slices.Contains(cp.Counts, c.Count) {
			return nil, errors.New("coin packet count not found")
		}

		packet.Coins = c.Coins
		packet.Value = c.Coins
	}

	if packet.Value < GloNotifyMinDiamonds && c.GlobalNotify {
		return nil, errors.New("global notify is not supported for small redpacket")
	}

	if packet.Value < CondMinDiamonds && c.Condition != ConditionNone {
		return nil, errors.New("condition is not supported for small redpacket")
	}

	prizes, err := m.makePrizePool(packet, at)
	if err != nil {
		return nil, fmt.Errorf("make prize pool: %w", err)
	}

	if err := m.mc.TryTxn(ctx, func(ctx context.Context) error {
		if _, err := m.mc.Collection(CollectionRedPacket(sessionAt)).InsertOne(ctx, packet); err != nil {
			return fmt.Errorf("insert redpacket: %w", err)
		}

		if _, err := m.mc.Collection(CollectionPrize(sessionAt)).InsertMany(ctx, lo.ToAnySlice(prizes)); err != nil {
			return fmt.Errorf("insert prizes: %w", err)
		}

		if err := m.fm.Expend(ctx,
			c.Sender, fund.JTypeOthers, fund.PTypeDiamond, packet.Value, fund.WithTime(at), fund.WithDetail("Enviar Hongbao"), fund.WithTrade(packet.Id.Hex())); err != nil {
			return fmt.Errorf("expend redpacket: %w", err)
		}

		return nil
	}); err != nil {
		return nil, err
	}

	m.addSessionPacket(ctx, c.SessionId, packet.Id.Hex())

	logger := m.logger.With(
		zap.String("id", packet.Id.Hex()),
		zap.String("sessionId", c.SessionId),
		zap.String("sender", c.Sender),
		zap.Time("grabStartAt", grabAt),
		zap.Time("grabEndAt", grabAt.Add(grabDuration)),
		zap.Int("value", packet.Value),
		zap.Any("condition", c.Condition),
		zap.Any("globalNotify", c.GlobalNotify),
		zap.Any("prizes", prizes),
	)

	logger.Info("create redpacket done")

	if c.GlobalNotify {
		m.rm.Post(func() {
			dict := flytext.NewDict().
				AddDefValue("user", m.rm.MustBuildUser(ctx, c.Sender).Nickname).
				AddDefValue("anchor", m.rm.MustBuildUser(ctx, c.Anchor).Nickname).
				AddDefValue("roomId", c.RoomId)

			if err := m.rm.SendGlobalFlyText(ctx, "flytext_redpacket2", dict); err != nil {
				logger.Error("send redpacket notify failed", zap.Error(err))
			} else {
				m.logger.Info("send redpacket notify ok")
			}
		})
	}

	m.evb.Emit(ctx, evt.EvSendRedpacket, &evt.UserSendRedpacket{
		AnchorId: c.Anchor,
		UserId:   c.Sender,
		Value:    packet.Value,
		At:       at,
	})

	return packet, nil
}

func (m *Manager) getRedPacket(ctx context.Context, id string) (*RedPacket, error) {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var out RedPacket
	if err := m.mc.Collection(CollectionRedPacket(oid.Timestamp().Local())).FindOne(ctx, bson.M{"_id": oid}).Decode(&out); err != nil {
		return nil, fmt.Errorf("get redpacket: %w", err)
	}

	return &out, nil
}

func (m *Manager) GetRedPacket2(ctx context.Context, id string) (*RedPacket, error) {
	return m.packets.Get(id)
}

func (m *Manager) SessionPackets(ctx context.Context, sessionId string, limit int64, at time.Time) ([]*RedPacket, error) {
	oid, err := primitive.ObjectIDFromHex(sessionId)
	if err != nil {
		return nil, err
	}

	cur, err := m.mc.Collection(CollectionRedPacket(oid.Timestamp().Local())).Find(
		ctx,
		bson.M{
			"sessionId": sessionId,
			"status":    StatusInit,
		},
		options.Find().SetSort(bson.M{"grabEndAt": 1}).SetLimit(limit),
	)
	if err != nil {
		return nil, err
	}

	var out []*RedPacket
	if err := cur.All(ctx, &out); err != nil {
		return nil, err
	}

	return out, nil
}

func (m *Manager) Grabbed(ctx context.Context, packetId string, userId string) (bool, error) {
	v, err := m.rc.ZScore(ctx, fmt.Sprintf(keyPacketGrabUserList, packetId), userId).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil
		}

		return false, err
	}

	return v != 0, nil
}

func (m *Manager) UserSessionGrabbed(ctx context.Context, userId, sessionId string) ([]string, error) {
	v, err := m.rc.ZRange(ctx, fmt.Sprintf(keyUserSessionGrabbed, userId, sessionId), 0, -1).Result()
	if err != nil {
		return nil, fmt.Errorf("list grabbed: %w", err)
	}

	return v, nil
}

func (m *Manager) Grab(ctx context.Context, packetId string, userId string, at time.Time) (*Prize, error) {
	packet, err := m.GetRedPacket2(ctx, packetId)
	if err != nil {
		return nil, fmt.Errorf("get redpacket: %w", err)
	}

	if at.Before(packet.GrabStartAt) {
		return nil, ErrRedPacketNotStarted
	}

	if at.After(packet.GrabEndAt) {
		return nil, ErrRedPacketAlreadyClosed
	}

	if packet.Status != StatusInit {
		return nil, ErrRedPacketGrabedOut
	}

	var (
		keyPacketGrabbed      = fmt.Sprintf(keyPacketGrabUserList, packetId)
		keyUserSessionGrabbed = fmt.Sprintf(keyUserSessionGrabbed, userId, packet.SessionId)
		keyUserDayGrabbed     = fmt.Sprintf(keyUserDayGrabbed, userId)
	)

	tx := m.rc.Pipeline()

	res := tx.ZAdd(ctx, keyPacketGrabbed, redis.Z{Score: float64(at.Unix()), Member: userId})
	tx.Expire(ctx, keyPacketGrabbed, ttlGrabUserList)

	tx.ZAdd(ctx, keyUserSessionGrabbed, redis.Z{Score: float64(at.Unix()), Member: packetId})
	tx.Expire(ctx, keyUserSessionGrabbed, ttlGrabUserList)

	tx.ZAdd(ctx, keyUserDayGrabbed, redis.Z{Score: float64(at.Unix()), Member: packetId})
	tx.Expire(ctx, keyUserDayGrabbed, ttlUserDayGrabbed)
	tx.ZRemRangeByScore(ctx, keyUserDayGrabbed, "-inf", strconv.Itoa(int(at.Add(-ttlUserDayGrabbed).Unix())))

	if _, err := tx.Exec(ctx); err != nil {
		return nil, fmt.Errorf("grab redpacket: %w", err)
	}

	if res.Val() == 0 {
		return nil, ErrRedPacketAlreadyGrabed
	}

	var prize *Prize

	defer func() {
		m.logger.Info("grab redpacket", zap.String("packetId", packetId), zap.String("userId", userId), zap.Any("prize", prize))
	}()

	if err := m.mc.Collection(CollectionPrize(packet.Id.Timestamp().Local())).FindOneAndUpdate(
		ctx,
		bson.M{"packetId": packetId, "userId": ""},
		bson.M{"$set": bson.M{"userId": userId, "waitAck": true, "updatedAt": at}},
		options.FindOneAndUpdate().SetSort(bson.M{"last": 1}).SetReturnDocument(options.After),
	).Decode(&prize); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrRedPacketGrabedOut
		}
		return nil, fmt.Errorf("find one and update prize: %w", err)
	}

	return prize, nil
}

func (m *Manager) Winners(ctx context.Context, packetId string) ([]*Prize, error) {
	oid, err := primitive.ObjectIDFromHex(packetId)
	if err != nil {
		return nil, err
	}

	cur, err := m.mc.Collection(CollectionPrize(oid.Timestamp().Local())).Find(ctx, bson.M{"packetId": packetId, "userId": bson.M{"$ne": ""}})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var out []*Prize
	if err := cur.All(ctx, &out); err != nil {
		return nil, err
	}

	return out, nil
}

type PrizeItem struct {
	Coins int     `json:"c,omitempty"`
	Gifts GiftMap `json:"g,omitempty"`
}

func (m *Manager) makePrizePool(pkt *RedPacket, at time.Time) ([]*Prize, error) {
	prizes := make([]*Prize, 0, pkt.Count)
	if pkt.Type == TypeGift {
		chunks := ChunkCoinsV(pkt.Gifts.Count(), pkt.Count, pkt.Gifts.Top2PU(pkt.Count))
		for i, gifts := range ChunkGifts(pkt.Gifts, chunks) {
			value := 0
			for id, cnt := range gifts {
				gft, err := m.gm.GiftById(id)
				if err != nil {
					return nil, fmt.Errorf("gift by id %d: %w", id, err)
				}

				value += gft.Diamond * cnt
			}

			prizes = append(prizes, newPrize(pkt, 0, gifts, value, i == len(chunks)-1, at))
		}
	} else {
		chunks := ChunkCoinsV(pkt.Coins, pkt.Count, 1)
		for i, chunk := range chunks {
			prizes = append(prizes, newPrize(pkt, chunk, nil, chunk, i == len(chunks)-1, at))
		}
	}

	return prizes, nil
}

func (m *Manager) markPacketDone(ctx context.Context, id string, at time.Time) (*RedPacket, error) {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var pkt *RedPacket
	if err := m.mc.Collection(CollectionRedPacket(oid.Timestamp().Local())).FindOneAndUpdate(
		ctx,
		bson.M{"_id": oid},
		bson.M{"$set": bson.M{"status": StatusDone, "updatedAt": at}},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&pkt); err != nil {
		return nil, fmt.Errorf("update redpacket: %w", err)
	}

	m.remSessionPacket(ctx, pkt.SessionId, pkt.Id.Hex())

	m.packets.Remove(pkt.Id.Hex())

	m.logger.Info("mark packet done",
		zap.String("packetId", pkt.Id.Hex()),
		zap.String("sender", pkt.Sender),
		zap.String("anchor", pkt.Anchor),
		zap.Int("value", pkt.Value),
		zap.Int("refund", pkt.Refund),
	)

	return pkt, nil
}

func (m *Manager) getPrize(ctx context.Context, id string) (*Prize, error) {
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var out Prize
	if err := m.mc.Collection(CollectionPrize(oid.Timestamp().Local())).FindOne(ctx, bson.M{"_id": oid}).Decode(&out); err != nil {
		return nil, fmt.Errorf("getPrize: %w", err)
	}
	return &out, err
}

func (m *Manager) UserDayGrabbed(ctx context.Context, userId string) ([]string, error) {
	v, err := m.rc.ZRange(ctx, fmt.Sprintf(keyUserDayGrabbed, userId), 0, -1).Result()
	if err != nil {
		return nil, fmt.Errorf("list grabbed: %w", err)
	}

	return v, nil
}
