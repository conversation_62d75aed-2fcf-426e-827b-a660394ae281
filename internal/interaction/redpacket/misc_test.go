package redpacket

import (
	"context"
	"fmt"
	"math"
	"sync"
	"testing"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

func TestChunk2(t *testing.T) {
	res := ChunkCoinsV(100000, 100, 1)
	t.Log(res)
	sum := 0
	for _, v := range res {
		sum += v
	}
	t.Log(sum)
	if sum != 100000 {
		t.Errorf("sum = %v, want %v", sum, 100000)
	}
}

func FuzzChunkCoins(f *testing.F) {
	f.Add(10, 5)
	f.Fuzz(func(t *testing.T, total int, c int) {
		got := ChunkCoinsV(total, c, 1)
		if got != nil && len(got) != c {
			t.<PERSON>rrorf("ChunkCoinsV(%v, %v) = %v, expected length %v", total, c, got, c)
		}
		if got != nil {
			sum := 0
			for _, v := range got {
				sum += v
			}
			if sum != total {
				t.Errorf("sum = %v, want %v", sum, total)
			}
		}
	})
}

func TestChunkGifts(t *testing.T) {
	for range 100 {
		var (
			pkt    = lo.Sample(defaultConfig.GiftPacket)
			cnt    = lo.Sample(pkt.Counts)
			g1Id   = pkt.Gifts[0].GiftId
			g2Id   = pkt.Gifts[1].GiftId
			top2PU = pkt.Gifts.Top2PU(cnt)
		)

		if cnt > pkt.Gifts.Count() {
			cnt = pkt.Gifts.Count()
		}

		chunks := ChunkCoinsV(pkt.Gifts.Count(), cnt, top2PU)

		// assign gifts to chunks
		prizes := make([]PrizeItem, 0, cnt)
		for _, gifts := range ChunkGifts(pkt.Gifts, chunks) {
			prizes = append(prizes, PrizeItem{Gifts: gifts})
		}

		if len(prizes) != cnt {
			t.Errorf("len(prizes) = %v, want %v", len(prizes), cnt)
		}

		var total int
		for _, prize := range prizes {
			for _, v := range prize.Gifts {
				total += v
			}
		}

		if total != pkt.Gifts.Count() {
			t.Errorf("total = %v, want %v", total, pkt.Gifts.Count())
		}

		// 检查是否超过max
		for i, prize := range prizes {
			top2 := prize.Gifts[g1Id] + prize.Gifts[g2Id]
			if top2 > top2PU {
				t.Errorf("top2 = %v, want %v", top2, top2PU)
			}

			if prize.Gifts.Count() != chunks[i] {
				t.Errorf("prize.Gifts.Count() = %v, want %v", prize.Gifts.Count(), chunks[i])
			}
		}
	}
}

func BenchmarkChunkGifts(b *testing.B) {
	for range b.N {
		var (
			pkt = lo.Sample(defaultConfig.GiftPacket)
			cnt = lo.Sample(pkt.Counts)
		)

		if cnt > pkt.Gifts.Count() {
			cnt = pkt.Gifts.Count()
		}

		chunks := ChunkCoinsV(pkt.Gifts.Count(), cnt, pkt.Gifts.Top2PU(cnt))
		_ = ChunkGifts(pkt.Gifts, chunks)
	}
}

func TestTop2PU(t *testing.T) {
	var (
		cnt = 22
		n   = 5
	)

	t.Log(max(int(math.Ceil(float64(cnt)/float64(n))), 2))
}

var testMod = fx.Module("db", ctl.DB)

func TestGrab(t *testing.T) {
	fx2.Testing(t, testMod, func(mc *db.MongoClient) error {
		for range 1000 {
			grab(t, mc, lo.Sample([]int{10, 11}))
		}
		return nil
	})
}

func grab(t *testing.T, mc *db.MongoClient, size int) {
	var (
		ctx        = context.Background()
		collection = mc.Collection("redpacket.prize.test")
		pktId      = primitive.NewObjectID()
	)

	var prizes []*Prize
	for i := range size {
		prizes = append(prizes, &Prize{
			Id:       primitive.NewObjectID(),
			PacketId: pktId.Hex(),
			Last:     i == size-1,
		})
	}

	if _, err := collection.InsertMany(ctx, lo.ToAnySlice(prizes)); err != nil {
		t.Fatal(err)
	}

	// 模拟size-1个人去抢
	var (
		simulated = size - 1
		wg        sync.WaitGroup
		start     = make(chan struct{})
		results   = make(chan *Prize, simulated)
	)

	for i := range simulated {
		wg.Add(1)

		userId := fmt.Sprintf("user_%d", i)
		go func() {
			defer wg.Done()

			<-start

			var prize *Prize
			if err := collection.FindOneAndUpdate(
				ctx,
				bson.M{"packetId": pktId.Hex(), "userId": ""},
				bson.M{"$set": bson.M{"userId": userId, "waitAck": true, "updatedAt": time.Now()}},
				options.FindOneAndUpdate().SetSort(bson.M{"last": 1}).SetReturnDocument(options.After),
			).Decode(&prize); err != nil {
				fmt.Printf("find one and update prize: %v\n", err)
			}

			results <- prize
		}()
	}

	close(start)

	wg.Wait()
	close(results)

	var errored bool
	for prize := range results {
		if prize.Last {
			errored = true
		}
	}

	if !errored {
		t.Logf("[SUCCESS] size: %d, simSize: %d", size, simulated)
	} else {
		t.Logf("[FAILED] size: %d, simSize: %d", size, simulated)
	}
}
