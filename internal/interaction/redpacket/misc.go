package redpacket

import (
	"gitlab.sskjz.com/go/rng"
)

// 把total随机分成c份，保证每份至少为1
func ChunkCoinsV(total, count, minC int) []int {
	if total < count || count <= 0 {
		return nil
	}

	chunks := make([]int, count)
	remaining := total

	// 每个人至少得到1分钱
	minAmount := minC

	for i := 0; i < count-1; i++ {
		// 剩余待分配的人数
		restPeople := count - i - 1

		// 为每个人预留最小金额
		maxAmount := remaining - restPeople*minAmount

		// 计算当前红包的金额
		// 2*mean 即为期望的2倍，是微信的算法参考值
		mean := maxAmount / restPeople
		max := 2 * mean
		if max < minAmount {
			max = minAmount
		}

		amount := rng.Intn(max) + minAmount
		if amount > maxAmount {
			amount = maxAmount
		}

		chunks[i] = amount
		remaining -= amount
	}

	// 最后一个红包分配剩余金额
	chunks[count-1] = remaining

	return chunks
}

func ChunkGifts(gifts GiftItems, chunks []int) []GiftMap {
	var (
		gCounts = gifts.Map()
		top2PU  = gifts.Top2PU(len(chunks))
		t1Id    = gifts[0].GiftId
		t2Id    = gifts[1].GiftId
	)

	result := make([]GiftMap, len(chunks))
	for i := range result {
		result[i] = make(GiftMap)
	}

	slots := make([]int, len(chunks))
	for i := range slots {
		slots[i] = i
	}

	// 先分配top2的礼物，确保每个chunk中top2的礼物数量<=top2PU
	for _, g := range gifts[:2] {
		for gCounts[g.GiftId] > 0 {
			if len(slots) == 0 {
				panic("slots is empty")
			}

			var (
				si    = rng.Intn(len(slots))
				index = slots[si]
			)

			if result[index][t1Id]+result[index][t2Id] < top2PU {
				result[index][g.GiftId]++
				gCounts[g.GiftId]--

				if result[index].Count() == chunks[index] {
					slots = append(slots[:si], slots[si+1:]...)
				}
			} else {
				slots = append(slots[:si], slots[si+1:]...)
			}
		}
	}

	// 再分配剩余的礼物
	for _, g := range gifts[2:3] {
		for gCounts[g.GiftId] > 0 {
			if index := rng.Intn(len(chunks)); result[index].Count() < chunks[index] {
				result[index][g.GiftId]++
				gCounts[g.GiftId]--
			}
		}
	}

	return result
}
