package redpacket

import (
	"context"
	"fmt"

	"go.uber.org/zap"
)

func (m *Manager) addSessionPacket(ctx context.Context, sessionId, pktId string) {
	k := fmt.Sprintf(keySessionPackets, sessionId)

	tx := m.rc.Pipeline()
	tx.SAdd(ctx, k, pktId)
	tx.Expire(ctx, k, ttlSessionPackets)

	if _, err := tx.Exec(ctx); err != nil {
		m.logger.Error("push session packet",
			zap.Error(err),
			zap.String("sessionId", sessionId),
			zap.String("pktId", pktId),
		)
	} else {
		m.logger.Info("push session packet",
			zap.String("sessionId", sessionId),
			zap.String("pktId", pktId),
		)
		m.sessPackets.Remove(sessionId)
	}
}

func (m *Manager) remSessionPacket(ctx context.Context, sessionId, pktId string) {
	k := fmt.Sprintf(keySessionPackets, sessionId)
	if err := m.rc.SRem(ctx, k, pktId).Err(); err != nil {
		m.logger.Error("remove session packet",
			zap.Error(err),
			zap.String("sessionId", sessionId),
			zap.String("pktId", pktId),
		)
	} else {
		m.logger.Info("remove session packet",
			zap.String("sessionId", sessionId),
			zap.String("pktId", pktId),
		)
		m.sessPackets.Remove(sessionId)
	}
}

func (m *Manager) getSessionPacketIds(ctx context.Context, sessionId string) ([]string, error) {
	v, err := m.rc.SMembers(ctx, fmt.Sprintf(keySessionPackets, sessionId)).Result()
	if err != nil {
		return nil, fmt.Errorf("get session packets: %w", err)
	}
	return v, nil
}

func (m *Manager) SessionPacketIds(ctx context.Context, sessionId string) ([]string, error) {
	return m.sessPackets.Get(sessionId)
}
