package gifting

import (
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/redpacket"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Invoke(rc *redi.Client, dm *dq.Master, rm *redpacket.Manager, im *interact.Manager, lm *live.Manager, vnd log.Vendor) {
	if !env.APIServer() {
		return
	}

	s := &Sender{
		rc:     rc,
		im:     im,
		rm:     rm,
		lm:     lm,
		q:      dq.NewWith[*redpacket.GiftingTask](dm, redpacket.GiftingTopic),
		logger: vnd.<PERSON>ope("redpacket.sender"),
	}

	s.q.Register(s.Send, dq.LogCost("redpacket.send"))
}
