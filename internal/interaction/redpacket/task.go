package redpacket

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (m *Manager) processFastDone(ctx context.Context, at time.Time) error {
	cur, err := m.mc.Collection(CollectionPrize(at.Local())).Find(ctx, bson.M{"waitAck": true, "last": true, "done": true})
	if err != nil {
		return fmt.Errorf("find redpacket: %w", err)
	}

	defer cur.Close(ctx)

	var docs []*Prize
	if err := cur.All(ctx, &docs); err != nil {
		return fmt.Errorf("find redpacket: %w", err)
	}

	for _, doc := range docs {
		if err := m.fastDone(ctx, doc, at); err != nil {
			return fmt.Errorf("redpacket fast done: %w", err)
		} else {
			m.packets.Remove(doc.PacketId)
		}
	}
	return nil
}

func (m *Manager) fastDone(ctx context.Context, doc *Prize, at time.Time) error {
	pkt, err := m.markPacketDone(ctx, doc.PacketId, at)
	if err != nil {
		return fmt.Errorf("mark packet done: %w", err)
	}

	if _, err := m.mc.Collection(CollectionPrize(doc.Id.Timestamp().Local())).UpdateOne(
		ctx,
		bson.M{"_id": doc.Id, "waitAck": true},
		bson.M{"$set": bson.M{"waitAck": false, "updatedAt": at}},
	); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return fmt.Errorf("update prize: %w", err)
		}
	}

	m.logger.Info("redpacket fast done",
		zap.String("packetId", pkt.Id.Hex()),
		zap.String("sender", pkt.Sender),
		zap.String("anchor", pkt.Anchor),
		zap.Int("value", pkt.Value),
		zap.Int("refund", pkt.Refund),
	)

	return nil
}

func (m *Manager) processExpriedDone(ctx context.Context, at time.Time) error {
	cur, err := m.mc.Collection(CollectionRedPacket(at.Local())).Find(ctx, bson.M{"status": StatusInit, "grabEndAt": bson.M{"$lt": at}})
	if err != nil {
		return fmt.Errorf("find redpacket: %w", err)
	}

	defer cur.Close(ctx)

	if cur.RemainingBatchLength() == 0 {
		return nil
	}

	var docs []*RedPacket
	if err := cur.All(ctx, &docs); err != nil {
		return fmt.Errorf("find redpacket: %w", err)
	}

	for _, pkt := range docs {
		if err := m.expriredDone(ctx, pkt, at); err != nil {
			return fmt.Errorf("transform refund: %w", err)
		} else {
			m.packets.Remove(pkt.Id.Hex())
		}
	}

	return nil
}

func (m *Manager) expriredDone(ctx context.Context, pkt *RedPacket, at time.Time) error {
	prizes, err := m.mc.Collection(CollectionPrize(pkt.Id.Timestamp().Local())).Find(ctx, bson.M{"packetId": pkt.Id.Hex(), "userId": ""})
	if err != nil {
		return fmt.Errorf("find prizes: %w", err)
	}

	defer prizes.Close(ctx)

	var docs []*Prize

	if err := prizes.All(ctx, &docs); err != nil {
		return fmt.Errorf("find prizes: %w", err)
	}

	refund := 0
	for _, doc := range docs {
		refund += doc.Value
	}

	var done bool
	if err := m.mc.TryTxn(ctx, func(ctx context.Context) error {
		for _, p := range docs {
			if _, err := m.mc.Collection(CollectionPrize(p.Id.Timestamp().Local())).DeleteOne(ctx, bson.M{"_id": p.Id, "userId": ""}); err != nil {
				return fmt.Errorf("delete prize: %w", err)
			}
		}

		ur, err := m.mc.Collection(CollectionRedPacket(pkt.Id.Timestamp().Local())).UpdateOne(
			ctx,
			bson.M{"_id": pkt.Id, "status": StatusInit},
			bson.M{
				"$set": bson.M{
					"refund":    refund,
					"status":    lo.Ternary(refund > 0, StatusRefunding, StatusDone),
					"updatedAt": at,
				},
			},
		)

		if err != nil {
			return fmt.Errorf("update redpacket: %w", err)
		}

		if ur.ModifiedCount == 0 {
			return nil
		}

		done = true

		return nil
	}); err != nil {
		return fmt.Errorf("transform refund: %w", err)
	}

	if !done {
		return nil
	}

	m.remSessionPacket(ctx, pkt.SessionId, pkt.Id.Hex())

	m.logger.Info("try done",
		zap.String("id", pkt.Id.Hex()),
		zap.String("sender", pkt.Sender),
		zap.String("anchor", pkt.Anchor),
		zap.Int("value", pkt.Value),
		zap.Int("refund", refund),
	)
	return nil
}

func (m *Manager) processSendPrize(ctx context.Context, cTime, at time.Time) {
	cur, err := m.mc.Collection(CollectionPrize(cTime)).Find(
		ctx,
		bson.M{"done": false, "userId": bson.M{"$ne": ""}},
		options.Find().SetSort(bson.M{"createdAt": 1}),
	)
	if err != nil {
		m.logger.Error("find redpacket log1", zap.Error(err))
		return
	}

	defer cur.Close(ctx)

	if cur.RemainingBatchLength() == 0 {
		return
	}

	var docs []*Prize
	if err := cur.All(ctx, &docs); err != nil {
		m.logger.Error("find redpacket log2", zap.Error(err))
		return
	}

	var wg sync.WaitGroup
	for _, doc := range docs {
		wg.Add(1)
		m.pool.Submit(func() {
			defer wg.Done()

			if err := m.sendPrize(ctx, doc, at); err != nil {
				m.logger.Error("send prize",
					zap.Error(err),
					zap.String("id", doc.Id.Hex()),
					zap.String("packetId", doc.PacketId),
					zap.String("sender", doc.Sender),
					zap.String("userId", doc.UserId),
					zap.Int("coins", doc.Coins),
					zap.Any("gifts", doc.Gifts),
				)
			} else {
				m.packets.Remove(doc.PacketId)
			}
		})
	}

	wg.Wait()
	if len(docs) > 0 {
		m.logger.Info("process send prize redpacket log", zap.Int("count", len(docs)), zap.Duration("duration", time.Since(at)))
	}
}

func (m *Manager) sendPrize(ctx context.Context, doc *Prize, at time.Time) error {
	logger := m.logger.WithLazy(
		zap.String("id", doc.Id.Hex()),
		zap.String("packetId", doc.PacketId),
		zap.String("sender", doc.Sender),
		zap.String("userId", doc.UserId),
		zap.Int("coins", doc.Coins),
		zap.Any("gifts", doc.Gifts),
	)

	if err := m.mc.TryTxn(ctx, func(ctx context.Context) error {
		if ur, err := m.mc.Collection(CollectionPrize(doc.Id.Timestamp().Local())).UpdateOne(
			ctx,
			bson.M{"_id": doc.Id, "done": false},
			bson.M{"$set": bson.M{"done": true, "updatedAt": at}},
		); err != nil {
			return fmt.Errorf("update redpacket log: %w", err)
		} else if ur.ModifiedCount == 0 {
			logger.Debug("redpacket prize already done")
			return nil
		}

		if doc.Coins > 0 {
			orderId := doc.Id.Hex()
			if err := m.om.Income(
				ctx,
				tradeApp,
				orderId,
				doc.UserId,
				fund.JTypeOthers,
				fund.PTypeDiamond,
				doc.Coins,
				fund.WithTime(at),
				fund.WithDetail("Pegar Hongbao"),
				fund.WithTrade(orderId),
			); err != nil {
				if !errors.Is(err, order.ErrOrderDuplicated) {
					logger.Error("send coins failed", zap.Error(err))
					return fmt.Errorf("send coins failed: %w", err)
				}
			}
			return nil
		}

		if len(doc.Gifts) > 0 {
			for gid, cnt := range doc.Gifts {
				orderId := fmt.Sprintf("%s_%d", doc.Id.Hex(), gid)
				if err := m.psm.AddItem(ctx, at,
					doc.UserId, propc.GPropId(gid), cnt, props.WithTradeNo(orderId),
				); err != nil {
					if !errors.Is(err, props.ErrDuplicatedTradeNo) {
						logger.Error("send gifts failed", zap.Error(err), zap.Int("gid", gid), zap.Int("cnt", cnt))
						return fmt.Errorf("add item: %w", err)
					}
				}
			}
			return nil
		}

		return nil
	}); err != nil {
		return err
	}

	logger.Info("send prize ok")
	return nil
}

func (m *Manager) processRefund(ctx context.Context, cTime, at time.Time) {
	cur, err := m.mc.Collection(CollectionRedPacket(cTime)).Find(ctx, bson.M{"status": StatusRefunding})
	if err != nil {
		m.logger.Error("find redpacket", zap.Error(err))
		return
	}

	defer cur.Close(ctx)

	if cur.RemainingBatchLength() == 0 {
		return
	}

	var docs []*RedPacket
	if err := cur.All(ctx, &docs); err != nil {
		m.logger.Error("get redpacket", zap.Error(err))
		return
	}

	var wg sync.WaitGroup
	for _, doc := range docs {
		wg.Add(1)
		m.pool.Submit(func() {
			defer wg.Done()

			if err := m.refund(ctx, doc, at); err != nil {
				m.logger.Error("refund failed",
					zap.Error(err),
					zap.String("id", doc.Id.Hex()),
					zap.String("sessionId", doc.SessionId),
					zap.String("anchor", doc.Anchor),
					zap.String("sender", doc.Sender),
					zap.Int("coins", doc.Coins),
					zap.Int("count", doc.Count),
					zap.Any("gifts", doc.Gifts),
					zap.Int("refund", doc.Refund),
				)
			} else {
				m.packets.Remove(doc.Id.Hex())
			}
		})
	}

	wg.Wait()
	if len(docs) > 0 {
		m.logger.Info("process refund redpacket done", zap.Int("count", len(docs)), zap.Duration("duration", time.Since(at)))
	}
}

func (m *Manager) refund(ctx context.Context, doc *RedPacket, at time.Time) error {
	logger := m.logger.WithLazy(
		zap.String("id", doc.Id.Hex()),
		zap.String("sessionId", doc.SessionId),
		zap.String("anchor", doc.Anchor),
		zap.String("sender", doc.Sender),
		zap.Int("coins", doc.Coins),
		zap.Int("count", doc.Count),
		zap.Any("gifts", doc.Gifts),
		zap.Int("refund", doc.Refund),
	)

	if err := m.mc.TryTxn(ctx, func(ctx context.Context) error {
		ur, err := m.mc.Collection(CollectionRedPacket(doc.Id.Timestamp().Local())).UpdateOne(
			ctx,
			bson.M{"_id": doc.Id, "status": StatusRefunding},
			bson.M{"$set": bson.M{"status": StatusDone, "updatedAt": at}},
		)
		if err != nil {
			return fmt.Errorf("update redpacket: %w", err)
		}

		if ur.ModifiedCount == 0 {
			logger.Info("redpacket refund already done")
			return nil
		}

		if doc.Refund > 0 {
			orderId := fmt.Sprintf("%s_refund", doc.Id.Hex())
			if err := m.om.Income(ctx,
				tradeApp,
				orderId,
				doc.Sender,
				fund.JTypeOthers,
				fund.PTypeDiamond,
				doc.Refund,
				fund.WithTime(at),
				fund.WithDetail("Devolução de Hongbao"),
				fund.WithTrade(orderId),
			); err != nil {
				logger.Error("refund failed", zap.Error(err))
				return fmt.Errorf("refund income: %w", err)
			}
		}

		return nil
	}); err != nil {
		return err
	}

	logger.Info("refund ok")

	m.pool.Submit(func() {
		acc, err := m.ug.Account(ctx, doc.Sender)
		if err != nil {
			logger.Error("get account", zap.Error(err))
		}

		t := doc.CreatedAt.In(ctz.Timezone(acc.Timezone))

		// [Aviso do saldo] O valor que sobrou do Hong bao que você enviou em {XX:XX de xx/xx} foi {} coins e já foi devolvido ao seu saldo.
		msg := fmt.Sprintf("[Aviso do saldo] O valor que sobrou do Hong bao que você enviou em %02d:%02d de %d/%d foi %d coins e já foi devolvido ao seu saldo.",
			t.Hour(), t.Minute(), t.Day(), t.Month(), doc.Refund)

		if err := m.imm.SendSystemNoticeTextToUser(context.Background(), doc.Sender, msg); err != nil {
			logger.Error("send system notice text to user", zap.Error(err))
		}
	})

	return nil
}
