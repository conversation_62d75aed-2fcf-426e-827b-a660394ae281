package redpacket

// GiftRedPacket 礼物红包配置
type GiftRedPacket struct {
	Id     int       // 礼物红包Id
	Gifts  GiftItems // 礼物列表
	Counts []int     // 支持的红包份数
}

// CoinRedPacket 金币红包配置
type CoinRedPacket struct {
	Coins  int   // 金币总数
	Counts []int // 支持的红包份数
}

type Config struct {
	GiftPacket []GiftRedPacket
	CoinPacket []CoinRedPacket
	Countdowns []int
}

var defaultConfig = Config{
	GiftPacket: []GiftRedPacket{
		{
			Id: 5,
			Gifts: GiftItems{
				&GiftItem{GiftId: 9, Count: 3},    // Biquíni 100
				&GiftItem{GiftId: 25, Count: 8},   // cocar 50
				&GiftItem{GiftId: 26, Count: 150}, // Rosas 20
			},
			Counts: []int{5, 10, 20, 50},
		},
		{
			Id: 6,
			Gifts: GiftItems{
				&GiftItem{GiftId: 12, Count: 3},   // Sapatos 500
				&GiftItem{GiftId: 10, Count: 10},  // Coqueiro 200
				&GiftItem{GiftId: 23, Count: 150}, // Arvore 100
			},
			Counts: []int{5, 10, 20, 50},
		},
		{
			Id: 7,
			Gifts: GiftItems{
				&GiftItem{GiftId: 10034, Count: 6}, // Hi（特殊礼物） 1000
				&GiftItem{GiftId: 12, Count: 16},   // Sapatos 500
				&GiftItem{GiftId: 27, Count: 300},  // perfumao 200
			},
			Counts: []int{5, 10, 20, 50},
		},
		{
			Id: 8,
			Gifts: GiftItems{
				&GiftItem{GiftId: 10017, Count: 6},  // Brinde（特殊礼物） 5000
				&GiftItem{GiftId: 10037, Count: 20}, // Rosa（特殊礼物）2000
				&GiftItem{GiftId: 9, Count: 300},    // Anel 1000
			},
			Counts: []int{5, 10, 20, 50},
		},
	},
	CoinPacket: []CoinRedPacket{
		{
			Coins:  1000,
			Counts: []int{5, 10, 20, 50, 100},
		},
		{
			Coins:  5000,
			Counts: []int{5, 10, 20, 50, 100},
		},
		{
			Coins:  10000,
			Counts: []int{5, 10, 20, 50, 100},
		},
		{
			Coins:  50000,
			Counts: []int{5, 10, 20, 50, 100},
		},
		{
			Coins:  100000,
			Counts: []int{5, 10, 20, 50, 100},
		},
	},
	Countdowns: []int{10, 30, 60, 180},
}

func (c *Config) GiftPacketById(id int) *GiftRedPacket {
	for _, p := range c.GiftPacket {
		if p.Id == id {
			return &p
		}
	}
	return nil
}

func (c *Config) CoinPacketByCoin(coins int) *CoinRedPacket {
	for _, p := range c.CoinPacket {
		if p.Coins == coins {
			return &p
		}
	}
	return nil
}

func Preset() *Config {
	return &defaultConfig
}
