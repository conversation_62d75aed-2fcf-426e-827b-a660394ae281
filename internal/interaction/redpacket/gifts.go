package redpacket

import (
	"fmt"
	"math"
	"strings"
)

type GiftMap map[int]int

func (g GiftMap) Range(fn func(id int, cnt int)) {
	for id, cnt := range g {
		fn(id, cnt)
	}
}

func (g GiftMap) Count() int {
	count := 0
	for _, c := range g {
		count += c
	}
	return count
}

type GiftItem struct {
	GiftId int
	Count  int
}

type GiftItems []*GiftItem

func (gifts GiftItems) Count() int {
	count := 0
	for _, item := range gifts {
		count += item.Count
	}
	return count
}

func (gifts GiftItems) Map() GiftMap {
	out := make(GiftMap, len(gifts))
	for _, gift := range gifts {
		out[gift.GiftId] = gift.Count
	}
	return out
}

func (gifts GiftItems) Range(fn func(id int, cnt int)) {
	for _, gift := range gifts {
		fn(gift.GiftId, gift.Count)
	}
}

func (gifts GiftItems) Top2PU(n int) int {
	return max(int(math.Ceil(float64((gifts[0].Count+gifts[1].Count))/float64(n))), 2)
}

func (gifts GiftItems) String() string {
	var out []string
	for _, gift := range gifts {
		out = append(out, fmt.Sprintf("%d:%d", gift.GiftId, gift.Count))
	}
	return strings.Join(out, ",")
}
