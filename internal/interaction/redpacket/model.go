package redpacket

import (
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CollectionRedPacket(t time.Time) string {
	return fmt.Sprintf("redpacket2.packets.%s", t.Local().Format("200601"))
}

func CollectionPrize(t time.Time) string {
	return fmt.Sprintf("redpacket2.prizes.%s", t.Local().Format("200601"))
}

type Type int

const (
	TypeCoin Type = 1 // 金币红包
	TypeGift Type = 2 // 礼物红包
)

// 领取条件
type Condition int

const (
	ConditionNone         Condition = 0 // 无条件
	ConditionFollowAnchor Condition = 1 // 关注主播
	ConditionFollowSender Condition = 2 // 关注发红包的人
)

type CoinPacket struct {
	Coins int `json:"coins"` // 金币数
	Count int `json:"count"` // 总份数
}

type GiftPacket struct {
	Gifts map[int]int `json:"gifts"` // 礼物id和数量
}

type Status int

const (
	StatusInit      Status = 0
	StatusRefunding Status = 1
	StatusDone      Status = 2
)

type RedPacket struct {
	Id          primitive.ObjectID `bson:"_id"`
	SessionId   string             `bson:"sessionId"`
	Type        Type               `bson:"type"`
	Coins       int                `bson:"coins"`
	Gifts       GiftItems          `bson:"gifts"`
	Value       int                `bson:"value"` // 红包价值
	Count       int                `bson:"count"` // 真实红包份数
	Condition   Condition          `bson:"condition"`
	Countdown   int                `bson:"countdown"`   // 倒计时，单位：秒
	Anchor      string             `bson:"anchor"`      // 主播
	Sender      string             `bson:"sender"`      // 发红包的人
	GrabStartAt time.Time          `bson:"grabStartAt"` // 可以抢的时间
	GrabEndAt   time.Time          `bson:"grabEndAt"`   // 抢完的时间, 之后需要处理退款
	Refund      int                `bson:"refund"`      // 需要退款的金额
	Status      Status             `bson:"status"`      // 状态
	CreatedAt   time.Time          `bson:"createdAt"`   // 创建时间
	UpdatedAt   time.Time          `bson:"updatedAt"`   // 更新时间
}

func (r *RedPacket) MustFollow() string {
	if r.Condition == ConditionNone {
		return ""
	}

	if r.Condition == ConditionFollowAnchor {
		return r.Anchor
	}

	if r.Condition == ConditionFollowSender {
		return r.Sender
	}

	return ""
}

type Prize struct {
	Id        primitive.ObjectID `bson:"_id"` // id 时间跟随红包
	PacketId  string             `bson:"packetId"`
	UserId    string             `bson:"userId"`
	Sender    string             `bson:"sender"` // 红包来源用户
	Coins     int                `bson:"coins"`
	Gifts     GiftMap            `bson:"gifts"`
	Value     int                `bson:"value"`   // 红包价值
	Last      bool               `bson:"last"`    // 是否是这个红包最后的一份
	WaitAck   bool               `bson:"waitAck"` // 是否等待确认, 如果为true, 调度器需要确认红包状态
	Done      bool               `bson:"done"`    // 是否已发放
	UpdatedAt time.Time          `bson:"updatedAt"`
	CreatedAt time.Time          `bson:"createdAt"`
}

func newPrize(pkt *RedPacket, coins int, gifts GiftMap, value int, last bool, at time.Time) *Prize {
	return &Prize{
		Id:        primitive.NewObjectIDFromTimestamp(pkt.Id.Timestamp()),
		PacketId:  pkt.Id.Hex(),
		Sender:    pkt.Sender,
		UserId:    "",
		Coins:     coins,
		Gifts:     gifts,
		Value:     value,
		Last:      last,
		WaitAck:   false,
		Done:      false,
		UpdatedAt: at,
		CreatedAt: at,
	}
}

var packetIndexer = []db.Indexer{
	{
		Name: "idx_session",
		Keys: bson.D{{Key: "sessionId", Value: 1}},
	},
	{
		Name: "idx_status_session",
		Keys: bson.D{{Key: "status", Value: 1}, {Key: "sessionId", Value: 1}},
	},
	{
		Name: "idx_status_grabEndAt",
		Keys: bson.D{{Key: "status", Value: 1}, {Key: "grabEndAt", Value: 1}},
	},
}

var prizeIndexer = []db.Indexer{
	{
		Name: "idx_packetId_userId",
		Keys: bson.D{{Key: "packetId", Value: 1}, {Key: "userId", Value: 1}},
	},
	{
		Name: "idx_userId_createdAt",
		Keys: bson.D{{Key: "userId", Value: 1}, {Key: "createdAt", Value: -1}},
	},
	{
		Name: "idx_userId_done",
		Keys: bson.D{{Key: "userId", Value: 1}, {Key: "done", Value: 1}},
	},
	{
		Name: "idx_last_waitAck",
		Keys: bson.D{{Key: "last", Value: 1}, {Key: "waitAck", Value: 1}},
	},
}
