package seller

import (
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.Client, rc *redi.Client, um *user.Manager, fm *fund.Manager, ev ev.Bus, syn cc.Sync, vnd log.Vendor) (*Manager, Getter) {
	mgr := newManager(db, rc, um, fm, ev, vnd.Scope("seller.mgr"))
	mgr.initCache(syn)
	return mgr, mgr
}
