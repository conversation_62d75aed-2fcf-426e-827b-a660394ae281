package seller

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/shopspring/decimal"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

func (s *Manager) Balance(ctx context.Context, userId string) (fund.Decimal, error) {
	return s.balance(ctx, s.Take(ctx, userId))
}

func (s *Manager) balance(ctx context.Context, p *Profile) (fund.Decimal, error) {
	accId := p.UserId
	if !p.Master() {
		accId = p.Parent
	}
	ww, err := s.fm.Wallet(ctx, accId, fund.PTypeTokens)
	if err != nil {
		return fund.New(0), err
	}
	balance := ww.BVal(fund.PTypeTokens)
	if limit := p.Config.TradeLimit; limit > 0 {
		remains := fund.New(limit).Sub(fund.New(s.dayTradeAmount(ctx, time.Now(), p)))
		if remains.IsNegative() {
			remains = fund.New(0)
		}
		balance = decimal.Min(balance, remains)
	}
	return balance, nil
}

func (s *Manager) Recharge(ctx context.Context, at time.Time, userId string, amount int, remark string) error {
	return db.Transaction(ctx, s.db, func(ctx context.Context, _ *gorm.DB) error {
		if err := s.fm.Income(ctx, userId, fund.JTypeRecharge, fund.PTypeTokens, amount, fund.WithTime(at)); err != nil {
			return err
		}
		return s.onRecharge(ctx, at, userId, amount, remark)
	})
}

func (s *Manager) Deduction(ctx context.Context, at time.Time, userId string, amount int, remark string) error {
	return db.Transaction(ctx, s.db, func(ctx context.Context, _ *gorm.DB) error {
		if err := s.fm.Expend(ctx, userId, fund.JTypeRecharge, fund.PTypeTokens, amount, fund.WithTime(at)); err != nil {
			return err
		}
		return s.onDeduction(ctx, at, userId, amount, remark)
	})
}

func (s *Manager) Exchange(ctx context.Context, at time.Time, userId string, fruits, tokens int) error {
	return db.Transaction(ctx, s.db, func(ctx context.Context, _ *gorm.DB) error {
		if err := s.fm.Expend(ctx, userId, fund.JTypeExchange, fund.PTypeFruits, fruits, fund.WithTime(at), fund.WithExtra(fund.Extra{
			"prop":   fmt.Sprintf("%d", fund.PTypeTokens),
			"income": strconv.Itoa(tokens),
		})); err != nil {
			return err
		}
		if err := s.fm.Income(ctx, userId, fund.JTypeExchange, fund.PTypeTokens, tokens, fund.WithTime(at), fund.WithExtra(fund.Extra{
			"prop":   fmt.Sprintf("%d", fund.PTypeFruits),
			"expend": strconv.Itoa(fruits),
		})); err != nil {
			return err
		}
		return s.onRecharge(ctx, at, userId, tokens, "exchange")
	})
}
