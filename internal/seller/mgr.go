package seller

import (
	"context"
	"encoding/json"
	"errors"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrProfileNotExists     = errors.New("profile not exists")
	ErrProfileAlreadyExists = biz.Legacy("seller profile already exists")
)

func newManager(db *db.Client, rc *redi.Client, um *user.Manager, fm *fund.Manager, ev ev.Bus, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Profile{}, &Trade{})
	return &Manager{db: db, rc: rc, um: um, fm: fm, ev: ev, log: log}
}

type Manager struct {
	db  *db.Client
	rc  *redi.Client
	um  *user.Manager
	fm  *fund.Manager
	ev  ev.Bus
	log *zap.Logger
	cached
}

func (s *Manager) take(ctx context.Context, userId string) (*Profile, error) {
	return s.take2(ctx, "user_id = ?", userId)
}

func (s *Manager) take2(ctx context.Context, query any, args ...any) (*Profile, error) {
	var prof Profile
	if err := db.UseTx(ctx, s.db).Where(query, args...).Take(&prof).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrProfileNotExists
		}
		return nil, err
	}
	return &prof, nil
}

func (s *Manager) Create(ctx context.Context, userId, password string, invisible bool) error {
	if _, err := s.take(ctx, userId); err == nil {
		return ErrProfileAlreadyExists
	} else if !errors.Is(err, ErrProfileNotExists) {
		return err
	}

	stats, err := s.Stats(ctx, userId, TradeOut)
	if err != nil {
		return err
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Create(&Profile{
			UserId:      userId,
			TradeCount:  stats.Count,
			TradeAmount: stats.Amount,
			Password:    password,
			Invisible:   invisible,
		}).Error; err != nil {
			return err
		}
		return s.um.Update(ctx, userId, user.SetRole(user.RoleSeller))
	}); err != nil {
		return err
	}

	if _, err := s.fm.Wallet(ctx, userId, fund.PTypeTokens); err != nil {
		if !errors.Is(err, fund.ErrAccountNotFound) {
			return err
		}
		if _, err := s.fm.Create2(ctx, userId, fund.PTypeTokens); err != nil {
			return err
		}
	}

	return nil
}

func (s *Manager) Update(ctx context.Context, userId string, data map[string]any) error {
	p, err := s.take(ctx, userId)
	if err != nil {
		return err
	}
	return s.update(ctx, p, data)
}

func (s *Manager) update(ctx context.Context, p *Profile, data map[string]any) error {
	if err := db.UseTx(ctx, s.db).Model(p).Updates(data).Error; err != nil {
		return err
	}
	s.invalidCache(p.UserId)
	return nil
}

func (s *Manager) Remove(ctx context.Context, userId string) error {
	p, err := s.take(ctx, userId)
	if err != nil {
		return err
	}
	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Delete(p).Error; err != nil {
			return err
		}
		return s.um.Update(ctx, userId, user.UnsetRole(user.RoleSeller))
	}); err != nil {
		return err
	}
	s.invalidCache(userId)
	return nil
}

func (s *Manager) SetPassword(ctx context.Context, userId, old, new string) error {
	p, err := s.take(ctx, userId)
	if err != nil {
		return err
	} else if p.Password != old {
		return ErrWrongPassword
	}
	return s.update(ctx, p, map[string]any{"password": new})
}

func (s *Manager) SetConfig(ctx context.Context, userId string, modifier func(c *Config)) error {
	p, err := s.take(ctx, userId)
	if err != nil {
		return err
	}
	modifier(&p.Config)
	config, err := json.Marshal(p.Config)
	if err != nil {
		return err
	}
	return s.update(ctx, p, map[string]any{"config": config})
}
