package seller

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	keyTradePeers = "SELLER:TRADE:PEERS:%s" // userId
	ttlTradePeers = time.Hour * 24 * 30
	maxTradePeers = 100
)

func (s *Manager) RecentTradePeers(ctx context.Context, userId string) ([]string, error) {
	key := fmt.Sprintf(keyTradePeers, userId)

	list, err := s.rc.ZRevRange(ctx, key, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	if len(list) > maxTradePeers {
		list = list[:maxTradePeers]
		s.rc.ZRemRangeByRank(ctx, key, 0, maxTradePeers-int64(len(list)))
	}

	return list, nil
}

func (s *Manager) logTradePeer(ctx context.Context, at time.Time, from, to string) {
	key := fmt.Sprintf(keyTradePeers, from)

	txp := s.rc.Pipeline()
	txp.ZAdd(ctx, key, redis.Z{Score: float64(at.Unix()), Member: to})
	txp.Expire(ctx, key, ttlTradePeers)

	if _, err := txp.Exec(ctx); err != nil {
		s.log.Warn("log trade peer failed", zap.Error(err))
	}
}
