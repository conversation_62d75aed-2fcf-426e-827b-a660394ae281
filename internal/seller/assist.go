package seller

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gorm.io/gorm"
)

var (
	ErrTooManyAssistants = biz.Legacy("too many assistants")
)

func (s *Manager) ListAssistant(ctx context.Context, parent string) ([]*Profile, error) {
	var out []*Profile
	if err := db.UseTx(ctx, s.db).Where("parent = ?", parent).Find(&out).Error; err != nil {
		return nil, err
	}
	return out, nil
}

func (s *Manager) AddAssistant(ctx context.Context, parent, userId, password string, tradeLimit int) error {
	if p, err := s.take(ctx, parent); err != nil {
		return err
	} else if !p.Master() {
		return errors.New("not direct seller")
	}

	if list, err := s.ListAssistant(ctx, parent); err != nil {
		return err
	} else if len(list) >= 3 {
		return ErrTooManyAssistants
	}

	if _, err := s.take(ctx, userId); err == nil {
		return ErrProfileAlreadyExists
	} else if !errors.Is(err, ErrProfileNotExists) {
		return err
	}

	return db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Create(&Profile{
			Parent:    parent,
			UserId:    userId,
			Password:  password,
			Invisible: true,
			Config: Config{
				TradeLimit: tradeLimit,
			},
		}).Error; err != nil {
			return err
		}
		return s.um.Update(ctx, userId, user.SetRole(user.RoleSeller))
	})
}

func (s *Manager) DelAssistant(ctx context.Context, parent, userId string) error {
	p, err := s.take2(ctx, "user_id = ? AND parent = ?", userId, parent)
	if err != nil {
		return err
	}
	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Delete(p).Error; err != nil {
			return err
		}
		return s.um.Update(ctx, userId, user.UnsetRole(user.RoleSeller))
	}); err != nil {
		return err
	}
	s.invalidCache(userId)
	return nil
}
