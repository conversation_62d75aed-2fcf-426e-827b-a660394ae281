package seller

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

var (
	tz = ctz.Brazil
)

func idOf(t time.Time) int {
	t = t.In(tz)
	return t.Year()*1e4 + int(t.Month())*1e2 + t.Day()
}

func dayTTL(t time.Time) time.Duration {
	t = t.In(tz)
	return now.New(t).EndOfDay().Sub(t).Truncate(time.Second) + time.Second
}

const (
	keyTradeAmount = "SELLER:TRADE:AMOUNT:%d:%s" // date, userId
)

func (s *Manager) dayTradeAmount(ctx context.Context, at time.Time, p *Profile) int {
	amount, _ := s.rc.Get(ctx, fmt.Sprintf(keyTradeAmount, idOf(at), p.UserId)).Int()
	return amount
}

func (s *Manager) incTradeAmount(ctx context.Context, at time.Time, p *Profile, amount int) {
	if p.Master() {
		return
	}

	key := fmt.Sprintf(keyTradeAmount, idOf(at), p.UserId)

	txp := s.rc.Pipeline()
	txp.IncrBy(ctx, key, int64(amount))
	txp.Expire(ctx, key, dayTTL(at))

	if _, err := txp.Exec(ctx); err != nil {
		s.log.Warn("inc trade amount failed", zap.Error(err))
	}
}
