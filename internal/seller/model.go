package seller

import "time"

type Config struct {
	Passwordless bool  `json:"passwordless"` // 无需密码
	TradeLimit   int   `json:"tradeLimit"`   // 每日限额
	InputAmounts []int `json:"inputAmounts"` // 常用金额
}

type Profile struct {
	ID          uint      `gorm:"primaryKey"`
	Parent      string    `gorm:"not null;size:32;default:''"`          // 上级账户
	UserId      string    `gorm:"not null;size:32;uniqueIndex:user_id"` // 关联用户
	TradeCount  int       `gorm:"not null;type:bigint unsigned"`        // 交易次数
	TradeAmount int       `gorm:"not null;type:decimal(16,0) unsigned"` // 交易总额
	Password    string    `gorm:"not null;size:24"`                     // 交易密码
	Invisible   bool      `gorm:"not null;default:false"`               // 隐藏头衔
	Disabled    bool      `gorm:"not null;default:false"`               // 禁用账户
	Config      Config    `gorm:"not null;serializer:json"`             // 账户配置
	CreatedAt   time.Time `gorm:"not null"`
	UpdatedAt   time.Time `gorm:"not null"`
}

func (p *Profile) TableName() string {
	return "sellers"
}

func (p *Profile) Valid() bool {
	return p != nil
}

func (p *Profile) Hidden() bool {
	return p == nil || p.Invisible
}

func (p *Profile) Usable() bool {
	return p != nil && !p.Disabled
}

func (p *Profile) Master() bool {
	return p != nil && p.Parent == ""
}

func (p *Profile) Chargeable() bool {
	return p.Usable() && p.Master()
}

type TType string

const (
	TradeIn  TType = "in"  // 转入
	TradeOut TType = "out" // 转出
	TradeSub TType = "sub" // 扣除
)

type Trade struct {
	ID        uint      `gorm:"primaryKey"`
	Type      TType     `gorm:"not null;size:4;index:user_view,priority:2"`  // 交易类型
	UserId    string    `gorm:"not null;size:32;index:user_view,priority:1"` // 用户ID
	Target    string    `gorm:"not null;size:32"`                            // 接收方
	Amount    int       `gorm:"not null;type:decimal(10,0) unsigned"`        // 金额
	Remark    string    `gorm:"not null;size:255"`                           // 备注
	CreatedAt time.Time `gorm:"not null"`
}

func (t *Trade) TableName() string {
	return "seller_trades"
}
