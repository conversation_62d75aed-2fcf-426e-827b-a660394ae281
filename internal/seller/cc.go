package seller

import (
	"context"

	"gitlab.sskjz.com/go/cc"
)

type Getter interface {
	Take(ctx context.Context, userId string) *Profile
}

type cached struct {
	cc cc.Cache[string, *Profile]
}

func (s *Manager) initCache(syn cc.Sync) {
	s.cc = cc.New[string, *Profile](1000, cc.LRU, cc.LoaderFunc(func(userId string) (*Profile, error) {
		return s.take(context.TODO(), userId)
	}),
		cc.ExportStats("seller.profiles"),
		cc.WithSync(syn, "seller.profiles"),
	)
}

func (s *cached) Take(ctx context.Context, userId string) *Profile {
	prof, _ := s.cc.Get(userId)
	return prof
}

func (s *cached) invalidCache(userId string) {
	s.cc.Remove(userId)
}
