package seller

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

type Stats struct {
	Count  int
	Amount int
}

func (s *Manager) Stats(ctx context.Context, userId string, typ TType) (*Stats, error) {
	var stats Stats
	if err := db.UseTx(ctx, s.db).Model(&Trade{}).Where("user_id = ? AND type = ?", userId, typ).
		Select("COUNT(target) AS count, SUM(amount) AS amount").
		Take(&stats).Error; err != nil {
		return nil, err
	}
	return &stats, nil
}

func (s *Manager) onRecharge(ctx context.Context, at time.Time, userId string, amount int, remark string) error {
	return db.UseTx(ctx, s.db).Create(&Trade{
		Type:      TradeIn,
		UserId:    userId,
		Amount:    amount,
		Remark:    remark,
		CreatedAt: at,
	}).Error
}

func (s *Manager) onDeduction(ctx context.Context, at time.Time, userId string, amount int, remark string) error {
	return db.UseTx(ctx, s.db).Create(&Trade{
		Type:      TradeSub,
		UserId:    userId,
		Amount:    amount,
		Remark:    remark,
		CreatedAt: at,
	}).Error
}

func (s *Manager) onTransfer(ctx context.Context, at time.Time, from, to string, amount int) error {
	return db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Model(&Profile{}).Where("user_id = ?", from).Updates(map[string]any{
			"trade_count":  gorm.Expr("trade_count + ?", 1),
			"trade_amount": gorm.Expr("trade_amount + ?", amount),
		}).Error; err != nil {
			return err
		}
		return tx.Create(&Trade{
			Type:      TradeOut,
			UserId:    from,
			Target:    to,
			Amount:    amount,
			CreatedAt: at,
		}).Error
	})
}
