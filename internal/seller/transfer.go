package seller

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gorm.io/gorm"
)

var (
	ErrWrongPassword = biz.NewError(biz.ErrWrongPassword, "wrong password")
	ErrForbidTrading = biz.NewError(biz.ErrProhibitExchange, "forbid trading")
)

type TradeResult struct {
	AccId string
}

func (s *Manager) Trade(ctx context.Context, at time.Time, from, to string, amount int, password string) (*TradeResult, error) {
	p := s.Take(ctx, from)
	if !p.Usable() || (!p.Master() && !s.Take(ctx, p.Parent).Usable()) {
		return nil, ErrForbidTrading
	}

	if !p.Config.Passwordless && p.Password != password {
		return nil, ErrWrongPassword
	}

	opts := []fund.PayOpt{fund.WithTime(at), fund.WithTarget(to)}

	accId := p.UserId
	if !p.Master() {
		if b, err := s.balance(ctx, p); err != nil {
			return nil, err
		} else if b.LessThan(fund.New(amount)) {
			return nil, fund.ErrBalanceNotEnough
		}
		accId = p.Parent
		opts = append(opts, fund.WithExtra(fund.Extra{"operator": p.UserId}))
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, _ *gorm.DB) error {
		if err := s.fm.Transfer(ctx, fund.JTypeRecharge,
			fund.Ticket{
				UserId: accId,
				Prop:   fund.PTypeTokens,
				Amount: amount,
			},
			fund.Ticket{
				UserId: to,
				Prop:   fund.PTypeDiamond,
				Amount: amount,
			},
			opts...,
		); err != nil {
			return err
		}
		return s.onTransfer(ctx, at, accId, to, amount)
	}); err != nil {
		return nil, err
	}

	s.ev.Emit(ctx, evt.SellerTransfer, &evt.SellerTransferData{
		At:     at,
		From:   accId,
		To:     to,
		Amount: amount,
	})

	s.incTradeAmount(ctx, at, p, amount)
	s.logTradePeer(ctx, at, from, to)

	return &TradeResult{AccId: accId}, nil
}
