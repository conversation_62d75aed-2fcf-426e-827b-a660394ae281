package ul

import (
	"context"
	"gitlab.sskjz.com/go/i3n"
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Manager struct {
	mc      *db.MongoClient
	records cc.Cache[string, *LangInfo]
}

func (m *Manager) getUserLang(ctx context.Context, userId string) (*LangInfo, error) {
	var out LangInfo
	if err := m.mc.Collection(CollectionUserLang).FindOne(ctx, bson.M{"_id": userId}).Decode(&out); err != nil {
		return nil, err
	}

	return &out, nil
}

func (m *Manager) UpdateUserLang(ctx context.Context, userId string, lang string) error {
	if has := m.records.Has(userId); has {
		v, err := m.records.Get(userId)
		if err != nil {
			return err
		}

		if v.Lang == lang {
			return nil
		}
	}

	if v, _ := m.records.Get(userId); v != nil && v.Lang == lang {
		return nil
	}

	if _, err := m.mc.Collection(CollectionUserLang).UpdateOne(ctx,
		bson.M{"_id": userId},
		bson.M{"$set": bson.M{"lang": lang, "updatedAt": time.Now()}},
		options.Update().SetUpsert(true)); err != nil {
		return err
	}

	m.records.Remove(userId)
	return nil
}

func (m *Manager) Lang(ctx context.Context, userId string) (string, error) {
	li, err := m.getUserLang(ctx, userId)
	if err != nil {
		return "", err
	}

	return li.Lang, nil
}

func (m *Manager) WithLang(ctx context.Context, userId string) context.Context {
	lang, err := m.Lang(ctx, userId)
	if err != nil {
		return i3n.Wrap(ctx, "en")
	}

	return i3n.Wrap(ctx, lang)
}
