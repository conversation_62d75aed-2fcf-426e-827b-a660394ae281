package ul

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(mc *db.MongoClient, syn cc.Sync) *Manager {
	m := &Manager{
		mc: mc,
	}

	m.records = cc.New[string, *LangInfo](2048,
		cc.LRU,
		cc.Expiration(time.Hour),
		cc.ExportStats("user_lang"),
		cc.WithSync(syn, "user_lang"),
		cc.LoaderFunc(func(userId string) (*LangInfo, error) {
			return m.getUserLang(context.TODO(), userId)
		}),
	)

	return m
}
