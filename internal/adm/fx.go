package adm

import (
	"context"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(um *user.Manager, am *agency.Manager, lsm *ls.Manager, db *db.Client, ev ev.Bus, vnd log.Vendor) *Manager {
	mgr := newManager(um, am, lsm, db, ev, vnd.Scope("adm.mgr"))
	return mgr
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
) {
	evb.Watch(evt.AgencyVerify, "adm.agency.relation", ev.NewWatcher(mgr.onAgencyVerify))
	evb.Watch(evt.AgencyDelete, "adm.agency.delete", ev.NewWatcher(mgr.onAgencyDelete))
	evb.Watch(evt.AgencyInviteEdit, "adm.invite.edit", ev.NewWatcher(mgr.onAgencyInviteEdit))
}

func InvokeInScheduler(mgr *Manager, sch *cron.Scheduler) {
	{
		task := sch.Exclusive("adm.ls.data", func(ctx context.Context) error {
			return mgr.BatchInitLiveSummaryAdmMonth(ctx)
		})

		// UTC每日凌晨3点 5分(巴西时间每天0点5分)
		sch.Cron("5 3 * * *").Do(task)
	}
}

func InvokeQueueInAPI(mgr *Manager, evq mq.Queue) {
	if !env.APIServer() {
		return
	}

	// 新的公会和ADM关联时，初始数据
	evt.Watch(evq, evt.AdmAgencyAdd, "adm.agency.add.mq", mgr.onAdmAgencyAdd)
}
