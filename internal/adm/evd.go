package adm

import (
	"context"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strconv"
)

func (m *Manager) onAgencyVerify(ctx context.Context, evd *evt.AgencyVerifyEvt) error {
	if evd.VerifyStatus != int(agency.VerifyStatusApproved) {
		return nil
	}

	if evd.InviteCode == "" {
		return nil
	}

	agencyInfo, err := m.am.GetAgencyById(ctx, int64(evd.AgencyId))
	if err != nil {
		return err
	}

	numId, _ := strconv.Atoi(evd.InviteCode)
	inviteAgencyInfo, err := m.am.GetAgencyInfoByNumId(ctx, int64(numId))
	if err != nil && err != agency.ErrNotFoundAgency {
		return err
	}

	admInfo, err := m.GetAdmInfo(ctx, inviteAgencyInfo.ChiefId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	if admInfo != nil && admInfo.ID > 0 {
		if err := m.AddAdmAgency(ctx, int(admInfo.ID), agencyInfo.ShowId); err != nil {
			return err
		}
	}

	m.log.Info("adm agency bind success", zap.Any("data", evd))

	return nil
}

func (m *Manager) onAgencyDelete(ctx context.Context, evd *evt.AgencyDeleteEvt) error {
	adm, err := m.GetAgencyAdm(ctx, evd.AgencyId)
	if err == nil && adm.ID > 0 {
		if err := m.DeleteAdmAgency(ctx, int(adm.ID), evd.AgencyId); err != nil {
			return err
		}
		m.log.Info("onAgencyDelete adm agency delete success", zap.Any("data", evd))
	}

	m.log.Info("onAgencyDelete success", zap.Any("data", evd))
	return nil
}

func (m *Manager) onAgencyInviteEdit(ctx context.Context, evd *evt.AgencyInviteEditEvt) error {
	// 处理原邀请码
	if evd.OriginalInviteCode != "" {
		numId, _ := strconv.Atoi(evd.OriginalInviteCode)
		oInviteAgency, err := m.am.GetAgencyInfoByNumId(ctx, int64(numId))
		if err == nil && oInviteAgency.ID > 0 {
			// 是否是ADM
			oAdm, err := m.GetAdmInfo(ctx, oInviteAgency.ChiefId)
			if err == nil && oAdm.ID > 0 {
				if err := m.DeleteAdmAgency(ctx, int(oAdm.ID), evd.AgencyId); err != nil {
					return err
				}
				m.log.Info("onAgencyInviteEdit adm agency delete success", zap.Any("data", evd))
			}
		}
	}

	// 处理当前邀请码
	if evd.CurrentInviteCode != "" {
		numId, _ := strconv.Atoi(evd.CurrentInviteCode)
		cInviteAgency, err := m.am.GetAgencyInfoByNumId(ctx, int64(numId))
		if err == nil && cInviteAgency.ID > 0 {
			// 是否是ADM
			cAdm, err := m.GetAdmInfo(ctx, cInviteAgency.ChiefId)
			if err == nil && cAdm.ID > 0 {
				if err := m.AddAdmAgency(ctx, int(cAdm.ID), evd.AgencyShowId); err != nil {
					return err
				}
				m.log.Info("onAgencyInviteEdit adm agency add success", zap.Any("data", evd))
			}
		}
	}

	m.log.Info("onAgencyInviteEdit success", zap.Any("data", evd))

	return nil
}
