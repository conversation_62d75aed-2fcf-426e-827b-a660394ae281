package adm

import "time"

type Adm struct {
	ID               uint      `gorm:"primaryKey"`
	UserId           string    `gorm:"not null;size:32;index:idx_userid"` // 用户Id
	AgencyId         uint      `gorm:"not null"`                          // 公会表id
	AgencyInviteCode string    `gorm:"not null;size:32;default:''"`       // 公会邀请码
	AgencyNum        int       `gorm:"not null;default:0"`                // 旗下公会数量
	TrialPeriodAt    time.Time `gorm:"not null"`                          // 试用期截止
	Status           int       `gorm:"not null;default:1"`                // adm状态 0取消 1试用 2转正
	ApprovedAt       time.Time // 转正时间，暂时无用
	UpdatedAt        time.Time `gorm:"not null"` // 更新时间
	CreatedAt        time.Time `gorm:"not null"` // 创建时间
}

func (g *Adm) TableName() string {
	return "adms"
}

type AdmAgency struct {
	ID        uint      `gorm:"primaryKey"`
	AdmId     uint      `gorm:"not null;index:idx_admid"`    // admid
	AgencyId  uint      `gorm:"not null;index:idx_agencyid"` // 旗下公会id
	Status    int       `gorm:"not null;default:1"`          // 状态 1正常 0取消
	UpdatedAt time.Time `gorm:"not null"`                    // 更新时间
	CreatedAt time.Time `gorm:"not null"`                    // 创建时间
}

func (g *AdmAgency) TableName() string {
	return "adm_agencies"
}
