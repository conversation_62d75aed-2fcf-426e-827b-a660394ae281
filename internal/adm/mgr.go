package adm

import (
	"context"
	"gitlab.sskjz.com/go/ev"

	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	um  *user.Manager
	am  *agency.Manager
	lsm *ls.Manager
	db  *db.Client
	ev  ev.Bus
	log *zap.Logger
}

func newManager(um *user.Manager, am *agency.Manager, lsm *ls.Manager, db *db.Client, ev ev.Bus, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Adm{}, &AdmAgency{})
	mgr := &Manager{
		um:  um,
		am:  am,
		lsm: lsm,
		db:  db,
		ev:  ev,
		log: log,
	}

	return mgr
}

func (m *Manager) GetAdmInfo(ctx context.Context, userId string) (*Adm, error) {
	var adm Adm
	if err := m.db.Where("user_id = ? and status >= ?", userId, StatusTrial).First(&adm).Error; err != nil {
		return nil, err
	}

	return &adm, nil
}

func (m *Manager) GetAgencyAdm(ctx context.Context, agencyId int) (*Adm, error) {
	var admAgency AdmAgency
	if err := m.db.Where("agency_id = ? and status = ?", agencyId, AgencyStatusNormal).First(&admAgency).Error; err != nil {
		return nil, err
	}

	var adm Adm
	if err := m.db.Where("id = ? and status >= ?", admAgency.AdmId, StatusTrial).First(&adm).Error; err != nil {
		return nil, err
	}

	return &adm, nil
}
