package adm

import (
	"context"
	"errors"
	"strconv"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrRecordNotFound = errors.New("record not found")
)

func (m *Manager) CreateAdm(ctx context.Context, userId string, admStatus int, trialPeriod int64) error {
	// 是否是公会长
	myOwnAgency, err := m.am.MyOwnAgency(ctx, userId)
	if err != nil {
		return err
	}

	// 是否已经是Adm
	admInfo, err := m.GetAdmInfo(ctx, userId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if admInfo != nil && admInfo.ID > 0 {
		return biz.NewError(biz.ErrInvalidParam, "already an ADM")
	}

	var (
		admId      int
		agencyList []int
	)
	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		// 构建公会关系
		inviteList, err := m.am.GetInviteAgencyList(ctx, strconv.Itoa(int(myOwnAgency.NumId)))
		if err != nil {
			return err
		}

		adm := &Adm{
			UserId:           userId,
			AgencyId:         myOwnAgency.ID,
			AgencyInviteCode: strconv.Itoa(int(myOwnAgency.NumId)),
			AgencyNum:        len(inviteList),
			TrialPeriodAt:    time.Unix(trialPeriod, 0),
			Status:           admStatus,
			ApprovedAt:       time.Now(),
			UpdatedAt:        time.Now(),
			CreatedAt:        time.Now(),
		}

		if err := tx.Create(&adm).Error; err != nil {
			return err
		}

		admId = int(adm.ID)
		if len(inviteList) > 0 {
			var admAgencyList []AdmAgency
			for _, v := range inviteList {
				admAgencyList = append(admAgencyList, AdmAgency{
					AdmId:     adm.ID,
					AgencyId:  v.ID,
					Status:    AgencyStatusNormal,
					UpdatedAt: time.Now(),
					CreatedAt: time.Now(),
				})
				agencyList = append(agencyList, int(v.ID))
			}

			if err := tx.Create(admAgencyList).Error; err != nil {
				return err
			}
		}

		return m.um.Update(ctx, userId, user.SetRole(user.RoleADM))
	}); err != nil {
		m.log.Error("create adm err", zap.Error(err))
		return err
	}

	m.ev.Emit(ctx, evt.AdmAgencyAdd, &evt.AdmAgencyAddData{
		AdmId:      admId,
		UserId:     userId,
		AgencyList: agencyList,
		UpdateAt:   time.Now(),
	})

	return nil
}

func (m *Manager) AdmUpdate(ctx context.Context, admId int, admStatus int, trialPeriod int64) error {
	if !lo.Contains([]int{StatusTrial, StatusApproved}, admStatus) {
		return biz.NewError(biz.ErrInvalidParam, "status err")
	}

	var update = map[string]any{}
	update["status"] = admStatus
	if admStatus == StatusApproved {
		update["approved_at"] = time.Now()
	}
	if admStatus == StatusTrial {
		update["trial_period_at"] = time.Unix(trialPeriod, 0)
	}
	return m.db.Model(&Adm{}).Where("id = ?", admId).Updates(update).Error
}

func (m *Manager) AddAdmAgency(ctx context.Context, admId int, agencyShowId string) error {
	// Adm是否存在
	var admInfo Adm
	err := m.db.Where("id = ? and status >= ?", admId, StatusTrial).First(&admInfo).Error
	if err != nil {
		return err
	}

	// 公会是否存在
	agencyInfo, err := m.am.GetAgencyInfoByShowId(ctx, agencyShowId)
	if err != nil {
		return err
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		// 公会已经在其他ADM旗下
		var exists uint
		if err := tx.
			Model(&AdmAgency{}).Select("id").Where("agency_id = ? and status = ? FOR UPDATE", agencyInfo.ID, AgencyStatusNormal).
			Find(&exists).Error; err != nil {
			return err
		}
		if exists > 0 {
			return biz.NewError(biz.ErrInvalidParam, "already joined other ADM")
		}

		if err := tx.Model(&Adm{}).
			Where("id = ?", admInfo.ID).
			Update("agency_num", gorm.Expr("agency_num + 1")).Error; err != nil {
			return err
		}

		return tx.Create(&AdmAgency{
			AdmId:     admInfo.ID,
			AgencyId:  agencyInfo.ID,
			Status:    AgencyStatusNormal,
			UpdatedAt: time.Now(),
			CreatedAt: time.Now(),
		}).Error
	}); err != nil {
		m.log.Error("add adm agency err", zap.Error(err))
		return err
	}

	m.ev.Emit(ctx, evt.AdmAgencyAdd, &evt.AdmAgencyAddData{
		AdmId:      int(admInfo.ID),
		UserId:     admInfo.UserId,
		AgencyList: []int{int(agencyInfo.ID)},
		UpdateAt:   time.Now(),
	})

	return nil
}

func (m *Manager) DeleteAdmAgency(ctx context.Context, admId, agencyId int) error {
	// Adm是否存在
	var admInfo Adm
	err := m.db.Where("id = ? and status >= ?", admId, StatusTrial).First(&admInfo).Error
	if err != nil {
		return err
	}

	// 公会是否存在
	agecnyInfo, err := m.am.GetAgencyById(ctx, int64(agencyId))
	if err != nil {
		return err
	}

	if agecnyInfo.ID == admInfo.AgencyId {
		return biz.NewError(biz.ErrInvalidParam, "adm base agency can not delete")
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		var Agency AdmAgency
		if err := tx.
			Model(&AdmAgency{}).
			Where("adm_id = ? and agency_id = ? and status = ?", admId, agencyId, AgencyStatusNormal).First(&Agency).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return biz.NewError(biz.ErrInvalidParam, "agency not found")
			} else {
				return err
			}
		}

		if err := tx.Model(&Adm{}).
			Where("id = ?", admInfo.ID).
			Update("agency_num", gorm.Expr("agency_num - 1")).Error; err != nil {
			return err
		}

		return tx.Model(&AdmAgency{}).Where("adm_id = ? and agency_id = ?", admId, agencyId).Update("status", AgencyStatusDelete).Error
	}); err != nil {
		m.log.Error("add adm agency err", zap.Error(err))
		return err
	}

	return nil
}

func (m *Manager) DeleteAdm(ctx context.Context, admId int) error {
	var admInfo Adm
	if err := m.db.Where("id = ? and status >= ?", admId, StatusTrial).First(&admInfo).Error; err != nil {
		return err
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := tx.Model(&Adm{}).Where("id = ?", admId).Update("status", StatusDelete).Error; err != nil {
			return err
		}

		if err := tx.Model(&AdmAgency{}).Where("adm_id = ? and status = ?", admId, AgencyStatusNormal).Update("status", AgencyStatusDelete).Error; err != nil {
			return err
		}

		return m.um.Update(ctx, admInfo.UserId, user.UnsetRole(user.RoleADM))
	}); err != nil {
		m.log.Error("delete adm err", zap.Error(err))
		return err
	}

	return nil
}

func (m *Manager) GetAdmList(ctx context.Context, userId string, status, page, pageSize int) ([]Adm, int64, error) {
	var admList []Adm
	tx := m.db.Model(&Adm{})
	if userId != "" {
		tx = tx.Where("user_id = ?", userId)
	}

	if status != -1 {
		tx = tx.Where("status = ?", status)
	} else {
		tx = tx.Where("status >= ?", StatusTrial)
	}

	var total int64
	if err := tx.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if err := tx.Offset((page - 1) * pageSize).Limit(pageSize).Order("id desc").Find(&admList).Error; err != nil {
		return admList, total, err
	}

	return admList, total, nil
}

func (m *Manager) GetAdmAgencyList(ctx context.Context, admId int, page, pageSize int) ([]AdmAgency, int64, error) {
	var (
		total         int64
		admAgencyList []AdmAgency
	)
	tx := m.db.Model(&AdmAgency{}).Where("adm_id = ? and status = ?", admId, AgencyStatusNormal)

	if err := tx.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if err := tx.
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Order("id desc").
		Find(&admAgencyList).Error; err != nil {
		return admAgencyList, total, err
	}

	return admAgencyList, total, nil
}

func (m *Manager) GetAdmAgencyIds(ctx context.Context, admId int) ([]int, error) {
	var (
		admAgencyIds []int
	)
	if err := m.db.
		Model(&AdmAgency{}).
		Select("agency_id").
		Where("adm_id = ? and status = ?", admId, AgencyStatusNormal).
		Find(&admAgencyIds).Error; err != nil {
		return admAgencyIds, err
	}

	return admAgencyIds, nil
}

func (m *Manager) GetAdmAgencyByAgencyId(ctx context.Context, agencyId int64) (*AdmAgency, error) {
	var admAgency AdmAgency
	if err := m.db.Where("agency_id = ? and status = ?", agencyId, AgencyStatusNormal).First(&admAgency).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}

		return nil, err
	}

	return &admAgency, nil
}

// 不考虑状态查询
func (m *Manager) GetAdmAgencyByAgencyIdIgnoreStatus(ctx context.Context, agencyId int64) (*AdmAgency, error) {
	var admAgency AdmAgency
	if err := m.db.Where("agency_id = ?", agencyId).First(&admAgency).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}

		return nil, err
	}

	return &admAgency, nil
}

func (m *Manager) GetAdmById(ctx context.Context, admId int64) (*Adm, error) {
	var adm Adm
	if err := m.db.Where("id = ?", admId).First(&adm).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}

		return nil, err
	}

	return &adm, nil
}

func (m *Manager) GetAdmByAgencyId(ctx context.Context, agencyId int64) (*Adm, error) {
	admAgency, err := m.GetAdmAgencyByAgencyId(ctx, agencyId)

	if err != nil {
		return nil, err
	}

	return m.GetAdmById(ctx, int64(admAgency.AdmId))
}
