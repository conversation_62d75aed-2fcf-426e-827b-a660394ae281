package adm

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (m *Manager) initLiveSummaryAdmMonth(ctx context.Context, admUserId string, agencyId int64, month string) (*mongo.UpdateResult, error) {
	return m.lsm.InitAdmMonth(ctx, admUserId, agencyId, month)
}

func (m *Manager) BatchInitLiveSummaryAdmMonth(ctx context.Context) error {
	t := time.Now().In(ctz.Brazil)
	month := t.Format("200601")

	page := 1
	pageSize := 500

	logger := m.log.With(
		zap.String("month", month),
		zap.String("from", "batch"),
	)

	logger.Info("开始批量初始化ADM月数据")

	for {
		var admList []Adm
		tx := m.db.Model(&Adm{}).Where("status >= ?", StatusTrial)

		if err := tx.Offset((page - 1) * pageSize).Limit(pageSize).Order("id asc").Find(&admList).Error; err != nil {
			logger.Error(
				"初始化ADM月数据",
				zap.Error(err),
			)

			return err
		}

		if len(admList) == 0 {
			break
		}

		for _, a := range admList {
			var admAgencyIds []int

			if err := m.db.
				Model(&AdmAgency{}).
				Select("agency_id").
				Where("adm_id = ? and status = ?", a.ID, AgencyStatusNormal).
				Find(&admAgencyIds).Error; err != nil {

				logger.Error(
					"初始化ADM月数据",
					zap.Error(err),
					zap.Int("admId", int(a.ID)),
					zap.String("admUserId", a.UserId),
				)

				return err
			}

			for _, agencyId := range admAgencyIds {
				res, err := m.initLiveSummaryAdmMonth(ctx, a.UserId, int64(agencyId), month)

				if err != nil {
					logger.Error(
						"初始化ADM月数据",
						zap.Error(err),
						zap.Int("admId", int(a.ID)),
						zap.String("admUserId", a.UserId),
						zap.Int("agencyId", agencyId),
						zap.Any("response", res),
					)

					return err
				}

				if res.UpsertedCount > 0 {
					logger.Info(
						"初始化ADM月数据",
						zap.Int("admId", int(a.ID)),
						zap.String("admUserId", a.UserId),
						zap.Int("agencyId", agencyId),
						zap.Any("response", res),
					)
				}
			}
		}

		page++
	}

	return nil
}

func (m *Manager) onAdmAgencyAdd(ctx context.Context, evd *evt.AdmAgencyAddData) error {
	// 无需处理幂等性
	admId := evd.AdmId
	admUserId := evd.UserId
	agencyIdList := evd.AgencyList
	month := evd.UpdateAt.In(ctz.Brazil).Format("200601")

	logger := m.log.With(
		zap.Int("admId", admId),
		zap.String("admUserId", admUserId),
		zap.String("month", month),
		zap.String("from", "event"),
	)

	for _, agencyId := range agencyIdList {
		ur, err := m.initLiveSummaryAdmMonth(ctx, admUserId, int64(agencyId), month)

		if err != nil {
			logger.Error("初始化ADM月数据", zap.Error(err), zap.Int("agencyId", agencyId))

			continue
		}

		if ur.UpsertedCount > 0 {
			logger.Info("初始化ADM月数据", zap.Int("agencyId", agencyId), zap.Any("response", ur))
		}
	}

	return nil
}
