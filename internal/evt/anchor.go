package evt

import "time"

const (
	NewAnchorFlagSet     = "newAnchor.flag.set"
	EvaluationApply      = "evaluation.apply"
	NewAnchorSupportTask = "newAnchor.support.task" // 新主播扶持每日任务
)

type SetNewAnchorFlag struct {
	UserId string
	Flag   bool
	Time   time.Time
}

type ApplyEvaluation struct {
	UserId string
}

type SupportNewAnchorTask struct {
	UserId string    // 主播ID
	Date   time.Time // 巴西时区扶持日零点
}
