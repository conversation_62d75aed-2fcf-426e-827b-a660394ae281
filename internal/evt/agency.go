package evt

const (
	AgencyApply       = "agency.apply"        // AgencyApplyEvt
	AgencyVerify      = "agency.verify"       // AgencyVerifyEvt
	AgencyMemberApply = "agency.member.apply" // AgencyMemberApplyEvt
	AgencyInviteEdit  = "agency.invite.edit"  // AgencyInviteEditEvt
	AgencyDelete      = "agency.delete"       // AgencyDeleteEvt
	AgencyJoinVerify  = "agency.join.verify"  // AgencyJoinVerifyEvt
	AgencyQuitVerify  = "agency.quit.verify"  // AgencyQuitVerifyEvt
)

type AgencyApplyEvt struct {
	UserId     string
	InviteCode string
}

type AgencyVerifyEvt struct {
	UserId          string
	InviteCode      string
	VerifyStatus    int
	AgencyId        int
	Manage          bool
	ContactWhatsapp string
}

type AgencyMemberApplyEvt struct {
	UserId  string // 申请用户ID
	ChiefId string // 公会长ID
}

type AgencyInviteEditEvt struct {
	AgencyId           int    // 公会id
	AgencyShowId       string // 公会showid
	OriginalInviteCode string // 原邀请码
	CurrentInviteCode  string // 现邀请码
}

type AgencyDeleteEvt struct {
	AgencyId int // 公会id
}

type AgencyJoinVerifyEvt struct {
	AgencyId     int    // 公会id
	AgencyShowId string // 公会showid
	UserId       string // 被审核用户信息
	VerifyStatus int    // 审核状态
}

type AgencyQuitVerifyEvt struct {
	AgencyId     int    // 公会id
	AgencyShowId string // 公会showid
	UserId       string // 被审核用户信息
	VerifyStatus int    // 审核状态
}
