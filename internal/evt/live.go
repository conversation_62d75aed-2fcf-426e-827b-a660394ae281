package evt

import "time"

const (
	LiveStart   = "live.start"    // StartLive
	LiveStop    = "live.stop"     // StopLive
	LiveWatch   = "live.watch"    // WatchLive
	LiveLike    = "live.like"     // LikeLive
	LiveFlagSet = "live.flag.set" // SetLiveFlag
)

const (
	LiveFlagSetOpAdd    = "add"
	LiveFlagSetOpRemove = "remove"
)

type StartLive struct {
	UserId    string
	RoomId    string
	SessionId string
	StartTime time.Time
}

type StopLive struct {
	UserId    string
	RoomId    string
	SessionId string
	StartTime time.Time
	EndTime   time.Time
}

type WatchLive struct {
	UserId    string
	RoomId    string
	SessionId string
}

type LikeLive struct {
	UserId       string
	RoomId       string
	AnchorUserId string
	SessionId    string
	Count        int
	UpdatedCount int
	At           time.Time
}

type SetLiveFlag struct {
	AnchorId string
	RoomId   string
	Flag     int
	Op       string // add or remove
}
