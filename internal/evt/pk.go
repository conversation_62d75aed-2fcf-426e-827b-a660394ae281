package evt

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
)

const (
	EvPKWin          = "pk.user.win"
	EvPkUserSendGift = "pk.user.send.gift"
)

type UserWinPk struct {
	SessionId string    `json:"sessionId"`
	UserId    string    `json:"userId"`
	At        time.Time `json:"at"`
}

type UserSendGift struct {
	AnchorId string    `json:"anchorId"`
	UserId   string    `json:"userId"`
	Gift     gift.Gift `json:"gift"`
	Count    int       `json:"count"`
	At       time.Time `json:"at"`
}
