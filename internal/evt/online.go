package evt

import "time"

const (
	UserLeaveTopic         = "room.user.leave"
	UserDateOnlineTopic    = "room.user.date.online"
	UserSessionOnlineTopic = "room.user.session.online"
)

type UserLeave struct {
	RoomId    string    `json:"roomId"`
	SessionId string    `json:"sessionId"`
	UserIds   []string  `json:"userIds"`
	At        time.Time `json:"at"`
}

type UserDateOnline struct {
	UserId   string        `json:"userId"`
	Date     time.Time     `json:"date"`
	Duration time.Duration `json:"duration"`
	TimeZone string        `json:"timeZone"`
	At       time.Time     `json:"at"`
}

type UserSessionOnline struct {
	UserId    string        `json:"userId"`
	SessionId string        `json:"sessionId"`
	Duration  time.Duration `json:"duration"`
	At        time.Time     `json:"at"`
}
