package evt

const (
	MomentPublish = "moment.publish" // PublishMoment
	MomentRead    = "moment.read"    // PublishRead
	MomentDelete  = "moment.delete"  // DeleteMoment
	MomentWatch   = "moment.watch"   // WatchMoment
	MomentComment = "moment.comment" // WatchMomentComment
	MomentLike    = "moment.like"    // WatchMomentLike
	MomentShare   = "moment.share"   // WatchMomentShare
	CommentLike   = "comment.like"   // LikeComment

	MomentVisibleAll  = "moment.visible.all"
	MomentVisibleHide = "moment.visible.hide"
)

type WatchMoment struct {
	UserId   string
	MomentId string
	AnchorId string
}

type PublishMoment struct {
	MomentId string
	AnchorId string
}

type ReadMoment struct {
	MomentId string
	UserId   string
}

type DeleteMoment struct {
	MomentId uint
	AnchorId string
}

type LikeMoment struct {
	MomentId uint
	AnchorId string
	UserId   string
}

type ShareMoment struct {
	MomentId uint
	AnchorId string
	UserId   string
}

type CommentPublish struct {
	MomentId  uint   // 动态id
	AnchorId  string // 动态作者
	ParentId  uint   // 如果是回复，最上层级评论id
	TargetId  uint   // 如果是回复，回复的目标评论id
	UserId    string // 发布人id
	CommentId uint   // 本次发布的评论id
}

type MomentVisibleData struct {
	MomentId string
	AnchorId string
}

type LikeComment struct {
	MomentId  uint   // 评论所在动态
	CommentId uint   // 被点赞的评论
	AnchorId  string // 评论作者
	UserId    string // 点赞人
}
