package evt

import "time"

const (
	EvGamePlay = "game.play"
	EvGameWin  = "game.win"
	EvGamePaid = "game.paid"
)

type PlayGame struct {
	Platform string    `json:"platform"`
	GameId   string    `json:"gameId"`
	UserId   string    `json:"userId"`
	At       time.Time `json:"at"`
}

type PaidInGame struct {
	Platform string    `json:"platform"`
	GameId   string    `json:"gameId"`
	UserId   string    `json:"userId"`
	Amount   int64     `json:"amount"`
	At       time.Time `json:"at"`
}

type WinGame struct {
	Platform string    `json:"platform"`
	GameId   string    `json:"gameId"`
	UserId   string    `json:"userId"`
	Amount   int64     `json:"amount"`
	At       time.Time `json:"at"`
}
