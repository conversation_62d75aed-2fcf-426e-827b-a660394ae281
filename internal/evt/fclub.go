package evt

import "time"

const (
	FansclubUserJoin  = "fansclub.user.join"  // UserJoinFansclub
	FansclubLvUpgrade = "fansclub.lv.upgrade" // FansLevelUpgrade
	FansclubActivate  = "fansclub.activate"   // ActivateFansclub
)

type UserJoinFansclub struct {
	RoomId   string
	AnchorId string
	UserId   string
	Number   int // 第几名成员
	Cost     int // 门票价格
	At       time.Time
}

type FansLevelUpgrade struct {
	RoomId   string
	AnchorId string
	UserId   string
	Level    int // 当前等级
	Prev     int // 之前的等级
}

type ActivateFansclub struct {
	RoomId   string
	AnchorId string
	UserId   string
}
