package evt

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

const (
	GiftSend          = "gift.send"          // SendGift
	GiftSendAdvanced  = "gift.send.advanced" // SendGiftAdvanced
	GiftSendLuckyRoom = "gift.luckyroom"     // SendGiftLuckyRoom 幸运礼物
)

type Pows []int // 中的倍率

type SendGift struct {
	UserId   string // 送礼人
	AnchorId string // 收礼的主播userId
	RoomId   string // 房间ID
	Session  string // 房间的sessionId
	HostId   string // 房间的主播userId
	GiftId   int
	Gift     gift.Gift
	Count    int
	Diamond  int // 礼物总价值（单价x数量）
	Prizes   Pows
	At       time.Time
	BlindBox int // 盲盒礼物ID
}

func (s *SendGift) SendToHost() bool {
	return s.HostId == s.AnchorId
}

type SendGiftAdvanced struct {
	SendGift
	EventId     string         // 使用EventId作为幂等标识
	Ratio       SendGiftRatio  // 分成信息，非实时，按session场次缓存
	Agency      SendGiftAgency // 公会信息，非实时，按session场次缓存
	RatioIncome SendGiftIncome // 按分成信息计算的各方收入信息
}

type SendGiftRatio struct {
	Luck   float64 // 主播幸运礼物分成比例
	Gift   float64 // 主播特效礼物分成比例
	Agency float64 // 公会分成比例
}

type SendGiftAgency struct {
	AgencyId     int64  // 公会ID
	AgencyUserId string // 公会长用户ID
	AdmId        int64  // 公会所属ADM ID
	AdmUserId    string // ADM用户ID
}

type SendGiftIncome struct {
	LuckIncome   fund.Decimal // 主播幸运礼物收入，水晶
	GiftIncome   fund.Decimal // 主播特效礼物收入，水晶
	AgencyIncome fund.Decimal // 公会收入，水晶
}

type SendGiftLuckyRoom struct {
	UserId        string
	AnchorId      string
	RoomId        string
	Session       string
	GiftId        int
	Gift          gift.Gift
	Count         int
	Diamond       int // 礼物总价值（单价x数量）
	LuckDrawPrize []protocol.LuckDrawPrize
	At            time.Time
}
