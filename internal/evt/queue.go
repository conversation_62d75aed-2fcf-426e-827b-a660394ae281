package evt

import (
	"context"

	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/mg"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const mqPrefix = "osl-"

func Invoke(q mq.Queue, evb ev.Bus) {
	{
		Forward[*user.Account](q, evb, user.EvUserCreated)
		Forward[*user.EvUpdating](q, evb, user.EvUserUpdated)
	}
	{
		Forward[*StartLive](q, evb, LiveStart)
		Forward[*StopLive](q, evb, LiveStop)
	}
	{
		Forward[*EventRtcSnapshot](q, evb, RtcSnapshotEvent)
		Forward[*ReviewResultData](q, evb, ReviewResult)
	}
	{
		Forward[*SendGiftAdvanced](q, evb, GiftSendAdvanced)
	}
	{
		Forward[*AdmAgencyAddData](q, evb, AdmAgencyAdd)
	}
}

func Forward[T any](q mq.Queue, evb ev.Bus, topic string) {
	queue := mg.New[T](q)
	topic2 := mqPrefix + topic
	evb.Watch(topic, "evt.forward", ev.NewWatcher(func(ctx context.Context, data T) error {
		return queue.Publish(ctx, topic2, data)
	}), ev.WithAsync(co.Size(co.Proc())))
}

func Watch[T any](q mq.Queue, topic, channel string, handler mg.Handler[T], opts ...mq.SubOption) {
	queue := mg.New[T](q)
	topic2 := mqPrefix + topic
	if err := queue.Subscribe(topic2, channel, handler, append([]mq.SubOption{mq.ConcurrencySub()}, opts...)...); err != nil {
		panic(err)
	}
}
