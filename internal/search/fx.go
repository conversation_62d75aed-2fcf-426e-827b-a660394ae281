package search

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(
	dbmc *db.MongoClient,
	rc *redi.Client,
	lm *live.Manager,
	ev ev.Bus,
	vnd log.Vendor,
) (*Manager, error) {
	mgr := newManager(dbmc, rc, lm, ev, vnd.Scope("search.mgr"))

	return mgr, nil
}
