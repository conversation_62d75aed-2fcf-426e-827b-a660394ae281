package search

import (
	"context"
	"fmt"
	"strconv"
	"time"
)

const (
	keyClickedUser = "HASH:SEARCH:C:%s"
	ttlClickedUser = time.Hour * 24 * 15
	keyClickLimit  = "STR:SEARCH:C:L:%s:%s"
)

func (m *Manager) Click(ctx context.Context, userId, clickUserId string) {
	// 当前只记录主播点击数
	if _, err := m.lm.RoomByUserId(ctx, clickUserId); err != nil {
		return
	}

	// 每天一个人对单个主播的点击统计限制10次
	if m.rc.Incr(ctx, fmt.Sprintf(keyClickLimit, userId, clickUserId)).Val() > 10 {
		return
	}

	at := time.Now()

	key := m.getClickedUserKey(clickUserId)

	m.rc.HIncrBy(ctx, key, at.Format("20060102"), 1)

	m.rc.Expire(ctx, key, ttlClickedUser)

	// 删除过期的点击数
	if m.rc.HLen(ctx, key).Val() > 15 {
		keys := m.rc.Keys(ctx, key).Val()

		for _, k := range keys {
			if t, _ := time.Parse("20060102", k); t.Add(ttlClickedUser).Before(at) {
				m.rc.HDel(ctx, key, k)
			}
		}
	}
}

func (m *Manager) GetClickCount(ctx context.Context, userId string) int64 {
	key := m.getClickedUserKey(userId)

	at := time.Now()

	res := m.rc.HGetAll(ctx, key).Val()

	var count int64

	for k, v := range res {
		t, _ := time.Parse("20060102", k)

		if t.Add(ttlClickedUser).Before(at) {
			m.rc.HDel(ctx, key, k)
			continue
		}

		c, _ := strconv.ParseInt(v, 10, 64)

		count += c
	}

	return count
}

func (m *Manager) getClickedUserKey(userId string) string {
	return fmt.Sprintf(keyClickedUser, userId)
}
