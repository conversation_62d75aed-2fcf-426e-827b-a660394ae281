package device

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"go.mongodb.org/mongo-driver/bson"
)

type Status int

const (
	Normal  Status = 0 // 正常使用
	Blocked Status = 1 // 禁止访问
)

func (s Status) Blocked() bool {
	return s == Blocked
}

var deviceIdx = []db.Indexer{
	{
		Name: "user_view",
		Keys: bson.D{
			{Key: "userId", Value: 1},
		},
	},
}

type Device struct {
	Id       string    `bson:"_id"`      // 设备ID
	Lang     string    `bson:"lang"`     // 设备语言
	Model    string    `bson:"model"`    // 设备型号
	Status   Status    `bson:"status"`   // 设备状态
	UserId   string    `bson:"userId"`   // 用户ID（上次使用）
	LiveAt   time.Time `bson:"liveAt"`   // 活跃时间
	ClientIP string    `bson:"clientIP"` // 客户端IP
	Country  string    `bson:"country"`  // 设备国家
	Channel  string    `bson:"channel"`  // 市场渠道
	Version  string    `bson:"version"`  // 应用版本号
	BuildId  string    `bson:"buildId"`  // 应用构建ID
	// extra fields
	PushOption `bson:",inline"`
}

const deviceDB = "user.devices"

func updateAttr(ctx context.Context) bson.M {
	out := bson.M{"liveAt": time.Now()}
	if g := api.Unwrap(ctx); g != nil {
		notEmpty(out, "lang", parseLang(g.GetHeader("Accept-Language")))
		notEmpty(out, "model", app.DeviceName(g))
		notEmpty(out, "clientIP", g.ClientIP())
		notEmpty(out, "country", app.Country(g))
		notEmpty(out, "channel", app.ChannelId(g))
		notEmpty(out, "version", app.Version(g).String())
		notEmpty(out, "buildId", app.BuildId(g))
	}
	return out
}

func notEmpty(out bson.M, key, val string) {
	if val != "" {
		out[key] = val
	}
}

var linkedIdx = []db.Indexer{
	{
		Name: "user_view",
		Uniq: lo.ToPtr(true),
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "deviceId", Value: 1},
		},
	},
}

type Linked struct {
	UserId    string    `bson:"userId"`    // 用户ID
	DeviceId  string    `bson:"deviceId"`  // 设备ID
	CreatedAt time.Time `bson:"createdAt"` // 绑定时间
}

const linkedDB = "user.devices.bind"
