package device

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBadDev(t *testing.T) {
	assert.Equal(t, false, ValidID(""))
	assert.Equal(t, false, ValidID("test"))
	assert.Equal(t, false, Valid<PERSON>("7edaccad4a81050"))
	assert.Equal(t, false, <PERSON><PERSON><PERSON>("7edaccad4a81050x"))
	assert.Equal(t, true, Valid<PERSON>("7edaccad4a810500"))
	assert.Equal(t, false, Valid<PERSON>("00000000-0000-0000-0000-000000000000"))
	assert.Equal(t, false, ValidID("ef1e7655-c709-499x-bd58-811c3319f9b6"))
	assert.Equal(t, true, ValidID("7c83a545-69d6-4103-a4af-8b27353da828"))
}

func BenchmarkBadDev(b *testing.B) {
	tests := []string{
		"7edaccad4a81050",
		"7edaccad4a81050x",
		"7edaccad4a810500",
		"00000000-0000-0000-0000-000000000000",
		"ef1e7655-c709-499x-bd58-811c3319f9b6",
		"7c83a545-69d6-4103-a4af-8b27353da828",
	}
	for _, test := range tests {
		b.Run(test, func(b *testing.B) {
			for range b.N {
				_ = ValidID(test)
			}
			b.ReportAllocs()
		})
	}
}
