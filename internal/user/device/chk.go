package device

import (
	"encoding/hex"

	"github.com/google/uuid"
)

func ValidID(id string) bool {
	if len(id) == 16 && valid16(id) {
		return true
	} else if len(id) == 36 && valid36(id) {
		return true
	} else {
		return false
	}
}

func valid16(id string) bool {
	_, err := hex.DecodeString(id)
	return err == nil
}

func valid36(id string) bool {
	if id == "00000000-0000-0000-0000-000000000000" {
		return false
	}
	return uuid.Validate(id) == nil
}
