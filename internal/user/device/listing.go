package device

import (
	"context"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (s *Manager) ListCurr(ctx context.Context, userId string) ([]*Device, error) {
	return s.listCurr(ctx, userId, 0)
}

func (s *Manager) ListUsed(ctx context.Context, userId string) ([]*Device, error) {
	return s.listBind(ctx, userId, 0)
}

func (s *Manager) ListUsers(ctx context.Context, deviceId string) ([]*Linked, error) {
	return db.DecodeAll[*Linked](ctx)(s.db.Collection(linkedDB).Find(ctx, bson.M{"deviceId": deviceId}))
}

func (s *Manager) listCurr(ctx context.Context, userId string, limit int64) ([]*Device, error) {
	return db.DecodeAll[*Device](ctx)(s.db.Collection(deviceDB).Find(ctx, bson.M{"userId": userId},
		options.Find().SetSort(bson.M{"liveAt": -1}).SetLimit(limit),
	))
}

func (s *Manager) listBind(ctx context.Context, userId string, limit int64) ([]*Device, error) {
	links, err := db.DecodeAll[*Linked](ctx)(s.db.Collection(linkedDB).Find(ctx, bson.M{"userId": userId}))
	if err != nil {
		return nil, err
	}
	devIds := lo.Map(links, func(link *Linked, _ int) string { return link.DeviceId })
	return db.DecodeAll[*Device](ctx)(s.db.Collection(deviceDB).Find(ctx, bson.M{"_id": bson.M{"$in": devIds}},
		options.Find().SetSort(bson.M{"liveAt": -1}).SetLimit(limit),
	))
}
