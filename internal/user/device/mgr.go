package device

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func newManager(db *db.MongoClient, log *zap.Logger) *Manager {
	return &Manager{db: db, log: log}
}

type Manager struct {
	db  *db.MongoClient
	log *zap.Logger
	cached
}

func (s *Manager) take(ctx context.Context, deviceId string) (*Device, error) {
	var dev Device
	if err := s.db.Collection(deviceDB).FindOne(ctx, bson.M{"_id": deviceId}).Decode(&dev); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &Device{Id: deviceId}, nil
		}
		return nil, err
	}
	return &dev, nil
}

func (s *Manager) Last(ctx context.Context, userId string) (*Device, error) {
	if list, err := s.listCurr(ctx, userId, 1); err != nil {
		return nil, err
	} else if len(list) == 0 {
		return nil, errors.New("no device found")
	} else {
		return list[0], nil
	}
}

func (s *Manager) Touch(ctx context.Context, userId, deviceId string) error {
	if !ValidID(deviceId) {
		if deviceId != "" {
			s.log.Info("invalid device", zap.String("userId", userId), zap.String("deviceId", deviceId))
		}
		return nil
	}
	dev, err := s.Take(ctx, deviceId)
	if err != nil {
		return err
	}
	if dev.UserId != userId {
		// bind device
		if err := s.linking(ctx, userId, deviceId); err != nil {
			s.log.Warn("link device failed", zap.String("userId", userId), zap.Error(err))
		}
		// update device
		attr := updateAttr(ctx)
		attr["userId"] = userId
		return s.updating(ctx, deviceId, attr, options.Update().SetUpsert(true))
	}
	if time.Since(dev.LiveAt) > 15*time.Minute {
		// update live time
		return s.updating(ctx, deviceId, updateAttr(ctx))
	}
	return nil
}

func (s *Manager) updating(ctx context.Context, deviceId string, attr bson.M, opts ...*options.UpdateOptions) error {
	if resp, err := s.db.Collection(deviceDB).UpdateByID(ctx, deviceId, bson.M{"$set": attr}, opts...); err != nil {
		return err
	} else if resp.MatchedCount == 0 || resp.ModifiedCount == 0 {
		return nil
	}
	s.invalidCache(deviceId)
	return nil
}

func (s *Manager) linking(ctx context.Context, userId, deviceId string) error {
	_, err := s.db.Collection(linkedDB).UpdateOne(ctx,
		bson.M{"userId": userId, "deviceId": deviceId},
		bson.M{"$setOnInsert": bson.M{"createdAt": time.Now()}},
		options.Update().SetUpsert(true),
	)
	return err
}

func (s *Manager) SetStatus(ctx context.Context, deviceId string, status Status) error {
	return s.updating(ctx, deviceId, bson.M{"status": status})
}

func (s *Manager) forgetAll(ctx context.Context, userId string) error {
	_, _ = s.db.Collection(deviceDB).DeleteMany(ctx, bson.M{"userId": userId})
	_, _ = s.db.Collection(linkedDB).DeleteMany(ctx, bson.M{"userId": userId})
	return nil
}
