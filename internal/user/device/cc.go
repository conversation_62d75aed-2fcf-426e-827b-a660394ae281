package device

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/cc"
)

type cached struct {
	cc cc.Cache[string, *Device]
}

func (s *Manager) initCache(syn cc.Sync) {
	s.cc = cc.New[string, *Device](
		40000, cc.LRU,
		cc.Expiration(time.Hour),
		cc.LoaderFunc(func(deviceId string) (*Device, error) {
			return s.take(context.TODO(), deviceId)
		}),
		cc.ExportStats("device.cache"),
		cc.WithSync(syn, "device.cache"),
	)
}

func (s *cached) Take(ctx context.Context, deviceId string) (*Device, error) {
	return s.cc.Get(deviceId)
}

func (s *cached) invalidCache(deviceId string) {
	s.cc.Remove(deviceId)
}
