package device

import (
	"context"
	"slices"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

// TODO move from device to push manager

var pushIdx = []db.Indexer{
	{
		Name: "pushToken",
		Keys: bson.D{
			{Key: "pushToken", Value: 1},
		},
	},
}

type PushOption struct {
	PushToken  string   `bson:"pushToken"`  // 推送令牌
	PushIgnore []string `bson:"pushIgnore"` // 关闭的频道
}

func (s *Manager) SetPushToken(ctx context.Context, deviceId, pushToken string) error {
	if dev, err := s.Take(ctx, deviceId); err != nil {
		return err
	} else if dev.PushToken == pushToken {
		return nil
	}
	return s.updating(ctx, deviceId, bson.M{"pushToken": pushToken})
}

func (s *Manager) ClsPushToken(ctx context.Context, pushToken string) error {
	_, err := s.db.Collection(deviceDB).UpdateMany(ctx, bson.M{"pushToken": pushToken}, bson.M{"$unset": bson.M{"pushToken": ""}})
	return err
}

func (s *Manager) SetPushIgnore(ctx context.Context, deviceId string, option map[string]bool) error {
	dev, err := s.take(ctx, deviceId)
	if err != nil {
		return err
	}
	channels := slices.Clone(dev.PushIgnore)
	for id, accept := range option {
		if accept {
			if idx := slices.Index(channels, id); idx >= 0 {
				channels = append(channels[:idx], channels[idx+1:]...)
			}
		} else {
			if idx := slices.Index(channels, id); idx < 0 {
				channels = append(channels, id)
			}
		}
	}
	if slices.Equal(dev.PushIgnore, channels) {
		return nil
	}
	return s.updating(ctx, deviceId, bson.M{"pushIgnore": channels})
}
