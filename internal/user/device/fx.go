package device

import (
	"context"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.MongoClient, syn cc.Sync, vnd log.Vendor) *Manager {
	db.SyncSchema(deviceDB, 1, append(deviceIdx, pushIdx...)...)
	db.SyncSchema(linkedDB, 1, linkedIdx...)
	mgr := newManager(db, vnd.Scope("device.mgr"))
	mgr.initCache(syn)
	return mgr
}

func Invoke(evb ev.Bus, mgr *Manager) {
	evb.Watch(user.EvUserDeleted, "device.cleanup", ev.<PERSON>Watcher(func(ctx context.Context, acc *user.Account) error {
		return mgr.forgetAll(ctx, acc.UserId)
	}))
}
