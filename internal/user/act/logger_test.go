package act

import (
	"context"
	"testing"

	"github.com/glebarez/sqlite"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func TestLogger(t *testing.T) {
	dial := sqlite.Open(":memory:")
	gdb, err := gorm.Open(dial)
	if err != nil {
		t.Fatal(err)
	}

	log, _ := zap.NewDevelopment()

	dbc := &db.Client{DB: gdb.Debug()}
	l := newLogger(dbc, log)

	if err := dbc.DoAutoMigrate(); err != nil {
		t.Fatal(err)
	}

	var (
		ctx   = context.TODO()
		user  = "user"
		scene = "scene"
		deed  = "act"
	)

	act0, err := l.Take(ctx, user, scene, deed)
	if assert.NoError(t, err) {
		assert.Equal(t, user, act0.UserId)
		assert.Equal(t, scene, act0.Scene)
		assert.Equal(t, deed, act0.Deed)
		assert.Equal(t, 0, act0.Count)
	}

	rec1 := l.Record(ctx, user, scene, deed)
	if rec1 != nil {
		t.Fatal(rec1)
	}

	cnt1, _ := l.Count(ctx, user, scene, deed)
	assert.Equal(t, 1, cnt1)

	rec2 := l.Record(ctx, user, scene, deed)
	if rec2 != nil {
		t.Fatal(rec2)
	}

	cnt2, _ := l.Count(ctx, user, scene, deed)
	assert.Equal(t, 2, cnt2)

	rec3 := l.Record(ctx, user, scene, deed)
	if rec3 != nil {
		t.Fatal(rec3)
	}

	cnt3, _ := l.Count(ctx, user, scene, deed)
	assert.Equal(t, 3, cnt3)

	cntS, _ := l.Counts(ctx, user, scene)
	assert.Equal(t, 3, cntS[deed])

	_ = l.Reset(ctx, user, scene, deed)
	cnt4, _ := l.Count(ctx, user, scene, deed)
	assert.Equal(t, 0, cnt4)
}
