package act

import "time"

const (
	maxScene = 16
	maxDeed  = 16
)

type Action struct {
	ID        uint      `gorm:"primaryKey"`
	UserId    string    `gorm:"not null;size:32;uniqueIndex:user_action"`
	Scene     string    `gorm:"not null;size:16;uniqueIndex:user_action"` // 场景
	Deed      string    `gorm:"not null;size:16;uniqueIndex:user_action"` // 行为
	Count     int       `gorm:"not null;default:0"`                       // 计数
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (a *Action) TableName() string {
	return "user_actions"
}
