package act

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrSceneDeedTooLong = errors.New("scene or deed too long")
)

type Logger interface {
	Take(ctx context.Context, userId, scene, deed string) (*Action, error)
	Takes(ctx context.Context, userId, scene string, deeds ...string) (Deeds, error)
	Count(ctx context.Context, userId, scene, deed string) (int, error)
	Counts(ctx context.Context, userId, scene string, deeds ...string) (map[string]int, error)
	Record(ctx context.Context, userId, scene, deed string) error
	RecordBy(ctx context.Context, userId, scene, deed string, amount int) error
	Reset(ctx context.Context, userId, scene, deed string) error
	Forget(ctx context.Context, userId, scene, deed string) error
}

func newLogger(db *db.Client, log *zap.Logger) *logger {
	db.ApplyMigrate(&Action{})
	return &logger{
		db:  db,
		log: log,
	}
}

type logger struct {
	db  *db.Client
	log *zap.Logger
}

func (l *logger) take(ctx context.Context, userId, scene, deed string, fields ...string) (*Action, error) {
	var act Action
	if err := db.UseTx(ctx, l.db).Select(fields).Where("user_id = ? AND scene = ? AND deed = ?", userId, scene, deed).Take(&act).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &Action{UserId: userId, Scene: scene, Deed: deed}, nil
		}
		return nil, err
	}
	return &act, nil
}

func (l *logger) takes(ctx context.Context, userId, scene string, deeds []string, fields ...string) ([]*Action, error) {
	q := db.UseTx(ctx, l.db).Where("user_id = ? AND scene = ?", userId, scene)
	if len(deeds) > 0 {
		q = q.Where("deed IN ?", deeds)
	}

	var acts []*Action
	if err := q.Select(fields).Find(&acts).Error; err != nil {
		return nil, err
	}

	return acts, nil
}

func (l *logger) Take(ctx context.Context, userId, scene, deed string) (*Action, error) {
	return l.take(ctx, userId, scene, deed, "*")
}

func (l *logger) Takes(ctx context.Context, userId, scene string, deeds ...string) (Deeds, error) {
	records, err := l.takes(ctx, userId, scene, deeds, "*")
	if err != nil {
		return nil, err
	}

	happens := make(Deeds)
	for _, act := range records {
		happens[act.Deed] = act
	}

	return happens, nil
}

func (l *logger) Count(ctx context.Context, userId, scene, deed string) (int, error) {
	if act, err := l.take(ctx, userId, scene, deed, "count"); err != nil {
		return 0, err
	} else {
		return act.Count, nil
	}
}

func (l *logger) Counts(ctx context.Context, userId, scene string, deeds ...string) (map[string]int, error) {
	records, err := l.takes(ctx, userId, scene, deeds, "deed", "count")
	if err != nil {
		return nil, err
	}

	counts := make(map[string]int)
	for _, act := range records {
		counts[act.Deed] = act.Count
	}

	return counts, nil
}

func (l *logger) Record(ctx context.Context, userId, scene, deed string) error {
	return l.record(ctx, userId, scene, deed, 1)
}

func (l *logger) RecordBy(ctx context.Context, userId, scene, deed string, amount int) error {
	return l.record(ctx, userId, scene, deed, amount)
}

func (l *logger) record(ctx context.Context, userId, scene, deed string, amount int) error {
	if len(scene) > maxScene || len(deed) > maxDeed {
		l.log.Error("act scene or deed too long", zap.String("userId", userId), zap.String("scene", scene), zap.String("deed", deed))
		return ErrSceneDeedTooLong
	}

	increase := map[string]any{
		"count": gorm.Expr("count + ?", amount),
	}

	updated := db.UseTx(ctx, l.db).Model(&Action{}).Where("user_id = ? AND scene = ? AND deed = ?", userId, scene, deed).Updates(increase)
	if err := updated.Error; err != nil {
		return err
	} else if updated.RowsAffected > 0 {
		return nil
	}

	act := &Action{
		UserId: userId,
		Scene:  scene,
		Deed:   deed,
		Count:  amount,
	}

	created := db.UseTx(ctx, l.db).Clauses(clause.OnConflict{
		DoUpdates: append(
			clause.Assignments(increase),
			clause.AssignmentColumns([]string{"updated_at"})...,
		),
	}).Create(act)
	if err := created.Error; err != nil {
		return err
	}

	return nil
}

func (l *logger) Reset(ctx context.Context, userId, scene, deed string) error {
	updated := db.UseTx(ctx, l.db).Model(&Action{}).Where("user_id = ? AND scene = ? AND deed = ?", userId, scene, deed).Update("count", 0)
	if err := updated.Error; err != nil {
		return err
	}
	return nil
}

func (l *logger) Forget(ctx context.Context, userId, scene, deed string) error {
	deleted := db.UseTx(ctx, l.db).Where("user_id = ? AND scene = ? AND deed = ?", userId, scene, deed).Delete(&Action{})
	if err := deleted.Error; err != nil {
		return err
	}
	return nil
}
