package uctz

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/geoip"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Invoke(users user.Hook) {
	users.PreCreate(func(ctx context.Context, acc *user.Account) error {
		{
			// get from header
			if acc.Country == "" {
				if g := api.Unwrap(ctx); g != nil {
					acc.Country = app.Country(g)
				}
			}
			// get from client ip
			if acc.Country == "" {
				acc.Country = geoip.Client(ctx).Country
			}
			// must have country
			if acc.Country == "" {
				return errors.New("invalid country")
			}
		}
		{
			// get from header
			if acc.Timezone == "" {
				if g := api.Unwrap(ctx); g != nil {
					if tz := app.Timezone(g); tz != ctz.L() {
						acc.Timezone = tz.String()
					}
				}
			}
			// get from client ip
			if acc.Timezone == "" {
				acc.Timezone = geoip.Client(ctx).Timezone
			}
			// get from default
			if acc.Timezone == "" {
				acc.Timezone = ctz.L().String()
			}
		}
		return nil
	})
}
