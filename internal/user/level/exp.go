package level

import "gitlab.sskjz.com/overseas/live/osl/pkg/lv"

var grades = []int{
	1, 10000, 22800, 41300, 64100, 94000, 128200, 188100, 249400, 346300,
	456000, 599900, 816500, 1043100, 1383600, 1852400, 2422400, 3134900, 4132400, 5414800,
	7409700, 9404700, 12397100, 15674400, 21374200, 29923900, 37048700, 48448200, 62697700, 85496900,
	99746400, 142494800, 185243300, 242241200, 327738100, 427484500, 555729900, 726723700, 968964900, 1139958700,
	1353701000, 1567443200, 1852432900, 2279917400, 2707402000, 3134886500, 3704865800, 4417340000, 5272309100, 6127278100,
	7267236800, 8407195600, 9832144000, 11399587200, 13537009800, 15674432400, 18524329200, 21374226000, 24224122800, 28498968000,
	34198761600, 39898555200, 45598348800, 54148039200, 62697729600, 71247420000, 79797110400, 96896491200, 113995872000, 139644943200,
	163869066000, 185243292000, 213742260000, 242241228000, 284989680000,
}

func level(exp int) int {
	return lv.Lv(grades, exp)
}

func lvExp(at int) (min, max int) {
	return lv.Exp(grades, at)
}

func lvInfo(exp int) *lv.Info {
	return lv.Make(grades, exp)
}
