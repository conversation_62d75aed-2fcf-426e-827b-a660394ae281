package level

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func newManager(ev ev.Bus, db *db.Client, dm *redi.Mutex, um *user.Manager, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Profile{})
	return &Manager{
		ev:  ev,
		db:  db,
		dm:  dm,
		um:  um,
		log: log,
	}
}

type Manager struct {
	ev  ev.Bus
	db  *db.Client
	dm  *redi.Mutex
	um  *user.Manager
	log *zap.Logger
}

func (s *Manager) take(ctx context.Context, userId string) (*Profile, error) {
	var profile Profile
	if err := db.UseTx(ctx, s.db).Where("user_id = ?", userId).Take(&profile).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &Profile{UserId: userId}, nil
		}
		return nil, err
	}
	return &profile, nil
}

func (s *Manager) LevelInfo(ctx context.Context, userId string) (*Info, error) {
	pp, err := s.take(ctx, userId)
	if err != nil {
		return nil, err
	}
	return lvInfo(pp.Points), nil
}
