package level

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	keyUserMutex = "USR:LV:MUTEX:%s" // userId
)

func (s *Manager) Gifting(ctx context.Context, userId string, diamonds int, lucky bool, roomId string) (*Info, error) {
	amount := diamonds * 10
	if lucky {
		amount = diamonds
	}
	return s.AddExpInRoom(ctx, userId, amount, roomId)
}

func (s *Manager) AddExpInRoom(ctx context.Context, userId string, amount int, roomId string) (*Info, error) {
	exp, err := s.addExp(ctx, userId, amount)
	if err != nil {
		return nil, err
	}

	if pLv, cLv := level(exp.prev), level(exp.curr); pLv != cLv {
		s.ev.Emit(ctx, evt.UserLevelUpgrade, &evt.LevelUpgrade{
			RoomId: roomId,
			UserId: userId,
			Level:  cLv,
			Prev:   pLv,
		})
	}

	return lvInfo(exp.curr), nil
}

func (s *Manager) AddExp(ctx context.Context, userId string, amount int) (*Info, error) {
	exp, err := s.addExp(ctx, userId, amount)
	if err != nil {
		return nil, err
	}
	return lvInfo(exp.curr), nil
}

type addExp struct {
	prev, curr int
}

func (s *Manager) addExp(ctx context.Context, userId string, exp int) (*addExp, error) {
	l, err := s.dm.Lock(ctx, fmt.Sprintf(keyUserMutex, userId))
	if err != nil {
		return nil, err
	}
	defer l.MustUnlock()

	pp, err := s.take(ctx, userId)
	if err != nil {
		return nil, err
	}

	if err := s.recordBy(ctx, userId, "points", exp); err != nil {
		return nil, err
	}

	prev := pp.Points
	curr := prev + exp

	if pLv, cLv := level(prev), level(curr); pLv != cLv {
		if err := s.um.Update(ctx, userId, user.SetLevel(cLv)); err != nil {
			s.log.Error("update level failed", zap.String("userId", userId), zap.Int("curr", curr), zap.Error(err))
		}
	}

	s.log.Debug("add exp", zap.String("userId", userId),
		zap.Int("exp", exp), zap.Int("curr", curr),
	)

	return &addExp{prev, curr}, nil
}

func (s *Manager) recordBy(ctx context.Context, userId string, field string, amount int) error {
	update := map[string]any{
		field: gorm.Expr(fmt.Sprintf("%s + ?", field), amount),
	}

	updated := db.UseTx(ctx, s.db).Model(&Profile{}).Where("user_id = ?", userId).Updates(update)
	if err := updated.Error; err != nil {
		return err
	} else if updated.RowsAffected > 0 {
		return nil
	}

	create := map[string]any{
		"updated_at": time.Now(),
		"user_id":    userId,
		field:        amount,
	}

	created := db.UseTx(ctx, s.db).Model(&Profile{}).Clauses(clause.OnConflict{
		DoUpdates: append(
			clause.Assignments(update),
			clause.AssignmentColumns([]string{"updated_at"})...,
		),
	}).Create(create)
	if err := created.Error; err != nil {
		return err
	}

	return nil
}
