package avatar

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPathGen(t *testing.T) {
	tests := []struct {
		bucket string
		key    string
		exp    string
	}{
		{"bucket", "key", "oss://bucket/key"},
	}
	for _, tt := range tests {
		t.Run(tt.exp, func(t *testing.T) {
			assert.Equal(t, tt.exp, genUrl(tt.bucket, tt.key))
		})
	}
}

func TestPathExp(t *testing.T) {
	tests := []struct {
		bucket string
		raw    string
		exp    string
	}{
		{"bucket", "oss://bucket/key", "key"},
		{"uac-test", "oss://uac-test/111", "111"},
	}
	for _, tt := range tests {
		t.Run(tt.raw, func(t *testing.T) {
			assert.Equal(t, tt.exp, expUrl(tt.bucket, tt.raw))
		})
	}
}
