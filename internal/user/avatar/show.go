package avatar

import (
	"context"
	"errors"
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func newShower(oss *sto.Client, cfg conf.Avatar, log *zap.Logger) *Show {
	return &Show{cli: oss, conf: oss.Conf(), style: cfg.Style, log: log}
}

type Show struct {
	cli   *sto.Client
	conf  sto.Conf
	style conf.AvatarStyle
	log   *zap.Logger
}

func (s *Show) Style() conf.AvatarStyle {
	return s.style
}

func (s *Show) Default(opts ...URLOpt) string {
	opt := makeOpts(s, opts)
	return s.conf.ExternalURL("avatar/default", opt.style)
}

func (s *Show) LargeOf(url string) string {
	if idx := strings.LastIndex(url, s.style.Thumb); idx >= 0 {
		url = url[:idx] + s.style.Large + url[idx+len(s.style.Thumb):]
	}
	return url
}

func (s *Show) Check(key string) (string, error) {
	exists, err := s.cli.IsObjectExist(key)
	if err != nil {
		return "", err
	} else if !exists {
		return "", errors.New("file not exists")
	}
	return genUrl(s.conf.Bucket, key), nil
}

func (s *Show) URI(key string) string {
	return genUrl(s.conf.Bucket, key)
}

func (s *Show) URL(raw string, opts ...URLOpt) string {
	opt := makeOpts(s, opts)

	if key := expUrl(s.conf.Bucket, raw); key != "" {
		return s.conf.ExternalURL(key, opt.style)
	}

	return raw
}

func (s *Show) checking(ctx context.Context, uac *user.Account) error {
	if user.NoHooking(ctx) {
		return nil
	}

	if uac.Avatar == "" {
		uac.Avatar = s.Default()
		return nil
	}

	if post := s.URL(uac.Avatar); post != uac.Avatar {
		uac.Avatar = post
	}

	return nil
}
