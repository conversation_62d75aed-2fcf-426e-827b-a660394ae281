package avatar

import (
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(desc *conf.Setting, si sto.Instance, vnd log.Vendor) (*Show, error) {
	main, err := si.Take(desc.Avatar.Store)
	if err != nil {
		return nil, err
	}
	return newShower(main, desc.Avatar, vnd.Scope("avatar.show")), nil
}

func Invoke(show *Show, users user.Hook) error {
	users.PreCreate(show.uploading)
	users.OnFetching(show.checking)
	return nil
}
