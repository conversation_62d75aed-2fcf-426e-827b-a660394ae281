package avatar

import (
	"context"
	"net/http"
	"strings"
	"time"

	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func (s *Show) uploading(ctx context.Context, uac *user.Account) error {
	if uac.Avatar == "" || !strings.HasPrefix(uac.Avatar, "http") {
		return nil
	}

	po := co.Apply(co.Named("avatar.uploader"))

	done := make(chan struct{})
	po.Submit(func() {
		resp, err := http.Get(uac.Avatar)
		if err != nil {
			s.log.Warn("download avatar failed", zap.Error(err))
			return
		}
		defer resp.Body.Close()
		uri := "avatar/" + uac.UserId + "/" + time.Now().Format("********") + "/default"
		if err := s.cli.PutObject(uri, resp.Body); err != nil {
			s.log.Warn("upload avatar failed", zap.Error(err))
			return
		}
		uac.Avatar = genUrl(s.cli.<PERSON>, uri)
		close(done)
	})

	select {
	case <-time.After(3 * time.Second):
		s.log.Info("upload avatar timeout", zap.String("url", uac.Avatar))
		uac.Avatar = ""
	case <-done:
		s.log.Debug("upload avatar success", zap.String("url", uac.Avatar))
	}

	return nil
}
