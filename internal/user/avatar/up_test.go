package avatar

import (
	"context"
	"testing"

	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/fx"
)

var testMod = fx.Module("avatar", fx.Provide(
	sto.Provide, Provide,
))

func TestUpload(t *testing.T) {
	fx2.Testing(t, testMod, func(s *Show) error {
		uac := &user.Account{
			UserId: "21ad0083d6104ed1b742ce821cc8395e",
			Avatar: "https://lh3.googleusercontent.com/a/ACg8ocIZuwvJ5oh0xYVJt6Ay9rlJ2V__bhDIdTK84wkbrY3V2C4mrUU=s96-c",
		}
		if err := s.uploading(context.TODO(), uac); err != nil {
			return err
		}
		t.Logf("avatar uploaded: %s", uac.Avatar)
		return nil
	})
}
