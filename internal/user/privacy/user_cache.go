package privacy

import (
	"context"
	"slices"

	"github.com/samber/lo"
)

var (
	userCares = lo.Map([]Mask{
		HideGender,
		HideBirthday,
	}, func(key Mask, _ int) string { return maskFields[key] })
)

type usync struct {
	syncu func(userId string)
}

func (s *Manager) postUpdate(ctx context.Context, userId string, update map[string]any) error {
	s.invalidCache(userId)

	if s.syncu == nil {
		return nil
	}

	for k := range update {
		if slices.Contains(userCares, k) {
			s.syncu(userId)
			break
		}
	}

	return nil
}
