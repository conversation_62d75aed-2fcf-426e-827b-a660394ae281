package privacy

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/cc"
)

type Getter interface {
	Take(ctx context.Context, userId string) (*Setting, error)
}

func (s *Manager) initCache(syn cc.Sync) {
	s.sc = cc.New[string, *Setting](
		40000, cc.LRU,
		cc.Expiration(time.Hour),
		cc.LoaderFunc(func(userId string) (*Setting, error) {
			return s.take(context.TODO(), userId)
		}),
		cc.ExportStats("privacy.cache"),
		cc.WithSync(syn, "privacy.cache"),
	)
}

type cached struct {
	sc cc.Cache[string, *Setting]
}

func (s *cached) Take(ctx context.Context, userId string) (*Setting, error) {
	return s.sc.Get(userId)
}

func (s *cached) invalidCache(userId string) {
	s.sc.Remove(userId)
}
