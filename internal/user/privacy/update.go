package privacy

type Mask int

const (
	ShowGender Mask = 1 << iota
	HideGender
	ShowBirthday
	HideBirthday
	ShowMomentLike
	HideMomentLike
	ShowLiveSession
	HideLiveSession
	maxSet
)

func (s Mask) Has(cmp Mask) bool {
	return s&cmp == cmp
}

func (s Mask) Set(in ...Mask) Mask {
	n := s
	for _, add := range in {
		n |= add
	}
	return n
}

var (
	maskFields = map[Mask]string{
		HideGender:      "hide_gender",
		HideBirthday:    "hide_birthday",
		HideMomentLike:  "hide_moment_like",
		HideLiveSession: "hide_live_session",
	}
	maskValues = map[Mask]bool{
		HideGender:      true,
		HideBirthday:    true,
		HideMomentLike:  true,
		HideLiveSession: true,
	}
)

func maskData(raw Mask) map[string]any {
	out := make(map[string]any)
	for i := Mask(1); i < maxSet; i <<= 1 {
		if !raw.Has(i) {
			continue
		}
		k, has := maskFields[i]
		if !has {
			k = maskFields[i<<1]
		}
		v, has := maskValues[i]
		if !has {
			v = !maskValues[i<<1]
		}
		out[k] = v
	}
	return out
}
