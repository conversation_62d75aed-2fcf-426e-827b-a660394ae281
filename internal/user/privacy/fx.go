package privacy

import (
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.Client, syn cc.Sync, vnd log.Vendor) (*Manager, Getter) {
	mgr := newManager(db, vnd.Scope("privacy.mgr"))
	mgr.initCache(syn)
	return mgr, mgr
}

func Invoke(pm *Manager, um *user.Manager, users user.Hook) {
	pm.syncu = um.ForceSync
	users.OnFetching(pm.masking)
}
