package privacy

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func newManager(db *db.Client, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Setting{})
	return &Manager{
		db:  db,
		log: log,
	}
}

type Manager struct {
	db  *db.Client
	log *zap.Logger
	cached
	usync
}

func (s *Manager) take(ctx context.Context, userId string) (*Setting, error) {
	var setting Setting
	if err := db.UseTx(ctx, s.db).Where("user_id = ?", userId).Take(&setting).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &Setting{UserId: userId}, nil
		}
		return nil, err
	}
	return &setting, nil
}

func (s *Manager) Update(ctx context.Context, userId string, mask Mask) error {
	update := maskData(mask)
	if len(update) == 0 {
		return nil
	}

	updated := db.UseTx(ctx, s.db).Model(&Setting{}).Where("user_id = ?", userId).Updates(update)
	if err := updated.Error; err != nil {
		return err
	} else if updated.RowsAffected > 0 {
		return s.postUpdate(ctx, userId, update)
	}

	update["user_id"] = userId
	update["updated_at"] = time.Now()

	created := db.UseTx(ctx, s.db).Model(&Setting{}).Create(update)
	if err := created.Error; err != nil {
		return err
	}

	return s.postUpdate(ctx, userId, update)
}
