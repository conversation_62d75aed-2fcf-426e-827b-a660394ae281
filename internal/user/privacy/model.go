package privacy

import (
	"time"
)

type Setting struct {
	ID              uint      `gorm:"primaryKey"`
	UserId          string    `gorm:"not null;size:32;unique"`
	HideGender      bool      `gorm:"not null;default:false"` // 隐藏性别
	HideBirthday    bool      `gorm:"not null;default:false"` // 隐藏生日
	HideMomentLike  bool      `gorm:"not null;default:false"` // 隐藏点赞动态
	HideLiveSession bool      `gorm:"not null;default:false"` // 隐藏直播动态
	UpdatedAt       time.Time `gorm:"not null"`
}

func (s *Setting) TableName() string {
	return "user_privacy"
}
