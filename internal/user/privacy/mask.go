package privacy

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var noBirthday = time.Date(1000, 1, 1, 0, 0, 0, 0, time.Local)

func (s *Manager) masking(ctx context.Context, acc *user.Account) error {
	if user.NoHooking(ctx) || noMasking(ctx) {
		return nil
	}

	setting, err := s.Take(ctx, acc.UserId)
	if err != nil {
		return err
	}

	if setting.HideGender {
		acc.Gender = 0
	}

	if setting.HideBirthday {
		acc.Birthday = noBirthday
	}

	return nil
}
