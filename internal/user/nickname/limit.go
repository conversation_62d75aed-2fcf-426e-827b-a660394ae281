package nickname

import (
	"context"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/act"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"time"
)

const (
	period = 30
	limit  = 4
	scene  = "nickname"
	deed   = "update"
)

type Limiter struct {
	act act.Logger
}

func (s *Limiter) Status(ctx context.Context, userId string) (*Status, error) {
	do, err := s.act.Take(ctx, userId, scene, deed)
	if err != nil {
		return nil, err
	}
	begin := do.CreatedAt
	if do.Count == 0 {
		begin = time.Now()
	} else if time.Since(begin) > period*24*time.Hour {
		_ = s.act.Forget(ctx, userId, scene, deed)
		begin = time.Now()
		do.Count = 0
	}
	return &Status{
		Period: period,
		Limit:  limit,
		Used:   do.Count,
		Expire: begin.AddDate(0, 0, period),
	}, nil
}

func (s *Limiter) onUserUpdated(ctx context.Context, evd *user.EvUpdating) error {
	old, acc := evd.Old, evd.New
	if old.Nickname == acc.Nickname {
		return nil
	}
	return s.act.Record(ctx, acc.UserId, scene, deed)
}
