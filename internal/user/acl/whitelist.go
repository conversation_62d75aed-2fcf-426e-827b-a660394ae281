package acl

import (
	"context"
	"errors"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

func (s *Manager) initCache(syn cc.Sync) {
	s.ids = cc.New[int, []string](1, cc.Simple, cc.LoaderFunc(func(_ int) ([]string, error) {
		list, _ := s.ListWhitelist(context.TODO())
		return lo.Map(list, func(dev *Whitelist, _ int) string { return dev.Id }), nil
	}),
		cc.WithSync(syn, "acl.whitelist"),
		cc.ExportStats("acl.whitelist.ids"),
	)
	s.ips = cc.New[int, []string](1, cc.Simple, cc.LoaderExpireFunc(func(_ int) ([]string, time.Duration, error) {
		ids, _ := s.ids.Get(0)
		ips := make([]string, 0, len(ids))
		for _, id := range ids {
			if dev, _ := s.dm.Take(context.TODO(), id); dev != nil && dev.ClientIP != "" {
				if time.Since(dev.LiveAt) < 3*24*time.Hour {
					ips = append(ips, dev.ClientIP)
				}
			}
		}
		return ips, 15 * time.Minute, nil
	}),
		cc.ExportStats("acl.whitelist.ips"),
	)
}

type whiteCC struct {
	ids cc.Cache[int, []string]
	ips cc.Cache[int, []string]
}

func (s *Manager) InWhitelist(ctx context.Context, deviceId string) bool {
	list, _ := s.ids.Get(0)
	return slices.Contains(list, deviceId)
}

func (s *Manager) InWhitelist2(ctx context.Context, deviceId, clientIP string) bool {
	if s.InWhitelist(ctx, deviceId) {
		return true
	}
	list, _ := s.ips.Get(0)
	return slices.Contains(list, clientIP)
}

func (s *Manager) ListWhitelist(ctx context.Context) ([]*Whitelist, error) {
	cursor, err := s.db.Collection(whitelistDB).Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	var list []*Whitelist
	if err := cursor.All(ctx, &list); err != nil {
		return nil, err
	}
	return list, nil
}

func (s *Manager) AddWhitelist(ctx context.Context, deviceId, remark string) error {
	if deviceId == "" {
		return errors.New("invalid deviceId")
	}
	if _, err := s.db.Collection(whitelistDB).InsertOne(ctx, &Whitelist{
		Id:        deviceId,
		Remark:    remark,
		CreatedAt: time.Now(),
	}); err != nil {
		if db.IsDuplicate(err) {
			return nil
		}
		return err
	}
	s.ids.Remove(0)
	return nil
}

func (s *Manager) DelWhitelist(ctx context.Context, deviceId string) error {
	_, err := s.db.Collection(whitelistDB).DeleteOne(ctx, bson.M{"_id": deviceId})
	if err != nil {
		return err
	}
	s.ids.Remove(0)
	return nil
}
