package acl

import (
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

func newManager(rc *redi.Client, db *db.MongoClient, dm *device.Manager, log *zap.Logger) *Manager {
	return &Manager{rc: rc, db: db, dm: dm, log: log}
}

type Manager struct {
	rc  *redi.Client
	db  *db.MongoClient
	dm  *device.Manager
	log *zap.Logger
	whiteCC
}
