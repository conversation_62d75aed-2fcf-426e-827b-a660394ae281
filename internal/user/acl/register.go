package acl

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
)

var (
	ErrDeviceBlocked = biz.NewError(biz.ErrUsernameExists, "username exists")
)

func Invoke(links ulink.Hook, dm *device.Manager) {
	links.PreCreate(func(ctx context.Context, src *ulink.Connect) error {
		if g := api.Unwrap(ctx); g != nil {
			if dev, err := dm.Take(ctx, app.DeviceId(g)); err == nil && dev.Status.Blocked() {
				return ErrDeviceBlocked
			}
		}
		return nil
	})
}
