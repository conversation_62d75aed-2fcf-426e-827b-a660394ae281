package acl

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

const (
	keyForbidLogs = "ACL:FORBID:LOGS"
)

func (s *Manager) LogForbid(ctx *api.Context) error {
	req := &Access{
		Id:      app.DeviceId(ctx),
		IP:      ctx.ClientIP(),
		Name:    app.DeviceName(ctx),
		Channel: app.ChannelId(ctx),
		Version: ctx.GetHeader(app.HdrVersion),
		Time:    time.Now(),
	}
	if req.Id == "" {
		return nil
	}
	log, _ := sonic.MarshalString(req)
	txp := s.rc.Pipeline()
	txp.LPush(ctx, keyForbidLogs, log)
	txp.LTrim(ctx, keyForbidLogs, 0, 99)
	txp.Expire(ctx, keyForbidLogs, 24*time.Hour)
	_, err := txp.Exec(ctx)
	return err
}

type Access struct {
	Id      string    `json:"id"`
	IP      string    `json:"ip"`
	Name    string    `json:"name"`
	Channel string    `json:"channel"`
	Version string    `json:"version"`
	Time    time.Time `json:"time"`
}

func (s *Manager) ForbidLogs(ctx context.Context) ([]*Access, error) {
	raw := s.rc.LRange(ctx, keyForbidLogs, 0, 99).Val()
	out := make([]*Access, 0, len(raw))
	for _, bs := range raw {
		var dev Access
		if err := sonic.UnmarshalString(bs, &dev); err == nil {
			out = append(out, &dev)
		}
	}
	return out, nil
}
