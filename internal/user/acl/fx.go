package acl

import (
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(rc *redi.Client, db *db.MongoClient, dm *device.Manager, syn cc.Sync, vnd log.Vendor) *Manager {
	db.SyncSchema(whitelistDB, 1)
	mgr := newManager(rc, db, dm, vnd.Scope("acl.mgr"))
	mgr.initCache(syn)
	return mgr
}
