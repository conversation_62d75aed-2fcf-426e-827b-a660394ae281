package dress

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type Vehicle struct {
	Id       string    `bson:"id"`
	ExpireAt time.Time `bson:"expireAt"`
}

func (s *Vehicle) Valid() bool {
	if s == nil {
		return false
	}
	return s.ExpireAt.After(time.Now())
}

func (s *Manager) Vehicle(ctx context.Context, userId string) string {
	p, err := s.Take(ctx, userId)
	if err != nil {
		return ""
	}
	if !p.Vehicle.Valid() {
		return ""
	}
	return p.Vehicle.Id
}

func (s *Manager) SetVehicle(ctx context.Context, userId, id string, expireAt time.Time) error {
	return s.update(ctx, userId, bson.M{"vehicle": &Vehicle{Id: id, ExpireAt: expireAt}})
}
