package vehicle

const (
	Roadster1 = "vehicle_01" // 梦幻跑车
	Roadster2 = "vehicle_02" // 豪华跑车
	Airplane1 = "vehicle_03" // 纯金飞机
	JokerCar  = "vehicle_04" // 愚人节：小丑
	PartyCar  = "vehicle_05" // 愚人节：马戏团
	RabbitCar = "vehicle_06" // 复活节：兔兔汽车
)

type Vehicle struct {
	Id       string
	Name     map[string]string // lang -> name
	Image    string
	EffectId int
	Duration int
}

var Vehicles = []Vehicle{
	{
		Id: Roadster1,
		Name: map[string]string{
			"default": "Luxury car",
			"pt":      "Carro luxo",
		},
		Image:    "https://godzilla-live-oss.kako.live/res/vehicle/roadster1.png",
		EffectId: 30003,
		Duration: 6000,
	},
	{
		Id: Roadster2,
		Name: map[string]string{
			"default": "Super Car",
			"pt":      "Super Karrão",
		},
		Image:    "https://godzilla-live-oss.kako.live/res/vehicle/roadster2.png",
		EffectId: 30002,
		Duration: 5000,
	},
	{
		Id: Airplane1,
		Name: map[string]string{
			"default": "Golden Hunt",
			"pt":      "Caça dourado",
		},
		Image:    "https://godzilla-live-oss.kako.live/res/vehicle/airplane1.png",
		EffectId: 30001,
		Duration: 7000,
	},
	{
		Id: JokerCar,
		Name: map[string]string{
			"default": "Joker car",
			"pt":      "Palhaço",
		},
		Image:    "https://godzilla-live-oss.kako.live/res/vehicle/vehicle_xiaochou_b_250401.png",
		EffectId: 30005,
		Duration: 5000,
	},
	{
		Id: PartyCar,
		Name: map[string]string{
			"default": "Party car",
			"pt":      "Festão",
		},
		Image:    "https://godzilla-live-oss.kako.live/res/vehicle/vehicle_maxituan_b_250401.png",
		EffectId: 30004,
		Duration: 5000,
	},
	{
		Id: RabbitCar,
		Name: map[string]string{
			"default": "Rabbit car",
			"pt":      "Carro de coelho",
		},
		Image:    "https://godzilla-live-oss.kako.live/res/vehicle/vehicle_tutuqiche_250415.png",
		EffectId: 30006,
		Duration: 5000,
	},
}

func Find(id string) *Vehicle {
	for _, v := range Vehicles {
		if v.Id == id {
			return &v
		}
	}
	return nil
}
