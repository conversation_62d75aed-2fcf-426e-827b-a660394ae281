package dress

import (
	"context"
	"time"

	"gitlab.sskjz.com/go/cc"
)

type cached struct {
	pc cc.Cache[string, *Profile]
}

func (s *Manager) initCache(syn cc.Sync) {
	s.pc = cc.New[string, *Profile](
		40000, cc.LRU,
		cc.Expiration(time.Hour),
		cc.LoaderFunc(func(userId string) (*Profile, error) {
			return s.take(context.TODO(), userId)
		}),
		cc.ExportStats("dress.cache"),
		cc.WithSync(syn, "dress.cache"),
	)
}

func (s cached) Take(ctx context.Context, userId string) (*Profile, error) {
	return s.pc.Get(userId)
}

func (s cached) invalidCache(userId string) {
	s.pc.Remove(userId)
}
