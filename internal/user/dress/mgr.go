package dress

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func newManager(db *db.MongoClient, log *zap.Logger) *Manager {
	return &Manager{db: db, log: log}
}

type Manager struct {
	db  *db.MongoClient
	log *zap.Logger
	cached
}

func (s *Manager) take(ctx context.Context, userId string) (*Profile, error) {
	var prof Profile
	if err := s.db.Collection(profileDB).FindOne(ctx, bson.M{"_id": userId}).Decode(&prof); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &Profile{Id: userId}, nil
		}
		return nil, err
	}
	return &prof, nil
}

func (s *Manager) update(ctx context.Context, userId string, update bson.M) error {
	_, err := s.db.Collection(profileDB).UpdateByID(ctx, userId, bson.M{"$set": update}, options.Update().SetUpsert(true))
	if err == nil {
		s.invalidCache(userId)
	}
	return err
}
