package dress

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type Badge struct {
	Url      string    `bson:"url"`
	ExpireAt time.Time `bson:"expireAt"`
}

func (s *Badge) Valid() bool {
	if s == nil {
		return false
	}
	return s.ExpireAt.After(time.Now())
}

func (s *Manager) BadgeUrl(ctx context.Context, userId string) string {
	p, err := s.Take(ctx, userId)
	if err != nil {
		return ""
	}
	if !p.Badge.Valid() {
		return ""
	}
	return p.Badge.Url
}

func (s *Manager) SetBadge(ctx context.Context, userId, url string, expireAt time.Time) error {
	return s.update(ctx, userId, bson.M{"badge": &Badge{Url: url, ExpireAt: expireAt}})
}
