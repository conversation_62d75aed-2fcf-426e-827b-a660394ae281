package dress

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type AvatarBorder struct {
	Url      string    `bson:"url"`
	ExpireAt time.Time `bson:"expireAt"`
}

func (s *AvatarBorder) Valid() bool {
	if s == nil {
		return false
	}
	return s.ExpireAt.After(time.Now())
}

func (s *Manager) AvatarBorder(ctx context.Context, userId string) string {
	p, err := s.Take(ctx, userId)
	if err != nil {
		return ""
	}
	if !p.AvatarBorder.Valid() {
		return ""
	}
	return p.AvatarBorder.Url
}

func (s *Manager) SetAvatarBorder(ctx context.Context, userId, url string, expireAt time.Time) error {
	return s.update(ctx, userId, bson.M{"avatarBorder": &AvatarBorder{Url: url, ExpireAt: expireAt}})
}
