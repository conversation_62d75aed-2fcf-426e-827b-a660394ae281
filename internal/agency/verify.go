package agency

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrNotFoundMemberApply = biz.NewError(biz.ErrNotFoundMemberApply, "not found apply")
	ErrVerifyStatus        = biz.NewError(biz.ErrVerifyStatus, "verify status err")
)

const (
	keyCreateMutex = "AGENCY:CREATE:MUTEX:%s" // userId
)

func (m *Manager) AgencyApplyVerify(ctx context.Context, auditUserId string, applyId int, status VerifyStatus) error {
	a, err := m.getAgencyApplyById(ctx, applyId)
	if err != nil {
		return err
	}

	if a.Status != int(VerifyStatusDefault) {
		return ErrVerifyStatus
	}

	if a.Status == int(status) {
		return nil
	}

	// 所有创建公会的地方加锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf(keyCreateMutex, a.ChiefId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	// 会长是否是其他公会成员
	join, err := m.GetAgencyByMember(a.ChiefId)
	if err != nil && err != ErrNotJoinAgency {
		return err
	}

	var agencyId int
	if err == ErrNotJoinAgency && join == nil {
		if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
			switch status {
			case VerifyStatusApproved:
				// 取消用户的其他入会申请
				if err := tx.Model(&AgencyMemberApply{}).
					Where("user_id = ? and status = 0", a.ChiefId).
					Update("status", VerifyStatusRefused).Error; err != nil {
					return err
				}

				// 创建新公会
				var (
					numId int
					ok    bool
				)
				for i := 0; i < 5; i++ {
					numId = newNumId(numIdAdj)
					// 查询numid不重复
					var count int64
					if tx.Model(&Agency{}).Where("num_id = ?", numId).Count(&count); count == 0 {
						ok = true
						break
					}
				}
				if !ok {
					return ErrNoAuthority
				}
				AgencyModel := Agency{
					NumId:      int64(numId),
					Name:       a.Name,
					ImageUrl:   a.ImageUrl,
					ShowId:     strconv.FormatInt(int64(numId), 10),
					Ratio:      ratio,
					ChiefId:    a.ChiefId,
					Phone:      a.Phone,
					MemberNum:  1,
					InviteCode: a.InviteCode,
					WhatsappId: a.WhatsappId,
					Status:     AgencyStatusNormal,
					Platform:   a.Platform,
					Material:   a.Material,
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
				}

				if err := tx.Create(&AgencyModel).Error; err != nil {
					return err
				}

				// 将会长自动加入到新公会
				AgencyMemberModel := AgencyMember{
					AgencyId:  AgencyModel.ID,
					ChiefId:   a.ChiefId,
					UserId:    a.ChiefId,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}
				if err := tx.Create(&AgencyMemberModel).Error; err != nil {
					return err
				}

				agencyId = int(AgencyModel.ID)
			case VerifyStatusRefused:
				// 在名称后面增加时间戳后缀
				if err := tx.Model(&AgencyApply{}).Where("id = ?", applyId).Update("name", fmt.Sprintf("%s%d", a.Name, time.Now().Unix())).Error; err != nil {
					return err
				}
			default:
				return errors.New("status not in range")
			}

			// 修改申请表状态
			if err := tx.Model(&AgencyApply{}).Where("id = ?", applyId).Updates(map[string]any{
				"status":        status,
				"audit_user_id": auditUserId,
				"audited_at":    time.Now(),
			}).Error; err != nil {
				return err
			}

			return nil
		}); err != nil {
			m.log.Error("agency apply verify transaction err", zap.Error(err), zap.Int("applyid", applyId))
			return err
		}
	}

	m.ev.Emit(ctx, evt.AgencyVerify, &evt.AgencyVerifyEvt{
		UserId:          a.ChiefId,
		InviteCode:      a.InviteCode,
		VerifyStatus:    int(status),
		AgencyId:        agencyId,
		ContactWhatsapp: a.ContactWhatsapp,
	})

	return nil
}

func (m *Manager) MemberJoinVerify(ctx context.Context, agencyNumId int64, operation, userId string, result VerifyStatus) error {
	agencyInfo, err := m.GetAgencyInfoByNumId(ctx, agencyNumId)
	if err != nil {
		return err
	}

	if agencyInfo.ChiefId != operation {
		return ErrNoAuthority
	}

	userAgencyInfo, err := m.GetMyAgency(ctx, userId)
	if err != nil {
		return err
	}

	if userAgencyInfo.Status != MyAgencyStatusApplyJoin || userAgencyInfo.Agency.NumId != agencyNumId {
		return err
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		var memberApply AgencyMemberApply
		if err := tx.Where("agency_id = ? and user_id = ? and status = ?",
			agencyInfo.ID,
			userId,
			VerifyStatusDefault).Order("id desc").Take(&memberApply).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return ErrNotFoundMemberApply
			}
			return err
		}

		switch result {
		case VerifyStatusApproved:
			// 将成员加入公会
			AgencyMemberModel := AgencyMember{
				AgencyId:  agencyInfo.ID,
				ChiefId:   agencyInfo.ChiefId,
				UserId:    userId,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			if err := tx.Create(&AgencyMemberModel).Error; err != nil {
				return err
			}
			// 公会人员数量+1
			if err := tx.Model(&Agency{}).Where("id = ?", agencyInfo.ID).
				Update("member_num", gorm.Expr("member_num + 1")).Error; err != nil {
				return err
			}
		case VerifyStatusRefused:
		default:
			return ErrVerifyStatus
		}

		if err := tx.Model(&AgencyMemberApply{}).
			Where("id = ?", memberApply.ID).Update("status", result).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		m.log.Error("agency member join verify transaction err", zap.Error(err),
			zap.Int64("agencyNumId", agencyNumId), zap.String("userId", userId), zap.Int("result", int(result)))
		return err
	}

	m.ev.Emit(ctx, evt.AgencyJoinVerify, &evt.AgencyJoinVerifyEvt{
		AgencyId:     int(agencyInfo.ID),
		AgencyShowId: agencyInfo.ShowId,
		UserId:       userId,
		VerifyStatus: int(result),
	})

	return nil
}

func (m *Manager) MemberQuitVerify(ctx context.Context, agencyNumId int64, operation, userId string, result VerifyStatus) error {
	agencyInfo, err := m.GetAgencyInfoByNumId(ctx, agencyNumId)
	if err != nil {
		return err
	}

	if agencyInfo.ChiefId != operation {
		return ErrNoAuthority
	}

	userAgencyInfo, err := m.GetMyAgency(ctx, userId)
	if err != nil {
		return err
	}

	if userAgencyInfo.Status != MyAgencyStatusApplyOut || userAgencyInfo.Agency.NumId != agencyNumId {
		return err
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		var quitApply AgencyQuitApply
		if err := tx.Where("agency_id = ? and user_id = ? and status = ?",
			agencyInfo.ID,
			userId,
			VerifyStatusDefault).Order("id desc").Take(&quitApply).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return ErrNotFoundMemberApply
			}
			return err
		}

		switch result {
		case VerifyStatusApproved:
			// 移除公会成员
			if err := tx.Where("agency_id = ? and user_id = ?", agencyInfo.ID, userId).Delete(&AgencyMember{}).Error; err != nil {
				return err
			}
			// 公会人员数量-1
			if err := tx.Model(&Agency{}).Where("id = ?", agencyInfo.ID).
				Update("member_num", gorm.Expr("member_num - 1")).Error; err != nil {
				return err
			}
		case VerifyStatusRefused:
		default:
			return ErrVerifyStatus
		}

		// 关闭退会申请
		if err := tx.Model(&AgencyQuitApply{}).
			Where("id = ?", quitApply.ID).Updates(map[string]interface{}{
			"status":     result,
			"updated_at": time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		m.log.Error("agency member quit verify transaction err", zap.Error(err),
			zap.Int64("agencyNumId", agencyNumId), zap.String("userId", userId), zap.Int("result", int(result)))
		return err
	}

	m.ev.Emit(ctx, evt.AgencyQuitVerify, &evt.AgencyQuitVerifyEvt{
		AgencyId:     int(agencyInfo.ID),
		AgencyShowId: agencyInfo.ShowId,
		UserId:       userId,
		VerifyStatus: int(result),
	})

	return nil
}

func (m *Manager) GetAgencyApply(ctx context.Context) ([]AgencyApply, error) {
	var aa []AgencyApply
	if err := db.UseTx(ctx, m.db).Where("status = ?", VerifyStatusDefault).Find(&aa).Error; err != nil {
		return nil, err
	}
	return aa, nil
}

// 申请列表
func (m *Manager) AgencyApplyList(ctx context.Context, chiefId string, status int, page, pageSize int) ([]AgencyApply, int64, error) {
	var ret []AgencyApply

	if page < 1 {
		page = 1
	}

	var total int64

	query := db.UseTx(ctx, m.db).Model(&AgencyApply{})

	if status != -1000 {
		query = query.Where("status = ?", status)
	}

	if chiefId != "" {
		query = query.Where("chief_id = ?", chiefId)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Offset((page - 1) * pageSize).Limit(pageSize).Order("created_at desc").Find(&ret).Error; err != nil {
		return nil, 0, err
	}

	return ret, total, nil
}

func (m *Manager) ManageCreate(ctx context.Context, chiefId, name, img, whatsapp, phone, inviteCode string) error {
	// 所有创建公会的地方加锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf(keyCreateMutex, chiefId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	// 会长是否是其他公会成员
	join, err := m.GetAgencyByMember(chiefId)
	if err != nil && err != ErrNotJoinAgency {
		return err
	}

	if join != nil && join.ID > 0 {
		return ErrJoinedAgency
	}

	if err == ErrNotJoinAgency && join == nil {
		var agencyId int
		if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
			// 取消用户的其他入会申请
			if err := tx.Model(&AgencyMemberApply{}).
				Where("user_id = ? and status = 0", chiefId).
				Update("status", VerifyStatusRefused).Error; err != nil {
				return err
			}

			// 创建新公会
			numId := newNumId(numIdAdj)
			AgencyModel := Agency{
				NumId:      int64(numId),
				Name:       name,
				ImageUrl:   img,
				ShowId:     strconv.FormatInt(int64(numId), 10),
				Ratio:      ratio,
				ChiefId:    chiefId,
				Phone:      phone,
				MemberNum:  1,
				InviteCode: inviteCode,
				WhatsappId: whatsapp,
				Status:     AgencyStatusNormal,
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}

			if err := tx.Create(&AgencyModel).Error; err != nil {
				return err
			}

			// 将会长自动加入到新公会
			AgencyMemberModel := AgencyMember{
				AgencyId:  AgencyModel.ID,
				ChiefId:   chiefId,
				UserId:    chiefId,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			if err := tx.Create(&AgencyMemberModel).Error; err != nil {
				return err
			}

			agencyId = int(AgencyModel.ID)
			return nil
		}); err != nil {
			m.log.Error(
				"manage agency create err",
				zap.Error(err),
				zap.String("useeId", chiefId),
				zap.String("name", name),
				zap.String("phone", phone),
				zap.String("inviteCode", inviteCode))
			return err
		}

		m.ev.Emit(ctx, evt.AgencyVerify, &evt.AgencyVerifyEvt{
			UserId:       chiefId,
			InviteCode:   inviteCode,
			VerifyStatus: int(VerifyStatusApproved),
			AgencyId:     agencyId,
			Manage:       true,
		})
	}

	return nil
}
