package agency

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
)

func (m *Manager) AgencyJoinApply(ctx context.Context, numId int64, userId string) error {
	_, err := m.GetAgencyByMember(userId)
	if err != ErrNotJoinAgency {
		return ErrJoinedAgency
	}

	agencyInfo, err := m.GetAgencyInfoByNumId(ctx, numId)
	if err != nil {
		return err
	}

	var alreadyApply int64
	if err := m.db.Model(&AgencyMemberApply{}).
		Where("user_id = ? and agency_id = ? and status = ?", userId, agencyInfo.ID, VerifyStatusDefault).
		Count(&alreadyApply).Error; err != nil {
		return err
	}

	// 已申请
	if alreadyApply > 0 {
		return nil
	}

	agencyjoinModel := AgencyMemberApply{
		AgencyId: agencyInfo.ID,
		UserId:   userId,
		Status:   int(VerifyStatusDefault),
	}

	if err := m.db.Create(&agencyjoinModel).Error; err != nil {
		return err
	}

	m.ev.Emit(ctx, evt.AgencyMemberApply, &evt.AgencyMemberApplyEvt{
		UserId:  userId,
		ChiefId: agencyInfo.ChiefId,
	})

	return nil
}

func (m *Manager) AgencyJoinCancel(ctx context.Context, userId string) error {
	_, err := m.GetAgencyByMember(userId)
	if err != ErrNotJoinAgency {
		return ErrJoinedAgency
	}

	if err := m.db.Model(&AgencyMemberApply{}).Where("user_id = ? and status =?", userId, VerifyStatusDefault).
		Update("status", VerifyStatusCancel).Error; err != nil {
		return err
	}

	return nil
}

func (m *Manager) AgencyQuitApply(ctx context.Context, numId int64, userId string) error {
	userAgencyInfo, err := m.GetAgencyByMember(userId)
	if err == ErrNotJoinAgency {
		return err
	}

	if userAgencyInfo.NumId != numId {
		return ErrNoAuthority
	}

	// 公会长不允许退出公会
	if userAgencyInfo.ChiefId == userId {
		return ErrNoAuthority
	}

	var alreadyApply int64
	if err := m.db.Model(&AgencyQuitApply{}).
		Where("user_id = ? and agency_id = ? and status = ?", userId, userAgencyInfo.ID, VerifyStatusDefault).
		Count(&alreadyApply).Error; err != nil {
		return err
	}

	// 已申请
	if alreadyApply > 0 {
		return nil
	}

	agencyQuitModel := AgencyQuitApply{
		AgencyId: userAgencyInfo.ID,
		UserId:   userId,
		Status:   int(VerifyStatusDefault),
	}

	if err := m.db.Create(&agencyQuitModel).Error; err != nil {
		return err
	}

	return nil
}

func (m *Manager) AgencyQuitCancel(ctx context.Context, userId string) error {
	_, err := m.GetAgencyByMember(userId)
	if err == ErrNotJoinAgency {
		return err
	}

	if err := m.db.Model(&AgencyQuitApply{}).Where("user_id = ? and status =?", userId, VerifyStatusDefault).
		Update("status", VerifyStatusCancel).Error; err != nil {
		return err
	}

	return nil
}

// AgencyMemberVerifyList 获取成员列表
func (m *Manager) AgencyMemberVerifyList(ctx context.Context, userId string, numId int64) ([]AgencyMemberApply, []AgencyQuitApply, error) {
	// 获取公会信息
	agencyInfo, err := m.GetAgencyInfoByNumId(ctx, numId)
	if err != nil {
		return nil, nil, err
	}

	if agencyInfo.ChiefId != userId {
		return nil, nil, ErrNoAuthority
	}

	// 入会申请
	var joinList []AgencyMemberApply
	if err := m.db.Model(&AgencyMemberApply{}).
		Where("agency_id = ? and status = ?", agencyInfo.ID, VerifyStatusDefault).
		Order("id desc").
		Limit(200).
		Find(&joinList).Error; err != nil {
		return nil, nil, err
	}

	//  退出申请
	var quitList []AgencyQuitApply
	if err := m.db.Model(&AgencyQuitApply{}).
		Where("agency_id = ? and status = ?", agencyInfo.ID, VerifyStatusDefault).
		Order("id desc").
		Limit(200).
		Find(&quitList).Error; err != nil {
		return nil, nil, err
	}

	return joinList, quitList, nil
}

// AgencyMemberListByTime 获取成员列表，时间排序
func (m *Manager) AgencyMemberListByTime(ctx context.Context, userId string, numId int64, offset, num int) ([]AgencyMember, int64, error) {
	var (
		list  []AgencyMember
		total int64
	)

	// 获取公会信息
	agencyInfo, err := m.GetAgencyInfoByNumId(ctx, numId)
	if err != nil {
		return list, total, err
	}

	if agencyInfo.ChiefId != userId {
		return list, total, ErrNoAuthority
	}

	if err := m.db.Model(&AgencyMember{}).
		Where("agency_id = ?", agencyInfo.ID).
		Order("id desc").
		Offset(offset).
		Limit(num).
		Find(&list).Error; err != nil {
		return list, total, err
	}

	if err := m.db.Model(&AgencyMember{}).
		Where("agency_id = ?", agencyInfo.ID).
		Count(&total).Error; err != nil {
		return list, total, err
	}

	return list, total, nil
}

func (m *Manager) GetAnchorIdsByChiefId(ctx context.Context, userId string) ([]string, error) {
	var anchorIds []string
	err := m.db.Model(&AgencyMember{}).
		Where("chief_id = ?", userId).
		Pluck("user_id", &anchorIds).Limit(100).Error
	if err != nil {
		return nil, err
	}

	return anchorIds, nil
}
