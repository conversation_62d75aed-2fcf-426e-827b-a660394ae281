package agency

type MyAgency struct {
	Agency *Agency
	Status int // 0未加入 1已申请加入 2已加入 3已申请退出 4创建公会申请中
}

type AgencyCreateApply struct {
	Name       string   // 公会名称
	ImageUrl   string   // 公会图片
	WhatsappId string   // 联系方式
	MemberNum  int      // 成员数量
	Platform   string   // 合作平台
	Material   []string // 资料信息
	InviteCode string   // 邀请码
	Phone      string   // 发送验证码电话
	Code       string   // 需要验证码
}
