package agency

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

type Manager struct {
	im  *im.Manager
	db  *db.Client
	ev  ev.Bus
	dm  *redi.Mutex
	log *zap.Logger
}

func newManager(im *im.Manager, db *db.Client, ev ev.Bus, dm *redi.Mutex, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Agency{}, &AgencyMember{}, &AgencyMemberApply{}, &AgencyApply{}, &AgencyQuitApply{})

	m := &Manager{
		im:  im,
		db:  db,
		ev:  ev,
		dm:  dm,
		log: log,
	}

	return m
}
