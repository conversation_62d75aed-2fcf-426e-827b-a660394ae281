output := .
copy := scp

ifneq (,$(shell which rsync))
override copy := rsync -avzP
endif

ROOT_DIR := $(dir $(lastword $(MAKEFILE_LIST)))

build-apiserver:
	scripts/build-apiserver.sh

build-scheduler:
	scripts/build-scheduler.sh

build-gateway:
	scripts/build-gateway.sh

build-roomsrv:
	scripts/build-roomsrv.sh

build-ctl-prod:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o $(output)/bin/ctl cmd/ctl/main.go

deploy-config-test:
	$(copy) $(output)/locales/*.yaml ec2-user@***********:~/apps/osl/locales/
	$(copy) $(output)/configs/test.yaml ec2-user@***********:~/apps/osl/configs/
	$(copy) $(output)/deployments/pm2.test.config.js ec2-user@***********:~/apps/osl/deployments/

deploy-apiserver-test: build-apiserver
	@date
	$(copy) $(output)/bin/apiserver ec2-user@***********:~/apps/osl/bin/
	ssh ec2-user@*********** "pm2 restart osl-apiserver"

deploy-scheduler-test: build-scheduler
	@date
	$(copy) $(output)/bin/scheduler ec2-user@***********:~/apps/osl/bin/
	ssh ec2-user@*********** "pm2 restart osl-scheduler"

deploy-gateway-test: build-gateway
	@date
	$(copy) $(output)/bin/gateway ec2-user@***********:~/apps/osl/bin/
	ssh ec2-user@*********** "pm2 restart osl-gateway osl-public-gateway"

deploy-roomsrv-test: build-roomsrv
	@date
	$(copy) $(output)/bin/roomsrv ec2-user@***********:~/apps/osl/bin/
	ssh ec2-user@*********** "pm2 restart osl-roomsrv"

deploy-config-dev:
	$(copy) $(output)/locales/*.yaml dev01@**************:/home/<USER>/apps/osl/locales/
	$(copy) $(output)/configs/config.yaml dev01@**************:/home/<USER>/apps/osl/configs/
	$(copy) $(output)/deployments/pm2.dev.config.js dev01@**************:/home/<USER>/apps/osl/deployments/

deploy-apiserver-dev: build-apiserver
	@date
	$(copy) $(output)/bin/apiserver dev01@**************:/home/<USER>/apps/osl/bin/
	ssh dev01@************** "pm2 restart osl-apiserver"

deploy-scheduler-dev: build-scheduler
	@date
	$(copy) $(output)/bin/scheduler dev01@**************:/home/<USER>/apps/osl/bin/
	ssh dev01@************** "pm2 restart osl-scheduler"

deploy-gateway-dev: build-gateway
	@date
	$(copy) $(output)/bin/gateway dev01@**************:/home/<USER>/apps/osl/bin/
	ssh dev01@************** "pm2 restart osl-gateway osl-public-gateway"

deploy-roomsrv-dev: build-roomsrv
	@date
	$(copy) $(output)/bin/roomsrv dev01@**************:/home/<USER>/apps/osl/bin/
	ssh dev01@************** "pm2 restart osl-roomsrv"

test:
	go run -tags debug cmd/apiserver/main.go -c configs/config.yaml

i18n:
	./scripts/i18n-update.sh

swag:
	./scripts/swag.sh
