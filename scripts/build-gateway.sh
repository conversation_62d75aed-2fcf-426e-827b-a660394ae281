#!/usr/bin/env bash

set -e

APPNAME="gateway"

CUR_DIR=$(cd "$(dirname "$0")";pwd)
OUTPUT_DIR="${CUR_DIR}/../bin"

BUILDPATH="${CUR_DIR}/../cmd/gateway"

# BUILD_GOOS=${GOOS:-$(go env GOOS)}
BUILD_GOOS=linux
BUILD_GOARCH=amd64
GOBINARY=${GOBINARY:-go}

mkdir -p ${OUTPUT_DIR}

OUTPUT_PATH="${OUTPUT_DIR}/${APPNAME}"
if [ "${BUILD_GOOS}" = "windows" ];then
    OUTPUT_PATH="${OUTPUT_PATH}.exe"
fi

GOOS=${BUILD_GOOS} CGO_ENABLED=0 GOARCH=${BUILD_GOARCH} ${GOBINARY} build -tags debug -o "${OUTPUT_PATH}" "${BUILDPATH}"