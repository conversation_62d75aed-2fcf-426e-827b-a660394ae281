package pp

import (
	"time"
)

type PostPolicyOpt func(*PostPolicy)

func NewPolicy(opts ...PostPolicyOpt) *PostPolicy {
	pp := &PostPolicy{}
	for _, opt := range opts {
		opt(pp)
	}

	if pp.Expiration == "" {
		Expiration(time.Minute)(pp)
	}

	return pp
}

type PostPolicy struct {
	Expiration string  `json:"expiration"`
	Conditions [][]any `json:"conditions"`
}

func (pp *PostPolicy) AddCondition(op, key, value any) {
	pp.Conditions = append(pp.Conditions, []any{op, key, value})
}

func Expiration(ttl time.Duration) PostPolicyOpt {
	return func(pp *PostPolicy) {
		pp.Expiration = time.Now().Add(ttl).UTC().Format("2006-01-02T15:04:05Z")
	}
}

func SizeLimit(min, max int) PostPolicyOpt {
	return func(pp *PostPolicy) {
		pp.AddCondition("content-length-range", min, max)
	}
}

func AllowTypes(mimes ...string) PostPolicyOpt {
	return func(pp *PostPolicy) {
		pp.AddCondition("in", "$content-type", mimes)
	}
}

func StrictKey(name string) PostPolicyOpt {
	return func(pp *PostPolicy) {
		pp.AddCondition("eq", "$key", name)
	}
}

func PrefixKey(name string) PostPolicyOpt {
	return func(pp *PostPolicy) {
		pp.AddCondition("starts-with", "$key", name)
	}
}
