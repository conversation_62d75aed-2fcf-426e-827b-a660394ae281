package sto

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"hash"

	"github.com/bytedance/sonic"

	"gitlab.sskjz.com/overseas/live/osl/pkg/sto/pp"
)

type UploadForm map[string]string

type UploadConf struct {
	Endpoint  string
	KeyId     string
	Policy    string
	Signature string
	Key       string
	callback  *uploadCB
}

func (c *UploadConf) SetCallback(url string, opts ...upcbOpt) {
	c.callback = newUploadCB(url, opts)
}

func (c *UploadConf) Form() UploadForm {
	form := UploadForm{
		"OSSAccessKeyId": c.KeyId,
		"policy":         c.Policy,
		"signature":      c.Signature,
		"key":            c.Key,
	}

	if c.callback != nil {
		form["callback"] = c.callback.String()
	}

	return form
}

func MakeUploadConf(conf Conf, key string, opts ...pp.PostPolicyOpt) (*UploadConf, error) {
	policy := pp.NewPolicy(opts...)

	result, err := sonic.Marshal(policy)
	if err != nil {
		return nil, err
	}

	pp64 := base64.StdEncoding.EncodeToString(result)

	h := hmac.New(func() hash.Hash { return sha1.New() }, []byte(conf.AccessKeySecret))
	h.Write([]byte(pp64))

	sign64 := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return &UploadConf{
		Endpoint:  conf.EndpointURL(),
		KeyId:     conf.AccessKeyId,
		Policy:    pp64,
		Signature: sign64,
		Key:       key,
	}, nil
}
