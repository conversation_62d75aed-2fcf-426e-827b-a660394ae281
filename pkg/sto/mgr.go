package sto

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

const (
	defaultConf = "default"
)

type Instance interface {
	Conf(key string) Conf
	Take(key string) (*Client, error)
}

func newManager(conf conf.OSS) *mgr {
	return &mgr{
		cfg: conf,
	}
}

type mgr struct {
	cfg conf.OSS
}

func (m *mgr) Conf(key string) Conf {
	var def = m.cfg[defaultConf]

	c, has := m.cfg[key]
	if !has {
		c = def
	}

	if c.Endpoint == "" {
		c.Endpoint = def.Endpoint
	}

	if c.Accelerate == "" {
		c.Accelerate = def.Accelerate
	}

	if c.AccessKeyId == "" {
		c.AccessKeyId = def.AccessKeyId
	}

	if c.AccessKeySecret == "" {
		c.AccessKeySecret = def.AccessKeySecret
	}

	if c.Bucket == "" {
		c.Bucket = def.Bucket
	}

	if c.Domain == "" {
		if def.Domain != "" {
			c.Domain = def.Domain
		} else {
			c.Domain = c.Bucket + "." + c.Endpoint
		}
	}

	if c.Scheme == "" {
		c.Scheme = "https"
	}

	return newConf(c)
}

func (m *mgr) Take(key string) (*Client, error) {
	return newClient(m.Conf(key))
}
