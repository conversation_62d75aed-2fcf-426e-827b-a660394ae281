package sto

import (
	"net/url"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func newClient(conf Conf) (*Client, error) {
	client, err := oss.New(conf.Endpoint, conf.AccessKeyId, conf.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	bucket, err := client.Bucket(conf.Bucket)
	if err != nil {
		return nil, err
	}

	return &Client{Bucket: bucket, cfg: conf}, nil
}

type Client struct {
	*oss.Bucket
	cfg Conf
}

func (s *Client) Conf() Conf {
	return s.cfg
}

func (s *Client) ExternalURL(raw string) string {
	p, err := url.Parse(raw)
	if err != nil {
		return raw
	}

	if p.Host != s.cfg.Domain {
		p.Host = s.cfg.Domain
	}

	return p.String()
}
