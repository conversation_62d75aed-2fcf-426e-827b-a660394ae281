package sto

import (
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

func newConf(raw conf.AliOSS) Conf {
	return Conf{raw}
}

type Conf struct {
	conf.AliOSS
}

func (c *Conf) EndpointURL() string {
	if c.Accelerate != "" {
		return c.Scheme + "://" + c.Accelerate
	}
	return c.Scheme + "://" + strings.Replace(c.Endpoint, "-internal", "", 1)
}

func (c *Conf) ExternalURL(parts ...string) string {
	url := c.Scheme + "://" + c.Domain
	if len(parts) > 0 {
		url += "/" + strings.TrimPrefix(strings.Join(parts, "/"), "/")
	}
	return url
}
