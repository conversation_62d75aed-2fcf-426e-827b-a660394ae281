package sto

import (
	"strings"
)

type CallbackTPL int

const (
	CallbackTPLBase CallbackTPL = iota
)

var tplVars = map[CallbackTPL]map[string]string{
	CallbackTPLBase: {
		"bucket": "$[bucket]$",
		"object": "$[object]$",
	},
}

type CallbackInfoBase struct {
	Bucket string `json:"bucket"`
	Object string `json:"object"`
}

func replaceVars(body string) string {
	body = strings.ReplaceAll(body, "\"$[", "${")
	body = strings.ReplaceAll(body, "]$\"", "}")
	return body
}
