package sto

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"io"
	"maps"
	"net/http"

	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
)

type upcbOpt func(*uploadCB)

func WithUploadBody(body map[string]string) upcbOpt {
	return func(cb *uploadCB) {
		maps.Copy(cb.body, body)
	}
}

func WithUploadVars(tpls ...CallbackTPL) upcbOpt {
	return func(cb *uploadCB) {
		cb.vars = append(cb.vars, tpls...)
		for _, tpl := range tpls {
			maps.Copy(cb.body, tplVars[tpl])
		}
	}
}

func newUploadCB(url string, opts []upcbOpt) *uploadCB {
	upcb := &uploadCB{
		url:  url,
		body: make(map[string]string),
	}
	for _, opt := range opts {
		opt(upcb)
	}
	return upcb
}

type uploadCB struct {
	url  string
	body map[string]string
	vars []CallbackTPL
}

type uploadCBout struct {
	Url  string `json:"callbackUrl"`
	Body string `json:"callbackBody"`
	Type string `json:"callbackBodyType"`
}

func (c *uploadCB) String() string {
	body, _ := sonic.MarshalString(c.body)
	if len(c.vars) > 0 {
		body = replaceVars(body)
	}
	data, _ := sonic.Marshal(&uploadCBout{
		Url:  c.url,
		Body: body,
		Type: "application/json",
	})
	return base64.StdEncoding.EncodeToString(data)
}

func ValidCallback(req *http.Request) error {
	// Get PublicKey bytes
	bytePublicKey, err := getPublicKey(req)
	if err != nil {
		return err
	}

	// Get Authorization bytes : decode from Base64String
	byteAuthorization, err := getAuthorization(req)
	if err != nil {
		return err
	}

	body, err := io.ReadAll(req.Body)
	if err != nil {
		return err
	}

	if len(body) == 0 {
		if v := req.Context().Value(ctxBody); v != nil {
			body = v.([]byte)
		}
	}

	req.Body = io.NopCloser(bytes.NewReader(body))

	// Get MD5 bytes from Newly Constructed Authorization String.
	byteMD5, err := getMD5FromNewAuthString(req)
	if err != nil {
		return err
	}

	req.Body = io.NopCloser(bytes.NewReader(body))

	// verifySignature and response to client
	if !verifySignature(bytePublicKey, byteMD5, byteAuthorization) {
		return errors.New("verify signature failed")
	}

	return nil
}

type ctxKey int

const ctxBody ctxKey = iota

func CallbackWrap(next gin.HandlerFunc) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		body, err := ctx.GetRawData()
		if err != nil {
			_ = ctx.AbortWithError(http.StatusBadRequest, err)
			return
		}
		ctx.Request = ctx.Request.WithContext(context.WithValue(ctx.Request.Context(), ctxBody, body))
		ctx.Request.Body = io.NopCloser(bytes.NewReader(body))
		next(ctx)
	}
}
