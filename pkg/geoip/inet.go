package geoip

import (
	"io"
	"net/http"
	"regexp"
)

func publicIP() string {
	r1, err := http.Get("https://nstool.netease.com")
	if err != nil {
		return ""
	}
	defer r1.Body.Close()
	bs, err := io.ReadAll(r1.Body)
	if err != nil {
		return ""
	}
	re := regexp.MustCompile(`iframe src='(.*?)'`)
	m1 := re.FindStringSubmatch(string(bs))
	if len(m1) != 2 {
		return ""
	}
	r2, err := http.Get(m1[1])
	if err != nil {
		return ""
	}
	defer r2.Body.Close()
	bs2, err := io.ReadAll(r2.Body)
	if err != nil {
		return ""
	}
	re = regexp.MustCompile(`(\b25[0-5]|\b2[0-4][0-9]|\b[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}`)
	m2 := re.FindStringSubmatch(string(bs2))
	if len(m2) != 4 {
		return ""
	}
	return m2[0]
}
