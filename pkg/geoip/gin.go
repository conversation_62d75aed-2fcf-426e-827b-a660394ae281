package geoip

import (
	"context"
	"errors"

	"github.com/gin-gonic/gin"
)

const ctxIns = "geoip"

func Client(ctx context.Context) *Record {
	if g, is := ctx.Value(gin.ContextKey).(*gin.Context); is {
		if v, has := g.Get(ctxIns); has {
			if r, err := v.(*Query).Lookup(ctx, g.ClientIP()); err == nil {
				return r
			}
		}
	}
	return defaultData
}

func Lookup(ctx context.Context, ipAddr string) (*Record, error) {
	if g, is := ctx.Value(gin.ContextKey).(*gin.Context); is {
		if v, has := g.Get(ctxIns); has {
			return v.(*Query).Lookup(ctx, ipAddr)
		}
	}
	return nil, errors.New("no query in context")
}

func (s *Query) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(ctxIns, s)
		c.Next()
	}
}
