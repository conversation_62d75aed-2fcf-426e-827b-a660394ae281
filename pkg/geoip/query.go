package geoip

import (
	"context"
	"net"

	"github.com/oschwald/geoip2-golang"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"go.uber.org/zap"
)

func newQuery(path string, log *zap.Logger) (*Query, error) {
	rr, err := geoip2.Open(path)
	if err != nil {
		return nil, err
	}
	q := &Query{rr: rr, log: log}
	q.rc = cc.New[string, *geoip2.City](4000, cc.LRU, cc.LoaderFunc(q.lookup), cc.ExportStats("geoip"))
	return q, nil
}

type Query struct {
	rr  *geoip2.Reader
	rc  cc.Cache[string, *geoip2.City]
	log *zap.Logger
}

func (s *Query) Lookup(ctx context.Context, ipAddr string) (*Record, error) {
	if s.rr == nil {
		return defaultData, nil
	}
	raw, err := s.rc.Get(ipAddr)
	if err != nil {
		return nil, err
	} else if raw == nil {
		return defaultData, nil
	}
	return makeRecord(ctx, raw), nil
}

func (s *Query) lookup(ipAddr string) (*geoip2.City, error) {
	rec, err := s.rr.City(net.ParseIP(ipAddr))
	if err != nil {
		return nil, err
	}
	if rec.Country.IsoCode == "" {
		if dbg.Ing() {
			return s.lookupInet(ipAddr)
		}
		s.log.Info("unknown ip address", zap.String("ipAddr", ipAddr))
		return nil, nil
	}
	return rec, nil
}

func (s *Query) lookupInet(ipAddr string) (*geoip2.City, error) {
	if ip := net.ParseIP(ipAddr); ip.IsPrivate() || ip.IsLoopback() {
		if ipAddr = publicIP(); ipAddr != "" {
			s.log.Info("use external ip address", zap.String("ipAddr", ipAddr))
			return s.rr.City(net.ParseIP(ipAddr))
		}
	}
	return nil, nil
}
