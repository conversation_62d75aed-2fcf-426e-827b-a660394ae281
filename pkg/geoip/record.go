package geoip

import (
	"context"
	"slices"

	"github.com/oschwald/geoip2-golang"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
)

var defaultData = &Record{
	Country: "ZZ",
}

type Record struct {
	Country  string // ISO code
	Timezone string // default to empty
	CityName string // localize city name
}

var (
	lngAliases = map[string]string{"zh": "zh-CN"}
	lngAllowed = []string{"zh-CN", "en"}
)

func makeRecord(ctx context.Context, from *geoip2.City) *Record {
	return &Record{
		Country:  from.Country.IsoCode,
		Timezone: from.Location.TimeZone,
		CityName: i18n.Lookup(ctx, "en", func(l string) string {
			if ll, ok := lngAliases[l]; ok {
				l = ll
			}
			if !slices.Contains(lngAllowed, l) {
				return ""
			}
			return from.City.Names[l]
		}),
	}
}
