package geoip

import (
	"os"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

func Provide(desc *conf.Setting, vnd log.Vendor) (*Query, error) {
	if desc.GeoIP.Path == "" {
		return &Query{}, nil
	}
	q, err := newQuery(desc.GeoIP.Path, vnd.Scope("geoip"))
	if err != nil {
		if dbg.Ing() && os.IsNotExist(err) {
			return &Query{}, nil
		}
		return nil, err
	}
	return q, nil
}
