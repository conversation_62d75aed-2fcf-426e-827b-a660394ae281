package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUserStatus(t *testing.T) {
	var sta Status
	assert.Equal(t, true, sta.None())

	sta = sta.Set(StatusDeleted)
	assert.Equal(t, true, sta.Deleted())

	assert.Equal(t, false, sta.Blocked())
	sta = sta.Set(StatusBlocked)
	assert.Equal(t, true, sta.Blocked())

	sta = sta.Set(StatusVerified)
	assert.Equal(t, true, sta.Verified())

	sta = sta.Unset(StatusVerified)
	assert.Equal(t, false, sta.Verified())
}
