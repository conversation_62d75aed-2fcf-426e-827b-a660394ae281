package user

import (
	"context"
	"errors"
	"fmt"

	"gitlab.sskjz.com/go/ev"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrAccountNotExists = biz.NewError(biz.ErrAccountNotExists, "account not exists")
	ErrAccountNotUsable = fmt.Errorf("deleted: %w", ErrAccountNotExists)
)

func newManager(db *db.Client, ev ev.Bus, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Account{})
	return &Manager{
		db:  db,
		ev:  ev,
		log: log,
	}
}

type Manager struct {
	db  *db.Client
	ev  ev.Bus
	log *zap.Logger
	cached
	hooks
}

func (u *Manager) Take(ctx context.Context, userId string) (*Account, error) {
	return u.take(ctx, "user_id = ?", userId)
}

func (u *Manager) take(ctx context.Context, query any, args ...any) (*Account, error) {
	var acc Account
	if err := db.UseTx(ctx, u.db).Where(query, args...).Take(&acc).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAccountNotExists
		}
		return nil, err
	}

	if err := u.onFetching(ctx, &acc); err != nil {
		return nil, err
	}

	return &acc, nil
}

type idOnly struct {
	UserId string
}

func (u *Manager) takeId(ctx context.Context, query any, args ...any) (string, error) {
	var acc idOnly
	if err := db.UseTx(ctx, u.db).Model(&Account{}).Where(query, args...).Take(&acc).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", ErrAccountNotExists
		}
		return "", err
	}
	return acc.UserId, nil
}
