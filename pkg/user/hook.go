package user

import "context"

type hookFn func(ctx context.Context, acc *Account) error

type Hook interface {
	PreCreate(do hookFn)
	PostCreate(do hookFn)
	OnFetching(do hookFn)
}

type hooks struct {
	preCreates  []hookFn
	postCreates []hookFn
	onFetches   []hookFn
}

func (h *hooks) PreCreate(do hookFn) {
	h.preCreates = append(h.preCreates, do)
}

func (h *hooks) preCreate(ctx context.Context, acc *Account) error {
	for _, do := range h.preCreates {
		if err := do(ctx, acc); err != nil {
			return err
		}
	}
	return nil
}

func (h *hooks) PostCreate(do hookFn) {
	h.postCreates = append(h.postCreates, do)
}

func (h *hooks) postCreate(ctx context.Context, acc *Account) error {
	for _, do := range h.postCreates {
		if err := do(ctx, acc); err != nil {
			return err
		}
	}
	return nil
}

func (h *hooks) OnFetching(do hookFn) {
	h.onFetches = append(h.onFetches, do)
}

func (h *hooks) onFetching(ctx context.Context, acc *Account) error {
	for _, do := range h.onFetches {
		if err := do(ctx, acc); err != nil {
			return err
		}
	}
	return nil
}
