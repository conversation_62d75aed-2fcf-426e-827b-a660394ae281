package user

import (
	"context"
)

type ctxKey int

const (
	ctxNoHooking ctxKey = iota
	ctxOmitFields
	ctxSkip<PERSON><PERSON><PERSON>
)

func noHooking(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxNoHooking, struct{}{})
}

func NoHooking(ctx context.Context) bool {
	_, ok := ctx.Value(ctxNoHooking).(struct{})
	return ok
}

func OmitFields(ctx context.Context, fields ...string) context.Context {
	return context.WithValue(ctx, ctxOmitFields, fields)
}

func omitFields(ctx context.Context) []string {
	if v, ok := ctx.Value(ctxOmitFields).([]string); ok {
		return v
	}
	return nil
}

func Ski<PERSON><PERSON><PERSON>cker(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxSkip<PERSON><PERSON><PERSON>, struct{}{})
}

func skipChecker(ctx context.Context) bool {
	_, ok := ctx.Value(ctx<PERSON>ki<PERSON><PERSON><PERSON><PERSON>).(struct{})
	return ok
}
