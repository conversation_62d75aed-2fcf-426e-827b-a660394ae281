package user

import (
	"errors"
	"fmt"
	"testing"

	"github.com/samber/lo"
)

func TestNumIdGen(t *testing.T) {
	fmt.Println("  1e3", newNumId(1e3))
	fmt.Println("  1e4", newNumId(1e4))
	fmt.Println("  1e5", newNumId(1e5))
	fmt.Println("  1e6", newNumId(1e6))
	fmt.Println("5e6*1", newNumId(5e6*1))
	fmt.Println("5e6*2", newNumId(5e6*2))
	fmt.Println("5e6*3", newNumId(5e6*3))
	fmt.Println("5e6*4", newNumId(5e6*4))
	fmt.Println("5e6*5", newNumId(5e6*5))
}

func BenchmarkNumIdGen(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = newNumId(numIdAdj)
	}
	b.ReportAllocs()
}

func TestNumIdRetry(t *testing.T) {
	for x := 1; x <= id1Try; x++ {
		t.Run(fmt.Sprintf("iter %d", x), func(t *testing.T) {
			ids := make(map[int]struct{})
			dup := 0
			run := 1000000
			for i := 0; i < run; i++ {
				if _, err := lo.Attempt(x, func(_ int) error {
					id := newNumId(numIdAdj)
					if _, has := ids[id]; has {
						return errors.New("dup")
					}
					ids[id] = struct{}{}
					return nil
				}); err != nil {
					dup++
				}
			}
			t.Logf("[%d] duplicated numId: %d/%d", x, dup, run)
		})
	}
}
