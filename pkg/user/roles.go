package user

type Role uint32

const (
	RolePatroller Role = 1 << iota // 巡管
	RoleSeller                     // 币商
	RoleADM                        // ADM
	RoleOfficial                   // 官方
)

func (s Role) Has(cmp Role) bool {
	return s&cmp == cmp
}

func (s Role) Set(in ...Role) Role {
	n := s
	for _, add := range in {
		n |= add
	}
	return n
}

func (s Role) Unset(in ...Role) Role {
	n := s
	for _, del := range in {
		n &= ^del
	}
	return n
}
