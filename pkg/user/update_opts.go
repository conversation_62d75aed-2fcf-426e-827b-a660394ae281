package user

import (
	"context"
)

type UpData = map[string]any

type UpOption func(c context.Context, m *Manager, old *Account) (UpData, error)

func UpAccount(to *Account) UpOption {
	return func(c context.Context, m *Manager, old *Account) (UpData, error) {
		up := make(UpData)

		if err := reset1(c, m, to); err != nil {
			return nil, err
		}

		// showId
		if to.ShowId != "" && old.ShowId != to.ShowId {
			if stripped, err := m.checkShowId(c, to.ShowId); err != nil {
				return nil, err
			} else {
				to.ShowId = stripped
			}
			up["show_id"] = to.ShowId
		}

		// nickname
		if to.Nickname != "" && old.Nickname != to.Nickname {
			if stripped, err := m.checkNickname(c, to.Nickname); err != nil {
				return nil, err
			} else {
				to.Nickname = stripped
			}
			up["nickname"] = to.Nickname
		}

		// gender
		if validGender(to.Gender) && old.Gender != to.Gender {
			up["gender"] = to.Gender
		}

		// birthday
		if !to.Birthday.IsZero() && !old.Birthday.Equal(to.Birthday) {
			up["birthday"] = to.Birthday
		}

		{
			upNonEmpty(up, "avatar", old.Avatar, to.Avatar)
		}

		if err := reset2(c, m, up); err != nil {
			return nil, err
		}

		return up, nil
	}
}

func upNonEmpty[T comparable](w UpData, name string, old, new T) {
	var nil T
	if new != nil && old != new {
		w[name] = new
	}
}

type Nullable struct {
	Signature *string
	Region    *string
	School    *string
}

func UpNullable(to *Nullable) UpOption {
	return func(_ context.Context, _ *Manager, old *Account) (UpData, error) {
		up := make(UpData)

		{
			upIfExists(up, "signature", old.Signature, to.Signature)
			upIfExists(up, "region", old.Region, to.Region)
			upIfExists(up, "school", old.School, to.School)
		}

		return up, nil
	}
}

func upIfExists[T comparable](w UpData, name string, old T, new *T) {
	if new != nil && old != *new {
		w[name] = new
	}
}

func SetStatus(add Status) UpOption {
	return func(_ context.Context, _ *Manager, old *Account) (UpData, error) {
		if old.Status.Has(add) {
			return nil, nil
		}
		return UpData{"status": old.Status.Set(add)}, nil
	}
}

func UnsetStatus(del Status) UpOption {
	return func(_ context.Context, _ *Manager, old *Account) (UpData, error) {
		if !old.Status.Has(del) {
			return nil, nil
		}
		return UpData{"status": old.Status.Unset(del)}, nil
	}
}

func SetLevel(level int) UpOption {
	return func(_ context.Context, _ *Manager, old *Account) (UpData, error) {
		if old.Level == level {
			return nil, nil
		}
		return UpData{"level": level}, nil
	}
}

func SetRole(add Role) UpOption {
	return func(_ context.Context, _ *Manager, old *Account) (UpData, error) {
		if old.Roles.Has(add) {
			return nil, nil
		}
		return UpData{"roles": old.Roles.Set(add)}, nil
	}
}

func UnsetRole(del Role) UpOption {
	return func(_ context.Context, _ *Manager, old *Account) (UpData, error) {
		if !old.Roles.Has(del) {
			return nil, nil
		}
		return UpData{"roles": old.Roles.Unset(del)}, nil
	}
}
