package user

import (
	"context"
	"errors"
	"math/rand"
	"strings"
	"unicode"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/exp/utf8string"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

const (
	nnMinLen = 2
	nnMaxLen = 20
	charSize = 1 // UTF8字符的长度
)

var (
	nnBlocked = []string{"kakoofficial", "kakooficial", "inspetor"}
)

var (
	ErrEmptyNickname    = biz.NewError(biz.ErrEmptyNickname, "empty nickname")
	ErrInvalidNickname  = biz.NewError(biz.ErrInvalidNickname, "invalid nickname")
	ErrNicknameExists   = biz.NewError(biz.ErrNicknameExists, "nickname already exists")
	ErrNicknameTooShort = biz.NewError(biz.ErrNicknameTooShort, "nickname is too short")
	ErrNicknameTooLong  = biz.NewError(biz.ErrNicknameTooLong, "nickname is too long")
)

func (u *Manager) RandNickname(ctx context.Context) (string, error) {
	return u.makeNickname(ctx, "", true)
}

func (u *Manager) makeNickname(ctx context.Context, input string, allowRand bool) (string, error) {
	stripped, err1 := u.checkNickname(ctx, input)
	if err1 == nil {
		return stripped, nil
	}

	if !allowRand {
		return "", err1
	}

	var nn string

	if n, err := lo.AttemptWhile(nnTry, func(_ int) (error, bool) {
		nn = nnMake()
		_, err := u.checkNickname(ctx, nn)
		return err, errors.Is(err, ErrNicknameExists)
	}); err != nil {
		return "", err
	} else if n > logTry {
		u.log.Warn("found duplicated nickname", zap.Int("try", n))
	}

	return nn, nil
}

func (u *Manager) checkNickname(ctx context.Context, value string) (string, error) {
	value = nnTrim(value)
	if err := nnValid(value); err != nil {
		if !errors.Is(err, ErrInvalidNickname) || !skipChecker(ctx) {
			return value, err
		}
	}

	var cnt int64
	if err := db.UseTx(ctx, u.db).Model(&Account{}).Where("nickname = ?", value).Count(&cnt).Error; err != nil {
		return value, err
	} else if cnt > 0 {
		return value, ErrNicknameExists
	}

	return value, nil
}

func nnTrim(in string) string {
	out := make([]rune, 0, len(in))
	for _, char := range in {
		if unicode.IsControl(char) || unicode.IsSpace(char) {
			continue
		}
		out = append(out, char)
	}
	return string(out)
}

func nnValid(in string) error {
	str := utf8string.NewString(in)

	var ascii, character int
	for i := 0; i < str.RuneCount(); i++ {
		if str.At(i) > unicode.MaxASCII {
			character++
		} else {
			ascii++
		}
	}

	size := ascii + (character * charSize)

	if size == 0 {
		return ErrEmptyNickname
	} else if size < nnMinLen {
		return ErrNicknameTooShort
	} else if size > nnMaxLen {
		return ErrNicknameTooLong
	}

	inl := make([]rune, 0, len(in))
	for _, v := range in {
		if unicode.IsLetter(v) {
			inl = append(inl, unicode.ToLower(v))
		}
	}
	ins := string(inl)
	for _, blocked := range nnBlocked {
		if strings.Contains(ins, blocked) {
			return ErrInvalidNickname
		}
	}

	return nil
}

func nnMake() string {
	return "Kako_" + randSeq(6)
}

var letters = []rune("123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz")

func randSeq(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}
