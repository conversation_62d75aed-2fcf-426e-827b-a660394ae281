package user

import (
	"errors"
	"fmt"
	"testing"

	"github.com/samber/lo"
)

func TestShowIdGen(t *testing.T) {
	for i := 0; i < 100; i++ {
		fmt.Println(newShowId(i))
	}
}

func TestShowIdCheck(t *testing.T) {
	if err := idValid("1008883389"); err != nil {
		t.Fatal(err)
	}
}

func TestShowIdRetry(t *testing.T) {
	for x := 1; x <= id2Try; x++ {
		t.Run(fmt.Sprintf("iter %d", x), func(t *testing.T) {
			ids := make(map[string]struct{})
			dup := 0
			run := 1000000
			for i := 0; i < run; i++ {
				if _, err := lo.Attempt(x, func(_ int) error {
					id := newShowId(i)
					if _, has := ids[id]; has {
						return errors.New("dup")
					}
					ids[id] = struct{}{}
					return nil
				}); err != nil {
					dup++
				}
			}
			t.Logf("[%d] duplicated showId: %d/%d", x, dup, run)
		})
	}
}
