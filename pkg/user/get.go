package user

import (
	"context"
	"errors"
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/gdk"

	"gitlab.sskjz.com/overseas/live/osl/pkg/gtag"
)

var (
	ErrMissingUserId = errors.New("no userId provided")
)

type caching interface {
	Account(ctx context.Context, userId string) (*Account, error)
	Session(ctx context.Context, tags, meta gdk.KVPair) (*Profile, error)
	GetByNumId(ctx context.Context, numId int64) (*Account, error)
	OnAccess(do hookFn)
}

type Getter interface {
	caching
	GetByShowId(ctx context.Context, showId string) (*Account, error)
	Ensure(ctx context.Context, acc *Account) *Account
}

func (u *Manager) initCache(syn cc.Sync) {
	u.users = cc.New[string, *Account](
		40000, cc.LRU,
		cc.LoaderFunc(func(userId string) (*Account, error) {
			return u.take(context.TODO(), "user_id = ?", userId)
		}),
		cc.Expiration(time.Hour),
		cc.ExportStats("user.cache"),
		cc.WithSync(syn, "user.cache"),
	)
	u.numIds = cc.New[int64, string](
		80000, cc.LRU,
		cc.LoaderFunc(func(numId int64) (string, error) {
			return u.takeId(context.TODO(), "num_id = ?", numId)
		}),
		cc.ExportStats("user.cache.num_ids"),
	)
}

type cached struct {
	users  cc.Cache[string, *Account]
	numIds cc.Cache[int64, string] // numId -> userId
	// hook
	onAccess []hookFn
}

func (c *cached) OnAccess(do hookFn) {
	c.onAccess = append(c.onAccess, do)
}

// trigger when user updated
func (c *cached) syncing(userId string) {
	c.users.Remove(userId)
}

func (c *cached) Account(ctx context.Context, userId string) (*Account, error) {
	acc, err := c.users.Get(userId)
	if err != nil {
		return nil, err
	}
	for _, do := range c.onAccess {
		if err := do(ctx, acc); err != nil {
			return nil, err
		}
	}
	return acc, nil
}

func (c *cached) Session(ctx context.Context, tags, meta gdk.KVPair) (*Profile, error) {
	userId, has := tags[gtag.UserId]
	if !has {
		return nil, ErrMissingUserId
	}
	acc, err := c.Account(ctx, userId)
	if err != nil {
		return nil, err
	}
	return makeProfile(acc, tags, meta), nil
}

func (c *cached) GetByNumId(ctx context.Context, numId int64) (*Account, error) {
	userId, err := c.numIds.Get(numId)
	if err != nil {
		return nil, err
	}
	return c.Account(ctx, userId)
}

func (u *Manager) GetByShowId(ctx context.Context, showId string) (*Account, error) {
	return u.take(ctx, "show_id = ?", showId)
}

func (u *Manager) Ensure(ctx context.Context, acc *Account) *Account {
	acc = acc.Clone()
	_ = u.onFetching(ctx, acc)
	return acc
}

func (u *Manager) ForceSync(userId string) {
	u.syncing(userId)
}
