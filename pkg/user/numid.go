package user

import (
	"context"
	"errors"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

const (
	numIdAdj  = 1
	numIdBase = 1e9
	numIdLine = 5e6
	numIdSpan = 1e7
)

func newNumId(total int) int {
	pow := total / numIdLine
	return numIdBase + rng.Range(numIdSpan*pow, numIdSpan*(pow+1))
}

func (u *Manager) makeNumId(ctx context.Context) (int, error) {
	var id int

	if n, err := lo.AttemptWhile(id1Try, func(_ int) (error, bool) {
		id = newNumId(numIdAdj)
		_, err := u.checkNumId(ctx, id)
		return err, errors.Is(err, ErrShowIdExists)
	}); err != nil {
		return 0, err
	} else if n > logTry {
		u.log.Warn("found duplicated numId", zap.Int("try", n))
	}

	return id, nil
}

func (u *Manager) checkNumId(ctx context.Context, value int) (int, error) {
	var cnt int64
	if err := db.UseTx(ctx, u.db).Model(&Account{}).Where("num_id = ?", value).Count(&cnt).Error; err != nil {
		return value, err
	} else if cnt > 0 {
		return value, ErrShowIdExists
	}
	return value, nil
}
