package user

import (
	"context"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

const (
	nnTry  = 5 // 昵称重复时尝试次数
	id1Try = 5 // numId重复时尝试次数
	id2Try = 9 // showId重复时尝试次数
	logTry = 3 // 尝试超过N次时写日志
)

func NewId() string {
	return strings.ReplaceAll(uuid.NewString(), "-", "")
}

func (u *Manager) Register(ctx context.Context, acc *Account, opts ...RegisterOpt) (*Account, error) {
	opt := makeRegisterOpts(opts)

	if numId, err := u.makeNumId(ctx); err != nil {
		return nil, err
	} else {
		acc.NumId = int64(numId)
	}

	if acc.UserId == "" {
		acc.UserId = NewId()
	}

	if acc.ShowId == "" {
		var err error
		acc.ShowId, err = u.makeShowId(ctx)
		if err != nil {
			return nil, err
		}
	}

	if showId, err := u.checkShowId(ctx, acc.ShowId); err != nil {
		return nil, err
	} else {
		acc.ShowId = showId
	}

	if nickname, err := u.makeNickname(ctx, acc.Nickname, opt.allowRandNN); err != nil {
		return nil, err
	} else {
		acc.Nickname = nickname
	}

	if !validGender(acc.Gender) {
		acc.Gender = 0
	}

	if acc.Status.None() {
		acc.Status = StatusDefault
	} else if !acc.Status.Has(StatusDefault) {
		acc.Status = acc.Status.Set(StatusDefault)
	}

	if err := db.Transaction(ctx, u.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := u.preCreate(ctx, acc); err != nil {
			return err
		}
		if err := tx.Create(acc).Error; err != nil {
			if err := isDuplicated(err); err != nil {
				return err
			}
			return err
		}
		if err := u.postCreate(ctx, acc); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}

	u.ev.Emit(ctx, EvUserCreated, acc)
	return acc, nil
}
