package user

import (
	"gitlab.sskjz.com/go/gdk"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/gtag"
)

func makeProfile(acc *Account, tags, meta gdk.KVPair) *Profile {
	return &Profile{
		// base
		UserId: acc.UserId,

		// live
		RoomId: tags[gtag.RoomId],

		// account
		Nickname: acc.Nickname,
		Avatar:   acc.Avatar,
		Gender:   acc.Gender,

		// session
		ClientIP: meta[gtag.ClientIP],
	}
}

type Profile struct {
	// BASE
	UserId string `json:"userId"`

	// LIVE
	RoomId string `json:"roomId"`

	// ACCOUNT
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Gender   int    `json:"gender"`

	// SESSION
	ClientIP string `json:"clientIP"`
}

func (p *Profile) Fields() []zap.Field {
	return []zap.Field{
		zap.String("userId", p.UserId),
		zap.String("clientIP", p.ClientIP),
	}
}
