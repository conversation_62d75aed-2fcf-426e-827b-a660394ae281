package user

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMake(t *testing.T) {
	for i := 0; i < 10; i++ {
		fmt.Println(nnMake())
	}
}

func TestTrim(t *testing.T) {
	in := "\t\n\v\f\r\t\n\v\f"
	out := nnTrim(in)
	assert.Equal(t, 0, len(out))
}

func TestValid(t *testing.T) {
	assert.<PERSON><PERSON><PERSON>(t, nn<PERSON>alid("1"))
	assert.<PERSON><PERSON><PERSON>(t, nn<PERSON><PERSON><PERSON>("123456789012345678901"))
	assert.NoError(t, nn<PERSON>alid("kako"))
	assert.<PERSON>rror(t, nn<PERSON><PERSON><PERSON>("kako oficial"))
	assert.<PERSON><PERSON><PERSON>(t, nn<PERSON><PERSON><PERSON>("kako official"))
	assert.<PERSON><PERSON><PERSON>(t, nn<PERSON><PERSON><PERSON>("Kako_Oficial."))
	assert.<PERSON>rror(t, nn<PERSON><PERSON>d("kako inspetor"))
}
