package user

import "time"

// Account 系统账户
type Account struct {
	ID        uint      `gorm:"primaryKey"`
	NumId     int64     `gorm:"not null;size:64;uniqueIndex:num_id"`   // 内部数字ID
	UserId    string    `gorm:"not null;size:32;uniqueIndex:user_id"`  // 内部用户ID
	ShowId    string    `gorm:"not null;size:12;uniqueIndex:show_id"`  // 用户自定义ID
	Nickname  string    `gorm:"not null;size:20;uniqueIndex:nickname"` // 用户昵称
	Avatar    string    `gorm:"not null;size:200"`                     // 用户头像
	Signature string    `gorm:"not null;size:242"`                     // 个性签名
	Gender    int       `gorm:"not null;size:8;default:0"`             // 性别：1男2女
	Birthday  time.Time `gorm:"not null;type:date;default:1000-01-01"` // 生日
	Region    string    `gorm:"not null;size:30"`                      // 地区
	School    string    `gorm:"not null;size:56"`                      // 学校
	Level     int       `gorm:"not null;size:8;default:0"`             // 等级
	Roles     Role      `gorm:"not null;size:32;default:0"`            // 身份
	Status    Status    `gorm:"not null;size:32;default:1"`            // 账户状态
	Country   string    `gorm:"not null;size:2"`                       // 国家
	Timezone  string    `gorm:"not null;size:32"`                      // 时区
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (a *Account) Clone() *Account {
	var n Account
	n = *a
	return &n
}

func (a *Account) TableName() string {
	return "user_accounts"
}
