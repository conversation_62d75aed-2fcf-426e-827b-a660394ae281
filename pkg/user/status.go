package user

import (
	"strings"
)

// Status 用户状态
type Status uint32

const (
	StatusDefault  Status = 1 << iota
	StatusBlocked         // 已禁用
	StatusDeleted         // 已注销
	StatusVerified        // 实名认证
)

func (s Status) None() bool {
	return s == 0
}

func (s Status) Normal() bool {
	return !s.Blocked() && !s.Deleted()
}

func (s Status) Blocked() bool {
	return s.Has(StatusBlocked)
}

func (s Status) Deleted() bool {
	return s.Has(StatusDeleted)
}

func (s Status) Verified() bool {
	return s.Has(StatusVerified)
}

func (s Status) Has(cmp Status) bool {
	return s&cmp == cmp
}

func (s Status) Set(in ...Status) Status {
	n := s
	for _, add := range in {
		n |= add
	}
	return n
}

func (s Status) Unset(in ...Status) Status {
	n := s
	for _, del := range in {
		n &= ^del
	}
	return n
}

func (s Status) String() string {
	tests := map[Status]string{
		StatusBlocked:  "blocked",
		StatusDeleted:  "deleted",
		StatusVerified: "verified",
	}
	var out []string
	for k, v := range tests {
		if s.Has(k) {
			out = append(out, v)
		}
	}
	if len(out) == 0 {
		return "default"
	}
	return strings.Join(out, ",")
}
