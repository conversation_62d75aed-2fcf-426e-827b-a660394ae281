package user

import (
	"context"
	"fmt"
	"testing"

	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/fx"
)

var testMod = fx.Module("user", fx.Provide(
	redi.Provide, unq.Provide, cron.Provide, db.ProvideGORM,
	ev.Provide, cc.Provide,
	Provide,
))

func TestUpdateEv(t *testing.T) {
	fx2.Testing(t, testMod, func(mgr *Manager) error {
		var (
			ctx    = context.TODO()
			userId = "21ad0083d6104ed1b742ce821cc8395e"
			update = &Account{
				ShowId: fmt.Sprintf("MOYO%d", rng.Range(1, 9)),
			}
		)
		if err := mgr.Update(ctx, userId, UpAccount(update)); err != nil {
			return err
		}
		return nil
	})
}
