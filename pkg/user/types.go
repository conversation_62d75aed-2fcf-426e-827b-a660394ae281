package user

import "gitlab.sskjz.com/overseas/live/osl/pkg/db"

const (
	ResetNickname = "---"
	ResetAvatar   = "---"
)

func validGender(v int) bool {
	return v == 1 || v == 2
}

func isDuplicated(err error) error {
	switch {
	case db.IsDuplicate(err, "accounts.nickname"):
		return ErrNicknameExists
	case db.IsDuplicate(err, "accounts.show_id"):
		return ErrShowIdExists
	default:
		return nil
	}
}
