package user

import (
	"context"
	"strconv"

	"github.com/samber/lo"
)

func (u *Manager) Forget(ctx context.Context, userId string) error {
	old, err := u.Take(noHooking(ctx), userId)
	if err != nil {
		return err
	}

	if old.Status.Deleted() {
		return ErrAccountNotUsable
	}

	if err := u.Update(ctx, userId,
		UpAccount(&Account{
			Nickname: strconv.Itoa(int(old.NumId)),
			Avatar:   ResetAvatar,
		}),
		UpNullable(&Nullable{
			Signature: lo.ToPtr(""),
			Region:    lo.ToPtr(""),
			School:    lo.To<PERSON>tr(""),
		}),
		SetStatus(StatusDeleted),
	); err != nil {
		return err
	}

	u.ev.Emit(ctx, EvUserDeleted, old)
	return nil
}

func reset1(c context.Context, m *Manager, to *Account) error {
	if to.Nickname == ResetNickname {
		if nickname, err := m.<PERSON>(c); err != nil {
			return err
		} else {
			to.Nickname = nickname
		}
	}
	return nil
}

func reset2(_ context.Context, _ *Manager, up UpData) error {
	if up["avatar"] == ResetAvatar {
		up["avatar"] = ""
	}
	return nil
}
