package user

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"unicode"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

var (
	ErrInvalidShowId = biz.NewError(biz.ErrInvalidShowId, "invalid showId")
	ErrShowIdExists  = biz.NewError(biz.ErrShowIdExists, "showId already exists")
)

const (
	showIdAdj  = 1
	showIdBase = 1e7
	showIdLine = 5e5
	showIdSpan = 1e6
)

func newShowId(total int) string {
	pow := total / showIdLine
	showId := showIdBase + rng.Range(showIdSpan*pow, showIdSpan*(pow+1))
	return strconv.Itoa(showId)
}

func (u *Manager) makeShowId(ctx context.Context) (string, error) {
	var id string

	if n, err := lo.AttemptWhile(id2Try, func(_ int) (error, bool) {
		id = newShowId(showIdAdj)
		_, err := u.checkShowId(ctx, id)
		return err, errors.Is(err, ErrShowIdExists)
	}); err != nil {
		return "", err
	} else if n > logTry {
		u.log.Warn("found duplicated showId", zap.Int("try", n))
	}

	return id, nil
}

func (u *Manager) checkShowId(ctx context.Context, value string) (string, error) {
	value = strings.TrimSpace(value)
	if err := idValid(value); err != nil {
		return value, err
	}

	var cnt int64
	if err := db.UseTx(ctx, u.db).Model(&Account{}).Where("show_id = ?", value).Count(&cnt).Error; err != nil {
		return value, err
	} else if cnt > 0 {
		return value, ErrShowIdExists
	}

	return value, nil
}

const (
	showIdMinLen = 4
	showIdMaxLen = 12
)

func idValid(in string) error {
	if len(in) < showIdMinLen {
		return ErrInvalidShowId
	}

	if len(in) > showIdMaxLen {
		return ErrInvalidShowId
	}

	for _, c := range in {
		if !unicode.IsLetter(c) && !unicode.IsNumber(c) {
			return ErrInvalidShowId
		}
	}

	return nil
}
