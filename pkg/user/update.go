package user

import (
	"context"
	"errors"
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrNoUpdateValue = errors.New("no update value")
)

func (u *Manager) PreUpdate(ctx context.Context, userId string, opts ...UpOption) (*Account, UpData, error) {
	old, err := u.Take(noHooking(ctx), userId)
	if err != nil {
		return nil, nil, err
	}

	data := make(UpData)
	for _, opt := range opts {
		if up, err := opt(ctx, u, old); err != nil {
			return nil, nil, err
		} else {
			for k, v := range up {
				data[k] = v
			}
		}
	}

	if len(data) == 0 {
		return nil, nil, ErrNoUpdateValue
	}

	return old, data, nil
}

func (u *Manager) Update(ctx context.Context, userId string, opts ...UpOption) error {
	old, up, err := u.PreUpdate(ctx, userId, opts...)
	if err != nil {
		return fmt.Errorf("precheck failed: %w", err)
	}

	acc := old.Clone()
	if err := db.UseTx(ctx, u.db).Model(acc).Omit(omitFields(ctx)...).Updates(up).Error; err != nil {
		if err := isDuplicated(err); err != nil {
			return err
		}
		return fmt.Errorf("update failed: %w", err)
	}

	u.syncing(userId)
	u.ev.Emit(ctx, EvUserUpdated, &EvUpdating{Old: old, New: acc})
	return nil
}
