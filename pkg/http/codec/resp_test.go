package codec

import (
	"bufio"
	"net"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
)

func BenchmarkMakeResp(b *testing.B) {
	ctx := &gin.Context{Writer: newTw()}
	for range b.N {
		MakeResp(ctx, 0, "", nil)
	}
	b.ReportAllocs()
}

func newTw() gin.ResponseWriter {
	return &tw{
		h: make(http.Header),
	}
}

type tw struct {
	h http.Header
}

func (t *tw) Header() http.Header                          { return t.h }
func (t *tw) Write(bytes []byte) (int, error)              { return len(bytes), nil }
func (t *tw) WriteHeader(statusCode int)                   {}
func (t *tw) Hijack() (net.Conn, *bufio.ReadWriter, error) { return nil, nil, nil }
func (t *tw) Flush()                                       {}
func (t *tw) CloseNotify() <-chan bool                     { return nil }
func (t *tw) Status() int                                  { return http.StatusOK }
func (t *tw) Size() int                                    { return 0 }
func (t *tw) WriteString(s string) (int, error)            { return len(s), nil }
func (t *tw) Written() bool                                { return false }
func (t *tw) WriteHeaderNow()                              {}
func (t *tw) Pusher() http.Pusher                          { return nil }
