package codec

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Error any    `json:"error,omitempty"`
	Data  any    `json:"data"`
}

func MakeResp(ctx *gin.Context, code int, msg string, data any) {
	enc := outCodec(ctx)
	resp := &Response{Code: code, Msg: msg, Data: data}
	if resp.Code != 0 && resp.Data != nil {
		resp.Error, resp.Data = resp.Data, nil
	}
	if bs, err := enc.<PERSON>(resp); err != nil {
		ctx.Status(http.StatusInternalServerError)
	} else {
		ctx.Header("Content-Length", strconv.Itoa(len(bs)))
		ctx.Data(http.StatusOK, enc.MIME(), bs)
	}
}
