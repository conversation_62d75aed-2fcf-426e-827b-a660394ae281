package codec

import (
	"encoding/json"

	"github.com/bytedance/sonic"
)

const typeJson = "application/json"

var codec = sonic.Config{NoNullSliceOrMap: true}.Froze()

type jsonCodec struct{}

func (c *jsonCodec) MIME() string {
	return typeJson
}

func (c *jsonCodec) Marshal(msg any) ([]byte, error) {
	if jc, is := msg.(json.Marshaler); is {
		return jc.MarshalJSON()
	}
	return codec.Marshal(msg)
}

func (c *jsonCodec) Unmarshal(data []byte, bind any) error {
	return codec.Unmarshal(data, bind)
}
