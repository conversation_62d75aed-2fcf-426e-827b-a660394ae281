package codec

import (
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type ReqBinder func(obj any) error

func Bind(ctx *gin.Context) ReqBinder {
	if strings.Contains(ctx.GetHeader("Content-Type"), "form") {
		return func(obj any) error {
			return ctx.ShouldBindWith(obj, binding.Form)
		}
	}
	return func(obj any) error {
		if err := ctx.ShouldBindWith(obj, newBinder(inCodec(ctx))); err != nil {
			return err
		}
		if binding.Validator != nil {
			return binding.Validator.ValidateStruct(obj)
		}
		return nil
	}
}

func newBinder(codec Codec) *binder {
	return &binder{name: codec.MIME(), codec: codec}
}

type binder struct {
	name  string
	codec Codec
}

func (b *binder) Name() string {
	return b.name
}

func (b *binder) Bind(req *http.Request, obj any) error {
	if req.Body == nil || req.Body == http.NoBody {
		return nil
	}
	if bs, err := io.ReadAll(req.Body); err != nil {
		return err
	} else {
		return b.codec.Unmarshal(bs, &obj)
	}
}
