package rlimit

import (
	"time"

	ratelimit "github.com/JGLTechnologies/gin-rate-limit"
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
)

func Provide(rc *redi.Client, vnd log.Vendor) *Handler {
	return &Handler{mw: ratelimit.RateLimiter(
		newStore(rc, vnd.Scope("ratelimit"), "HTTP:RATE:",
			time.Second, 5,
		),
		&ratelimit.Options{
			ErrorHandler: func(ctx *gin.Context, info ratelimit.Info) {
				ctx.String(429, "Too many requests. Try again in "+time.Until(info.ResetTime).String())
			},
			KeyFunc: func(ctx *gin.Context) string {
				return ctx.ClientIP()
			},
		},
	)}
}

type Handler struct {
	mw gin.HandlerFunc
}

func (s *Handler) Middleware() gin.HandlerFunc {
	return s.mw
}
