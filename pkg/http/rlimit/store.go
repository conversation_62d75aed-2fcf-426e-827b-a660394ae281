package rlimit

import (
	"time"

	ratelimit "github.com/JGLTechnologies/gin-rate-limit"
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/redi"
	"go.uber.org/zap"
)

func newStore(cli *redi.Client, log *zap.Logger, prefix string, rate time.Duration, limit uint) *store {
	return &store{
		cli:   cli,
		log:   log,
		keyp:  prefix,
		rate:  rate,
		limit: limit,
	}
}

type store struct {
	cli   *redi.Client
	log   *zap.Logger
	keyp  string
	rate  time.Duration
	limit uint
}

func (s *store) Limit(key string, ctx *gin.Context) ratelimit.Info {
	key = s.keyp + key

	var (
		now     = time.Now()
		startAt = now.Truncate(s.rate)
		endAt   = startAt.Add(s.rate)
	)

	txp := s.cli.Pipeline()
	next := txp.Incr(ctx, key)
	txp.ExpireAt(ctx, key, endAt)

	if _, err := txp.Exec(ctx); err != nil {
		s.log.Warn("failed to execute pipeline", zap.Error(err))
		return ratelimit.Info{RateLimited: false}
	}

	remains := int64(s.limit) - next.Val()

	return ratelimit.Info{
		Limit:         s.limit,
		RateLimited:   remains < 0,
		ResetTime:     endAt,
		RemainingHits: uint(max(remains, 0)),
	}
}
