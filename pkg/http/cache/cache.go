package cache

import (
	"bytes"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type Store interface {
	Get(key string) (Response, bool)
	Set(key string, val Response, expire time.Duration)
}

type Response struct {
	Status int
	Header http.Header
	Data   []byte
}

func makeResponse(w *cachedWriter) Response {
	return Response{
		Status: w.Status(),
		Header: w.<PERSON>er(),
		Data:   w.body.Bytes(),
	}
}

type cachedWriter struct {
	gin.ResponseWriter
	body bytes.Buffer
}

var _ gin.ResponseWriter = &cachedWriter{}

func newCachedWriter(writer gin.ResponseWriter) *cachedWriter {
	return &cachedWriter{ResponseWriter: writer}
}

func (w *cachedWriter) Write(data []byte) (int, error) {
	ret, err := w.ResponseWriter.Write(data)
	if err == nil {
		w.body.Write(data)
	}
	return ret, err
}

func (w *cachedWriter) WriteString(data string) (n int, err error) {
	ret, err := w.ResponseWriter.WriteString(data)
	if err == nil {
		w.body.WriteString(data)
	}
	return ret, err
}
