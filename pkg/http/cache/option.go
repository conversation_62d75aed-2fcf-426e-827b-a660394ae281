package cache

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

func createKey(c *gin.Context) string {
	return c.Request.RequestURI
}

func codeValidator(resp Response) bool {
	if resp.Status < 200 || resp.Status >= 300 {
		return false
	}
	if code := gjson.GetBytes(resp.Data, "code"); code.Exists() && code.Int() == 0 {
		return true
	}
	return false
}

func newOptions(opts []Option) *options {
	o := &options{
		customKey: createKey,
		validator: codeValidator,
		expire:    time.Minute,
	}
	for _, opt := range opts {
		opt(o)
	}
	return o
}

type options struct {
	customKey keyGenerator
	validator validator
	expire    time.Duration
}

type Option func(*options)

type keyGenerator func(*gin.Context) string

func WithCustomKey(ck keyGenerator) Option {
	return func(o *options) {
		o.customKey = ck
	}
}

type validator func(Response) bool

func WithValidator(cv validator) Option {
	return func(o *options) {
		o.validator = cv
	}
}

func WithExpire(expire time.Duration) Option {
	return func(o *options) {
		o.expire = expire
	}
}
