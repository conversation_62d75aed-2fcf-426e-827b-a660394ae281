package cache

import (
	"context"
	"time"

	"github.com/cespare/xxhash/v2"
	"github.com/elastic/go-freelru"
	"gitlab.sskjz.com/go/ob/meter"
)

func strHash(s string) uint32 {
	return uint32(xxhash.Sum64String(s))
}

func newMemory() Store {
	cc, err := freelru.NewSynced[string, Response](1e5, strHash)
	if err != nil {
		panic(err)
	}
	meter.Visitor(func(ctx context.Context) {
		metrics := cc.Metrics()
		meter.NewGauge[uint64]("http_cache_hit", meter.WithContext(ctx)).Set(metrics.Hits)
		meter.NewGauge[uint64]("http_cache_miss", meter.WithContext(ctx)).Set(metrics.Misses)
	})
	return &memStore{cc: cc}
}

type memStore struct {
	cc freelru.Cache[string, Response]
}

func (s *memStore) Get(key string) (Response, bool) {
	return s.cc.Get(key)
}

func (s *memStore) Set(key string, val Response, expire time.Duration) {
	s.cc.AddWithLifetime(key, val, expire)
}
