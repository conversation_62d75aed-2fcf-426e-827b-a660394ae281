package cache

import (
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/singleflight"
)

const (
	cacheHdr  = "X-Cache-Status"
	hitCache  = "HIT"
	missCache = "MISS"
)

type Handler struct {
	sfg singleflight.Group
	sto Store
}

func (s *Handler) Middleware(opts ...Option) gin.HandlerFunc {
	return newHandler(&s.sfg, s.sto, opts)
}

func newHandler(sfg *singleflight.Group, sto Store, opts []Option) gin.HandlerFunc {
	opt := newOptions(opts)
	return func(c *gin.Context) {
		key := opt.customKey(c)
		if key == "" { // skip cache
			c.Next()
			return
		}
		cache, has := sto.Get(key)
		if !has {
			var bypass bool
			rc, _, _ := sfg.Do(key, func() (any, error) {
				bypass = true
				c.Header(cacheHdr, missCache)
				writer := newCachedWriter(c.Writer)
				c.Writer = writer
				c.Next()
				if c.IsAborted() {
					return nil, nil
				}
				resp := makeResponse(writer)
				if !opt.validator(resp) {
					return nil, nil
				}
				sto.Set(key, resp, opt.expire)
				return resp, nil
			})
			if bypass || rc == nil {
				return
			}
			cache = rc.(Response)
		}
		c.Abort()
		c.Status(cache.Status)
		header := c.Writer.Header()
		header.Set(cacheHdr, hitCache)
		for k, vs := range cache.Header {
			if header.Get(k) == "" {
				header.Set(k, vs[0])
			}
		}
		_, _ = c.Writer.Write(cache.Data)
	}
}
