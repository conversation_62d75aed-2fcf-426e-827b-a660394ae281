package auth

import (
	"context"

	jwt "gitlab.sskjz.com/go/gin-jwt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func getUser(ctx context.Context, claims jwt.MapClaims, ug user.Getter) *user.Account {
	acc, err := ug.Account(ctx, claims[jwtUserId].(string))
	if err != nil {
		return nil
	}
	return acc
}

func validUser(in any) error {
	if v, is := in.(*user.Account); is && v != nil {
		if !v.Status.Normal() {
			return user.ErrAccountNotUsable
		}
		return nil
	}
	return user.ErrAccountNotExists
}
