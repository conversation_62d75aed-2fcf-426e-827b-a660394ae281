package auth

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	goJwt "github.com/golang-jwt/jwt/v4"
	jwt "gitlab.sskjz.com/go/gin-jwt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func newJWT(jwt *jwt.GinJWTMiddleware, ug user.Getter) *JWT {
	return &JWT{jwt: jwt, ug: ug}
}

type JWT struct {
	jwt *jwt.GinJWTMiddleware
	ug  user.Getter
}

func (j *JWT) Middleware() gin.HandlerFunc {
	return j.jwt.MiddlewareFunc()
}

func (j *JWT) WithGuest() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer c.Next()
		claims, err := j.jwt.GetClaimsFromJWT(c)
		if err != nil {
			return
		}
		c.Set("JWT_PAYLOAD", claims)
		identity := j.jwt.IdentityHandler(c)
		if j.jwt.Authorizator(identity, c) {
			c.Set(j.jwt.IdentityKey, identity)
		}
	}
}

func (j *JWT) AssignLogin(ctx *gin.Context, acc *user.Account) error {
	ctx.Set(tmpLoginKey, acc)
	j.jwt.LoginHandler(ctx)
	return nil
}

func (j *JWT) TokenGenerator(ctx context.Context, acc *user.Account) (string, time.Time, error) {
	if err := validUser(acc); err != nil {
		return "", time.Time{}, err
	}
	return j.jwt.TokenGenerator(acc)
}

func (j *JWT) ParseToken(ctx context.Context, token string) (*user.Account, error) {
	t, err := j.jwt.ParseTokenString(token)
	if err != nil {
		return nil, err
	}
	acc := getUser(ctx, jwt.ExtractClaimsFromToken(t), j.ug)
	return acc, validUser(acc)
}

func (j *JWT) RefreshHandler(ctx *gin.Context) {
	if t, err := j.jwt.ParseToken(ctx); err == nil {
		if err := validUser(getUser(ctx, jwt.ExtractClaimsFromToken(t), j.ug)); err != nil {
			codec.MakeResp(ctx, biz.ErrUnauthorized, jwt.ErrExpiredToken.Error(), nil)
			return
		}
		if exp := t.Claims.(goJwt.MapClaims)["exp"]; exp != nil {
			if expTs, is := exp.(float64); is {
				expire := time.Unix(int64(expTs), 0)
				if expire.Add(-72 * time.Hour).After(time.Now()) {
					makePayload(ctx, t.Raw, expire)
					return
				}
			}
		}
	}
	j.jwt.RefreshHandler(ctx)
}

func (j *JWT) LogoutHandler(ctx *gin.Context) {
	j.jwt.LogoutHandler(ctx)
}
