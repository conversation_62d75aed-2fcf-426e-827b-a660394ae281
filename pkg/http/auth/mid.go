package auth

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	jwt "gitlab.sskjz.com/go/gin-jwt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const (
	tmpLoginKey = "tLogin"
	ContextUser = "aUser"
	jwtUserId   = "uid"
)

func NewJWT(key string, ug user.Getter) (*JWT, error) {
	jmw, err := jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "osl",
		Key:         []byte(key),
		Timeout:     time.Hour * 24 * 90,
		MaxRefresh:  time.Hour * 24 * 365,
		IdentityKey: ContextUser,
		PayloadFunc: func(data any) jwt.MapClaims {
			if v, ok := data.(*user.Account); ok {
				return jwt.MapClaims{
					jwtUserId: v.UserId,
				}
			}
			return jwt.MapClaims{}
		},
		IdentityHandler: func(c *gin.Context) any {
			return getUser(c, jwt.ExtractClaims(c), ug)
		},
		Authenticator: func(c *gin.Context) (any, error) {
			if v, has := c.Get(tmpLoginKey); has {
				return v, validUser(v)
			}
			return nil, jwt.ErrFailedAuthentication
		},
		Authorizator: func(data any, c *gin.Context) bool {
			return validUser(data) == nil
		},
		Unauthorized: func(c *gin.Context, code int, message string) {
			codec.MakeResp(c, biz.ErrUnauthorized, message, nil)
		},
		LoginResponse: func(c *gin.Context, code int, token string, expire time.Time) {
			makePayload(c, token, expire)
		},
		RefreshResponse: func(c *gin.Context, code int, token string, expire time.Time) {
			makePayload(c, token, expire)
		},
		TokenLookup:   "header: Authorization, query: token, cookie: jwt",
		TokenHeadName: "Bearer",
		TimeFunc:      time.Now,
	})

	if err != nil {
		return nil, fmt.Errorf("create jwt failed: %w", err)
	}

	return newJWT(jmw, ug), nil
}
