package auth

import (
	"encoding/base64"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
)

type Payload struct {
	Token  string `json:"token"`
	Expire int64  `json:"expire"`
	UserId string `json:"userId"`
}

func makePayload(ctx *gin.Context, token string, expired time.Time) {
	var userId string
	if parts := strings.Split(token, "."); len(parts) > 1 {
		var grant struct {
			UserId string `json:"uid"`
		}
		claims, _ := base64.RawURLEncoding.DecodeString(parts[1])
		if sonic.Unmarshal(claims, &grant) == nil {
			userId = grant.UserId
		}
	}
	codec.MakeResp(ctx, 0, "", &Payload{
		Token:  token,
		Expire: expired.Unix(),
		UserId: userId,
	})
}
