package auth

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

var (
	ErrUserNotFound = biz.NewError(biz.ErrUnauthorized, "user not logged in")
)

type ctxGetter interface {
	Get(key string) (any, bool)
}

func User(ctx ctxGetter) (*user.Account, error) {
	if v, has := ctx.Get(ContextUser); has && validUser(v) == nil {
		return v.(*user.Account), nil
	}
	return nil, ErrUserNotFound
}
