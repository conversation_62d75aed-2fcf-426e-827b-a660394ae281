package api

import (
	"github.com/gin-gonic/gin"

	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
)

func NewRouter(gr gin.IRouter, jwt *auth.JWT) *Router {
	return &Router{IRouter: gr, jwt: jwt}
}

type Router struct {
	gin.IRouter
	jwt *auth.JWT
	routerExt
}

func (r *Router) With(middleware ...gin.HandlerFunc) *Router {
	return NewRouter(r.IRouter.Group("", middleware...), r.jwt)
}

func (r *Router) WithAuth(post ...gin.HandlerFunc) *Router {
	return r.With(append(append([]gin.HandlerFunc{r.jwt.Middleware()}, r.postAuth...), post...)...)
}

func (r *Router) TryAuth(post ...gin.HandlerFunc) *Router {
	return r.With(append([]gin.HandlerFunc{r.jwt.WithGuest()}, post...)...)
}
