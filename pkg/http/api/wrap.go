package api

import (
	"errors"

	"github.com/gin-gonic/gin"

	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
)

func Bind[REQ any](ctx *gin.Context) (REQ, error) {
	var req REQ

	bind := make([]codec.ReqBinder, 0, 1)
	switch ctx.Request.Method {
	case "GET", "DELETE":
		bind = append(bind, ctx.ShouldBindQuery)
	default:
		bind = append(bind, codec.Bind(ctx))
	}

	for _, fn := range bind {
		if err := fn(&req); err != nil {
			var nn REQ
			_ = ctx.Error(err).SetType(gin.ErrorTypeBind)
			return nn, err
		}
	}

	return req, nil
}

type Handler[REQ, RESP any] func(*Context, REQ) (RESP, error)

func Generic[REQ, RESP any](fn Handler[REQ, RESP]) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		req, err := Bind[REQ](ctx)
		if err != nil {
			makeError(ctx, err)
			return
		}

		resp, err := fn(newContext(ctx), req)
		if err != nil {
			var data *DataErr
			if errors.As(err, &data) {
				makeError2(ctx, err, data.data)
				return
			}
			makeError(ctx, err)
			return
		}

		makeResp(ctx, resp)
	}
}

func Request[REQ any](fn func(*Context, REQ) error) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		req, err := Bind[REQ](ctx)
		if err != nil {
			makeError(ctx, err)
			return
		}

		err2 := fn(newContext(ctx), req)
		if err2 != nil {
			makeError(ctx, err2)
			return
		}
	}
}

func Custom(fn func(*gin.Context) error) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if err := fn(ctx); err != nil {
			makeError(ctx, err)
			return
		}
	}
}
