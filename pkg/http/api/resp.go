package api

import (
	"errors"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
)

func makeResp(ctx *gin.Context, data any) {
	codec.MakeResp(ctx, 0, "", data)
}

func makeError(ctx *gin.Context, err error) {
	makeError2(ctx, err, nil)
}

func codeMsg(err error) (code int, msg string) {
	var bizErr *biz.Error
	if errors.As(err, &bizErr) {
		code = bizErr.Code
		msg = bizErr.Msg
	} else if v, is := err.(BizErr); is {
		code = v.ErrCode()
		msg = v.ErrMsg()
	} else {
		code = biz.ErrSystem
	}
	return
}

func makeError2(ctx *gin.Context, err error, data any) {
	code, msg := codeMsg(err)
	if code == biz.ErrSystem {
		if isBindErr(ctx) {
			code = biz.ErrInvalidParam
			logger(ctx).Info("bind fail", zap.Error(err))
		} else {
			logger(ctx).Info("sys error", zap.Error(err))
		}
	} else {
		logger(ctx).Debug("biz error", zap.Error(err))
	}
	code, msg = errMsgH(ctx, err, code, msg)
	codec.MakeResp(ctx, code, msg, data)
}

func isBindErr(ctx *gin.Context) bool {
	if le := ctx.Errors.Last(); le != nil && le.IsType(gin.ErrorTypeBind) {
		return true
	}
	return false
}
