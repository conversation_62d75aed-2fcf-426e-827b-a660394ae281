package api

import (
	"context"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const (
	contextLogger = "aLogger"
)

func newContext(ctx *gin.Context) *Context {
	return &Context{
		Context: ctx,
	}
}

type Context struct {
	*gin.Context
}

func (c *Context) User() (*user.Account, error) {
	return auth.User(c)
}

func Unwrap(ctx context.Context) *gin.Context {
	if v, is := ctx.Value(gin.ContextKey).(*gin.Context); is {
		return v
	}
	return nil
}

func WithLogger(log *zap.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Set(contextLogger, log)
	}
}

func logger(c *gin.Context) *zap.Logger {
	v, has := c.Get(contextLogger)
	if !has {
		return zap.L()
	}

	f := []zap.Field{
		zap.String("method", c.Request.Method),
		zap.String("path", c.FullPath()),
	}

	if u, err := auth.User(c); err == nil {
		f = append(f, zap.String("userId", u.UserId))
	}

	return v.(*zap.Logger).With(f...)
}
