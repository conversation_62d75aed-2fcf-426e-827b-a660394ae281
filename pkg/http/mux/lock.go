package mux

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/go/redi"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
)

const (
	keyMutex = "HTTP:MUX:%s" // userId
)

func newLocker(dm *redi.Mutex, log *zap.Logger) *Locker {
	return &Locker{dm: dm, log: log}
}

type Locker struct {
	dm  *redi.Mutex
	log *zap.Logger
}

func (s *Locker) Middleware(filters ...cond) gin.HandlerFunc {
	return func(c *gin.Context) {
		if v, err := auth.User(c); err == nil && accept(c, filters) {
			l, err := s.dm.Lock(c, fmt.Sprintf(keyMutex, v.UserId))
			if err != nil {
				s.log.Warn("lock failed", zap.String("path", c.<PERSON>()), zap.String("userId", v.UserId), zap.Error(err))
				c.AbortWithStatus(http.StatusConflict)
				return
			}
			defer l.MustUnlock()
		}
		c.Next()
	}
}
