package rlog

import (
	"bytes"
	"io"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
)

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

var skipPaths = []string{
	"/",
	"/health/ping",
}

func RequestLogger(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if slices.Contains(skipPaths, path) {
			c.Next()
			return
		}

		if strings.HasPrefix(path, "/swagger") {
			c.Next()
			return
		}

		if c.Request.Method == http.MethodOptions {
			c.Next()
			return
		}

		rid := requestId(c)

		t := time.Now()

		var bodyBytes []byte
		if c.Request.Body != nil && c.ContentType() == "application/json" {
			bodyBytes, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		}

		blw := &bodyLogWriter{ResponseWriter: c.Writer, body: bytes.NewBuffer(nil)}
		c.Writer = blw

		c.Next()

		config := GetConfig(c)

		if config.skipLog(c) {
			return
		}

		ff := make([]zap.Field, 0, 16)
		ff = append(ff,
			zap.Time("requestAt", t),
			zap.String("requestId", rid),
			zap.String("ip", c.ClientIP()),
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.Any("header", c.Request.Header),
			zap.Int("status", c.Writer.Status()),
			zap.Duration("latency", time.Since(t)),
		)

		if len(bodyBytes) > 0 {
			ff = append(ff, zap.ByteString("request", bodyBytes))
		}

		if c.Writer.Header().Get("Content-Type") == "application/json" {
			ff = append(ff, zap.ByteString("response", blw.body.Bytes()))
		}

		{
			if v := c.GetHeader(app.HdrDeviceId); v != "" {
				ff = append(ff, zap.String("deviceId", v))
			}
			if v := c.GetHeader(app.HdrChannelId); v != "" {
				ff = append(ff, zap.String("channel", v))
			}
			if v := c.GetHeader(app.HdrVersion); v != "" {
				ff = append(ff, zap.String("version", v))
			}
		}

		if cache := c.Writer.Header().Get("X-Cache-Status"); cache != "" {
			ff = append(ff, zap.String("cache", cache))
		}

		if v, err := auth.User(c); err == nil {
			ff = append(ff, zap.String("userId", v.UserId))
		}

		logger.Info("request", ff...)
	}
}
