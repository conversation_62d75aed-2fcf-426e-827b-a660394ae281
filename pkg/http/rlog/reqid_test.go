package rlog

import (
	"net/textproto"
	"testing"
)

func BenchmarkRequestId(b *testing.B) {
	b.<PERSON>("X-Request-ID", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			textproto.CanonicalMIMEHeaderKey("X-Request-ID")
		}
		b.ReportAllocs()
	})
	b.<PERSON>("X-Request-Id", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			textproto.CanonicalMIMEHeaderKey("X-Request-Id")
		}
		b.Report<PERSON>llocs()
	})
}
