package rlog

import (
	"math/rand/v2"

	"github.com/gin-gonic/gin"
)

type Config struct {
	ratio      float64
	skipCached bool
}

func defaultConfig() *Config {
	return &Config{ratio: 1, skipCached: true}
}

type Option func(*Config)

func WithRatio(ratio float64) Option {
	return func(o *Config) {
		o.ratio = ratio
	}
}

func WithSkipCached(skipCached bool) Option {
	return func(o *Config) {
		o.skipCached = skipCached
	}
}

func Opt(oo ...Option) gin.HandlerFunc {
	opt := defaultConfig()
	for _, o := range oo {
		o(opt)
	}

	return func(c *gin.Context) {
		c.Set("rlog", opt)
		c.Next()
	}
}

func GetConfig(c *gin.Context) *Config {
	v := c.Value("rlog")
	if v == nil {
		return defaultConfig()
	}
	return v.(*Config)
}

func (c *Config) skipLog(ctx *gin.Context) bool {
	if r := rand.Float64(); r >= c.ratio {
		return true
	}

	if c.skipCached && ctx.Writer.Header().Get("X-Cache-Status") == "HIT" {
		return true
	}

	return false
}
