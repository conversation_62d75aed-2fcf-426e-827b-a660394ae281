package rlog

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	hdrRequestId = "X-Request-Id"
)

func requestId(c *gin.Context) string {
	rid := c.GetHeader(hdrRequestId)
	if rid == "" {
		rid = uuid.NewString()
		c.Request.Header.Add(hdrRequestId, rid)
	}
	c<PERSON><PERSON>(hdrRequestId, rid)
	return rid
}

func RequestId(ctx context.Context) string {
	if g, is := ctx.Value(gin.ContextKey).(*gin.Context); is {
		return g.GetHeader(hdrRequestId)
	}
	return ""
}
