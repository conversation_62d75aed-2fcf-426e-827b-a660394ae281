package region

import (
	"encoding/xml"
	"github.com/tidwall/btree"
	"os"
	"strings"
)

type desc struct {
	XMLName       xml.Name `xml:"Location"`
	Text          string   `xml:",chardata"`
	CountryRegion []struct {
		Text  string `xml:",chardata"`
		Name  string `xml:"Name,attr"`
		Code  string `xml:"Code,attr"`
		State []struct {
			Text string `xml:",chardata"`
			Name string `xml:"Name,attr"`
			Code string `xml:"Code,attr"`
			City []struct {
				Text string `xml:",chardata"`
				Name string `xml:"Name,attr"`
				Code string `xml:"Code,attr"`
			} `xml:"City"`
		} `xml:"State"`
	} `xml:"CountryRegion"`
}

type Node struct {
	Edge bool
	Name string
}

const codeSep = "-"

func parentCode(code string) string {
	if i := strings.LastIndex(code, codeSep); i < 0 {
		return ""
	} else {
		return code[:i]
	}
}

type store = btree.Map[string, Node]

func loadFile(path string) (*store, error) {
	bs, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var loc desc
	if err := xml.Unmarshal(bs, &loc); err != nil {
		return nil, err
	}

	var m store

	for _, country := range loc.CountryRegion {
		code1 := country.Code
		if code1 == "1" {
			code1 = "CN"
		}
		m.Set(code1, Node{Edge: len(country.State) == 0, Name: country.Name})
		for _, state := range country.State {
			code2 := code1
			if state.Code != "" {
				code2 += codeSep + state.Code
				m.Set(code2, Node{Edge: len(state.City) == 0, Name: state.Name})
			}
			for _, city := range state.City {
				code3 := code2
				if city.Code != "" {
					code3 += codeSep + city.Code
					m.Set(code3, Node{Edge: true, Name: city.Name})
				}
			}
		}
	}

	return &m, nil
}

type Region struct {
	Code string    `json:"code,omitempty"` // 编码不为空时才能用
	Name string    `json:"name,omitempty"` // 区域名称
	List []*Region `json:"list,omitempty"` // 子区域列表
}

func dumpRegion(m *store) []*Region {
	resp := make([]*Region, 0, 240)
	link := make(map[string][]*Region, 267)
	m.Scan(func(code string, node Node) bool {
		pCode := parentCode(code)
		if pCode == "" {
			resp = append(resp, &Region{
				Code: code,
				Name: node.Name,
			})
		}
		link[pCode] = append(link[pCode], &Region{
			Code: code,
			Name: node.Name,
		})
		return true
	})
	for _, r1 := range resp {
		r1.List = link[r1.Code]
		if len(r1.List) > 0 {
			r1.Code = ""
		}
		for _, r2 := range r1.List {
			r2.List = link[r2.Code]
			if len(r2.List) > 0 {
				r2.Code = ""
			}
		}
	}
	return resp
}
