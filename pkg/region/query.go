package region

import (
	"context"
)

func newQuery() *Query {
	return &Query{
		locales: make(map[string]*store),
	}
}

type Query struct {
	locales map[string]*store
}

func (s *Query) setLocale(name string, dbfile string) error {
	m, err := loadFile(dbfile)
	if err != nil {
		return err
	}
	s.locales[name] = m
	return nil
}

// Take make code to region name
func (s *Query) Take(ctx context.Context, code string) string {
	db := s.locale(ctx)
	if db == nil {
		return ""
	}
	n, has := db.Get(code)
	if !has {
		return ""
	}
	return n.Name
}

// Query make region name to code
func (s *Query) Query(ctx context.Context, name string) string {
	db := s.locale(ctx)
	if db == nil {
		return ""
	}
	var hit string
	db.Scan(func(code string, node Node) bool {
		if node.Edge && node.Name == name {
			hit = code
			return false
		}
		return true
	})
	return hit
}

func (s *Query) Dump(ctx context.Context) []*Region {
	db := s.locale(ctx)
	if db == nil {
		return nil
	}
	return dumpRegion(db)
}
