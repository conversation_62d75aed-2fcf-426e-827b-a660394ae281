package gproto

import (
	"encoding/binary"
	"errors"
	"io"
)

var (
	ErrPacketLength = errors.New("packet length too short")
)

const (
	headerLength = 4 + 1
)

// Packet 是logic发送到网关的请求
type Packet struct {
	Length  uint32 // 总长度：4 + header + len(payload)
	Header  uint8  // 头部的长度（不包含头部4个字节+自己）
	Flags   uint32 // 头部内容：标记
	Payload []byte // 原始内容
}

func (p *Packet) Encode() []byte {
	length := 4 + 1 + len(p.Payload)
	var header uint8
	if p.Flags > 0 {
		length += 4
		header += 4
	}
	buff := make([]byte, length)
	binary.LittleEndian.PutUint32(buff[0:4], uint32(length))
	buff[4] = header
	if p.Flags > 0 {
		binary.LittleEndian.PutUint32(buff[5:9], p.Flags)
	}
	if len(p.Payload) > 0 {
		copy(buff[headerLength+header:], p.Payload)
	}
	return buff
}

func (p *Packet) WriteTo(bw io.Writer, pSize uint32) error {
	length := 4 + 1 + pSize
	var header uint8
	if p.Flags > 0 {
		length += 4
		header += 4
	}
	if err := binary.Write(bw, binary.LittleEndian, length); err != nil {
		return err
	}
	if err := binary.Write(bw, binary.LittleEndian, header); err != nil {
		return err
	}
	if p.Flags > 0 {
		if err := binary.Write(bw, binary.LittleEndian, p.Flags); err != nil {
			return err
		}
	}
	return nil
}

func Decode(data []byte) (*Packet, error) {
	if len(data) < headerLength {
		return nil, ErrPacketLength
	}
	p := &Packet{
		Length:  binary.LittleEndian.Uint32(data[:4]),
		Header:  data[4],
		Payload: data[headerLength+data[4]:],
	}
	if p.Header > 0 {
		p.Flags = binary.LittleEndian.Uint32(data[5:9])
	}
	return p, nil
}
