package gproto

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/valyala/bytebufferpool"
)

func TestPacket(t *testing.T) {
	t.Run("noFlag", func(t *testing.T) {
		p1 := &Packet{Payload: []byte("123")}
		bs := p1.Encode()
		t.Logf("encode packet: %+v", bs)
		assert.Equal(t, headerLength+3, len(bs))
		p2, err := Decode(bs)
		if assert.NoError(t, err) {
			t.Logf("decoded packet: %+v", p2)
			assert.Equal(t, p1.Payload, p2.Payload)
		}
	})
	t.Run("withFlag", func(t *testing.T) {
		p1 := &Packet{Payload: []byte("123"), Flags: FlagInRoom}
		bs := p1.Encode()
		t.Logf("encode packet: %+v", bs)
		assert.Equal(t, headerLength+3+4, len(bs))
		p2, err := Decode(bs)
		if assert.NoError(t, err) {
			t.Logf("decoded packet: %+v", p2)
			assert.Equal(t, p1.Payload, p2.Payload)
			assert.Equal(t, p1.Flags, p2.Flags)
		}
	})
}

func BenchmarkCodec(b *testing.B) {
	b.Run("noFlag", func(b *testing.B) {
		p := &Packet{Payload: []byte("123")}
		for range b.N {
			_, _ = Decode(p.Encode())
		}
		b.ReportAllocs()
	})
	b.Run("withFlag", func(b *testing.B) {
		p := &Packet{Payload: []byte("123"), Flags: FlagInRoom}
		for range b.N {
			_, _ = Decode(p.Encode())
		}
		b.ReportAllocs()
	})
}

func BenchmarkWrite(b *testing.B) {
	b.Run("noFlag", func(b *testing.B) {
		p := &Packet{Payload: []byte("123")}
		for range b.N {
			buf := bytebufferpool.Get()
			_ = p.WriteTo(buf, 3)
			bytebufferpool.Put(buf)
		}
		b.ReportAllocs()
	})
	b.Run("withFlag", func(b *testing.B) {
		p := &Packet{Payload: []byte("123"), Flags: FlagInRoom}
		for range b.N {
			buf := bytebufferpool.Get()
			_ = p.WriteTo(buf, 3)
			bytebufferpool.Put(buf)
		}
		b.ReportAllocs()
	})
}
