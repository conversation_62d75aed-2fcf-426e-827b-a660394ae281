package fx2

import (
	"go.uber.org/fx"
)

type appCloser struct {
	wait chan struct{}
}

func (c *appCloser) init() {
	c.wait = make(chan struct{})
}

func (c *appCloser) close() {
	close(c.wait)
}

func shutdowner() *appCloser {
	return &appCloser{}
}

func tryShutdown(ac *appCloser, c fx.Shutdowner) error {
	if ac.wait == nil {
		return c.Shutdown()
	}
	go func() {
		<-ac.wait
		_ = c.Shutdown()
	}()
	return nil
}
