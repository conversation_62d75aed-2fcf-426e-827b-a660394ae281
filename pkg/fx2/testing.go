package fx2

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest"
)

func Testing(t testing.TB, mod fx.Option, run any, needs ...any) {
	app := fx.New(mod,
		fx.Provide(testLogger(t), testConfig),
		fx.Provide(needs...),
		fx.Invoke(run, func(c fx.Shutdowner) error { return c.Shutdown() }),
		fx.WithLogger(fxevLogger),
	)
	if err := app.Err(); err != nil {
		t.Errorf("fx.New failed: %v", err)
		t.FailNow()
	}
	startCtx, cancel := context.WithTimeout(context.Background(), app.StartTimeout())
	defer cancel()
	if err := app.Start(startCtx); err != nil {
		t.<PERSON><PERSON>rf("application didn't start cleanly: %v", err)
		t.<PERSON>ail<PERSON>ow()
	}
}

func testLogger(t testing.TB) func() log.Vendor {
	return func() log.Vendor { return &testLogAPI{zaptest.NewLogger(t, zaptest.Level(zapcore.InfoLevel))} }
}

type testLogAPI struct {
	l *zap.Logger
}

func (t *testLogAPI) Scope(name string) *zap.Logger {
	return t.l.With(zap.String("scope", name))
}

func (t *testLogAPI) Logger() *zap.Logger {
	return t.l
}

func (t *testLogAPI) Stop() error {
	return nil
}

func testConfig() (*conf.Setting, error) {
	p, err := os.Getwd()
	if err != nil {
		return nil, err
	}
	ps := strings.Split(p, string(filepath.Separator))
	for i := len(ps) - 1; i >= 0; i-- {
		if ps[i] == "internal" || ps[i] == "pkg" {
			p = strings.Join(ps[:i], string(filepath.Separator))
			break
		}
	}
	return conf.Provide(p + "/configs/config.yaml")()
}
