package fx2

import (
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
)

func New(cmd *cobra.Command, args []string) *App {
	return &App{cmd: cmd, args: args}
}

type App struct {
	cmd   *cobra.Command
	args  []string
	mods  []fx.Option
	needs []any
	post  []any
}

func (s *App) With(p ...fx.Option) *App {
	s.mods = append(s.mods, p...)
	return s
}

func (s *App) Needs(p ...any) *App {
	s.needs = append(s.needs, p...)
	return s
}

func (s *App) Post(do any) *App {
	if err := checkDefer(do); err != nil {
		s.post = append(s.post, notDefer(err))
	} else {
		s.post = append(s.post, do)
	}
	return s
}

func (s *App) Run(cmd ...any) {
	cfg := s.cmd.Flag("config").Value.String()

	app := fx.New(append(s.mods,
		fx.Provide(conf.Provide(cfg)),
		fx.Provide(zapLogger),
		fx.Provide(shutdowner),
		fx.Provide(s.needs...),
		fx.Provide(s.post...),
		fx.Invoke(cmd...),
		fx.Invoke(runDefer(len(s.post) > 0)),
		fx.Invoke(tryShutdown),
		fx.WithLogger(fxevLogger),
	)...)

	app.Run()
}
