package fx2

import (
	"fmt"
	"io"
	"os"
	"strings"

	"go.uber.org/fx/fxevent"
)

func fxevLogger() fxevent.Logger {
	return &ConsoleLogger{
		W: os.<PERSON>,
		V: os.<PERSON>env("DEBUG") != "",
	}
}

type ConsoleLogger struct {
	W io.Writer
	V bool // verbose
}

func (l *ConsoleLogger) logf(msg string, args ...interface{}) {
	if !l.V && !strings.HasPrefix(strings.ToLower(msg), "error") {
		return
	}
	fmt.Fprintf(l.W, "[Fx] "+msg+"\n", args...)
}

// LogEvent logs the given event to the provided Zap logger.
func (l *ConsoleLogger) LogEvent(event fxevent.Event) {
	switch e := event.(type) {
	case *fxevent.OnStartExecuting:
		l.logf("HOOK OnStart\t\t%s executing (caller: %s)", e.<PERSON><PERSON><PERSON>, e.<PERSON>)
	case *fxevent.OnStartExecuted:
		if e.Err != nil {
			l.logf("HOOK OnStart\t\t%s called by %s failed in %s: %+v", e.FunctionName, e.CallerName, e.Runtime, e.Err)
		} else {
			l.logf("HOOK OnStart\t\t%s called by %s ran successfully in %s", e.FunctionName, e.CallerName, e.Runtime)
		}
	case *fxevent.OnStopExecuting:
		l.logf("HOOK OnStop\t\t%s executing (caller: %s)", e.FunctionName, e.CallerName)
	case *fxevent.OnStopExecuted:
		if e.Err != nil {
			l.logf("HOOK OnStop\t\t%s called by %s failed in %s: %+v", e.FunctionName, e.CallerName, e.Runtime, e.Err)
		} else {
			l.logf("HOOK OnStop\t\t%s called by %s ran successfully in %s", e.FunctionName, e.CallerName, e.Runtime)
		}
	case *fxevent.Supplied:
		if e.Err != nil {
			l.logf("ERROR\tFailed to supply %v: %+v", e.TypeName, e.Err)
		} else if e.ModuleName != "" {
			l.logf("SUPPLY\t%v from module %q", e.TypeName, e.ModuleName)
		} else {
			l.logf("SUPPLY\t%v", e.TypeName)
		}
	case *fxevent.Provided:
		var privateStr string
		if e.Private {
			privateStr = " (PRIVATE)"
		}
		for _, rtype := range e.OutputTypeNames {
			if e.ModuleName != "" {
				l.logf("PROVIDE%v\t%v <= %v from module %q", privateStr, rtype, e.ConstructorName, e.ModuleName)
			} else {
				l.logf("PROVIDE%v\t%v <= %v", privateStr, rtype, e.ConstructorName)
			}
		}
		if e.Err != nil {
			l.logf("Error after options were applied: %+v", e.Err)
		}
	case *fxevent.Replaced:
		for _, rtype := range e.OutputTypeNames {
			if e.ModuleName != "" {
				l.logf("REPLACE\t%v from module %q", rtype, e.ModuleName)
			} else {
				l.logf("REPLACE\t%v", rtype)
			}
		}
		if e.Err != nil {
			l.logf("ERROR\tFailed to replace: %+v", e.Err)
		}
	case *fxevent.Decorated:
		for _, rtype := range e.OutputTypeNames {
			if e.ModuleName != "" {
				l.logf("DECORATE\t%v <= %v from module %q", rtype, e.DecoratorName, e.ModuleName)
			} else {
				l.logf("DECORATE\t%v <= %v", rtype, e.DecoratorName)
			}
		}
		if e.Err != nil {
			l.logf("Error after options were applied: %+v", e.Err)
		}
	case *fxevent.Run:
		var moduleStr string
		if e.ModuleName != "" {
			moduleStr = fmt.Sprintf(" from module %q", e.ModuleName)
		}
		l.logf("RUN\t%v: %v%v", e.Kind, e.Name, moduleStr)
		if e.Err != nil {
			l.logf("Error returned: %+v", e.Err)
		}

	case *fxevent.Invoking:
		if e.ModuleName != "" {
			l.logf("INVOKE\t\t%s from module %q", e.FunctionName, e.ModuleName)
		} else {
			l.logf("INVOKE\t\t%s", e.FunctionName)
		}
	case *fxevent.Invoked:
		if e.Err != nil {
			l.logf("ERROR\t\tfx.Invoke(%v) called from:\n%+vFailed: %+v", e.FunctionName, e.Trace, e.Err)
		}
	case *fxevent.Stopping:
		l.logf("%v", strings.ToUpper(e.Signal.String()))
	case *fxevent.Stopped:
		if e.Err != nil {
			l.logf("ERROR\t\tFailed to stop cleanly: %+v", e.Err)
		}
	case *fxevent.RollingBack:
		l.logf("ERROR\t\tStart failed, rolling back: %+v", e.StartErr)
	case *fxevent.RolledBack:
		if e.Err != nil {
			l.logf("ERROR\t\tCouldn't roll back cleanly: %+v", e.Err)
		}
	case *fxevent.Started:
		if e.Err != nil {
			l.logf("ERROR\t\tFailed to start: %+v", e.Err)
		} else {
			l.logf("RUNNING")
		}
	case *fxevent.LoggerInitialized:
		if e.Err != nil {
			l.logf("ERROR\t\tFailed to initialize custom logger: %+v", e.Err)
		} else {
			l.logf("LOGGER\tInitialized custom logger from %v", e.ConstructorName)
		}
	}
}
