package fx2

import (
	"context"
	"errors"
	"reflect"

	"go.uber.org/fx"
	"go.uber.org/fx/fxevent"
)

type Defer func(context.Context) error

func checkDefer(f any) error {
	ft := reflect.TypeOf(f)
	if ft.Kind() != reflect.Func {
		return errors.New("not a func")
	}
	o0 := ft.Out(0)
	if _, is := reflect.New(o0).Elem().Interface().(Defer); !is {
		return errors.New("not a defer")
	}
	return nil
}

func notDefer(err error) func() (Defer, error) {
	return func() (Defer, error) {
		return nil, err
	}
}

type deferIn struct {
	fx.In
	Func Defer `optional:"true"`
}

func runDefer(must bool) func(lc fx.Lifecycle, ac *appCloser, in deferIn, log fxevent.Logger) error {
	return func(lc fx.Lifecycle, ac *appCloser, in deferIn, log fxevent.Logger) error {
		if in.Func == nil {
			if must {
				return errors.New("missing defer")
			}
			return nil
		}
		ac.init()
		ctx, stop := context.WithCancel(context.Background())
		lc.Append(fx.Hook{
			OnStart: func(context.Context) error {
				go func() {
					defer ac.close()
					if err := in.Func(ctx); err != nil {
						log.LogEvent(&fxevent.Run{
							Name: "POST",
							Err:  err,
						})
					}
				}()
				return nil
			},
			OnStop: func(context.Context) error {
				stop()
				<-ac.wait
				return nil
			},
		})
		return nil
	}
}
