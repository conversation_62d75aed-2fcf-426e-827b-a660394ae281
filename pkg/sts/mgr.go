package sts

import (
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	sts "github.com/alibabacloud-go/sts-20150401/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
)

const (
	defaultConf = "default"
)

type Mgr struct {
	cfg       conf.STS
	sto       sto.Instance
	stsClient cc.Cache[string, *stsClient]
	stsCache  cc.Cache[string, *AssumeRoleResponse]
}

func newManager(cfg conf.STS, sto sto.Instance) *Mgr {
	m := &Mgr{cfg: cfg, sto: sto}
	m.stsClient = cc.New[string, *stsClient](0, cc.Simple, cc.LoaderFunc(m.initClient))
	m.stsCache = cc.New[string, *AssumeRoleResponse](2000, cc.LRU, cc.ExportStats("sts.cache"))
	return m
}

type stsClient struct {
	conf   Conf
	client *sts.Client
}

func (m *Mgr) initClient(module string) (*stsClient, error) {
	cfg := m.Conf(module)
	cli, err := sts.NewClient(&openapi.Config{
		AccessKeyId:     tea.String(cfg.AccessKeyId),
		AccessKeySecret: tea.String(cfg.AccessKeySecret),
		Endpoint:        tea.String(cfg.Endpoint),
		ConnectTimeout:  tea.Int(10),
	})
	if err != nil {
		return nil, err
	}
	return &stsClient{conf: cfg, client: cli}, nil
}

type Conf struct {
	conf.AliSTS
	OSS sto.Conf
}

func (m *Mgr) Conf(key string) Conf {
	var def = m.cfg[defaultConf]

	c, has := m.cfg[key]
	if !has {
		c = def
	}

	if c.Endpoint == "" {
		c.Endpoint = def.Endpoint
	}

	if c.AccessKeyId == "" {
		c.AccessKeyId = def.AccessKeyId
	}

	if c.AccessKeySecret == "" {
		c.AccessKeySecret = def.AccessKeySecret
	}

	if c.RoleArn == "" {
		c.RoleArn = def.RoleArn
	}

	if c.Storage == "" {
		if def.Storage != "" {
			c.Storage = def.Storage
		} else {
			c.Storage = key
		}
	}

	return Conf{AliSTS: c, OSS: m.sto.Conf(c.Storage)}
}
