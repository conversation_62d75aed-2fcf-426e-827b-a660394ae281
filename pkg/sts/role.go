package sts

import (
	"strings"
	"time"

	sts "github.com/alibabacloud-go/sts-20150401/v2/client"
	"github.com/alibabacloud-go/tea/tea"
)

type assumeOpts struct {
	prefix string
}

func newAssumeOpts(opts []AssumeOpt) *assumeOpts {
	o := new(assumeOpts)
	for _, opt := range opts {
		opt(o)
	}
	return o
}

type AssumeOpt func(*assumeOpts)

func WithPrefix(prefix string) AssumeOpt {
	return func(o *assumeOpts) {
		o.prefix = strings.TrimPrefix(prefix, "/")
	}
}

func (m *Mgr) AssumeRole(module, userId string, opts ...AssumeOpt) (*AssumeRoleResponse, error) {
	st, err := m.stsClient.Get(module)
	if err != nil {
		return nil, err
	}

	opt := newAssumeOpts(opts)

	ccKey := module + userId + opt.prefix
	if v, err := m.stsCache.Get(ccKey); err == nil {
		return v, nil
	}

	req := &sts.AssumeRoleRequest{
		RoleArn:         tea.String(st.conf.RoleArn),
		RoleSessionName: tea.String(userId),
		DurationSeconds: tea.Int64(3600),
	}

	if opt.prefix != "" {
		req.Policy = tea.String(makePrefixPolicy(st.conf.OSS, opt.prefix))
	}

	resp, err := st.client.AssumeRole(req)
	if err != nil {
		return nil, err
	}

	expiration, err := time.Parse(time.RFC3339, tea.StringValue(resp.Body.Credentials.Expiration))
	if err != nil {
		return nil, err
	}

	ret := &AssumeRoleResponse{
		AccessKeyId:     *resp.Body.Credentials.AccessKeyId,
		AccessKeySecret: *resp.Body.Credentials.AccessKeySecret,
		SecurityToken:   *resp.Body.Credentials.SecurityToken,
		Expiration:      expiration,
		Storage:         st.conf.OSS,
		Prefix:          opt.prefix,
	}

	ttl := expiration.Sub(time.Now()) - time.Minute
	m.stsCache.SetWithExpire(ccKey, ret, ttl)

	return ret, nil
}
