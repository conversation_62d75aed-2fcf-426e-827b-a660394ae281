package sts

import (
	"bytes"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"testing"
)

func Test_mgr_initModuleClient(t *testing.T) {
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	m := newManager(desc.STS, sto.Provide(desc))
	e, err := m.AssumeRole("moment", "test", WithPrefix("/moment/test"))
	if err != nil {
		t.Fatal(err)
	}
	cli, err := oss.New(e.Storage.EndpointURL(), e.AccessKeyId, e.AccessKeySecret, oss.SecurityToken(e.SecurityToken))
	if err != nil {
		t.Fatal(err)
	}
	buk, err := cli.Bucket(e.Storage.Bucket)
	if err != nil {
		t.Fatal(err)
	}
	if err := buk.PutObject("moment/test/1.txt", bytes.NewBuffer([]byte("hello world"))); err != nil {
		t.Fatal(err)
	}
	t.Logf("wrote object to oss")
}
