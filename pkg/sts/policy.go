package sts

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"strings"
)

var prefixPolicy = `{
    "Version": "1",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "oss:PutObject"
            ],
            "Resource": [
                "acs:oss:*:*:$bucket$/$prefix$/*"
            ]
        }
    ]
}`

func makePrefixPolicy(conf sto.Conf, prefix string) string {
	out := prefixPolicy
	out = strings.Replace(out, "$bucket$", conf.Bucket, -1)
	out = strings.Replace(out, "$prefix$", prefix, -1)
	return out
}
