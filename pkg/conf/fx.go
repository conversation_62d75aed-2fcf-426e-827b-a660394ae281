package conf

import (
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
)

func Provide(path string) func() (*Setting, error) {
	return func() (*Setting, error) {
		if path == "" {
			viper.SetConfigName("config")     // name of config file (without extension)
			viper.SetConfigType("yaml")       // REQUIRED if the config file does not have the extension in the name
			viper.AddConfigPath("./configs/") // optionally look for config in the working directory
		} else {
			viper.SetConfigFile(path)
		}

		err := viper.ReadInConfig() // Find and read the config file
		if err != nil {             // Handle errors reading the config file
			return nil, err
		}

		var setting Setting
		if err := viper.Unmarshal(&setting, func(dc *mapstructure.DecoderConfig) {
			dc.TagName = "yaml"
		}); err != nil {
			return nil, err
		}

		return &setting, setting.Prepare()
	}
}
