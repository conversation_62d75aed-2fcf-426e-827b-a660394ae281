package conf

import "time"

type KVal map[string]string

type Setting struct {
	Debug     bool      `yaml:"debug"`
	PProf     bool      `yaml:"pprof"`
	Admin     Admin     `yaml:"admin" envPrefix:"ADMIN_"`
	OTEL      OTEL      `yaml:"otel"`
	RPC       RPC       `yaml:"rpc"`
	Auth      Auth      `yaml:"auth"`
	ApiServer ApiServer `yaml:"api_server"`
	Gateway   Gateway   `yaml:"gateway" envPrefix:"GATEWAY_"`
	Worker    Worker    `yaml:"worker"`
	Redis     Redis     `yaml:"redis"`
	Mongo     Mongo     `yaml:"mongo"`
	DB        DB        `yaml:"db"`
	MQ        MQ        `yaml:"mq"`
	DQ        DQ        `yaml:"dq"`
	ES        ES        `yaml:"es"`
	OSS       OSS       `yaml:"oss"`
	STS       STS       `yaml:"sts"`
	SMS       SMS       `yaml:"sms"`
	Log       Log       `yaml:"log" envPrefix:"LOG_"`
	GeoIP     GeoIP     `yaml:"geoip"`
	Region    Region    `yaml:"region"`
	Locale    Locale    `yaml:"locale"`
	Avatar    Avatar    `yaml:"avatar"`
	// 3rd platform
	Dun        Dun        `yaml:"dun"`
	Connect    Connect    `yaml:"connect"`
	Payermax   Payermax   `yaml:"payermax"`
	Ganopay    Ganopay    `yaml:"ganopay"`
	Binance    Binance    `yaml:"binance"`
	Paypal     Paypal     `yaml:"paypal"`
	Usolve     Usolve     `yaml:"usolve"`
	Volcengine Volcengine `yaml:"volcengine"`
	Shortener  Shortener  `yaml:"shortener"`
	Share      Share      `yaml:"share"`
	Game       Game       `yaml:"game"` // 三方游戏配置
	Review     Review     `yaml:"review"`
	Marketing  Marketing  `yaml:"marketing"` // 运营数据，用户留存等
}

type ApiServer struct {
	Addr string `yaml:"addr"` // 服务监听地址
	Host string `yaml:"host"` // 外部访问域名
}

type Gateway struct {
	Public bool   `yaml:"public" env:"PUBLIC"`
	HTTP   string `yaml:"http" env:"HTTP"`
	RPC    string `yaml:"rpc" env:"RPC"`
}

type Worker struct {
	RPC string `yaml:"rpc"`
}

type Redis struct {
	DSN     string `yaml:"dsn"`
	Cluster KVal   `yaml:"cluster"`
}

type Mongo struct {
	DSN            string        `yaml:"dsn"`
	DB             string        `yaml:"db"`
	PoolSize       uint64        `yaml:"pool_size"`
	ConnectTimeout time.Duration `yaml:"connect_timeout"`
	NotSupportTxn  bool          `yaml:"not_support_txn"`
}

type Log struct {
	Level   string `yaml:"level"`
	File    string `yaml:"file" env:"FILE"`
	Size    int    `yaml:"size"`
	Age     int    `yaml:"age"`
	Backup  int    `yaml:"backup"`
	Buffer  bool   `yaml:"buffer"`
	Console bool   `yaml:"console"`
}

type Auth struct {
	JWTKey string `yaml:"jwt_key"`
}

type GeoIP struct {
	Path string `yaml:"path"`
}

type Region struct {
	Locale   map[string]string `yaml:"locale"`
	Fallback string            `yaml:"fallback"`
}

type Locale struct {
	Texts string `yaml:"texts"`
}

type DB struct {
	DSN           string        `yaml:"dsn"`
	Cluster       KVal          `yaml:"cluster"`
	MaxIdle       int           `yaml:"max_idle"`
	MaxOpen       int           `yaml:"max_open"`
	MaxLife       time.Duration `yaml:"max_life"`
	PrepareStmt   bool          `yaml:"prepare_stmt"`
	SkipDefaultTX bool          `yaml:"skip_default_tx"`
	NoAutoMigrate bool          `yaml:"no_auto_migrate"`
}

type MQ struct {
	DSN string `yaml:"dsn"`
}

type DQ struct {
	DSN string `yaml:"dsn"`
}

type Mock struct {
	Enabled bool `yaml:"enabled"`
}

type Admin struct {
	Listen  string `yaml:"listen" env:"LISTEN"`
	Rolling string `yaml:"rolling"`
}

type RPC struct {
	Registry string `yaml:"registry"`
}

type OTEL struct {
	Tracing string `yaml:"tracing"`
	Metrics string `yaml:"metrics"`
}

type ES struct {
	Endpoint    string `yaml:"endpoint"`
	NoAutoIndex bool   `yaml:"no_auto_index"`
}

type OSS map[string]AliOSS

type AliOSS struct {
	Endpoint        string `yaml:"endpoint"`
	Accelerate      string `yaml:"accelerate"`
	AccessKeyId     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
	Bucket          string `yaml:"bucket"`
	Domain          string `yaml:"domain"`
	Scheme          string `yaml:"scheme"`
	Region          string `yaml:"region"`
}

type STS map[string]AliSTS

type AliSTS struct {
	Endpoint        string `yaml:"endpoint"`
	AccessKeyId     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
	RoleArn         string `yaml:"role_arn"`
	Storage         string `yaml:"storage"` // OSS key
}

type SMS struct {
	Vendor map[int]string `yaml:"vendor"`
}

type Connect map[string]OAuth2

type OAuth2 struct {
	ClientID     string `yaml:"client_id"`
	ClientSecret string `yaml:"client_secret"`
}

type Payermax struct {
	Endpoint    string `yaml:"endpoint"`
	AppId       string `yaml:"app_id"`
	MerchantNo  string `yaml:"merchant_no"`
	CallbackUrl string `yaml:"callback_url"`
	PrivateKey  string `yaml:"private_key"`
	PublicKey   string `yaml:"public_key"`
}

type Ganopay struct {
	Endpoint    string `yaml:"endpoint"`
	MerchantId  string `yaml:"merchant_id"`
	SecretKey   string `yaml:"secret_key"`
	CallbackUrl string `yaml:"callback_url"`
	PrivateKey  string `yaml:"private_key"`
	PublicKey   string `yaml:"public_key"`
}

type Binance struct {
	Endpoint    string `yaml:"endpoint"`
	Proxy       string `yaml:"proxy"`
	ApiKey      string `yaml:"api_key"`
	SecretKey   string `yaml:"secret_key"`
	CallbackUrl string `yaml:"callback_url"`
}

type Paypal struct {
	Endpoint    string `yaml:"endpoint"`
	ClientId    string `yaml:"client_id"`
	SecretKey   string `yaml:"secret_key"`
	WebhookId   string `yaml:"webhook_id"`
	CallbackUrl string `yaml:"callback_url"`
}

type Usolve struct {
	Endpoint    string `yaml:"endpoint"`
	MerchantId  string `yaml:"merchant_id"`
	SecretKey   string `yaml:"secret_key"`
	CallbackUrl string `yaml:"callback_url"`
}

type Avatar struct {
	Store string      `yaml:"store"`
	Style AvatarStyle `yaml:"style"`
}

type AvatarStyle struct {
	Thumb string `yaml:"thumb"`
	Large string `yaml:"large"`
}

type Dun struct {
	SecretId   string `yaml:"secretId"`
	SecretKey  string `yaml:"secretKey"`
	TextBizId  string `yaml:"textBizId"`
	ImageBizId string `yaml:"imageBizId"`
	LpBizId    string `yaml:"lpBizId"`   // 活体检测业务ID
	FaceBizId  string `yaml:"faceBizId"` // 人脸检测业务ID
}

// 火山引擎
type Volcengine struct {
	AccessKeyId     string        `yaml:"access_key_id"`
	SecretAccessKey string        `yaml:"secret_access_key"`
	RTC             VolcengineRTC `yaml:"rtc"`
	IM              VolcengineIM  `yaml:"im"`
	Translate       Translate     `yaml:"translate"`
}

type VolcengineRTC struct {
	AppId     string `yaml:"app_id"`
	AppKey    string `yaml:"app_key"`
	SecretKey string `yaml:"secret_key"` // 回调签名验证密钥
}

type VolcengineIM struct {
	AppId     int32  `yaml:"app_id"`
	AppKey    string `yaml:"app_key"`
	Region    string `yaml:"region"`     // 国内服务 region 设为 cn-north-1，海外服务 region 设为 ap-southeast-1
	SecretKey string `yaml:"secret_key"` // 回调签名验证密钥
}

type Translate struct {
	Detector   Detector   `yaml:"detector"`
	Translator Translator `yaml:"translator"`
}

type Detector struct {
	Region string `yaml:"region"` // 国内服务 region 设为 cn-north-1，海外服务 region 设为 ap-southeast-1
}

type Translator struct {
	Region string `yaml:"region"` // 国内服务 region 设为 cn-north-1，海外服务 region 设为 ap-southeast-1
}

type Shortener struct {
	Host string `yaml:"host"`
}

type Share struct {
	Host string `yaml:"host"`
}

type Game struct {
	BaiShun  BaiShun `yaml:"baishun"`
	BaiShun2 BaiShun `yaml:"baishun2"`
	SS       SSGame  `yaml:"ss"`
}

type BaiShun struct {
	Host     string `yaml:"host"`
	Channel  string `yaml:"channel"`
	AppId    int    `yaml:"app_id"`
	AppKey   string `yaml:"app_key"`
	GSP      int    `yaml:"gsp"`
	TokenKey string `yaml:"token_key"`
}

type Review struct {
	Endpoint        string `yaml:"endpoint"`
	AccessKeyId     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
}

type Marketing struct {
	Mongo Mongo `yaml:"mongo"`
}

type SSGame struct {
	Host   string `yaml:"host"`
	AppId  string `yaml:"app_id"`
	AppKey string `yaml:"app_key"`
}
