package db

import (
	"fmt"
	"reflect"

	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsoncodec"
	"go.mongodb.org/mongo-driver/bson/bsonrw"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func init() {
	addTypeCodec(typeCodec{t: reflect.TypeOf(decimal.Decimal{}), e: &bigDec{}, d: &bigDec{}})
}

type bigDec struct{}

func (dc *bigDec) EncodeValue(ctx bsoncodec.EncodeContext, w bsonrw.ValueWriter, value reflect.Value) error {
	dec, ok := value.Interface().(decimal.Decimal)
	if !ok {
		return fmt.Errorf("value %v to encode is not of type decimal.Decimal", value)
	}

	pd, err := primitive.ParseDecimal128(dec.String())
	if err != nil {
		return fmt.Errorf("error converting decimal.Decimal %v to primitive.Decimal128: %v", dec, err)
	}

	return w.WriteDecimal128(pd)
}

func (dc *bigDec) DecodeValue(ctx bsoncodec.DecodeContext, r bsonrw.ValueReader, value reflect.Value) error {
	pd, err := r.ReadDecimal128()
	if err != nil {
		switch r.Type() {
		case bson.TypeInt32:
			v, err := r.ReadInt32()
			if err != nil {
				return fmt.Errorf("error reading decimal as int32: %v", err)
			}
			value.Set(reflect.ValueOf(decimal.NewFromInt32(v)))
			return nil
		}
		return fmt.Errorf("error reading primitive.Decimal128 from ValueReader: %v", err)
	}

	dec, err := decimal.NewFromString(pd.String())
	if err != nil {
		return fmt.Errorf("error converting primitive.Decimal128 %v to decimal.Decimal: %v", pd, err)
	}

	value.Set(reflect.ValueOf(dec))
	return nil
}
