package db

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cron"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
)

type Indexer struct {
	Name string
	Uniq *bool
	Keys bson.D
}

func FixedCollection(name string) func(time.Time) string {
	return func(time.Time) string {
		return name
	}
}

func SyncSchema(ctx context.Context, logger *zap.Logger, mc *MongoClient, collectionF func(time.Time) string, nMonth int, indexers ...Indexer) error {
	names, err := mc.DB().ListCollectionNames(ctx, bson.D{}, &options.ListCollectionsOptions{
		NameOnly: lo.<PERSON>tr(true),
	})
	if err != nil {
		if !errors.Is(err, mongo.ErrNilDocument) {
			return fmt.Errorf("list collection names error: %w", err)
		} else {
			logger.Info("list collection names error", zap.Error(err))
		}
	}

	var (
		now = time.Now()
		ee  []error
	)
	for i := 0; i < nMonth; i++ {
		ts := now
		if nMonth > 1 {
			ts = time.Date(now.Year(), now.Month()+time.Month(i), 15, 0, 0, 0, 0, now.Location())
		}

		name := collectionF(ts)

		logger.Debug("sync schema", zap.String("name", name))

		if !slices.Contains(names, name) {
			if err := mc.DB().CreateCollection(context.TODO(), name); err != nil {
				ee = append(ee, fmt.Errorf("create issue gift collection error: %w", err))
				continue
			} else {
				logger.Info("created collection", zap.String("name", name))
			}
		}

		if len(indexers) > 0 {
			exist, err := mc.DB().Collection(name).Indexes().ListSpecifications(ctx, &options.ListIndexesOptions{})
			if err != nil {
				ee = append(ee, fmt.Errorf("list issue gift collection indexes error: %w", err))
				continue
			}

			for _, indexer := range indexers {
				if !slices.ContainsFunc(exist, func(specification *mongo.IndexSpecification) bool {
					return specification.Name == indexer.Name
				}) {
					if _, err := mc.DB().Collection(name).Indexes().CreateOne(context.TODO(), mongo.IndexModel{
						Keys: indexer.Keys,
						Options: &options.IndexOptions{
							Name:   lo.ToPtr(indexer.Name),
							Unique: indexer.Uniq,
						},
					}); err != nil {
						ee = append(ee, fmt.Errorf("create collection index error: %w", err))
						continue
					} else {
						logger.Info("created collection index", zap.String("collection", name), zap.String("index", indexer.Name))
					}
				}
			}
		}
	}

	if len(ee) > 0 {
		return fmt.Errorf("create collection error: %w", errors.Join(ee...))
	}

	return nil
}

type collectionFn func(time.Time) string

type syncTask struct {
	named    collectionFn
	nMonth   int
	indexers []Indexer
}

func (c *MongoClient) SyncSchema(named any, nMonth int, indexers ...Indexer) {
	var cf collectionFn
	switch v := named.(type) {
	case string:
		cf = FixedCollection(v)
	case func(time.Time) string:
		cf = v
	default:
		panic(fmt.Sprintf("unsupported collection type: %T", named))
	}
	c.syncTasks = append(c.syncTasks, syncTask{
		named:    cf,
		nMonth:   nMonth,
		indexers: indexers,
	})
}

func (c *MongoClient) syncSchemas(ctx context.Context) error {
	var errs []error
	for _, task := range c.syncTasks {
		if err := SyncSchema(ctx, c.logger, c, task.named, task.nMonth, task.indexers...); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.Join(errs...)
}

func (c *MongoClient) StartSync(ctx context.Context, sch *cron.Scheduler) error {
	syncer := sch.Exclusive("mongodb.schema.sync", c.syncSchemas)
	if _, err := sch.CronWithSeconds("0 0 4 1/1 * ?").Do(syncer); err != nil {
		return err
	}
	return c.syncSchemas(ctx)
}
