package db

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func Timestamp(id primitive.ObjectID) time.Time {
	// nolint
	return id.Timestamp().Local()
}

func prevMonthOf(at time.Time) time.Time {
	y, m, _ := at.Date()
	at = time.Date(y, m, 1, 0, 0, 0, 0, at.Location())
	return at.Add(-time.Nanosecond)
}

func MonthTravel[T any](max int, limit int, at time.Time, fn func(at time.Time, limit int64) ([]T, error)) ([]T, error) {
	rr, err := fn(at, int64(limit))
	if err != nil {
		return nil, err
	}
	if len(rr) < limit && max > 0 {
		nr, err := MonthTravel[T](max-1, limit-len(rr), prevMonthOf(at), fn)
		if err != nil {
			return nil, err
		}
		rr = append(rr, nr...)
	}
	return rr, nil
}

func DecodeAll[T any](ctx context.Context) func(cursor *mongo.Cursor, err error) ([]T, error) {
	return func(cursor *mongo.Cursor, err error) ([]T, error) {
		if err != nil {
			return nil, err
		}
		var list []T
		if err := cursor.All(ctx, &list); err != nil {
			return nil, err
		}
		return list, nil
	}
}

func DecodeOne[T any](ctx context.Context) func(cursor *mongo.Cursor, err error) (T, error) {
	return func(cursor *mongo.Cursor, err error) (T, error) {
		var v T
		if err != nil {
			return v, err
		}
		if !cursor.Next(ctx) {
			if err := cursor.Err(); err != nil {
				return v, err
			}
			return v, mongo.ErrNoDocuments
		}
		if err := cursor.Decode(&v); err != nil {
			return v, err
		}
		return v, nil
	}
}
