package db

import (
	"reflect"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsoncodec"
)

type typeCodec struct {
	t reflect.Type
	e bsoncodec.ValueEncoder
	d bsoncodec.ValueDecoder
}

var mongoTypes []typeCodec

func addTypeCodec(codec typeCodec) {
	mongoTypes = append(mongoTypes, codec)
}

func getTypeRegistry() *bsoncodec.Registry {
	r := bson.NewRegistry()
	for _, v := range mongoTypes {
		r.RegisterTypeEncoder(v.t, v.e)
		r.RegisterTypeDecoder(v.t, v.d)
	}
	return r
}
