package db

import (
	"context"
	"errors"
	"sync/atomic"
	"testing"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"github.com/stretchr/testify/assert"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/fx"
)

var testMGO = fx.Module("mongodb", fx.Provide(
	redi.Provide, unq.Provide, cron.Provide, ProvideMongo,
))

type testModel struct {
	Id     primitive.ObjectID `bson:"_id"`
	UserId string             `bson:"userId"`
}

var testIndex = []Indexer{
	{
		Name: "userId",
		Uniq: lo.ToPtr(true),
		Keys: bson.D{{Key: "userId", Value: 1}},
	},
}

func TestErrorCheck(t *testing.T) {
	fx2.Testing(t, testMGO, func(db *MongoClient) error {
		col := "test.case1"
		db.SyncSchema(FixedCollection(col), 1, testIndex...)
		if err := db.syncSchemas(context.TODO()); err != nil {
			return err
		}
		var ctx = context.TODO()
		{
			id := primitive.NewObjectID()
			_, _ = db.Collection(col).InsertOne(ctx, &testModel{Id: id, UserId: uuid.NewString()})
			_, err := db.Collection(col).InsertOne(ctx, &testModel{Id: id, UserId: uuid.NewString()})
			assert.True(t, IsDuplicate(err))
		}
		{
			_, _ = db.Collection(col).InsertOne(ctx, &testModel{Id: primitive.NewObjectID(), UserId: "123"})
			_, err := db.Collection(col).InsertOne(ctx, &testModel{Id: primitive.NewObjectID(), UserId: "123"})
			assert.True(t, IsDuplicate(err))
		}
		{
			id := primitive.NewObjectID()
			_, _ = db.Collection(col).InsertOne(ctx, &testModel{Id: id, UserId: uuid.NewString()})
			var wg conc.WaitGroup
			var hit int32
			for i := 0; i < 10; i++ {
				wg.Go(func() {
					if err := db.TryTxn(ctx, func(ctx context.Context) error {
						_, err := db.Collection(col).UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{"userId": uuid.NewString()}})
						return errors.Join(err)
					}); err != nil {
						if IsWriteConflict(err) {
							atomic.AddInt32(&hit, 1)
						}
					}
				})
			}
			wg.Wait()
			assert.Greater(t, hit, int32(0))
		}
		return nil
	})
}
