package db

import (
	"errors"
	"strings"

	"github.com/go-sql-driver/mysql"
	"go.mongodb.org/mongo-driver/mongo"
)

func isMongoError(err error, code int) bool {
	if se := mongo.ServerError(nil); errors.As(err, &se) {
		return se.HasErrorCode(code)
	}
	return false
}

func isMysqlError(err error, code uint16, key []string) bool {
	var mysqlErr *mysql.MySQLError
	if !errors.As(err, &mysqlErr) {
		return false
	}

	if mysqlErr.Number != code {
		return false
	}

	if len(key) > 0 {
		word := key[0]
		if !strings.Contains(mysqlErr.Message, word) {
			return false
		}
	}

	return true
}
