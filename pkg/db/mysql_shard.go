package db

import (
	"context"
	"errors"
	"reflect"
	"time"

	"gitlab.sskjz.com/go/cron"
	"go.uber.org/zap"
	"gorm.io/gorm/schema"
)

type TimedTable interface {
	schema.Tabler
	Timed(set time.Time)
}

func (c *Client) SyncTable(table TimedTable) {
	c.sharding = append(c.sharding, table)
}

func (c *Client) syncTable(ctx context.Context, table TimedTable, at time.Time) error {
	table = cloneOf(table)
	table.Timed(at)
	named := table.TableName()
	c.logger.Debug("sync table", zap.String("name", named))
	if c.Migrator().HasTable(named) {
		if !c.noAutoMigrate {
			return c.Table(named).Migrator().AutoMigrate(table)
		}
		return nil
	}
	if err := c.Table(named).Migrator().CreateTable(table); err != nil {
		return err
	}
	c.logger.Info("created table", zap.String("name", named))
	return nil
}

func (c *Client) syncTables(ctx context.Context) error {
	now := time.Now()
	errs := make([]error, 0, len(c.sharding)*2)
	for _, table := range c.sharding {
		errs = append(errs, c.syncTable(ctx, table, now))
		if nm := nextMonthOf(now); nm.Sub(now) < time.Hour*24*7 {
			errs = append(errs, c.syncTable(ctx, table, nm))
		}
	}
	return errors.Join(errs...)
}

func (c *Client) StartSync(ctx context.Context, sch *cron.Scheduler) error {
	syncer := sch.Exclusive("mysql.table.sync", c.syncTables)
	if _, err := sch.CronWithSeconds("0 0 4 1/1 * ?").Do(syncer); err != nil {
		return err
	}
	return c.syncTables(ctx)
}

func nextMonthOf(abs time.Time) time.Time {
	y, m, _ := abs.Date()
	begin := time.Date(y, m, 1, 0, 0, 0, 0, abs.Location())
	return begin.AddDate(0, 1, 0)
}

func cloneOf(v TimedTable) TimedTable {
	t := reflect.TypeOf(v)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	c := reflect.New(t).Interface()
	return c.(TimedTable)
}
