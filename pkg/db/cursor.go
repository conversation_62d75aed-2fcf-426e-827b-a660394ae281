package db

import (
	"bytes"
	"encoding/base64"
	"encoding/gob"
	"fmt"
)

func init() {
	gob.Register(&Cursor[int]{})
	gob.Register(&Cursor[int64]{})
}

type cursorType interface {
	~int | ~int64
}

type Cursor[T cursorType] struct {
	Offset T
	Limit  T
}

func ParseCursor[T cursorType](in string) (*Cursor[T], error) {
	if in == "" {
		return &Cursor[T]{0, 20}, nil
	}

	// decode by base64
	decoded, err := base64.StdEncoding.DecodeString(in)
	if err != nil {
		return nil, fmt.Errorf("parse history cursor: %w", err)
	}

	// decode by gob
	var out Cursor[T]
	if err := gob.NewDecoder(bytes.NewReader(decoded)).Decode(&out); err != nil {
		return nil, fmt.Errorf("parse history cursor: %w", err)
	}

	return &out, nil
}

func (h *Cursor[T]) Skip(n T) *Cursor[T] {
	h.Offset += n
	return h
}

func (h *Cursor[T]) Encode() (string, error) {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(h); err != nil {
		return "", fmt.Errorf("encode history cursor: %w", err)
	}

	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}

func (h *Cursor[T]) MustEncode() string {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(h); err != nil {
		panic(fmt.Errorf("encode history cursor: %w", err))
	}

	return base64.StdEncoding.EncodeToString(buf.Bytes())
}
