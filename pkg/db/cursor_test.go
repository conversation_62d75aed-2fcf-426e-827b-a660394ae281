package db

import (
	"bytes"
	"encoding/base64"
	"encoding/gob"
	"testing"
)

func TestHisCursor_Encode_Parse(t *testing.T) {
	h := &Cursor[int64]{
		Offset: 101,
		Limit:  20,
	}

	encoded, err := h.Encode()
	if err != nil {
		t.Fatalf("Failed to encode history cursor: %v", err)
	}

	decoded, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		t.Fatalf("Failed to decode encoded string: %v", err)
	}

	var decodedCursor Cursor[int64]
	err = gob.NewDecoder(bytes.NewReader(decoded)).Decode(&decodedCursor)
	if err != nil {
		t.Fatalf("Failed to decode history cursor: %v", err)
	}

	if decodedCursor.Offset != h.Offset || decodedCursor.Limit != h.Limit {
		t.Fatalf("Decoded cursor is not equal to original cursor: %v", decodedCursor)
	}

	t.Logf("Encoded cursor: %s", encoded)

	{
		cur, err := ParseCursor[int64](encoded)
		if err != nil {
			t.Fatalf("Failed to parse history cursor: %v", err)
		}

		if cur.Offset != h.Offset || cur.Limit != h.Limit {
			t.Fatalf("Parsed cursor is not equal to original cursor: %v", cur)
		}
	}
}
