package db

import (
	"context"
	"time"

	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MongoClient struct {
	rc           *mongo.Client
	db           string
	txnSupported bool
	syncTasks    []syncTask
	logger       *zap.Logger
}

func (c *MongoClient) DB() *mongo.Database {
	return c.rc.Database(c.db)
}

func (c *MongoClient) Collection(name string) *mongo.Collection {
	return c.DB().Collection(name)
}

func (c *MongoClient) TxnSupported() bool {
	return c.txnSupported
}

func (c *MongoClient) NoTxn(ctx context.Context, cb func(ctx context.Context) error) error {
	return cb(mongo.NewSessionContext(ctx, nil))
}

func (c *MongoClient) TryTxn(ctx context.Context, cb func(ctx context.Context) error, options ...Option) error {
	if !c.txnSupported {
		return cb(ctx)
	}

	sess := mongo.SessionFromContext(ctx)
	if sess != nil {
		return cb(ctx)
	}

	return c.NewTxn(ctx, cb, options...)
}

func (c *MongoClient) TryTxnBeta(ctx context.Context, cb func(ctx context.Context) error, options ...Option) error {
	if !c.txnSupported {
		return cb(ctx)
	}

	sess := mongo.SessionFromContext(ctx)
	if sess != nil {
		return cb(ctx)
	}

	return c.NewTxnBeta(ctx, cb, options...)
}

func (c *MongoClient) NewTxn(ctx context.Context, cb func(ctx context.Context) error, options ...Option) error {
	opt := defaultTxnOptions()
	for _, o := range options {
		o(opt)
	}

	return c.doTxn(ctx, cb, opt)
}

func (c *MongoClient) NewTxnBeta(ctx context.Context, cb func(ctx context.Context) error, options ...Option) error {
	opt := defaultTxnOptions()
	for _, o := range options {
		o(opt)
	}

	if opt.retry == 0 {
		return c.doTxn(ctx, cb, opt)
	}

	_, _, err := lo.AttemptWhileWithDelay(opt.retry+1, 3*time.Millisecond, func(i int, _ time.Duration) (error, bool) {
		err := c.doTxn(ctx, cb, opt)
		if err != nil {
			return err, IsWriteConflict(err)
		}
		return nil, false
	})

	return err
}

func (c *MongoClient) doTxn(ctx context.Context, cb func(ctx context.Context) error, opt *txnOptions) error {
	return c.rc.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}

		if err := cb(sessionContext); err != nil {
			_ = sessionContext.AbortTransaction(context.Background())
			return err
		}

		return sessionContext.CommitTransaction(context.Background())
	})
}
