package db

import (
	"context"
	"database/sql"

	"gitlab.sskjz.com/go/ob/meter"
)

func watchDBStats(db *sql.DB) {
	meter.Visitor(func(ctx context.Context) {
		stats := db.Stats()
		meter.NewGauge[int]("db.conn.busy").Set(stats.InUse)
		meter.NewGauge[int]("db.conn.idle").Set(stats.Idle)
		meter.NewGauge[int]("db.conn.max").Set(stats.MaxOpenConnections)
		meter.NewGauge[int64]("db.conn.wait.total").Set(stats.WaitCount)
		meter.NewGauge[float64]("db.conn.wait.seconds").Set(stats.WaitDuration.Seconds())
		meter.NewGauge[int64]("db.conn.close.total").Set(stats.MaxIdleClosed + stats.MaxIdleTimeClosed + stats.MaxLifetimeClosed)
	})
}
