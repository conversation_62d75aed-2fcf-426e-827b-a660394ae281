package db

import (
	"math/rand/v2"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestPrevMonth(t *testing.T) {
	at := time.Now().AddDate(-rand.IntN(4), -rand.IntN(12), -rand.IntN(31))
	pm := prevMonthOf(at)
	if at.Month() == time.January {
		assert.Equal(t, at.Year()-1, pm.Year())
		assert.Equal(t, time.December, pm.Month())
	} else {
		assert.Equal(t, at.Month()-1, pm.Month())
	}
}

func BenchmarkPrevMonth(b *testing.B) {
	at := time.Now()
	for range b.N {
		_ = prevMonthOf(at)
	}
	b.ReportAllocs()
}
