package db

import (
	"context"

	"gorm.io/gorm"
)

type ctxKey int

const (
	ctxDB ctxKey = iota // db transaction
)

func WithTx(ctx context.Context, tx *gorm.DB) context.Context {
	return context.WithValue(ctx, ctxDB, &mysqlTxn{db: tx})
}

func NoTx(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxDB, nil)
}

func HasTx(ctx context.Context) bool {
	return ctx.Value(ctxDB) != nil
}

func rawTx(ctx context.Context) *mysqlTxn {
	if tx := ctx.Value(ctxDB); tx != nil {
		return tx.(*mysqlTxn)
	}
	return nil
}

func UseTx(ctx context.Context, db *Client) *gorm.DB {
	if tx := rawTx(ctx); tx != nil {
		return tx.db
	}
	return db.G()
}

func Transaction(ctx context.Context, db *Client, fn func(context.Context, *gorm.DB) error) error {
	if HasTx(ctx) {
		return fn(ctx, UseTx(ctx, db))
	}
	err := db.Transaction(func(tx *gorm.DB) error {
		ctx = WithTx(ctx, tx)
		return fn(ctx, tx)
	})
	if err == nil {
		rawTx(ctx).commited()
	}
	return err
}
