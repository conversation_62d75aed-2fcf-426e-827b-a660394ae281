package db

import (
	"context"

	"gorm.io/gorm"
)

type mysqlTxn struct {
	db *gorm.DB
	// hooks
	acFs []func() // after commit
}

func (t *mysqlTxn) afterCommit(do func()) {
	t.acFs = append(t.acFs, do)
}

func (t *mysqlTxn) commited() {
	for _, fn := range t.acFs {
		fn()
	}
}

func AfterCommit(ctx context.Context, fn func(context.Context)) {
	if !HasTx(ctx) {
		fn(ctx)
		return
	}
	rawTx(ctx).afterCommit(func() {
		fn(NoTx(ctx))
	})
}
