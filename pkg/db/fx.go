package db

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.uber.org/fx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"moul.io/zapgorm2"
)

func ProvideMongo(lc fx.Lifecycle, desc *conf.Setting, sch *cron.Scheduler, vnd log.Vendor) (*MongoClient, error) {
	log2 := vnd.Scope("mongodb")

	tmo := desc.Mongo.ConnectTimeout
	if tmo == 0 {
		tmo = time.Second * 5
	}

	ctx, cf := context.WithTimeout(context.TODO(), tmo)
	defer cf()

	clientOptions := options.Client().ApplyURI(desc.Mongo.DSN)
	if desc.Mongo.PoolSize != 0 {
		clientOptions.SetMaxPoolSize(desc.Mongo.PoolSize)
	}

	// bson codec options
	clientOptions.SetBSONOptions(&options.BSONOptions{
		UseLocalTimeZone: true,
	})

	// custom type registry
	clientOptions.SetRegistry(getTypeRegistry())

	rc, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, fmt.Errorf("mongo connect: %w", err)
	}

	dc := &MongoClient{rc: rc, db: desc.Mongo.DB, txnSupported: !desc.Mongo.NotSupportTxn, logger: log2}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			if err := rc.Ping(ctx, readpref.Primary()); err != nil {
				return fmt.Errorf("mongo ping: %w", err)
			}
			return dc.StartSync(ctx, sch)
		},
	})

	log2.Debug("db connection established")

	return dc, nil
}

func ReleaseMongo(lc fx.Lifecycle, c *MongoClient, vnd log.Vendor) {
	logger := vnd.Scope("mongodb")
	lc.Append(fx.Hook{
		OnStop: func(ctx context.Context) error {
			logger.Debug("start to close db connection")
			if err := c.rc.Disconnect(ctx); err != nil {
				return err
			}
			logger.Debug("db connection closed")
			return nil
		},
	})
}

func ProvideGORM(lc fx.Lifecycle, desc *conf.Setting, sch *cron.Scheduler, vnd log.Vendor) (*Client, error) {
	log2 := vnd.Scope("mysql")

	sIndex := strings.Index(desc.DB.DSN, "://")
	scheme := desc.DB.DSN[0:sIndex]
	source := desc.DB.DSN[sIndex+3:]

	var dial gorm.Dialector

	switch scheme {
	case "mysql":
		dial = mysql.Open(source)
	default:
		return nil, errors.New("unsupported db type")
	}

	logger := zapgorm2.New(log2)
	logger.IgnoreRecordNotFoundError = true
	logger.SlowThreshold = time.Second
	logger.SkipCallerLookup = true

	cfg := &gorm.Config{
		Logger:                   logger,
		DisableNestedTransaction: true,
	}

	if desc.DB.PrepareStmt {
		cfg.PrepareStmt = true
	}

	if desc.DB.SkipDefaultTX {
		cfg.SkipDefaultTransaction = true
	}

	db, err := gorm.Open(dial, cfg)
	if err != nil {
		return nil, err
	}

	// Get generic database object sql.DB to use its functions
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	if desc.DB.MaxIdle != 0 {
		sqlDB.SetMaxIdleConns(desc.DB.MaxIdle)
	}

	// SetMaxOpenConns sets the maximum number of open connections to the database.
	if desc.DB.MaxOpen != 0 {
		sqlDB.SetMaxOpenConns(desc.DB.MaxOpen)
	}

	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	if desc.DB.MaxLife != 0 {
		sqlDB.SetConnMaxLifetime(desc.DB.MaxLife)
	}

	dc := &Client{DB: db, logger: log2, noAutoMigrate: desc.DB.NoAutoMigrate}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			if err := sqlDB.PingContext(ctx); err != nil {
				return err
			}
			watchDBStats(sqlDB)
			if err := dc.StartSync(ctx, sch); err != nil {
				return err
			}
			if desc.DB.NoAutoMigrate {
				return nil
			}
			return dc.DoAutoMigrate()
		},
	})

	log2.Debug("db connection established")

	return dc, nil
}

func ReleaseGORM(lc fx.Lifecycle, cli *Client, vnd log.Vendor) {
	logger := vnd.Scope("mysql")
	lc.Append(fx.Hook{
		OnStop: func(ctx context.Context) error {
			sqlDB, err := cli.G().DB()
			if err != nil {
				return err
			}
			logger.Debug("start to close db connection")
			if err := sqlDB.Close(); err != nil {
				return err
			}
			logger.Debug("db connection closed")
			return nil
		},
	})
}
