package db

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fx2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestMongoTransaction(t *testing.T) {
	fx2.Testing(t, testMGO, func(db *MongoClient) error {
		col := "test.case1"
		db.SyncSchema(FixedCollection(col), 1, testIndex...)
		if err := db.syncSchemas(context.TODO()); err != nil {
			return err
		}
		var ctx = context.TODO()
		{
			err := db.TryTxn(ctx, func(ctx context.Context) error {
				db.Collection(col).InsertOne(ctx, &testModel{Id: primitive.NewObjectID(), UserId: "123"})
				db.Collection(col).InsertOne(ctx, &testModel{Id: primitive.NewObjectID(), UserId: "123"})
				return nil
			})
			if assert.Error(t, err) {
				t.Logf("transaction failed: %v", err)
			}
		}
		return nil
	})
}
