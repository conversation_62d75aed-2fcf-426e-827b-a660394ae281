package fake

import (
	"context"
	"net/url"

	"gitlab.sskjz.com/overseas/live/osl/pkg/sms"
	"go.uber.org/zap"
)

func init() {
	sms.Register("fake", newFaker)
}

func newFaker(p *url.URL, log *zap.Logger) (sms.Sender, error) {
	code := p.Query().Get("code")
	return &fake{code: code, log: log}, nil
}

type fake struct {
	code string
	log  *zap.Logger
}

func (f *fake) VerifyCode(ctx context.Context, phone string, code *string) error {
	*code = f.code
	f.log.Debug("fake verify code", zap.String("phone", phone), zap.Stringp("code", code))
	return nil
}
