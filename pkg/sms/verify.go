package sms

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
)

const (
	keyVerifyCode = "SMS:VERIFY:%s" // phone
	resendWait    = 1 * time.Minute // 重发等待时间
	codeExpired   = 5 * time.Minute // 验证码过期时间
)

var (
	ErrInvalidAuthCode = biz.NewError(biz.ErrInvalidAuthCode, "invalid auth code")
)

type Verify interface {
	Send(ctx context.Context, phone string) (time.Duration, error)
	Check(ctx context.Context, phone, code string) error
}

func (m *manager) Send(ctx context.Context, phone string) (time.Duration, error) {
	country, phone, err := TrimPhone(ctx, phone)
	if err != nil {
		return 0, err
	}

	key := fmt.Sprintf(keyVerifyCode, phone)
	if l, err := m.dm.Lock(ctx, key); err != nil {
		return 0, err
	} else {
		defer l.MustUnlock()
	}

	if ttl, err := m.rc.TTL(ctx, key).Result(); err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	} else if ttl < 0 {
		// not exists
	} else if elapsed := codeExpired - ttl; elapsed < resendWait {
		return resendWait - elapsed, nil
	}

	code := fmt.Sprintf("%d", rng.Range(100000, 999999))
	if err := m.getSender(country).VerifyCode(ctx, phone, &code); err != nil {
		return 0, err
	}

	if err := m.rc.SetEx(ctx, key, code, codeExpired).Err(); err != nil {
		return 0, err
	}

	return resendWait, nil
}

func (m *manager) Check(ctx context.Context, phone, code string) error {
	_, phone, err := TrimPhone(ctx, phone)
	if err != nil {
		return err
	}

	key := fmt.Sprintf(keyVerifyCode, phone)
	ret, err := m.rc.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return ErrInvalidAuthCode
		}
		return err
	}

	if ret != code {
		return ErrInvalidAuthCode
	}

	m.rc.Del(ctx, key)
	return nil
}
