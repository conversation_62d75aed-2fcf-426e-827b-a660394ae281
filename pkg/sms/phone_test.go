package sms

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPhoneTrim(t *testing.T) {
	tests := []struct {
		raw string
		exp string
		err bool
	}{
		{"+5563959", "", true},
		{"+5500034368", "", true},
		{"+559211097075", "", true},
		{"+552950008571175126", "", true},
		{"+8613312345678", "+8613312345678", false},
	}
	for _, test := range tests {
		t.Run(test.raw, func(t *testing.T) {
			_, phone, err := TrimPhone(context.TODO(), test.raw)
			if test.err {
				assert.Error(t, err)
			} else if assert.NoError(t, err) {
				assert.Equal(t, test.exp, phone)
			}
		})
	}
}

func BenchmarkPhoneTrim(b *testing.B) {
	ctx := context.Background()
	for i := 0; i < b.N; i++ {
		_, _, _ = TrimPhone(ctx, "+8613312345678")
	}
	b.<PERSON>()
}
