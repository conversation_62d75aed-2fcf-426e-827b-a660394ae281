package sms

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.sskjz.com/go/rng"
)

type Admin interface {
	Make(ctx context.Context, phone string, expire time.Duration) (string, time.Duration, error)
}

func (m *manager) Make(ctx context.Context, phone string, expire time.Duration) (string, time.Duration, error) {
	_, phone, err := TrimPhone(ctx, phone)
	if err != nil {
		return "", 0, err
	}

	key := fmt.Sprintf(keyVerifyCode, phone)
	if l, err := m.dm.Lock(ctx, key); err != nil {
		return "", 0, err
	} else {
		defer l.<PERSON>()
	}

	if ttl, err := m.rc.TTL(ctx, key).Result(); err != nil && !errors.Is(err, redis.Nil) {
		return "", 0, err
	} else if ttl > 0 {
		code, err := m.rc.Get(ctx, key).Result()
		return code, ttl, err
	}

	code := fmt.Sprintf("%d", rng.Range(100000, 999999))
	if err := m.rc.SetEx(ctx, key, code, expire).Err(); err != nil {
		return "", 0, err
	}

	return code, expire, nil
}
