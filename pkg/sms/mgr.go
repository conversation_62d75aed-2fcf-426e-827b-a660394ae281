package sms

import (
	"fmt"
	"net/url"

	"gitlab.sskjz.com/go/redi"
	"go.uber.org/zap"
)

func newManager(rc *redi.Client, dm *redi.Mutex, log *zap.Logger) *manager {
	return &manager{
		rc:  rc,
		dm:  dm,
		cs:  make(map[int]Sender),
		nop: &nopSender{log: log},
		log: log,
	}
}

type manager struct {
	rc  *redi.Client
	dm  *redi.Mutex
	cs  map[int]Sender // country sender
	nop Sender
	log *zap.Logger
}

func (m *manager) initVnd(list map[int]string) error {
	for cn, dsn := range list {
		u, err := url.Parse(dsn)
		if err != nil {
			return fmt.Errorf("invalid vendor dsn %s: %w", dsn, err)
		}
		sender, err := getSender(u, m.log)
		if err != nil {
			return err
		}
		m.cs[cn] = sender
	}
	return nil
}

func (m *manager) getSender(country int) Sender {
	if v, has := m.cs[country]; has {
		return v
	}
	if v, has := m.cs[0]; has {
		return v
	}
	return m.nop
}
