package sms

import (
	"fmt"
	"net/url"

	"go.uber.org/zap"
)

type Vendor func(url *url.URL, log *zap.Logger) (Sender, error)

var registered = make(map[string]Vendor)

func Register(name string, init Vendor) {
	if _, has := registered[name]; has {
		panic(fmt.Sprintf("duplicated vendor: %s", name))
	}
	registered[name] = init
}

func getSender(dsn *url.URL, log *zap.Logger) (Sender, error) {
	name := dsn.Scheme
	init, has := registered[name]
	if !has {
		return nil, fmt.Errorf("unsupported vendor: %s", name)
	}
	return init(dsn, log.With(zap.String("vendor", name)))
}
