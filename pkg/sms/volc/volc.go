package volc

import (
	"context"
	"fmt"
	"net/url"

	vsms "github.com/volcengine/volc-sdk-golang/service/sms"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sms"
	"go.uber.org/zap"
)

func init() {
	sms.Register("volc", newVolc)
}

func newVolc(p *url.URL, log *zap.Logger) (sms.Sender, error) {
	q := p.Query()
	vi := vsms.NewInstance()
	vi.Client.SetAccessKey(q.Get("ak"))
	vi.Client.SetSecretKey(q.Get("sk"))
	return &volc{
		sms: vi,
		log: log,
		sa:  q.Get("sa"),
		sig: q.Get("sig"),
		tpl: q.Get("tpl"),
	}, nil
}

type volc struct {
	sms *vsms.SMS
	log *zap.Logger
	sa  string
	sig string
	tpl string
}

func (v *volc) VerifyCode(ctx context.Context, phone string, code *string) error {
	resp, _, err := v.sms.Send(&vsms.SmsRequest{
		SmsAccount:    v.sa,
		Sign:          v.sig,
		TemplateID:    v.tpl,
		TemplateParam: `{"xxxx":"` + *code + `"}`,
		PhoneNumbers:  phone,
	})
	if err != nil {
		v.log.Warn("send sms failed", zap.String("phone", phone), zap.Error(err))
		return err
	}
	if errObj := resp.ResponseMetadata.Error; errObj != nil {
		switch errObj.Code {
		case "RE:0011":
			return sms.ErrInvalidPhoneNum
		}
		v.log.Warn("send sms failed", zap.String("phone", phone), zap.Any("reason", errObj))
		return fmt.Errorf("%s - %s", errObj.Code, errObj.Message)
	}
	v.log.Debug("send sms success", zap.String("phone", phone), zap.Any("resp", resp))
	return nil
}
