package sms

import (
	"context"

	"github.com/nyaruka/phonenumbers"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/geoip"
)

var (
	ErrInvalidPhoneNum = biz.NewError(biz.ErrInvalidPhoneNum, "invalid phone number")
)

func TrimPhone(ctx context.Context, raw string) (country int, phone string, err error) {
	var region string
	if len(raw) > 0 && raw[0] != '+' {
		region = geoip.Client(ctx).Country
	}
	var number *phonenumbers.PhoneNumber
	number, err = phonenumbers.Parse(raw, region)
	if err != nil || !phonenumbers.IsValidNumber(number) {
		err = ErrInvalidPhoneNum
		return
	}
	country = int(number.GetCountryCode())
	phone = phonenumbers.Format(number, phonenumbers.E164)
	return
}
