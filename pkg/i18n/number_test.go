package i18n

import (
	"context"
	"testing"

	"gitlab.sskjz.com/go/i3n"
)

func TestNumberString(t *testing.T) {
	tests := []struct {
		ctx      context.Context
		input    float64
		expected string
	}{
		{i3n.Wrap(context.TODO(), "en"), 0, "0"},
		{i3n.Wrap(context.TODO(), "en"), 999, "999"},
		{i3n.Wrap(context.TODO(), "en"), 1000, "1,000"},
		{i3n.Wrap(context.TODO(), "en"), 1001, "1,001"},
		{i3n.Wrap(context.TODO(), "en"), 1499, "1,499"},
		{i3n.Wrap(context.TODO(), "en"), 1500, "1,500"},
		{i3n.Wrap(context.TODO(), "en"), 15000, "15K"},
		{i3n.Wrap(context.TODO(), "en"), 150000, "150K"},
		{i3n.Wrap(context.TODO(), "en"), 1500000, "1,500K"},

		{i3n.Wrap(context.TODO(), "zh"), 0, "0"},
		{i3n.Wrap(context.TODO(), "zh"), 999, "999"},
		{i3n.Wrap(context.TODO(), "zh"), 1000, "1,000"},
		{i3n.Wrap(context.TODO(), "zh"), 1001, "1,001"},
		{i3n.Wrap(context.TODO(), "zh"), 1499, "1,499"},
		{i3n.Wrap(context.TODO(), "zh"), 1500, "1,500"},
		{i3n.Wrap(context.TODO(), "zh"), 15000, "15K"},
		{i3n.Wrap(context.TODO(), "zh"), 150000, "150K"},
		{i3n.Wrap(context.TODO(), "zh"), 1500000, "1,500K"},

		{i3n.Wrap(context.TODO(), "pt"), 0, "0"},
		{i3n.Wrap(context.TODO(), "pt"), 999, "999"},
		{i3n.Wrap(context.TODO(), "pt"), 1000, "1.000"},
		{i3n.Wrap(context.TODO(), "pt"), 1001, "1.001"},
		{i3n.Wrap(context.TODO(), "pt"), 1499, "1.499"},
		{i3n.Wrap(context.TODO(), "pt"), 1500, "1.500"},
		{i3n.Wrap(context.TODO(), "pt"), 15000, "15K"},
		{i3n.Wrap(context.TODO(), "pt"), 150000, "150K"},
		{i3n.Wrap(context.TODO(), "pt"), 1500000, "1.500K"},
	}
	for _, test := range tests {
		result := NumberString(test.ctx, test.input)
		if result != test.expected {
			t.Errorf("NumberString(%v, %f) = %s, expected %s", test.ctx, test.input, result, test.expected)
		}
	}
}

func TestNumberStringHandlesNegativeNumbers(t *testing.T) {
	tests := []struct {
		ctx      context.Context
		input    float64
		expected string
	}{
		{i3n.Wrap(context.TODO(), "en"), -1000, "-1,000"},
		{i3n.Wrap(context.TODO(), "en"), -1500, "-1,500"},
		{i3n.Wrap(context.TODO(), "en"), -15000, "-15K"},
		{i3n.Wrap(context.TODO(), "zh"), -1000, "-1,000"},
		{i3n.Wrap(context.TODO(), "zh"), -1500, "-1,500"},
		{i3n.Wrap(context.TODO(), "zh"), -15000, "-15K"},
		{i3n.Wrap(context.TODO(), "pt"), -1000, "-1.000"},
		{i3n.Wrap(context.TODO(), "pt"), -1500, "-1.500"},
		{i3n.Wrap(context.TODO(), "pt"), -15000, "-15K"},
	}

	for _, test := range tests {
		result := NumberString(test.ctx, test.input)
		if result != test.expected {
			t.Errorf("NumberString(%v, %f) = %s, expected %s", test.ctx, test.input, result, test.expected)
		}
	}
}

func TestNumberStringHandlesZero(t *testing.T) {
	tests := []struct {
		ctx      context.Context
		input    float64
		expected string
	}{
		{i3n.Wrap(context.TODO(), "en"), 0, "0"},
		{i3n.Wrap(context.TODO(), "zh"), 0, "0"},
		{i3n.Wrap(context.TODO(), "pt"), 0, "0"},
	}

	for _, test := range tests {
		result := NumberString(test.ctx, test.input)
		if result != test.expected {
			t.Errorf("NumberString(%v, %f) = %s, expected %s", test.ctx, test.input, result, test.expected)
		}
	}
}

func TestAddThousandSeparatorHandlesNegativeNumbers(t *testing.T) {
	tests := []struct {
		ctx      context.Context
		input    float64
		expected string
	}{
		{i3n.Wrap(context.TODO(), "en"), -1000, "-1,000"},
		{i3n.Wrap(context.TODO(), "en"), -1500000, "-1,500,000"},
		{i3n.Wrap(context.TODO(), "zh"), -1000, "-1,000"},
		{i3n.Wrap(context.TODO(), "zh"), -1500000, "-1,500,000"},
		{i3n.Wrap(context.TODO(), "pt"), -1000, "-1.000"},
		{i3n.Wrap(context.TODO(), "pt"), -1500000, "-1.500.000"},
	}

	for _, test := range tests {
		result := AddThousandSeparator(test.ctx, test.input)
		if result != test.expected {
			t.Errorf("AddThousandSeparator(%v, %f) = %s, expected %s", test.ctx, test.input, result, test.expected)
		}
	}
}

func TestAddThousandSeparatorHandlesZero(t *testing.T) {
	tests := []struct {
		ctx      context.Context
		input    float64
		expected string
	}{
		{i3n.Wrap(context.TODO(), "en"), 0, "0"},
		{i3n.Wrap(context.TODO(), "zh"), 0, "0"},
		{i3n.Wrap(context.TODO(), "pt"), 0, "0"},
	}

	for _, test := range tests {
		result := AddThousandSeparator(test.ctx, test.input)
		if result != test.expected {
			t.Errorf("AddThousandSeparator(%v, %f) = %s, expected %s", test.ctx, test.input, result, test.expected)
		}
	}
}
