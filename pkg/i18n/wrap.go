package i18n

import (
	"context"
	"slices"
	"strings"

	"gitlab.sskjz.com/go/i3n"
)

var (
	usingLang = []string{
		"zh",
		"en",
		"pt",
	}
)

func PreferLang(ctx context.Context, def string) string {
	langs := i3n.UnWarpRaw(ctx)
	for _, lang := range langs {
		index := strings.Index(lang, "-")
		if index > 0 {
			lang = lang[:index]
		}

		if slices.Index(usingLang, lang) >= 0 {
			return lang
		}
	}

	return def
}

func Lookup[T comparable](ctx context.Context, def string, f func(lang string) T) T {
	var zero T
	lang := i3n.UnWarpRaw(ctx)
	if len(lang) == 0 {
		return f(def)
	}
	raw := lang[0]
	v := f(raw)
	if v != zero {
		return v
	}
	if i := strings.Index(raw, "-"); i > 0 {
		raw = raw[:i]
	}
	v = f(raw)
	if v != zero {
		return v
	}
	return f(def)
}
