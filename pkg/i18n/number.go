package i18n

import (
	"context"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ds"
)

type numberSymbol struct {
	ThousandSeparator string
	DecimalPoint      string
}

var symbolTable = map[string]numberSymbol{
	"en": {",", "."},
	"zh": {",", "."},
	"pt": {".", ","},
}

func NumberString(ctx context.Context, in float64) string {
	symbol, ok := symbolTable[i3n.UnWarp(ctx)]
	if !ok {
		symbol = symbolTable["en"]
	}

	return ds.NumberToUnitString2(in, symbol.ThousandSeparator, symbol.DecimalPoint)
}

func AddThousandSeparator(ctx context.Context, in float64) string {
	symbol, ok := symbolTable[i3n.UnWarp(ctx)]
	if !ok {
		symbol = symbolTable["en"]
	}

	return ds.AddThousandSeparator(in, symbol.ThousandSeparator, symbol.DecimalPoint)
}
