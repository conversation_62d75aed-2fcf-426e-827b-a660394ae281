package i18n

import (
	"context"

	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"golang.org/x/text/language"
)

func Invoke(desc *conf.Setting, vnd log.Vendor) error {
	b := i3n.NewBundle(i3n.WithFallback(language.English.String()), i3n.WithLogger(vnd.Logger().Sugar()))
	b.<PERSON>d<PERSON>oader(i3n.NewLocalFsLoader2("default", desc.Locale.Texts))
	if err := b.Load(context.Background()); err != nil {
		return err
	}

	i3n.SetDefaultBundle(b)
	return nil
}
