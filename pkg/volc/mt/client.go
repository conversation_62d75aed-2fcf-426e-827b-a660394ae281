package mt

import (
	"context"
	"slices"
	"time"

	"go.uber.org/zap"
)

type Client struct {
	detector   *Detector
	translator *Translator
	logger     *zap.Logger
}

func (c *Client) DetectLang(ctx context.Context, texts ...string) (*DetectLangOutput, error) {
	s := time.Now()
	v, err := c.detector.DetectLang(ctx, texts...)
	c.logger.Info("DetectLang",
		zap.Any("input", texts),
		zap.Any("output", v),
		zap.Error(err),
		zap.Duration("cost", time.Since(s)))

	return v, err
}

func (c *Client) Translate(ctx context.Context, texts []string, from, to string) (*TranslateTextOutput, error) {
	s := time.Now()
	_translateCounter.Add(1)
	defer func() {
		_translateCost.Record(time.Since(s).Milliseconds())
	}()

	texts2 := slices.Clone(texts)
	replaced := make(map[string]string)
	for i, text := range texts2 {
		if text == "" {
			continue
		}

		out := MaskEmoji(text)
		if out != text {
			replaced[text] = out
			texts2[i] = out
		}
	}

	v, err := c.translator.TranslateText(ctx, &TranslateTextInput{
		TextList:       texts2,
		SourceLanguage: from,
		TargetLanguage: to,
	})

	if err == nil && v != nil {
		for i := range v.TranslationList {
			if _, ok := replaced[texts[i]]; ok {
				v.TranslationList[i].Translation = UnmaskEmoji(v.TranslationList[i].Translation)
			}
		}
	}

	c.logger.Info("Translate",
		zap.Strings("texts", texts),
		zap.String("from", from),
		zap.String("to", to),
		zap.Any("output", v),
		zap.Error(err),
		zap.Duration("cost", time.Since(s)))

	return v, err
}
