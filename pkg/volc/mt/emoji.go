package mt

import (
	"regexp"
)

var table = map[string]string{
	"[smile]":        "[000]",
	"[verylike]":     "[001]",
	"[daze]":         "[002]",
	"[cool]":         "[003]",
	"[picknose]":     "[004]",
	"[cry3]":         "[005]",
	"[coverface]":    "[006]",
	"[angry]":        "[007]",
	"[bareteeth]":    "[008]",
	"[awkward]":      "[009]",
	"[shy]":          "[010]",
	"[naughty]":      "[011]",
	"[lick]":         "[012]",
	"[look]":         "[013]",
	"[heart]":        "[014]",
	"[sendheart2]":   "[015]",
	"[thumbsup]":     "[016]",
	"[applaud]":      "[017]",
	"[thank]":        "[018]",
	"[rose]":         "[019]",
	"[come]":         "[020]",
	"[idea]":         "[021]",
	"[doubleyeah]":   "[022]",
	"[hitface]":      "[023]",
	"[laugh]":        "[024]",
	"[smart]":        "[025]",
	"[sendheart]":    "[026]",
	"[moneyeyes]":    "[027]",
	"[hey]":          "[028]",
	"[clap]":         "[029]",
	"[politesmile]":  "[030]",
	"[spray]":        "[031]",
	"[cry2]":         "[032]",
	"[cry1]":         "[033]",
	"[wipe]":         "[034]",
	"[sillysmile]":   "[035]",
	"[insidious]":    "[036]",
	"[snicker]":      "[037]",
	"[think]":        "[038]",
	"[pitiful]":      "[039]",
	"[laughtcry]":    "[040]",
	"[almostcry]":    "[041]",
	"[confuse]":      "[042]",
	"[despise]":      "[043]",
	"[tongueout]":    "[044]",
	"[innocent]":     "[045]",
	"[whiteeyes]":    "[046]",
	"[eatmelon]":     "[047]",
	"[frown]":        "[048]",
	"[stay]":         "[049]",
	"[greenhat]":     "[050]",
	"[awesome]":      "[051]",
	"[kiss1]":        "[052]",
	"[fight]":        "[053]",
	"[amazed]":       "[054]",
	"[dizzy]":        "[055]",
	"[embarrassed]":  "[056]",
	"[shock]":        "[057]",
	"[sad]":          "[058]",
	"[squint]":       "[059]",
	"[curse]":        "[060]",
	"[shutup]":       "[061]",
	"[yawn]":         "[062]",
	"[hiss]":         "[063]",
	"[pout]":         "[064]",
	"[smileevilly]":  "[065]",
	"[showoff]":      "[066]",
	"[shock2]":       "[067]",
	"[stunned]":      "[068]",
	"[crazy]":        "[069]",
	"[happy]":        "[070]",
	"[smileevilly2]": "[071]",
	"[proud]":        "[072]",
	"[rolleyes]":     "[073]",
	"[dontdisturb]":  "[074]",
	"[wronged]":      "[075]",
	"[blowkisss]":    "[076]",
	"[fear]":         "[077]",
	"[music]":        "[078]",
	"[bye]":          "[079]",
	"[gaze]":         "[080]",
	"[bear]":         "[081]",
	"[blackface]":    "[082]",
	"[sweat]":        "[083]",
	"[pathead]":      "[084]",
	"[grimace]":      "[085]",
	"[drowsy]":       "[086]",
	"[redface]":      "[087]",
	"[beathead]":     "[088]",
	"[cryhard]":      "[089]",
	"[surprise]":     "[090]",
	"[coffee]":       "[091]",
	"[ease]":         "[092]",
	"[weak]":         "[093]",
	"[vomit]":        "[094]",
	"[boom]":         "[095]",
	"[pig]":          "[096]",
	"[melon]":        "[097]",
	"[sun]":          "[098]",
	"[moon]":         "[099]",
	"[wither]":       "[100]",
	"[fist]":         "[101]",
	"[yeah]":         "[102]",
	"[cofist]":       "[103]",
	"[kiss2]":        "[104]",
	"[cucumber]":     "[105]",
	"[beer]":         "[106]",
	"[skeleton]":     "[107]",
	"[strong]":       "[108]",
	"[fistbump]":     "[109]",
	"[okhand]":       "[110]",
	"[thumbdown]":    "[111]",
	"[left]":         "[112]",
	"[right]":        "[113]",
	"[highfive]":     "[114]",
	"[shakehands]":   "[115]",
	"[adult]":        "[116]",
	"[heartbroken]":  "[117]",
	"[shit]":         "[118]",
	"[gift]":         "[119]",
	"[cake]":         "[120]",
	"[celebrate]":    "[121]",
}

var revTable = map[string]string{}

func init() {
	for k, v := range table {
		revTable[v] = k
	}
}

func ReplaceEmoji(in string, table map[string]string) string {
	// 定义正则表达式来匹配所有被 [] 包围的子字符串
	re := regexp.MustCompile(`\[[^\]]+\]`)

	// 使用正则表达式查找所有匹配的子字符串并替换
	out := re.ReplaceAllStringFunc(in, func(matched string) string {
		if val, ok := table[matched]; ok {
			return val
		}
		return matched
	})

	return out
}

func MaskEmoji(in string) string {
	return ReplaceEmoji(in, table)
}

func UnmaskEmoji(in string) string {
	return ReplaceEmoji(in, revTable)
}
