package mt

import (
	"time"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

func Provide(cfg *conf.Setting, vnd log.Vendor) *Client {
	return &Client{
		detector:   NewDetector(cfg.Volcengine.AccessKeyId, cfg.Volcengine.SecretAccessKey, cfg.Volcengine.Translate.Detector.Region, time.Second),
		translator: NewTranslator(cfg.Volcengine.AccessKeyId, cfg.Volcengine.SecretAccessKey, cfg.Volcengine.Translate.Translator.Region, time.Second*5),
		logger:     vnd.Scope("volc.mt"),
	}
}
