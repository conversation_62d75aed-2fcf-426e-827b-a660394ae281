package mt

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/bytedance/sonic"
	"github.com/volcengine/volc-sdk-golang/base"
)

const (
	kServiceVersion = "2020-06-01"
)

type Detector struct {
	c *base.Client
}

func NewDetector(kAccessKey, kSecretKey, region string, tmo time.Duration) *Detector {
	serviceInfo := &base.ServiceInfo{
		Timeout: tmo,
		Host:    "open.volcengineapi.com",
		Header: http.Header{
			"Accept": []string{"application/json"},
		},
		Credentials: base.Credentials{Region: region, Service: "translate", AccessKeyID: kAccessKey, SecretAccessKey: kSecretKey},
	}

	if serviceInfo.Timeout == 0 {
		serviceInfo.Timeout = 5 * time.Second
	}

	apiInfoList := map[string]*base.ApiInfo{
		"LangDetect": {
			Method: http.MethodPost,
			Path:   "/",
			Query: url.Values{
				"Action":  []string{"LangDetect"},
				"Version": []string{kServiceVersion},
			},
		},
	}

	return &Detector{c: base.NewClient(serviceInfo, apiInfoList)}
}

type DetectLangInput struct {
	TextList []string `json:"TextList"`
}

type DetectedLanguage struct {
	Language   string  `json:"Language"`
	Confidence float64 `json:"Confidence"`
}

type DetectLangOutput struct {
	ResponseMetadata     base.ResponseMetadata `json:"ResponseMetadata"`
	DetectedLanguageList []DetectedLanguage    `json:"DetectedLanguageList"`
}

func (d *Detector) DetectLang(ctx context.Context, texts ...string) (*DetectLangOutput, error) {
	body, err := sonic.MarshalString(DetectLangInput{
		TextList: texts,
	})

	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	resp, code, err := d.c.Json("LangDetect", nil, body)
	if err != nil {
		return nil, fmt.Errorf("DetectLang: %w", err)
	}

	if code != 200 {
		return nil, fmt.Errorf("DetectLang: %s,code %d", string(resp), code)
	}

	var out DetectLangOutput
	if err := sonic.Unmarshal(resp, &out); err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	return &out, nil
}
