package mt

import (
	"context"
	"testing"
	"time"

	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

func TestTranslatorText(t *testing.T) {

	ak := "AKLTNTZjM2VjMTM2OTNjNGRlM2JkN2JmNTEzOWM3NjJiYTU"
	sk := "WTJKallqUXlZelEwTkdWak5HRXlOV0kzTVRnd1lqTmlZMlEyTkRJNVlURQ=="
	c := NewTranslator(ak, sk, `ap-southeast-1`, time.Second)

	c = NewTranslator(ak, sk, `cn-north-1`, time.Second)

	mc := &Client{
		translator: c,
		logger:     zap.NewNop(),
	}

	r := rate.NewLimiter(rate.Every(time.Second/5), 1)

	for k := range table {
		k := k
		t.Run(k, func(t *testing.T) {
			r.Wait(context.Background())
			input := "Hi " + k + " World stuff"
			result, err := mc.Translate(context.TODO(), []string{input}, "", "zh")
			if err != nil {
				t.Fatal(err)
			} else {
				if result == nil {
					t.<PERSON>rf("Translate(%q) = nil", input)
					return
				}

				if len(result.TranslationList) == 0 {
					if result.ResponseMetadata.Error != nil {
						t.Errorf("Translate(%q) = %q)", input, result.ResponseMetadata.Error.Message+" "+result.ResponseMetadata.Error.Code)
					}
					return
				}

				t.Logf("Translate(%q) = %q)", input, result.TranslationList[0].Translation)

			}

		})
	}

	// t.Run("base", func(t *testing.T) {
	// 	resp, err := mc.Translate(context.TODO(), []string{"dasdadjkh[vomit]dsajkldakdja"}, "", "id")
	// 	// resp, err := c.TranslateText(context.TODO(), &TranslateTextInput{
	// 	// 	// SourceLanguage: "ko",
	// 	// 	TargetLanguage: "id",
	// 	// 	TextList:       []string{"dasdadjkh[smile]dsajkldakdja"},
	// 	// })
	// 	if err != nil {
	// 		t.Fatal(err)
	// 	}
	// 	t.Log(resp.TranslationList, "cost:", time.Since(s))
	// 	fmt.Printf("resp: %+v cost %+v\n", resp, time.Since(s))
	// })
}
