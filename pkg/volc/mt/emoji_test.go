package mt

import (
	"testing"
)

func TestMaskEmoji(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"Hello [smile]", "Hello [10000]"},
		{"[verylike] this [daze]", "[10001] this [10002]"},
		{"No emoji here", "No emoji here"},
		{"Multiple [smile] [smile]", "Multiple [10000] [10000]"},
		{"Unknown [unknown] emoji", "Unknown [unknown] emoji"},
	}

	for _, test := range tests {
		result := MaskEmoji(test.input)
		if result != test.expected {
			t.<PERSON><PERSON><PERSON>("MaskEmoji(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}

func TestUnmaskEmoji(t *testing.T) {
	for k := range table {

		t.Run(k, func(t *testing.T) {
			input := "daskdja" + k + "daskdja"
			input2 := MaskEmoji(input)
			result := UnmaskEmoji(input2)
			if result != input {
				t.<PERSON><PERSON><PERSON>("UnmaskEmoji(%q) = %q; want %q", input2, result, input)
			} else {
				t.Logf("UnmaskEmoji(%q) = %q; want %q", input2, result, input)
			}
		})

	}

}
