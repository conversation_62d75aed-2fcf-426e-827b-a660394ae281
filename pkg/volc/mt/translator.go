package mt

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"github.com/volcengine/volc-sdk-golang/base"
)

type Translator struct {
	c *base.Client
}

func NewTranslator(kAccessKey, kSecretKey, region string, tmo time.Duration) *Translator {
	serviceInfo := &base.ServiceInfo{
		Timeout: tmo,
		Host:    "translate.volcengineapi.com",
		Header: http.Header{
			"Accept": []string{"application/json"},
		},
		Credentials: base.Credentials{Region: region, Service: "translate", AccessKeyID: kAccessKey, SecretAccessKey: kSecretKey},
		Retry:       base.RetrySettings{AutoRetry: true, RetryTimes: lo.ToPtr[uint64](3), RetryInterval: lo.ToPtr(time.Microsecond * 100)},
	}

	if serviceInfo.Timeout == 0 {
		serviceInfo.Timeout = 10 * time.Second
	}

	apiInfoList := map[string]*base.ApiInfo{
		"TranslateText": {
			Method: http.MethodPost,
			Path:   "/",
			Query: url.Values{
				"Action":  []string{"TranslateText"},
				"Version": []string{kServiceVersion},
			},
		},
	}

	return &Translator{c: base.NewClient(serviceInfo, apiInfoList)}
}

type TranslateTextInput struct {
	SourceLanguage string   `json:"SourceLanguage"`
	TargetLanguage string   `json:"TargetLanguage"`
	TextList       []string `json:"TextList"`
}

type Translation struct {
	Translation            string `json:"Translation"`
	DetectedSourceLanguage string `json:"DetectedSourceLanguage"`
}

type TranslateTextOutput struct {
	ResponseMetadata base.ResponseMetadata `json:"ResponseMetadata"`
	TranslationList  []Translation         `json:"TranslationList"`
}

func (t *Translator) TranslateText(ctx context.Context, input *TranslateTextInput) (*TranslateTextOutput, error) {
	body, err := sonic.Marshal(input)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal input: %v", err)
	}

	resp, code, err := t.c.Json("TranslateText", nil, string(body))
	if err != nil {
		return nil, fmt.Errorf("failed to request: %v", err)
	}

	if code != 200 {
		return nil, fmt.Errorf("failed to request: %s, code %d", string(resp), code)
	}

	var out TranslateTextOutput
	if err := sonic.Unmarshal(resp, &out); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	return &out, nil
}
