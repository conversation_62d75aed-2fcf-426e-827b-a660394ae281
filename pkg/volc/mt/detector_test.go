package mt

import (
	"context"
	"testing"
	"time"
)

func TestClient_DetectLang(t *testing.T) {
	ak := "AKLTNTZjM2VjMTM2OTNjNGRlM2JkN2JmNTEzOWM3NjJiYTU"
	sk := "WTJKallqUXlZelEwTkdWak5HRXlOV0kzTVRnd1lqTmlZMlEyTkRJNVlURQ=="
	c := NewDetector(ak, sk, `ap-southeast-1`, time.Second)

	s := time.Now()
	resp, err := c.DetectLang(context.Background(), "hello", "world", "한국어")
	if err != nil {
		t.<PERSON>al(err)
	}

	t.Log(resp, "cost:", time.Since(s))
}
