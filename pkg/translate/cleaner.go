package translate

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type Cleaner struct {
	mc     *db.MongoClient
	logger *zap.Logger
}

func (c *Cleaner) Clean(ctx context.Context) {
	coll := c.mc.Collection(translatedCollection)
	res, err := coll.DeleteMany(ctx, bson.M{
		"$or": bson.A{
			bson.M{
				"read_count":   bson.M{"$lt": 100},
				"last_read_at": bson.M{"$lt": time.Now().AddDate(0, -1, 0)},
			},
			bson.M{
				"last_read_at": bson.M{"$lt": time.Now().AddDate(0, -2, 0)},
			},
		},
	})
	if err != nil {
		c.logger.Error("clean cold data error", zap.Error(err))
		return
	}

	if res.DeletedCount > 0 {
		c.logger.Info("clean cold data", zap.Int64("deleted", res.DeletedCount))
		_cleanCounter.Add(res.DeletedCount)
	}
}
