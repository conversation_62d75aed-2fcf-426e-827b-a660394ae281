package translate

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/forPelevin/gomoji"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/volc/mt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

var (
	ErrNoResult          = errors.New("no result")
	ErrOperationTimeout  = errors.New("translate timeout")
	ErrMissingTargetLang = errors.New("no target lang")
	ErrEmptyText         = errors.New("no text")
)

type translation struct {
	Text           string
	TargetLanguage string
}

type Translator struct {
	rc                *redi.Client
	mc                *db.MongoClient
	mtc               *mt.Client
	detectedLangCache cc.Cache[string, string]
	translationCache  cc.Cache[translation, *Translated]
	pool              co.Pool
	limiter           *rate.Limiter
	logger            *zap.Logger
}

func (c *Translator) DetectLang(ctx context.Context, text string) (string, error) {
	v, err := c.detectedLangCache.Get(text)
	if err != nil {
		return "", fmt.Errorf("get detected lang error:%w", err)
	}

	return v, nil
}

func isAllEmojisAndSpace(in string) bool {
	in = strings.TrimSpace(in)
	if in == "" {
		return true
	}

	// remove all spaces from in
	in = strings.ReplaceAll(in, " ", "")

	return gomoji.RemoveEmojis(in) == ""
}

func (c *Translator) Translate(ctx context.Context, text string, toLanguage string) (*Translated, error) {
	if text == "" {
		return nil, ErrEmptyText
	}

	if toLanguage == "" {
		return nil, ErrMissingTargetLang
	}

	var (
		done       = make(chan struct{})
		translated *Translated
		err        error
	)

	c.pool.Submit(func() {
		translated, err = c.translationCache.Get(translation{Text: text, TargetLanguage: toLanguage})
		close(done)
	})

	select {
	case <-done:
		if err != nil {
			return nil, fmt.Errorf("get translated error: %w", err)
		}

		if translated == nil || len(translated.Translation) == 0 || translated.Translation[toLanguage] == "" {
			return nil, ErrNoResult
		}

		return translated, nil
	case <-time.After(time.Millisecond * 1300):
		return nil, ErrOperationTimeout
	}
}

// TranslateWithFallback 翻译文本，支持排除指定语言. 失败时返回原文
func (c *Translator) TranslateWithFallback(ctx context.Context, text string) string {
	toLang, excludes := UnWarp(ctx)

	if text == "" || toLang == "" {
		return text
	}

	if isAllEmojisAndSpace(text) {
		return text
	}

	v, err := c.Translate(ctx, text, toLang)
	if err != nil {
		c.logger.Info("translate error",
			zap.String("text", text),
			zap.String("toLang", toLang),
			zap.Strings("excludes", excludes),
			zap.Error(err))
		return text
	}

	if slices.Contains(excludes, v.Lang) {
		return text
	}

	return v.Translation[toLang]
}

func (c *Translator) doDetectLang(ctx context.Context, text string) (string, error) {
	var (
		coll = c.mc.Collection(translatedCollection)
		rec  *Translated
		s    = time.Now()
	)

	err := coll.FindOneAndUpdate(
		ctx,
		bson.M{"text": text},
		bson.M{
			"$inc": bson.M{"read_count": 1},
			"$set": bson.M{"last_read_at": s},
		},
	).Decode(&rec)
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return "", fmt.Errorf("find detected lang error: %w", err)
		}
	}

	if rec != nil {
		return rec.Lang, nil
	}

	if err := c.limiter.Wait(ctx); err != nil {
		return "", fmt.Errorf("wait rate limit error: %w", err)
	}

	resp, err := c.mtc.DetectLang(ctx, text)
	if err != nil {
		return "", err
	}

	if len(resp.DetectedLanguageList) == 0 {
		return "", fmt.Errorf("rpc detect lang error, no detected language")
	}

	doc := &Translated{
		ID:          primitive.NewObjectID(),
		Text:        text,
		Lang:        resp.DetectedLanguageList[0].Language,
		Translation: map[string]string{},
		ReadCount:   1,
		LastReadAt:  s,
		CreatedAt:   s,
	}

	if _, err := coll.InsertOne(ctx, doc); err != nil {
		if !db.IsDuplicate(err) {
			return "", fmt.Errorf("insert detected lang error: %w", err)
		} else {
			c.logger.Debug("insert detected lang duplicate", zap.String("key", text))
		}
	}

	return doc.Lang, nil
}

func (c *Translator) doTranslate(ctx context.Context, text string, toLang string) (*Translated, error) {
	var (
		coll       = c.mc.Collection(translatedCollection)
		s          = time.Now()
		translated *Translated
	)

	if err := coll.FindOneAndUpdate(
		ctx,
		bson.M{"text": text},
		bson.M{
			"$inc": bson.M{"read_count": 1},
			"$set": bson.M{"last_read_at": s},
		},
	).Decode(&translated); err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("find translated error: %w", err)
		}
	}

	if translated != nil && len(translated.Translation) > 0 && translated.Translation[toLang] != "" {
		return translated, nil
	}

	if err := c.limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("wait rate limit error: %w", err)
	}

	if translated == nil {
		out, err := c.mtc.Translate(ctx, []string{text}, "", toLang)
		if err != nil {
			return nil, fmt.Errorf("translate error:%w", err)
		}

		if len(out.TranslationList) == 0 {
			return nil, fmt.Errorf("rpc translate error, no translation")
		}

		got := out.TranslationList[0]

		translated = &Translated{
			ID:          primitive.NewObjectID(),
			Text:        text,
			Lang:        got.DetectedSourceLanguage,
			Translation: map[string]string{toLang: got.Translation},
			LastReadAt:  s,
			CreatedAt:   s,
		}
	} else {
		if translated.Translation[toLang] == "" {
			if translated.Lang != toLang {
				out, err := c.mtc.Translate(ctx, []string{text}, translated.Lang, toLang)
				if err != nil {
					return nil, fmt.Errorf("translate error:%w", err)
				}

				if len(out.TranslationList) == 0 {
					return nil, fmt.Errorf("rpc translate error, no translation")
				}

				translated.Translation[toLang] = out.TranslationList[0].Translation
			} else {
				translated.Translation[toLang] = text
			}
		}
	}

	if _, err := coll.UpdateOne(
		ctx,
		bson.M{"text": text},
		bson.M{
			"$set": bson.M{fmt.Sprintf("translation.%s", toLang): translated.Translation[toLang], "last_read_at": s},
			"$inc": bson.M{"read_count": 1},
			"$setOnInsert": bson.M{
				"lang":       translated.Lang,
				"created_at": s,
			},
		},
		options.Update().SetUpsert(true)); err != nil {
		return nil, fmt.Errorf("update translated error: %w", err)
	}

	return translated, nil
}
