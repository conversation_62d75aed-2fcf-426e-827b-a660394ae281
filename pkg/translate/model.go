package translate

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	translatedCollection = "translate.translated"
)

type Translated struct {
	ID          primitive.ObjectID `bson:"_id"`
	Text        string             `bson:"text"`
	Lang        string             `bson:"lang"`
	Translation map[string]string  `bson:"translation"`
	ReadCount   int64              `bson:"read_count"` // 读取次数
	LastReadAt  time.Time          `bson:"last_read_at"`
	CreatedAt   time.Time          `bson:"created_at"`
}
