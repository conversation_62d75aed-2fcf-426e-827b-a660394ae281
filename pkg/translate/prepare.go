package translate

import (
	"context"
	"strings"
	"time"

	"github.com/sourcegraph/conc/pool"
	"go.uber.org/zap"
)

// PrepareLanguages 预翻译的语言
var PrepareLanguages = []string{
	"en",
	"zh",
	"id",
	"pt",
}

func (c *Translator) Prepare(ctx context.Context, texts ...string) {
	return
	c.pool.Submit(func() {
		c.prepare(ctx, texts...)
	})
}

func (c *Translator) prepare(ctx context.Context, texts ...string) {
	s := time.Now()
	defer func() {
		c.logger.Debug("prepare translation", zap.Duration("cost", time.Since(s)))
	}()

	p := pool.New().WithMaxGoroutines(8)

	for _, lang := range PrepareLanguages {
		for _, text := range texts {
			if strings.TrimSpace(text) == "" {
				continue
			}

			p.Go(func() {
				if _, err := c.translationCache.Get(translation{Text: text, TargetLanguage: lang}); err != nil {
					c.logger.Error("prepare translation error", zap.Error(err))
				}
			})
		}
	}
	p.Wait()
}

func (c *Translator) prepare2(_ context.Context, texts ...string) {
	s := time.Now()
	defer func() {
		c.logger.Debug("prepare translation", zap.Duration("cost", time.Since(s)))
	}()

	p := pool.New().WithMaxGoroutines(8)

	for _, lang := range PrepareLanguages {
		for _, text := range texts {
			if strings.TrimSpace(text) == "" {
				continue
			}

			p.Go(func() {
				if _, err := c.translationCache.Get(translation{Text: text, TargetLanguage: lang}); err != nil {
					c.logger.Error("prepare translation error", zap.Error(err))
				}
			})
		}
	}
	p.Wait()
}
