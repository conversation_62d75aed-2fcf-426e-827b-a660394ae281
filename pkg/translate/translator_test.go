package translate

import "testing"

func TestIsAllEmojisAndSpace(t *testing.T) {
	// Test case 1: All emojis and spaces
	input1 := "😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 😉 😌 😍 😘 😗 😙 😚 ☺️ 🙂 🤗 🤩 🤔 🤨 😐 😑 😶 🙄 😏 😣 😥 😮 🤐 😯 😪 😫 😴 😌 😛 😜 😝 🤤 😒 😓 😔 😕 🙃 🤑 😲 ☹️ 🙁 😖 😞 😟 😤 😢 😭 😦 😧 😨 😩 🤯 😬 😰 😱 🥵 🥶 😳 🤪 😵 😡 😠 🤬 😷 🤒 🤕 🤢 🤮 🤧 😇 🤠 🤡 🥳 🥴 🥺 🤥 🤫 🤭 🧐 🤓 😈 👿 👹 👺 💀 👻 👽 🤖 💩 😺 😸 😹 😻 😼 😽 🙀 😿 😾"
	expected1 := true
	if output1 := isAllEmojisAndSpace(input1); output1 != expected1 {
		t.Errorf("Expected %v, but got %v for input: %s", expected1, output1, input1)
	}

	// Test case 2: Emojis and spaces mixed with other characters
	input2 := "Hello 😀 World! 🌍"
	expected2 := false
	if output2 := isAllEmojisAndSpace(input2); output2 != expected2 {
		t.<PERSON>rrorf("Expected %v, but got %v for input: %s", expected2, output2, input2)
	}

	// Test case 3: Empty string
	input3 := ""
	expected3 := true
	if output3 := isAllEmojisAndSpace(input3); output3 != expected3 {
		t.<PERSON><PERSON><PERSON>("Expected %v, but got %v for input: %s", expected3, output3, input3)
	}

	// Test case 4: String with only spaces
	input4 := "     "
	expected4 := true
	if output4 := isAllEmojisAndSpace(input4); output4 != expected4 {
		t.Errorf("Expected %v, but got %v for input: %s", expected4, output4, input4)
	}

	// Test case 5: String with only emojis, no spaces
	input5 := "😀😃😄😁😆😅😂🤣😊😇😉😌😍😘😗😙😚☺️🙂🤗🤩🤔🤨😐😑😶🙄😏😣😥😮🤐😯😪😫😴😌😛😜😝🤤😒😓😔😕🙃🤑😲☹️🙁😖😞😟😤😢😭😦😧😨😩🤯😬😰😱🥵🥶😳🤪😵😡😠🤬😷🤒🤕🤢🤮🤧😇🤠🤡🥳🥴🥺🤥🤫🤭🧐🤓😈👿👹👺💀👻👽🤖💩😺😸😹😻😼😽🙀😿😾"
	expected5 := true
	if output5 := isAllEmojisAndSpace(input5); output5 != expected5 {
		t.Errorf("Expected %v, but got %v for input: %s", expected5, output5, input5)
	}
}
