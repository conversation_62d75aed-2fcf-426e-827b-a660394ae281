package translate

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
)

const (
	langKey     string = "_translate_lang" // lang is the key for the language in the context
	excludesKey string = "_translate_excludes"
)

type Options struct {
	Default  string
	Excludes []string
}

type Option func(*Options)

func WithDefaultLang(lang string) Option {
	return func(o *Options) {
		o.Default = lang
	}
}

func WithExcludesLang(langs ...string) Option {
	return func(o *Options) {
		o.Excludes = langs
	}
}

func Middleware(opts ...Option) gin.HandlerFunc {
	options := &Options{}
	for _, opt := range opts {
		opt(options)
	}

	return func(c *gin.Context) {
		lang := c.GetHeader(app.HdrLang)
		if lang == "" {
			lang = options.Default
		}

		excludes := strings.Split(c.GetHeader(app.HdrLangEx), ",")
		if len(excludes) == 0 {
			excludes = options.Excludes
		}

		lang = i3n.TrimLang(lang)

		excludes = lo.Map(excludes, func(v string, _ int) string {
			return i3n.TrimLang(v)
		})

		c.Set(langKey, lang)
		c.Set(excludesKey, excludes)

		c.Next()
	}
}

func UnWarp(ctx context.Context) (string, []string) {
	lang, _ := ctx.Value(langKey).(string)
	excludes, _ := ctx.Value(excludesKey).([]string)
	return lang, excludes
}
