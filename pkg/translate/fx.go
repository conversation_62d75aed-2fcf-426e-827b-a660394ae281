package translate

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/volc/mt"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/time/rate"
)

func Provide(rc *redi.Client, mc *db.MongoClient, mtc *mt.Client, vnd log.Vendor) *Translator {
	mc.SyncSchema(translatedCollection, 1,
		db.Indexer{
			Name: "text",
			Uniq: lo.ToPtr(true),
			Keys: bson.D{{Key: "text", Value: 1}},
		},
		db.Indexer{
			Name: "last_read_at_read",
			Keys: bson.D{{Key: "last_read_at", Value: 1}, {Key: "read_count", Value: 1}},
		},
		db.Indexer{
			Name: "last_read_at",
			Keys: bson.D{{Key: "last_read_at", Value: 1}},
		},

		db.Indexer{
			Name: "read_count",
			Keys: bson.D{{Key: "read_count", Value: 1}},
		},
	)

	c := &Translator{
		mc:      mc,
		rc:      rc,
		mtc:     mtc,
		limiter: rate.NewLimiter(rate.Every(time.Second/50), 50),
		pool:    co.Apply(co.Size(512), co.LogCost("translate"), co.Named("translate")),
		logger:  vnd.Scope("translate"),
	}

	c.detectedLangCache = cc.New[string, string](1024, cc.LRU, cc.LoaderFunc(func(key string) (string, error) {
		return c.doDetectLang(context.TODO(), key)
	}), cc.ExportStats("detect_lang_cache"))

	c.translationCache = cc.New[translation, *Translated](1024, cc.LRU, cc.LoaderFunc(func(key translation) (*Translated, error) {
		return c.doTranslate(context.TODO(), key.Text, key.TargetLanguage)
	}), cc.ExportStats("translate_cache"))

	return c
}

func Invoke(sch *cron.Scheduler, mc *db.MongoClient, vnd log.Vendor) error {
	cleaner := &Cleaner{
		mc:     mc,
		logger: vnd.Scope("translate_cleaner"),
	}

	task := sch.Exclusive("clean_translated", func(ctx context.Context) error {
		cleaner.Clean(ctx)
		return nil
	})

	if _, err := sch.CronWithSeconds("0 0 0 * * *").Do(task); err != nil {
		return fmt.Errorf("cron clean_translated: %w", err)
	}

	return nil
}
