package ctz

import (
	"fmt"
	"time"
)

var defaultData = CTZ{
	Country:  "ZZ",
	Timezone: time.FixedZone("+00:00", 0),
}

func init() {
	_, offset := time.Now().Zone()
	defaultData.Timezone = time.FixedZone(Zone(offset/3600), offset)
}

func Zone(offset int) string {
	sign := "+"
	if offset < 0 {
		sign = "-"
		offset -= offset * 2
	}
	return fmt.Sprintf("%s%02d:00", sign, offset)
}

func L() *time.Location {
	return defaultData.Timezone
}
