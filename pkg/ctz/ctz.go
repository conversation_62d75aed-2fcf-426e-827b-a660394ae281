package ctz

import (
	"time"
)

func New(country, timezone string) string {
	if country == "" {
		country = defaultData.Country
	}
	if timezone == "" {
		timezone = defaultData.Timezone.String()
	}
	return country + ":" + timezone
}

type CTZ struct {
	Country  string // ISO code
	Timezone *time.Location
}

func (z CTZ) In(time time.Time) time.Time {
	if z.Timezone == nil {
		return time
	}
	return time.In(z.Timezone)
}

func (z CTZ) Now() time.Time {
	if z.Timezone == nil {
		return time.Now()
	}
	return time.Now().In(z.Timezone)
}
