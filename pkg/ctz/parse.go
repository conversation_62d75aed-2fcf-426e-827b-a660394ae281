package ctz

import (
	"strconv"
	"strings"
	"sync"
	"time"
)

var zoneInfo sync.Map

func Parse(in string) CTZ {
	idx := strings.Index(in, ":")
	if idx == -1 {
		return defaultData
	}

	cn, tz := in[:idx], in[idx+1:]
	ctz := CTZ{Country: cn, Timezone: defaultData.Timezone}
	if tz == "" {
		return ctz
	}

	if loc := Timezone(tz); loc != nil {
		ctz.Timezone = loc
	}

	return ctz
}

func Timezone(tz string) *time.Location {
	if tz == "" {
		return defaultData.Timezone
	}

	if v, has := zoneInfo.Load(tz); has {
		return v.(*time.Location)
	}

	var loc *time.Location
	if sign := tz[0]; sign == '+' || sign == '-' {
		hour, _ := strconv.Atoi(tz[1:3])
		offset := hour * 3600
		if sign == '-' {
			offset -= offset * 2
		}
		loc = time.FixedZone(tz, offset)
	} else {
		loc, _ = time.LoadLocation(tz)
	}

	zoneInfo.Store(tz, loc)
	return loc
}
