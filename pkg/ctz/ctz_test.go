package ctz

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestParseIn(t *testing.T) {
	n := time.Now().Truncate(time.Second)
	t.Log(Parse("CN:Asia/Shanghai").In(n))
	t.Log(Parse("ID:Asia/Jakarta").In(n))
	t.<PERSON>g(Parse("CN:+08:00").In(n))
	t.<PERSON>g(Parse("XX:+08:00").In(n))
	t.Log(Parse("XX:-08:00").In(n))
	t.Log(Parse(":+00:00").In(n))
	t.Log(Parse("ZZ:").In(n))
}

func TestTimezone(t *testing.T) {
	assert.Equal(t, Parse(":Asia/Shanghai").Timezone.String(), "Asia/Shanghai")
	assert.Equal(t, Parse(":+08:00").Timezone.String(), "+08:00")
	assert.Equal(t, Parse("").Timezone.String(), "+08:00")
}

func TestDefault(t *testing.T) {
	d1 := Parse("")
	assert.True(t, d1.Timezone == L())
	d2 := Parse("CN:")
	assert.True(t, d2.Timezone == L())
	d3 := Parse("CN:+08:00")
	assert.False(t, d3.Timezone == L())
}

func TestZone(t *testing.T) {
	for i := -12; i <= 14; i++ {
		t.Logf("%d -> %s", i, Zone(i))
	}
}

func BenchmarkNew(b *testing.B) {
	for range b.N {
		_ = New("CN", "Asia/Shanghai")
	}
	b.ReportAllocs()
}

func BenchmarkParse(b *testing.B) {
	b.Run("+00:00", func(b *testing.B) {
		for range b.N {
			Parse("CN:+08:00")
		}
		b.ReportAllocs()
	})
	b.Run("Named", func(b *testing.B) {
		for range b.N {
			Parse("CN:Asia/Shanghai")
		}
		b.ReportAllocs()
	})
}

func BenchmarkLocal(b *testing.B) {
	for range b.N {
		_ = L().String()
	}
	b.ReportAllocs()
}
