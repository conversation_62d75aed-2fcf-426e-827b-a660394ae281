package logic

import (
	"context"

	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/fx"
)

func Provide(ug user.Getter, gc gdk.Client, vnd log.Vendor) (*Core, *Client, gdk.Logic) {
	cli := NewClient(gc, JsonCode())

	cb := func(ctx context.Context, um *gdk.Message, up *user.Profile, msg *Message) context.Context {
		lang := Lang(um.Meta())
		return i3n.Wrap(ctx, lang)
	}

	core := NewLogic(cb, cli, JsonCode(), ug, vnd.Scope("logic.core"))

	return core, cli, core
}

func Invoke(lc fx.Lifecycle, core *Core) {
	lc.Append(fx.Hook{
		OnStop: func(ctx context.Context) error {
			return core.Shutdown(ctx)
		},
	})
}
