package logic

import (
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gtag"
	"golang.org/x/text/language"
)

func High32(n uint64) uint32 {
	return uint32(n >> 32)
}

func Low32(n uint64) uint32 {
	return uint32(n & 0xFFFFFFFF)
}

func Lang(meta map[string]string) string {
	lng := meta[gtag.Lang]
	if lng == "" {
		return "en"
	}

	if v, ok := parseAcceptLanguageCache.Load(lng); ok {
		return v.(string)
	} else {
		if tt, _, _ := language.ParseAcceptLanguage(lng); len(tt) > 0 {
			lang := i3n.TrimLang(tt[0].String())
			parseAcceptLanguageCache.Store(lng, lang)
			return lang
		}
	}

	return "en"
}
