package logic

import (
	"context"
	"errors"
	"net/textproto"
	"sync"

	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/gdk/bus"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gproto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gtag"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/atomic"
)

var ErrResponded = errors.New("request already responded")

type KVPair = gdk.KVPair

type Header map[string]string

func (h Header) Get(key string) string {
	v, ok := h[key]
	if ok {
		return v
	}

	return h[textproto.CanonicalMIMEHeaderKey(key)]
}

type Context struct {
	context.Context
	client       *Client
	codec        Codec
	user         *user.Profile
	raw          *gdk.Message
	peer         *bus.Peer
	tag          KVPair
	meta         KVPair
	request      *Message
	responded    atomic.Bool
	postResponse func()
}

func (c *Context) IsAnonymous() bool {
	return c.user == nil
}

func (c *Context) Profile() *user.Profile {
	return c.user
}

func (c *Context) Raw() *gdk.Message {
	return c.raw
}

func (c *Context) Peer() *bus.Peer {
	return c.peer
}

func (c *Context) Meta() Header {
	return Header(c.meta)
}

func (c *Context) Tags() KVPair {
	return c.tag
}

func (c *Context) DeviceId() string {
	return c.Meta().Get(app.HdrDeviceId)
}

func (c *Context) DeviceType() string {
	return c.Meta().Get(app.HdrDeviceType)
}

func (c *Context) BuildId() string {
	return c.Meta().Get(app.HdrBuildId)
}

func (c *Context) Version() string {
	return c.Meta().Get(app.HdrVersion)
}

func (c *Context) Country() string {
	return c.Meta().Get(app.HdrCountry)
}

func (c *Context) Timezone() string {
	return c.Meta().Get(app.HdrTimezone)
}

var (
	parseAcceptLanguageCache sync.Map
)

func (c *Context) Lang() string {
	return Lang(c.meta)
}

func (c *Context) RoomId() string {
	return c.raw.Tags()[gtag.RoomId]
}

func (c *Context) SetRoomId(roomId string) error {
	if roomId != "" {
		return toError(c.raw.SetOption(gdk.KVPair{gtag.RoomId: roomId}, nil))
	}
	return toError(c.raw.SetOption(gdk.KVPair{gtag.RoomId: ""}, nil))
}

func (c *Context) Request() *Message {
	return c.request
}

func (c *Context) Bind(out any) error {
	return c.codec.Unmarshal(c.request.Payload, out)
}

func (c *Context) Response(m any) error {
	if c.responded.CompareAndSwap(false, true) {
		return c.doSend(c.request.Seq, c.request.MsgType, m)
	}
	return ErrResponded
}

func (c *Context) Error(m any) error {
	switch t := m.(type) {
	case *biz.Error:
		return c.doSend(c.request.Seq, c.request.MsgType, &protocol.Error{Code: t.Code, Msg: t.Msg})
	case error:
		var be *biz.Error
		if errors.As(t, &be) {
			return c.doSend(c.request.Seq, c.request.MsgType, &protocol.Error{Code: be.Code, Msg: be.Msg})
		} else {
			return c.doSend(c.request.Seq, c.request.MsgType, &protocol.Error{Code: biz.ErrSystem, Msg: t.Error()})
		}
	default:
		return c.doSend(c.request.Seq, c.request.MsgType, t)
	}
}

// Notify 下发通知（给当前用户）
func (c *Context) Notify(t protocol.MsgType, m any) error {
	protocol.ResetEmptyId(m)

	bs, err := c.codec.Marshal(m)
	if err != nil {
		return err
	}

	p := protocol.Packet{
		MsgType:  uint32(t),
		Sequence: 0,
		Payload:  bs,
	}
	return toError(c.raw.Response((&gproto.Packet{Payload: p.Encode()}).Encode()))
}

func (c *Context) doSend(seq uint32, t protocol.MsgType, m any, flags ...uint32) error {
	bs, err := c.codec.Marshal(m)
	if err != nil {
		return err
	}

	p := protocol.Packet{
		MsgType:  uint32(t),
		Sequence: seq,
		Payload:  bs,
	}

	for _, f := range flags {
		p.Flags |= f
	}

	c.responded.Store(true)
	if c.postResponse != nil {
		c.postResponse()
	}

	return toError(c.raw.Response((&gproto.Packet{Payload: p.Encode()}).Encode()))
}

// Broadcast 下发广播
//func (c *Context) Broadcast(to bus.Target, mt protocol.MsgType, m any) error {
//	return c.client.Broadcast(c, to, mt, m)
//}

// SetOption 设置用户信息
//func (c *Context) SetOption(tag, meta KVPair) error {
//	return c.raw.SetOption(tag, meta)
//}
