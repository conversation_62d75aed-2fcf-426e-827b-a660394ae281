package logic

import (
	"github.com/bytedance/sonic"
)

type Codec interface {
	Marshal(message any) ([]byte, error)
	Unmarshal([]byte, any) error
}

type jsonCodec struct{}

func (j *jsonCodec) Marshal(message any) ([]byte, error) {
	return sonic.Marshal(message)
}

func (j *jsonCodec) Unmarshal(bs []byte, message any) error {
	return sonic.Unmarshal(bs, message)
}

func JsonCode() Codec {
	return &jsonCodec{}
}
