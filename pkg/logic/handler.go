package logic

type TypeHandler interface {
	Handler(ctx *Context) error
}

type TypeHandlerFunc func(ctx *Context) error

func (t TypeHandlerFunc) Handler(ctx *Context) error {
	return t(ctx)
}

func GenericHandler[T any](f func(*T) error) TypeHandler {
	return TypeHandlerFunc(func(ctx *Context) error {
		var req T
		if err := ctx.Bind(&req); err != nil {
			return err
		}

		return f(&req)
	})
}

func GenericHandler2[T any](f func(ctx *Context, t *T) error) TypeHandler {
	return TypeHandlerFunc(func(ctx *Context) error {
		var req T
		if err := ctx.Bind(&req); err != nil {
			return err
		}

		return f(ctx, &req)
	})
}
