package logic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/pkg/errors"
	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/ob/meter"
	"go.uber.org/atomic"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

// ContextBuilder generate context from upload message or something else to spread information, just like: trace id 、span id
type ContextBuilder func(ctx context.Context, um *gdk.Message, up *user.Profile, msg *Message) context.Context

var todoBuilder = func(ctx context.Context, um *gdk.Message, up *user.Profile, msg *Message) context.Context {
	return ctx
}

type Core struct {
	rw             sync.RWMutex
	contextBuilder ContextBuilder
	uGetter        user.Getter
	sessions       sync.Map
	handlers       map[protocol.MsgType][]TypeHandler
	pending        atomic.Int32
	logger         *zap.Logger
	client         *Client
	codec          Codec
	// metrics
	reqCnt          meter.Counter[int]
	reqHandleCost   meter.Histogram[int64]
	reqResponseCost meter.Histogram[int64]
}

func NewLogic(cb ContextBuilder, client *Client, codec Codec, ug user.Getter, l *zap.Logger) *Core {
	if cb == nil {
		cb = todoBuilder
	}

	if codec == nil {
		codec = JsonCode()
	}
	return &Core{
		contextBuilder:  cb,
		uGetter:         ug,
		handlers:        make(map[protocol.MsgType][]TypeHandler),
		logger:          l,
		client:          client,
		codec:           codec,
		reqCnt:          meter.NewCounter[int]("osl.logic.request.count"),
		reqHandleCost:   meter.NewHistogram[int64]("osl.logic.handle.cost"),
		reqResponseCost: meter.NewHistogram[int64]("osl.logic.response.cost"),
	}
}

func (l *Core) OnMessage(ctx context.Context, msg *gdk.Message) error {
	// l.logger.Debug("on message",
	// 	zap.Bool("public", msg.Tags()[gtag.UserId] == ""),
	// 	zap.Any("meta", msg.Meta()),
	// 	zap.Any("tags", msg.Tags()),
	// 	zap.Any("type", msg.Type()),
	// 	zap.ByteString("payload", msg.Payload()),
	// )

	profile, err := l.uGetter.Session(ctx, msg.Tags(), msg.Meta())

	switch msg.Type() {
	case gdk.EvMessage:
		if err != nil {
			return err
		}
		l.Process(ctx, msg, profile)
	case gdk.EvConnected:
		if err != nil && !errors.Is(err, user.ErrMissingUserId) {
			return fmt.Errorf("OnMessage: EvConnected get user profile failed: %w", err)
		}

		l.onEvent(ctx, msg, profile, &Message{MsgType: protocol.EventConnected})
	case gdk.EvDisconnect:
		if err != nil && !errors.Is(err, user.ErrMissingUserId) {
			return fmt.Errorf("OnMessage: EvDisconnect get user profile failed: %w", err)
		}
		l.onEvent(ctx, msg, profile, &Message{MsgType: protocol.EventDisconnected})
	}

	return nil
}

func (l *Core) Process(ctx context.Context, um *gdk.Message, profile *user.Profile) {
	packet, err := protocol.DecodePacket(um.Payload())
	if err != nil {
		l.logger.Error("decode packet", zap.Error(err), zap.ByteString("payload", um.Payload()))
		return
	}

	l.doCall(ctx, um, profile, &Message{MsgType: protocol.MsgType(packet.MsgType), Seq: packet.Seq(), Payload: packet.Payload})
}

func (l *Core) Register(t protocol.MsgType, handler TypeHandler) {
	l.rw.Lock()

	exist := l.handlers[t]
	if t.IsRPC() {
		if len(exist) > 0 {
			l.logger.Warn("type handler has registered!!!", zap.String("type", t.String()))
			delete(l.handlers, t)
		}
	}

	l.handlers[t] = append(l.handlers[t], handler)
	l.logger.Debug("register type handler", zap.String("type", t.String()))
	l.rw.Unlock()
}

func (l *Core) doCall(ctx context.Context, um *gdk.Message, up *user.Profile, msg *Message) {
	l.pending.Add(1)
	defer l.pending.Sub(1)

	//l.logger.Debug(
	//	"do call",
	//	zap.Any("meta", um.Meta()),
	//	zap.Any("tags", um.Tags()),
	//	zap.Any("type", msg.MsgType.String()),
	//	zap.ByteString("payload", um.Payload()),
	//)

	rpc := msg.MsgType.String()
	l.reqCnt.Add(1, "rpc", rpc)
	start := time.Now()
	defer func() { l.reqHandleCost.Record(time.Since(start).Milliseconds(), "rpc", rpc) }()

	l.rw.RLock()
	handlers := l.handlers[msg.MsgType]
	l.rw.RUnlock()

	if len(handlers) == 0 {
		return
	}

	req := &Context{
		Context: l.contextBuilder(ctx, um, up, msg),
		request: msg,
		raw:     um,
		peer:    um.Peer(),
		tag:     um.Tags(),
		meta:    um.Meta(),
		user:    up,
		client:  l.client,
		codec:   l.codec,
		postResponse: func() {
			l.reqResponseCost.Record(time.Since(start).Milliseconds(), "rpc", rpc)
		},
	}

	if err := handlers[0].Handler(req); err != nil {
		var be *biz.Error
		if !errors.As(err, &be) {
			ff := make([]zap.Field, 0, len(up.Fields())+2)
			ff = append(ff, up.Fields()...)
			ff = append(ff, zap.String("payload.type", msg.MsgType.String()))
			ff = append(ff, zap.Error(err))
			if biz.IsSilenceError(err) {
				l.logger.Info("handle msg", ff...)
			} else {
				l.logger.Error("handle msg", ff...)
			}
		}

		if !req.responded.Load() && !msg.MsgType.IsEvent() {
			_ = req.Error(err)
		}
	} else {
		if !req.responded.Load() && !msg.MsgType.IsEvent() {
			//panic("call without response: " + msg.MsgType.String())
			req.Response(&protocol.Error{Code: 0, Msg: "ok"})
		}
	}
}

func (l *Core) onEvent(ctx context.Context, um *gdk.Message, up *user.Profile, msg *Message) {
	l.pending.Add(1)
	defer l.pending.Sub(1)

	l.rw.RLock()
	handlers := l.handlers[msg.MsgType]
	l.rw.RUnlock()

	if len(handlers) == 0 {
		return
	}

	req := &Context{
		Context: l.contextBuilder(ctx, um, up, msg),
		request: msg,
		raw:     um,
		peer:    um.Peer(),
		tag:     um.Tags(),
		meta:    um.Meta(),
		user:    up,
		client:  l.client,
		codec:   l.codec,
	}

	for _, handler := range handlers {
		if err := handler.Handler(req); err != nil {
			var be *biz.Error
			if !errors.As(err, &be) {
				ff := make([]zap.Field, 0, len(up.Fields())+2)
				ff = append(ff, up.Fields()...)
				ff = append(ff, zap.String("payload.type", msg.MsgType.String()))
				ff = append(ff, zap.Error(err))
				if biz.IsSilenceError(err) {
					l.logger.Info("handle event", ff...)
				} else {
					l.logger.Error("handle event", ff...)
				}
			} else {
				l.logger.Info("handle event", zap.Error(err))
			}
		}
	}
}

func (l *Core) Shutdown(ctx context.Context) error {
	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			if l.pending.Load() == 0 {
				return nil
			}
		case <-time.After(time.Second):
			l.logger.Info("waiting logic core...", zap.Int32("pending", l.pending.Load()))
		}
	}
}
