package logic

import (
	"testing"

	"gitlab.sskjz.com/overseas/live/osl/pkg/gproto"
)

func TestHigh32(t *testing.T) {
	var x uint64 = 1<<32 | 3
	if High32(x) != 1 {
		t.<PERSON><PERSON><PERSON>("High32(%d) = %d, expected %d", x, High32(x), 1)
	}

	var y uint64 = 1<<32 | 3
	if High32(y) != 1 {
		t.<PERSON><PERSON>("High32(%d) = %d, expected %d", y, High32(y), 1)
	}
}

func TestLow32(t *testing.T) {
	var x uint64 = 1<<32 | 3
	if Low32(x) != 3 {
		t.<PERSON><PERSON><PERSON>("Low32(%d) = %d, expected %d", x, Low32(x), 3)
	}

	var y uint64 = 1<<32 | 4
	if Low32(y) != 4 {
		t.<PERSON><PERSON>rf("Low32(%d) = %d, expected %d", y, Low32(y), 4)
	}
}

func TestRevFlg(t *testing.T) {
	{
		var flag uint32
		flag ^= gproto.FlagInRoom
		if flag != gproto.FlagInRoom {
			t.Errorf("RevFlg(%d) = %d, expected %d", flag, flag, gproto.FlagInRoom)
		}
	}
	{

		var flag uint32
		flag |= gproto.FlagInRoom
		flag ^= gproto.FlagInRoom
		if flag != 0 {
			t.Errorf("RevFlg(%d) = %d, expected %d", flag, flag, 0)
		}

	}
}
