package logic

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strconv"

	"github.com/valyala/bytebufferpool"
	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/gdk/bus"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gproto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gtag"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

const (
	connectionLost = "connection lost"
)

var ErrConnectionLost = biz.SilenceError(errors.New(connectionLost))

func toError(err error) error {
	if err == nil {
		return nil
	}

	if err.Error() == connectionLost {
		return ErrConnectionLost
	}
	return err
}

func IsConnectionLost(err error) bool {
	if err == nil {
		return false
	}

	return errors.Is(err, ErrConnectionLost) || err.Error() == connectionLost
}

type Client struct {
	gdk.Client
	codec Codec
}

func NewClient(client gdk.Client, codec Codec) *Client {
	if codec == nil {
		codec = &jsonCodec{}
	}
	return &Client{Client: client, codec: codec}
}

func (c *Client) SetOption(ctx context.Context, peer *bus.Peer, tags, meta KVPair) error {
	return toError(c.Client.SetOption(ctx, peer, tags, meta))
}

func (c *Client) CloseConn(ctx context.Context, peer *bus.Peer, code int, msg string) error {
	return toError(c.Client.CloseConn(ctx, peer, code, msg))
}

func (c *Client) Unicast(ctx context.Context, peer *bus.Peer, mt protocol.MsgType, payload any, flag uint64) error {
	_unicast.Add(1, "mt", strconv.Itoa(int(mt)))

	buf := bytebufferpool.Get()
	defer bytebufferpool.Put(buf)

	if err := c.WritePackData(buf, mt, payload, flag); err != nil {
		return fmt.Errorf("write pack data failed: %w", err)
	}

	return toError(c.Client.Unicast(ctx, peer, buf.Bytes()))
}

func (c *Client) Broadcast(ctx context.Context, to bus.Target, mt protocol.MsgType, payload any, flags ...uint64) error {
	_broadcast.Add(1, "mt", strconv.Itoa(int(mt)))
	buf := bytebufferpool.Get()
	defer bytebufferpool.Put(buf)

	var flag uint64
	for _, f := range flags {
		flag |= f
	}

	if err := c.WritePackData(buf, mt, payload, flag); err != nil {
		return fmt.Errorf("write pack data failed: %w", err)
	}

	return c.Client.Broadcast(ctx, to, buf.Bytes())
}

func (c *Client) WritePackData(w io.Writer, mt protocol.MsgType, payload any, flag uint64) error {
	data, err := c.p2b(payload)
	if err != nil {
		return fmt.Errorf("pack data failed: %w", err)
	}

	packet := &protocol.Packet{
		MsgType: uint32(mt),
		Flags:   Low32(flag),
		Payload: data,
	}

	gf := High32(flag)
	gf ^= gproto.FlagInRoom

	if err := (&gproto.Packet{Flags: gf}).WriteTo(w, packet.Size()); err != nil {
		return err
	}

	return packet.WriteTo(w)
}

func (c *Client) p2b(payload any) ([]byte, error) {
	if payload == nil {
		return nil, nil
	}

	if b, ok := payload.([]byte); ok {
		return b, nil
	}

	return c.codec.Marshal(payload)
}

func (c *Client) SendUsersMsg(ctx context.Context, mt protocol.MsgType, data any, flag uint64, players ...string) error {
	_unicast.Add(1, "mt", strconv.Itoa(int(mt)))
	buf := bytebufferpool.Get()
	defer bytebufferpool.Put(buf)

	if err := c.WritePackData(buf, mt, data, flag); err != nil {
		return fmt.Errorf("write pack data failed: %w", err)
	}

	var (
		me []error
		bs = buf.Bytes()
	)
	for _, player := range players {
		if err := c.Client.Broadcast(ctx, bus.NewTag(gtag.UserId, player), bs); err != nil {
			me = append(me, fmt.Errorf("broadcast to user %s msg failed: %w", player, err))
		}
	}

	if len(me) > 0 {
		return errors.Join(me...)
	}

	return nil
}

func (c *Client) SendRoomMsg(ctx context.Context, mt protocol.MsgType, data any, flag uint64, rooms ...string) error {
	buf := bytebufferpool.Get()
	defer bytebufferpool.Put(buf)

	if err := c.WritePackData(buf, mt, data, flag); err != nil {
		return fmt.Errorf("write pack data failed: %w", err)
	}

	var (
		me []error
		bs = buf.Bytes()
	)

	for _, r := range rooms {
		if err := c.Client.Broadcast(ctx, bus.NewTag(gtag.RoomId, r), bs); err != nil {
			me = append(me, fmt.Errorf("broadcast to room %s msg failed: %w", r, err))
		}
	}

	if len(me) > 0 {
		return errors.Join(me...)
	}

	return nil
}
