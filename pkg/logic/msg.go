package logic

import (
	"strconv"
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

type Message struct {
	MsgType protocol.MsgType
	Seq     uint32
	Payload []byte
}

func (m *Message) String() string {
	var sb strings.Builder
	sb.WriteString("Message{")
	sb.WriteString("MsgType: ")
	sb.WriteString(m.MsgType.String())
	sb.WriteString(", Seq: ")
	sb.WriteString(strconv.Itoa(int(m.Seq)))
	sb.WriteString(", Payload: ")
	sb.WriteString(string(m.Payload))
	sb.WriteString("}")
	return sb.String()
}
