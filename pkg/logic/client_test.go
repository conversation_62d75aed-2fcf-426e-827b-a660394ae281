package logic

import (
	"errors"
	"fmt"
	"testing"

	"github.com/valyala/bytebufferpool"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gproto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

func TestPacketW(t *testing.T) {
	for range 3 {
		buf := bytebufferpool.Get()
		pkt := &protocol.Packet{
			MsgType:  uint32(protocol.RpcPing),
			Sequence: 123,
			Flags:    protocol.FlagAck,
			Payload:  []byte("hello"),
		}
		_ = (&gproto.Packet{Flags: gproto.FlagInRoom}).WriteTo(buf, pkt.Size())
		_ = pkt.WriteTo(buf)
		p1, _ := gproto.Decode(buf.Bytes())
		t.Logf("p1: %+v", p1)
		p2, _ := protocol.DecodePacket(p1.Payload)
		t.Logf("p2: %+v", p2)
		bytebufferpool.Put(buf)
	}
}
func TestToError(t *testing.T) {
	err := errors.New("connection lost")
	result := toError(err)
	if result != ErrConnectionLost {
		t.Errorf("Expected error: %v, got: %v", ErrConnectionLost, result)
	}

	err = errors.New("some other error")
	result = toError(err)
	if result != err {
		t.Errorf("Expected error: %v, got: %v", err, result)
	}

	err = nil
	result = toError(err)
	if result != nil {
		t.Errorf("Expected error: %v, got: %v", nil, result)
	}
}

func Test_isConnectionLost(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "connection lost",
			args: args{err: ErrConnectionLost},
			want: true,
		},
		{
			name: "connection lost",
			args: args{err: errors.New("connection lost")},
			want: true,
		},
		{
			name: "wrapped connection lost",
			args: args{err: fmt.Errorf("wrapped: %w", ErrConnectionLost)},
			want: true,
		},
		{
			name: "ml wrapped connection lost",
			args: args{err: fmt.Errorf("%w", fmt.Errorf("%w", ErrConnectionLost))},
			want: true,
		},
		{
			name: "ml wrapped connection lost2",
			args: args{err: fmt.Errorf("wrapped: %w", fmt.Errorf("wrapped: %w", ErrConnectionLost))},
			want: true,
		},
		{
			name: "nil",
			args: args{err: nil},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsConnectionLost(tt.args.err); got != tt.want {
				t.Errorf("isConnectionLost() = %v, want %v", got, tt.want)
			}
		})
	}
}
