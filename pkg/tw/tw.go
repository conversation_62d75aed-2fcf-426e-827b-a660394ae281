package tw

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gitlab.sskjz.com/go/redi"
)

func Incr(ctx context.Context, rc *redi.Client, key string, window time.Duration, threshold int64) (bool, error) {
	now := time.Now()

	tx := rc.TxPipeline()
	tx.ZAdd(ctx, key, redis.Z{Score: float64(now.UnixMilli()), Member: uuid.NewString()})
	tx.Expire(ctx, key, 2*window)
	tx.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", now.Add(-window).UnixMilli()))
	zc := tx.ZCard(ctx, key)
	_, err := tx.Exec(ctx)
	if err != nil {
		return false, err
	}

	nv, err := zc.Result()
	if err != nil {
		return false, err
	}
	return nv >= threshold, nil
}

func Exceed(ctx context.Context, rc *redi.Client, key string, window time.Duration, threshold int64) (bool, error) {
	now := time.Now()
	tx := rc.TxPipeline()
	tx.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", now.Add(-window).UnixMilli()))
	c := rc.ZCard(ctx, key)
	if _, err := tx.Exec(ctx); err != nil {
		return false, err
	}

	return c.Val() >= threshold, nil
}

func Count(ctx context.Context, rc *redi.Client, key string, window time.Duration) (int64, error) {
	now := time.Now()
	tx := rc.TxPipeline()
	tx.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", now.Add(-window).UnixMilli()))
	c := rc.ZCard(ctx, key)
	if _, err := tx.Exec(ctx); err != nil {
		return 0, err
	}

	return c.Result()
}
