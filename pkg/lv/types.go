package lv

type Info struct {
	Level    int // 当前用户等级
	TotalExp int // 总获得经验值
	GradeMax int // 下一等级需求
	CurrExp  int // 当前等级经验值
	CurrMax  int // 当前等级最大经验值
}

func Make(grades []int, exp int) *Info {
	ret := &Info{
		Level:    Lv(grades, exp),
		TotalExp: exp,
	}

	var gradeBase int
	gradeBase, ret.GradeMax = Exp(grades, ret.Level)
	if ret.GradeMax > 0 {
		ret.CurrExp = ret.TotalExp - gradeBase
		ret.CurrMax = ret.GradeMax - gradeBase
	} else if ret.Level == 0 {
		ret.CurrMax, _ = Exp(grades, 1)
		ret.GradeMax = ret.CurrMax
	} else { // max level
		ret.TotalExp = gradeBase
		ret.CurrExp = gradeBase
		ret.CurrMax = gradeBase
		ret.GradeMax = gradeBase
	}

	return ret
}
