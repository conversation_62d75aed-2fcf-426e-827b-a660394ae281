package lv

import (
	"testing"
)

func TestLvInfo(t *testing.T) {
	tests := [][]int{
		{0, 3, 5, 7},
		{1, 3, 5, 7},
	}
	for tt, grades := range tests {
		gMax := grades[len(grades)-1]
		for exp := 0; exp <= gMax+1; exp++ {
			v := Make(grades, exp)
			f := "%d lv=%d exp: %d/%d ~ %d/%d raw %d"
			a := []any{tt, v.Level, v.CurrExp, v.CurrMax, v.TotalExp, v.GradeMax, exp}
			if v.TotalExp == v.GradeMax && v.TotalExp != gMax {
				t.<PERSON>("!"+f, a...)
			} else {
				t.Logf("*"+f, a...)
			}
		}
	}
}
