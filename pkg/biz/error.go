package biz

import (
	"errors"
	"fmt"
)

type Error struct {
	Code int
	Msg  string
}

func (e *Error) ErrCode() int {
	return e.Code
}

func (e *Error) ErrMsg() string {
	return e.Msg
}

func (e *Error) Error() string {
	return fmt.Sprintf("%d: %s", e.Code, e.Msg)
}

func MakeError(code int) *Error {
	return &Error{Code: code}
}

func NewError(code int, msg string) *Error {
	return &Error{Code: code, Msg: msg}
}

func Legacy(msg string) *Error {
	return &Error{Code: ErrBusiness, Msg: msg}
}

func Is(err error) bool {
	var biz *Error
	return errors.As(err, &biz)
}

func As(err error) (*Error, bool) {
	var biz *Error
	if errors.As(err, &biz) {
		return biz, true
	}
	return nil, false
}

type doNotRetryError struct {
	error
}

func DoNotRetryError(err error) error {
	return &doNotRetryError{err}
}

func IsDoNotRetryError(err error) bool {
	var doNotRetryError *doNotRetryError
	ok := errors.As(err, &doNotRetryError)
	return ok
}

type silenceError struct {
	error
}

func (s *silenceError) Unwrap() error {
	return s.error
}

func SilenceError(err error) error {
	return &silenceError{err}
}

func IsSilenceError(err error) bool {
	var silenceError *silenceError
	ok := errors.As(err, &silenceError)
	return ok
}
