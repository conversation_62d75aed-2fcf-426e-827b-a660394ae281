// go generate stringer --type=MsgType
package protocol

type MsgType uint32

const (
	rpcBase    MsgType = 30000
	notifyBase         = 90000
)

const (
	EventConnected           MsgType = 1
	EventDisconnected        MsgType = 2
	EventConnHeartbeat       MsgType = 3
	RpcPing                  MsgType = 30000 + 0
	RpcJoinRoom              MsgType = 30000 + 1
	RpcLeaveRoom             MsgType = 30000 + 2
	RpcHeartbeat             MsgType = 30000 + 3  // 心跳
	NotifyRoomEnter          MsgType = 90000 + 1  // 进入消息
	NotifyRoomLeave          MsgType = 90000 + 2  // 离开消息
	NotifyRoomChat           MsgType = 90000 + 3  // 聊天消息
	NotifyRoomGift           MsgType = 90000 + 4  // 礼物消息
	NotifyRoomStatus         MsgType = 90000 + 5  // 直播间状态
	NotifyRoomLike           MsgType = 90000 + 6  // 点赞消息
	NotifyRoomFollow         MsgType = 90000 + 7  // 关注消息
	NotifySystemMessage      MsgType = 90000 + 8  // 系统消息
	NotifyGlobalRoomGift     MsgType = 90000 + 10 // 全站送礼通知
	NotifyUserLvUp           MsgType = 90000 + 11 // 用户等级提升通知
	NotifyGlobalGiftLuckDraw MsgType = 90000 + 12 // 幸运礼物中奖全站通知
	NotifyUserKicked         MsgType = 90000 + 13 // 用户被踢出房间通知
	NotifyPKConfirm          MsgType = 90000 + 14 // PK确认通知
	NotifyPKConfirmResult    MsgType = 90000 + 15 // PK确认结果通知(对方回复)
	// NotifyRoomPKStatus       MsgType = 90000 + 16 // PK状态通知，废弃，数据通过roomstatusnotify携带
	NotifyPKInviterCancel       MsgType = 90000 + 17 // PK邀请者取消PK
	NotifyPKReadyAsk            MsgType = 90000 + 18 // PK配对成功,需要进入匹配状态,需要用户发rdy确认
	NotifyRankTopUpdate         MsgType = 90000 + 19 // 排行榜更新
	NotifyRoomIntroduction      MsgType = 90000 + 20 // 直播间简介
	NotifyGiftComboFinish       MsgType = 90000 + 21 // 礼物连击结束
	NotifyUserMuteStatus        MsgType = 90000 + 22 // 用户禁言状态
	NotifyInviteLink            MsgType = 90000 + 23 // 邀请连线
	NotifyLinkAskReady          MsgType = 90000 + 24 // 连线确认请求
	NotifyLinkMsg               MsgType = 90000 + 26 // 连线提示消息
	NotifyUserPlayingGame       MsgType = 90000 + 27 // 用户正在玩游戏
	NotifyLuckyRoomGiftLuckDraw MsgType = 90000 + 28 // 幸运礼物房间的中奖消息
	NotifyGameWinning           MsgType = 90000 + 29 // 游戏胜利
	NotifyFlyText               MsgType = 90000 + 30 // 飞字
	NotifySticker               MsgType = 90000 + 31 // 直播间贴纸信息
	NotifyUserFansLevelUp       MsgType = 90000 + 32 // 用户粉丝团等级提升
	NotifyUserJoinFansclub      MsgType = 90000 + 33 // 用户加入粉丝团
	NotifyUserGiftLuckDraw      MsgType = 90000 + 34 // 用户中奖消息
	NotifyUserFansActive        MsgType = 90000 + 35 // 用户粉丝团激活
	NotifyRoomSticker           MsgType = 90000 + 36 // 房间贴纸消息
)

func (m MsgType) IsEvent() bool {
	return m < rpcBase
}

func (m MsgType) IsRPC() bool {
	return m >= rpcBase && m < notifyBase
}
