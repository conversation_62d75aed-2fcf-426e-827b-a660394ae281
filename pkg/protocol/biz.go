package protocol

import (
	"encoding/json"
	"strings"

	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

type LStatus uint64

const (
	LStatusLive       LStatus = 1 << 0
	LStatusPK         LStatus = 1 << 1
	LStatusPKFighting LStatus = 1 << 2
	LStatusPKPublish  LStatus = 1 << 3
	LStatusPKLinked   LStatus = 1 << 4
	LStatusLink       LStatus = 1 << 5 // 连线中
)

func (s LStatus) String() string {
	var sb strings.Builder
	if s&LStatusLive > 0 {
		sb.WriteString("Live ")
	}
	if s&LStatusPK > 0 {
		sb.WriteString("PK ")
	}
	if s&LStatusPKFighting > 0 {
		sb.WriteString("PKFighting ")
	}

	if s&LStatusPKPublish > 0 {
		sb.WriteString("PKPublish ")
	}

	if s&LStatusPKLinked > 0 {
		sb.WriteString("PKLinked ")
	}

	if s&LStatusLink > 0 {
		sb.WriteString("Linked ")
	}

	return sb.String()
}

type Error struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type Response[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

type User struct {
	ShowId   string    `json:"showId"`
	UserId   string    `json:"userId"`
	Nickname string    `json:"nickname"`
	Level    int       `json:"level"`
	NumId    int64     `json:"numId"`
	Avatar   string    `json:"avatar,omitempty"` // 头像，不需要的时候不返回该字段
	Gender   int       `json:"gender"`           // 性别：1男2女
	Role     user.Role `json:"role,omitempty"`   // 用户角色（空值为null）
	UserDress
}

type UserDress struct {
	AvatarBorder string `json:"avatarBorder,omitempty"` // 头像框（空值为null）
}

type RoomUser struct {
	FansLevel int    `json:"fansLevel,omitempty"` // 粉丝团等级（不是粉丝团成员时,没有这个字段）
	BadgeUrl  string `json:"badgeUrl,omitempty"`  // 勋章图片地址（没有时没有这个字段）https://icon/file?w=120&h=100
}

// Gift same as api types.Gift
type Gift struct {
	Id       uint   `json:"id"`       // 道具id
	EffectId uint   `json:"effectId"` // 特效资源ID
	Name     string `json:"name"`     // 道具名称
	Diamond  int    `json:"diamond"`  // 价值钻石
	Describe string `json:"describe"` // 送出描述，e.g. 送出粉丝团灯牌
	ImageUrl string `json:"imageUrl"` // 道具图标资源地址
}

type LuckyScore struct {
	Increment int64 `json:"increment"` // 增加的幸运值
	Current   int64 `json:"current"`   // 当前幸运值
}

type SendGiftNotify struct {
	Id         string          `json:"id"`
	RoomId     string          `json:"roomId"`
	User       User            `json:"user"`
	RoomUser   RoomUser        `json:"roomUser"`
	ToUser     User            `json:"toUser"`
	GiftId     int             `json:"giftId"`
	Gift       *Gift           `json:"gift"`
	GiftCount  int             `json:"giftCount"`
	ComboId    string          `json:"comboId"`
	Prizes     []LuckDrawPrize `json:"prizes"`
	TotalCount int             `json:"totalCount"`
	LuckyScore LuckyScore      `json:"luckyScore"` // 幸运值
	TagUrl     string          `json:"tagUrl"`     // 标记1
	SourceId   string          `json:"sourceId"`   // 非空时客户端需要处理
	// features
	BlindBoxId *int  `json:"blindBoxId,omitempty"` // 盲盒礼物ID
	BlindBox   *Gift `json:"blindBox,omitempty"`   // 盲盒礼物信息
}

type Vehicle struct {
	TemplateId string                       `json:"templateId"`         // 模板id
	Data       map[string]map[string]string `json:"data"`               // 数据 obj, map[key]map[lang]string
	EffectId   int                          `json:"effectId,omitempty"` // 特效资源ID（没有为null）
}

type JoinRoomNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	/*
		进场特效优先级:
		0、默认
		1、等级25级，进入直播间≤100人的直播间（进场特效弱）最优先5
		2、等级32级，进入全直播间（进场特效强）最优先4
		3、开通本直播间会员，进入本直播间（会员进场特效）最优先1
		4、粉丝团10级，本直播间≤10000人可用最优先3
		5、粉丝团11级，本直播间可用最优先2
	*/
	Type int `json:"type"`

	/*
		进场特效优先级:
		0、默认
		1、等级25级，初级进场特效，优先级6
		2、等级32级，中级进场特效，优先级5
		3、等级51级，高级进场特效，优先级4
		4、粉丝团6级，初级进场特效，优先级3
		5、粉丝团12级，中级进场特效，优先级2
		6、粉丝团20级，高级进场特效，优先级1
		...
		999、座驾
	*/
	Type2 int `json:"type2"` // 进场特效类型(新版本适用)

	Vehicle *Vehicle `json:"vehicle,omitempty"` // 座驾信息（没有为null）
}

type RoomChatNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	RefUser  *User    `json:"refUser"` // 被@的用户, 如果为null则没有@用户
	Text     string   `json:"text"`
	Sticker  string   `json:"sticker"` // 贴纸id
	Quotes   []User   `json:"quotes"`  // 引用的消息id
}

// NotifyRoomSticker           MsgType = 90000 + 36 // 房间贴纸消息
type RoomStickerNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	Sticker  string   `json:"sticker"` // 贴纸id
}

type RankUser struct {
	User
	Rank  int   `json:"rank"`
	Score int64 `json:"score"`
}

type PKAnchor struct {
	User    User       `json:"user"`
	RoomId  string     `json:"roomId"`
	Paused  bool       `json:"paused"`
	Score   int64      `json:"score"`
	Top     []RankUser `json:"top"`
	Winning int        `json:"winning"` // 连胜次数 >=2 需要展示
}

// NotifyRoomPKStatus,不主动下发，➡️roomstatusnotify携带
type RoomPKStatusNotify struct {
	SessionId        string     `json:"sessionId"`
	Anchors          []PKAnchor `json:"anchors"`          // pk参与的主播信息
	OngoingCountdown int        `json:"ongoingCountdown"` // pk fight倒计时
	PublishCountdown int        `json:"publishCountdown"` // pk结束时间
	Winner           string     `json:"winner"`           // 赢家
	LinkedAt         int64      `json:"linkedAt"`         // 连线时间戳
	LinkedFinishAt   int64      `json:"linkedFinishAt"`   // 连线结束时间戳
}

type LinkedUser struct {
	User   User   `json:"user"`
	RtcUid string `json:"rtcUid"`
	Mute   bool   `json:"mute"`  // 是否禁音
	Score  int64  `json:"score"` // 贡献值
}

type RoomLinkStatus struct {
	Allow       bool         `json:"allow"`       // 是否允许连线
	Requesters  []string     `json:"requesters"`  // 连线请求者
	Linked      []LinkedUser `json:"linked"`      // 已连线用户,按加入时间排序，加入最早的在第一个
	AnchorScore int64        `json:"anchorScore"` // 主播场次贡献值
}

type RankTopUser struct {
	User
	FansLevel int `json:"fansLevel,omitempty"` // 粉丝团等级（不是粉丝团成员时,没有这个字段）
}

// NotifyRoomStatus 直播间状态通知
type RoomStatusNotify struct {
	Id             string              `json:"id"`
	RoomId         string              `json:"roomId"`
	SessionId      string              `json:"sessionId"`      // 场次ID
	SessionStartAt int64               `json:"sessionStartAt"` // 场次开播时间
	StopReason     int                 `json:"stopReason"`     // 结束原因 0:主播自己结束 1:另一端重新开播 2+预留
	Ext            map[string]string   `json:"ext"`            // 客户端扩展字段
	Stream         string              `json:"stream"`         // 直播流地址
	Online         int64               `json:"online"`         // 在线数量
	Anchor         User                `json:"anchor"`         // 主播信息
	Rank10         []RankTopUser       `json:"rank10"`         // 在线前3榜单
	Likes          int64               `json:"likes"`          // 点赞
	Status         LStatus             `json:"status"`         // 综合房间状态
	PKStatus       *RoomPKStatusNotify `json:"pkStatus"`       // pk状态,只有在pk的时候才会有
	Paused         bool                `json:"paused"`         // 是否暂停
	LuckyScore     int64               `json:"luckyScore"`     // 幸运值
	LinkStatus     *RoomLinkStatus     `json:"linkStatus"`     // 连线状态
	Mute           bool                `json:"mute"`           // 主播是否设置静音
}

// NotifyRoomLike
type RoomLikeNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	Count    int64    `json:"count"`
}

// NotifyRoomFollow
type RoomFollowNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	Follow   bool     `json:"follow"` // 关注还是取消关注
}

// NotifySystemMessage
type SystemMessageNotify struct {
	Id     string `json:"id"`
	RoomId string `json:"roomId"`
	Msg    string `json:"msg"`
}

// NotifyRoomIntroduction
type RoomIntroductionNotify struct {
	Id     string `json:"id"`
	RoomId string `json:"roomId"`
	User   User   `json:"user"`
	Msg    string `json:"msg"`
}

type JoinRoomReq struct {
	RoomId   string `json:"roomId"`
	Streamer bool   `json:"streamer"` // 主播是否是开播推流的身份
	Silence  bool   `json:"silence"`  // 是否静默进入
}

type JoinRoomResp = Response[JoinRoomRespData]

type JoinRoomRespData struct {
	Token int64 `json:"token"`
	Muted bool  `json:"muted"`
}

type LeaveRoomReq struct {
	RoomId   string `json:"roomId"`
	Streamer bool   `json:"streamer"` // 主播是否是开播推流的身份
}

type LeaveRoomResp = Response[struct{}]

type HeartbeatReq struct {
	RoomId    string `json:"roomId"`
	Streamer  bool   `json:"streamer"`  // 主播是否是开播推流的身份
	JoinToken int64  `json:"joinToken"` // 进入房间获得token 目前为时间戳
}

type HeartbeatRespData struct {
	ServerTime int64  `json:"serverTime"`
	RoomId     string `json:"roomId"`
}

type HeartbeatResp = Response[HeartbeatRespData]

type PingReq struct {
	Ts int `json:"ts"`
}

type PingResp struct {
	Ts int `json:"ts"`
}

// NotifyUserLvUp
type RoomUserLvUpNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	RoomUser RoomUser `json:"roomUser"`
	User     User     `json:"user"`
	Type     int      `json:"type"` // 1: 0-19级 在聊天区展示，2: 20+ 飘屏展示；//  0-9 个人消息的方式 10+ 房间消息
}

type LuckDrawPrize struct {
	Mul   int `json:"mul"`   // 倍数
	Count int `json:"count"` // 中的次数
	Gain  int `json:"gain"`  // 收益
}

type LuckDrawNotifyLevel int

const (
	LuckDrawNotifyLow  LuckDrawNotifyLevel = 0 // 初级
	LuckDrawNotifyMid  LuckDrawNotifyLevel = 1 // 中级
	LuckDrawNotifyHigh LuckDrawNotifyLevel = 2 // 高级
)

// NotifyGlobalGiftLuckDraw MsgType = 90000 + 12 // 全站幸运礼物中奖通知
// NotifyUserGiftLuckDraw   MsgType = 90000 + 34 // 用户中奖消息
type GlobalUserLuckDrawNotify struct {
	Id        string              `json:"id"`
	User      User                `json:"user"`
	GiftId    int                 `json:"giftId"` // 礼物id
	Gift      *Gift               `json:"gift"`
	GiftCount int                 `json:"giftCount"` // 赠送的礼物数量
	Prizes    []LuckDrawPrize     `json:"prizes"`
	Level     LuckDrawNotifyLevel `json:"level"` // 飘条等级
}

const (
	KickReasonNormal        = -1 // 默认值
	KickReasonDuplicateJoin = 1  // 同一个身份重复进入(主播/观众只能有一个连接)
)

// NotifyUserKicked，客户端退出直播界面
type UserKickedNotify struct {
	Id        string `json:"id"`
	User      User   `json:"user"`
	Streamer  bool   `json:"streamer"`  // 主播是否是开播推流的身份
	Reason    int    `json:"reason"`    // 1: 他端登录直播间弹出 ，默认 -1
	RoomId    string `json:"roomId"`    // 被踢出的房间id, 非空时要判断和当前roomId是否一致
	Msg       string `json:"msg"`       // 被踢出的原因
	ForceExit bool   `json:"forceExit"` // 是否强制回退，true: 强制退出，不能进入下个直播间
}

// PKConfirmNotify 收到pk邀请通知
type PKConfirmNotify struct {
	Id        string `json:"id"`
	SessionId string `json:"sessionId"`
	User      User   `json:"user"`
	Online    int64  `json:"online"`
	Source    int    `json:"source"` // 1 来自好友 2 来自推荐 3 来自搜索 4 再来一局，来自连线直播间
}

// PKConfirmResultNotify PK确认结果通知,给邀请者发送
type PKConfirmResultNotify struct {
	Id        string `json:"id"`
	SessionId string `json:"sessionId"`
	User      User   `json:"user"`
	Result    int    `json:"result"` // 1: 同意, 2: 拒绝
}

// PKInviterCancelNotify 邀请者取消邀请通知
type PKInviterCancelNotify struct {
	Id        string `json:"id"`
	User      User   `json:"user"`
	SessionId string `json:"sessionId"`
}

// PKAskReadyNotify 询问客户端PK是否就绪通知
type PKAskReadyNotify struct {
	Id        string `json:"id"`
	SessionId string `json:"sessionId"`
	From      User   `json:"from"`
	To        User   `json:"to"`
	Source    int    `json:"source"` // 0 匹配 1 邀请
}

const (
	RankTypeHour = 1 // 小时榜单
	RankTypePop  = 2 // 人气榜单
)

// NotifyRankTopUpdate   MsgType = 90000 + 19 // 排行榜更新
type RankTopUpdateNotify struct {
	Id       string `json:"id"`
	User     User   `json:"user"`     // 主播
	RankType int    `json:"rankType"` // 1: 小时榜 2：人气榜
	Rank     int    `json:"rank"`     // 排名
	RoomId   string `json:"roomId"`   // 所在房间（主播房间号）用户跳转
	Living   bool   `json:"living"`   // 是否正在直播
	EffectId int    `json:"effectId"` // 特效资源ID 用于前三名直播间播放动画
}

// NotifyGiftComboFinish 礼物连击结束
type GiftComboFinishNotify struct {
	Id         string   `json:"id"`
	RoomId     string   `json:"roomId"`
	User       User     `json:"user"`
	RoomUser   RoomUser `json:"roomUser"`
	GiftId     int      `json:"giftId"`
	Gift       *Gift    `json:"gift"`
	ComboId    string   `json:"comboId"`
	TotalCount int      `json:"totalCount"`
}

type UserMuteStatusNotify struct {
	Id     string `json:"id"`
	Mute   bool   `json:"mute"`   // 禁言状态
	RoomId string `json:"roomId"` // 所在房间（当前为主播房间号）
}

// NotifyInviteLink       MsgType = 90000 + 23 // 邀请连线
type InviteLinkNotify struct {
	Id     string `json:"id"`
	Anchor User   `json:"anchor"` // 主播信息
	RoomId string `json:"roomId"` // 所在房间（主播房间号,客户端需校验当前是否是当前房间）
}

// NotifyLinkAskReady     MsgType = 90000 + 24 // 连线确认请求
type LinkAskReadyNotify struct {
	Id     string `json:"id"`
	RoomId string `json:"roomId"` // 所在房间（主播房间号,客户端需校验当前是否是当前房间）
	Token  string `json:"token"`  // 连线token, 用于确认连线
}

// LinkMsgNotify    MsgType = 90000 + 26 // 连线提示消息
type LinkMsgNotify struct {
	Id     string `json:"id"`
	RoomId string `json:"roomId"` // 所在房间（主播房间号,客户端需校验当前是否是当前房间）
	Msg    string `json:"reason"` // 展示文本
}

type Game struct {
	Id         string          `json:"id"`
	Name       string          `json:"name"`
	Desc       string          `json:"desc"`
	Tags       []string        `json:"tags"`
	Icon       string          `json:"icon"`
	Fullscreen bool            `json:"fullscreen"`
	Landscape  bool            `json:"landscape"`
	Sort       int             `json:"sort"`
	Config     any             `json:"config"` // 游戏配置,百顺的配置格式{"appId":123,"gsp":304}
	Entry      json.RawMessage `json:"entry"`  // 游戏入口，json格式原文
}

// NotifyUserPlayingGame  MsgType = 90000 + 27 // 用户正在玩游戏
type UserPlayingGameNotify struct {
	Id       string          `json:"id"`
	RoomId   string          `json:"roomId"` // 所在房间（主播房间号,客户端需校验当前是否是当前房间）
	User     User            `json:"user"`   // 用户信息
	RoomUser RoomUser        `json:"roomUser"`
	Game     map[string]Game `json:"game"` // 游戏信息 key: platform,比如 baishun
}

// NotifyLuckyRoomGiftLuckDraw MsgType = 90000 + 28 // 幸运礼物房间的中奖消息
type LuckyRoomUserLuckDrawNotify struct {
	Id        string              `json:"id"`
	RoomId    string              `json:"roomId"`
	User      User                `json:"user"`
	GiftId    int                 `json:"giftId"` // 礼物id
	Gift      *Gift               `json:"gift"`
	GiftCount int                 `json:"giftCount"` // 赠送的礼物数量
	Prizes    []LuckDrawPrize     `json:"prizes"`
	Level     LuckDrawNotifyLevel `json:"level"` // 飘条等级
}

type GameWinningLevel int

const (
	GameWinningLevelLow  GameWinningLevel = 0 // 初级
	GameWinningLevelMid  GameWinningLevel = 1 // 中级
	GameWinningLevelHigh GameWinningLevel = 2 // 高级
)

// NotifyGameWinning           MsgType = 90000 + 29 // 游戏获得大奖
type GameWinningNotify struct {
	Id      string           `json:"id"`
	User    User             `json:"user"`    // 用户信息
	Amount  int64            `json:"amount"`  // 金额
	WinType int              `json:"winType"` // 胜利类型 1 bigwin 2 supewin 3 magawin
	Game    map[string]Game  `json:"game"`    // 游戏信息 key: platform,比如 baishun
	Level   GameWinningLevel `json:"level"`   // 等级
}

// NotifyFlyText               MsgType = 90000 + 30 // 飞字
type FlyTextNotify struct {
	Id         string                       `json:"id"`
	TemplateId string                       `json:"templateId"` // 模板id
	Data       map[string]map[string]string `json:"data"`       // 数据 obj, map[key]map[lang]string
}

// NotifySticker               MsgType = 90000 + 31 // 直播间贴纸内容
type StickerNotify struct {
	Id      string `json:"id"`
	RoomId  string `json:"roomId"`
	Content string `json:"content"` // 贴纸内容
}

// NotifyUserFansLevelUp       MsgType = 90000 + 32 // 用户粉丝团等级提升
type UserFansLevelUpNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	Show     bool     `json:"show"` // 是否展示
}

// NotifyUserJoinFansclub      MsgType = 90000 + 33 // 用户加入粉丝团
type UserJoinFansclubNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
	Number   int      `json:"number"`   // 粉丝团第N个粉丝
	EffectId int      `json:"effectId"` // 特效资源ID
}

// NotifyUserFansActive        MsgType = 90000 + 35 // 用户粉丝团激活
type UserFansActiveNotify struct {
	Id       string   `json:"id"`
	RoomId   string   `json:"roomId"`
	User     User     `json:"user"`
	RoomUser RoomUser `json:"roomUser"`
}
