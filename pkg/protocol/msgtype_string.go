// Code generated by "stringer --type=MsgType"; DO NOT EDIT.

package protocol

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[rpcBase-30000]
	_ = x[EventConnected-1]
	_ = x[EventDisconnected-2]
	_ = x[EventConnHeartbeat-3]
	_ = x[RpcPing-30000]
	_ = x[RpcJoinRoom-30001]
	_ = x[RpcLeaveRoom-30002]
	_ = x[RpcHeartbeat-30003]
	_ = x[NotifyRoomEnter-90001]
	_ = x[NotifyRoomLeave-90002]
	_ = x[NotifyRoomChat-90003]
	_ = x[NotifyRoomGift-90004]
	_ = x[NotifyRoomStatus-90005]
	_ = x[NotifyRoomLike-90006]
	_ = x[NotifyRoomFollow-90007]
	_ = x[NotifySystemMessage-90008]
	_ = x[NotifyGlobalRoomGift-90010]
	_ = x[NotifyUserLvUp-90011]
	_ = x[NotifyGlobalGiftLuckDraw-90012]
	_ = x[NotifyUserKicked-90013]
	_ = x[NotifyPKConfirm-90014]
	_ = x[NotifyPKConfirmResult-90015]
	_ = x[NotifyPKInviterCancel-90017]
	_ = x[NotifyPKReadyAsk-90018]
	_ = x[NotifyRankTopUpdate-90019]
	_ = x[NotifyRoomIntroduction-90020]
	_ = x[NotifyGiftComboFinish-90021]
	_ = x[NotifyUserMuteStatus-90022]
	_ = x[NotifyInviteLink-90023]
	_ = x[NotifyLinkAskReady-90024]
	_ = x[NotifyLinkMsg-90026]
	_ = x[NotifyUserPlayingGame-90027]
	_ = x[NotifyLuckyRoomGiftLuckDraw-90028]
	_ = x[NotifyGameWinning-90029]
	_ = x[NotifyFlyText-90030]
}

const (
	_MsgType_name_0 = "EventConnectedEventDisconnectedEventConnHeartbeat"
	_MsgType_name_1 = "rpcBaseRpcJoinRoomRpcLeaveRoomRpcHeartbeat"
	_MsgType_name_2 = "NotifyRoomEnterNotifyRoomLeaveNotifyRoomChatNotifyRoomGiftNotifyRoomStatusNotifyRoomLikeNotifyRoomFollowNotifySystemMessage"
	_MsgType_name_3 = "NotifyGlobalRoomGiftNotifyUserLvUpNotifyGlobalGiftLuckDrawNotifyUserKickedNotifyPKConfirmNotifyPKConfirmResult"
	_MsgType_name_4 = "NotifyPKInviterCancelNotifyPKReadyAskNotifyRankTopUpdateNotifyRoomIntroductionNotifyGiftComboFinishNotifyUserMuteStatusNotifyInviteLinkNotifyLinkAskReady"
	_MsgType_name_5 = "NotifyLinkMsgNotifyUserPlayingGameNotifyLuckyRoomGiftLuckDrawNotifyGameWinningNotifyFlyText"
)

var (
	_MsgType_index_0 = [...]uint8{0, 14, 31, 49}
	_MsgType_index_1 = [...]uint8{0, 7, 18, 30, 42}
	_MsgType_index_2 = [...]uint8{0, 15, 30, 44, 58, 74, 88, 104, 123}
	_MsgType_index_3 = [...]uint8{0, 20, 34, 58, 74, 89, 110}
	_MsgType_index_4 = [...]uint8{0, 21, 37, 56, 78, 99, 119, 135, 153}
	_MsgType_index_5 = [...]uint8{0, 13, 34, 61, 78, 91}
)

func (i MsgType) String() string {
	switch {
	case 1 <= i && i <= 3:
		i -= 1
		return _MsgType_name_0[_MsgType_index_0[i]:_MsgType_index_0[i+1]]
	case 30000 <= i && i <= 30003:
		i -= 30000
		return _MsgType_name_1[_MsgType_index_1[i]:_MsgType_index_1[i+1]]
	case 90001 <= i && i <= 90008:
		i -= 90001
		return _MsgType_name_2[_MsgType_index_2[i]:_MsgType_index_2[i+1]]
	case 90010 <= i && i <= 90015:
		i -= 90010
		return _MsgType_name_3[_MsgType_index_3[i]:_MsgType_index_3[i+1]]
	case 90017 <= i && i <= 90024:
		i -= 90017
		return _MsgType_name_4[_MsgType_index_4[i]:_MsgType_index_4[i+1]]
	case 90026 <= i && i <= 90030:
		i -= 90026
		return _MsgType_name_5[_MsgType_index_5[i]:_MsgType_index_5[i+1]]
	default:
		return "MsgType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
