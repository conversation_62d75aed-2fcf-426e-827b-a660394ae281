package protocol

import (
	"github.com/rs/xid"
	"reflect"
	"sync"
)

var (
	structIdF sync.Map
)

func HasIdField(in any) bool {
	rt := reflect.TypeOf(in)
	if rt.Kind() != reflect.Struct {
		return false
	}

	if v, ok := structIdF.Load(rt); ok {
		return v.(bool)
	}

	for i := 0; i < rt.NumField(); i++ {
		if rt.Field(i).Name == "id" {
			structIdF.Store(rt, true)
			return true
		}
	}

	structIdF.Store(rt, false)
	return false
}

func ResetEmptyId(in any) {
	if !HasIdField(in) {
		return
	}

	rt := reflect.TypeOf(in)
	rv := reflect.ValueOf(in)

	if sf, ok := rt.FieldByName("id"); ok {
		if sf.Tag.Get("json") == "id" {
			if v, ok := rv.FieldByName("id").Interface().(string); ok && v == "" {
				reflect.ValueOf(in).FieldByName("id").SetString(xid.New().String())
			}
		}
	}
}
