package protocol

import (
	"bytes"
	"encoding/binary"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPacket_Encode_Decode(t *testing.T) {
	p := Packet{
		MsgType:  123,
		Sequence: 456,
		Payload:  []byte("hello"),
	}

	bs := p.Encode()

	p2, err := DecodePacket(bs)
	if err != nil {
		t.Errorf("decode packet failed: %v", err)
	}

	if p2.Sequence != p.Sequence {
		t.Errorf("sequence not equal, expect %d, got %d", p.Sequence, p2.Sequence)
	}

	assert.Equal(t, p.MsgType, p2.MsgType, "msg type not equal")
	assert.Equal(t, p.Sequence, p2.Sequence, "sequence not equal")
	assert.Equal(t, p.Payload, p2.Payload, "payload not equal")
}
func TestPacket_Write(t *testing.T) {
	p := Packet{
		MsgType:  123,
		Sequence: 456,
		Payload:  []byte("hello"),
	}
	var buf bytes.Buffer
	if err := p.WriteTo(&buf); err != nil {
		t.Errorf("write packet failed: %v", err)
	}
	bs := buf.Bytes()

	expectedLength := uint32(headerLength + len(p.Payload))
	actualLength := binary.LittleEndian.Uint32(bs[:4])
	if actualLength != expectedLength {
		t.Errorf("length not equal, expect %d, got %d", expectedLength, actualLength)
	}

	expectedMsgType := p.MsgType
	actualMsgType := binary.LittleEndian.Uint32(bs[4:8])
	if actualMsgType != expectedMsgType {
		t.Errorf("msg type not equal, expect %d, got %d", expectedMsgType, actualMsgType)
	}

	expectedSequence := p.Sequence
	actualSequence := binary.LittleEndian.Uint32(bs[8:12])
	if actualSequence != expectedSequence {
		t.Errorf("sequence not equal, expect %d, got %d", expectedSequence, actualSequence)
	}

	expectedFlags := p.Flags
	actualFlags := binary.LittleEndian.Uint32(bs[12:16])
	if actualFlags != expectedFlags {
		t.Errorf("flags not equal, expect %d, got %d", expectedFlags, actualFlags)
	}

	expectedPayload := p.Payload
	actualPayload := bs[headerLength:]
	if !bytes.Equal(actualPayload, expectedPayload) {
		t.Errorf("payload not equal, expect %v, got %v", expectedPayload, actualPayload)
	}

	bs2 := p.Encode()

	if !bytes.Equal(bs, bs2) {
		t.Errorf("write not equal encode")
	}

}
