package protocol

import (
	"encoding/binary"
	"errors"
	"io"
)

const (
	headerLength    = 16
	MinPacketLength = headerLength
)

var (
	ErrPacketLength = errors.New("packet length too short")
)

const (
	FlagNone = 0
	FlagAck  = 1 << 0
)

// Packet 是网关和客户端交互的数据结构，包含了消息类型和消息体, uint32都是小端的表示
type Packet struct {
	Length   uint32 // 长度，小端的表示，包含头部，等于headerLength+Payload的长度 目前值 16+Payload的长度
	MsgType  uint32 // 消息类型，小端的表示，比如登录，注册，聊天等
	Sequence uint32 // 消息顺序id 小端的表示
	Flags    uint32 // 预留
	Payload  []byte // 消息体，一般是json 序列化数据
}

func (p *Packet) MType() MsgType {
	return MsgType(p.MsgType)
}

func (p *Packet) Seq() uint32 {
	return p.Sequence
}

func DecodePacket(data []byte) (*Packet, error) {
	if len(data) < headerLength {
		return nil, ErrPacketLength
	}

	return &Packet{
		Length:   binary.LittleEndian.Uint32(data[:4]),
		MsgType:  binary.LittleEndian.Uint32(data[4:8]),
		Sequence: binary.LittleEndian.Uint32(data[8:12]),
		Flags:    binary.LittleEndian.Uint32(data[12:16]),
		Payload:  data[headerLength:],
	}, nil
}

func (p *Packet) Size() uint32 {
	return uint32(headerLength + len(p.Payload))
}

func (p *Packet) Encode() []byte {
	buff := make([]byte, len(p.Payload)+headerLength)
	binary.LittleEndian.PutUint32(buff[0:4], p.Size())
	binary.LittleEndian.PutUint32(buff[4:8], p.MsgType)
	binary.LittleEndian.PutUint32(buff[8:12], p.Sequence)
	binary.LittleEndian.PutUint32(buff[12:16], p.Flags)
	if len(p.Payload) > 0 {
		copy(buff[headerLength:], p.Payload)
	}

	return buff
}

func (p *Packet) WriteTo(bw io.Writer) error {
	if err := binary.Write(bw, binary.LittleEndian, p.Size()); err != nil {
		return err
	}
	if err := binary.Write(bw, binary.LittleEndian, p.MsgType); err != nil {
		return err
	}
	if err := binary.Write(bw, binary.LittleEndian, p.Sequence); err != nil {
		return err
	}
	if err := binary.Write(bw, binary.LittleEndian, p.Flags); err != nil {
		return err
	}

	if len(p.Payload) > 0 {
		if _, err := bw.Write(p.Payload); err != nil {
			return err
		}
	}
	return nil
}

func ReadSeq(data []byte) uint32 {
	return binary.LittleEndian.Uint32(data[8:12])
}

func WriteSeq(data []byte, seq uint32) {
	binary.LittleEndian.PutUint32(data[8:12], seq)
}

func ReadFlag(data []byte) uint32 {
	return binary.LittleEndian.Uint32(data[12:16])
}
