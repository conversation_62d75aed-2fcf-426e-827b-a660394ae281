package ulink

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gorm.io/gorm"
)

func (s *Manager) get(ctx context.Context, query any, args ...any) (*Connect, error) {
	var src Connect
	if err := db.UseTx(ctx, s.db).Where(query, args...).Take(&src).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, user.ErrAccountNotExists
		}
		return nil, err
	}
	return &src, nil
}

func (s *Manager) Get(ctx context.Context, appId, openId string) (*Connect, error) {
	return s.get(ctx, "app_id = ? AND open_id = ?", appId, openId)
}

var (
	ErrWrongPassword = biz.NewError(biz.ErrWrongPassword, "wrong account password")
)

type IFCond func(context.Context, *Connect) error

func ValidPassword(raw string) IFCond {
	return func(ctx context.Context, src *Connect) error {
		if !chkPassword(raw, src.Password) {
			return ErrWrongPassword
		}
		return nil
	}
}

func (s *Manager) GetIF(ctx context.Context, appId, openId string, checks ...IFCond) (*Connect, error) {
	src, err := s.Get(ctx, appId, openId)
	if err != nil {
		return nil, err
	}
	for _, check := range checks {
		if err := check(ctx, src); err != nil {
			return nil, err
		}
	}
	return src, nil
}

func (s *Manager) UserApp(ctx context.Context, userId, appId string) (*Connect, error) {
	return s.get(ctx, "user_id = ? AND app_id = ?", userId, appId)
}
