package ulink

import "golang.org/x/crypto/bcrypt"

func encPassword(raw string) (string, error) {
	out, err := bcrypt.GenerateFromPassword([]byte(raw), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(out), nil
}

func chkPassword(raw, hash string) bool {
	return bcrypt.CompareHashAndPassword([]byte(hash), []byte(raw)) == nil
}

func setPassword(src *Connect) error {
	if src.Password != "" {
		pwd, err := encPassword(src.Password)
		if err != nil {
			return err
		}
		src.Password = pwd
	}
	return nil
}
