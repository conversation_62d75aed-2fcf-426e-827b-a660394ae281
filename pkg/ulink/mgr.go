package ulink

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

var (
	ErrConflictBinding = biz.NewError(biz.ErrBindingConflict, "conflict binding")
	ErrUnbindingFailed = biz.NewError(biz.ErrUnbindingFailed, "unbinding failed")
)

func newManager(db *db.Client, um *user.Manager, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Connect{})
	return &Manager{
		db:  db,
		um:  um,
		log: log,
		vnd: make(map[string]Provider),
	}
}

type Manager struct {
	db  *db.Client
	um  *user.Manager
	log *zap.Logger
	vnd map[string]Provider
	hooks
}

func (s *Manager) countAll(ctx context.Context, userId string) (int, error) {
	var cnt int64
	if err := db.UseTx(ctx, s.db).Model(&Connect{}).Where("user_id = ?", userId).Count(&cnt).Error; err != nil {
		return 0, err
	}
	return int(cnt), nil
}

func (s *Manager) countApp(ctx context.Context, userId, appId string) (int, error) {
	var cnt int64
	if err := db.UseTx(ctx, s.db).Model(&Connect{}).Where("user_id = ? AND app_id = ?", userId, appId).Count(&cnt).Error; err != nil {
		return 0, err
	}
	return int(cnt), nil
}

func (s *Manager) List(ctx context.Context, userId string) ([]*Connect, error) {
	var list []*Connect
	if err := db.UseTx(ctx, s.db).Where("user_id = ?", userId).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (s *Manager) Linking(ctx context.Context, src *Connect, opts ...Option) (*user.Account, error) {
	if exists, err := s.Take(ctx, src.AppId, src.OpenId); err == nil {
		return exists, nil
	} else if !errors.Is(err, user.ErrAccountNotExists) {
		return nil, err
	}
	return s.regUser(ctx, src, newOpt(opts), &user.Account{}, user.AllowRandNN())
}

func (s *Manager) Binding(ctx context.Context, src *Connect, userId string) error {
	if exists, err := s.Take(ctx, src.AppId, src.OpenId); err != nil {
		if !errors.Is(err, user.ErrAccountNotExists) {
			return err
		}
	} else if exists.UserId != userId {
		return ErrConflictBinding
	} else { // already binding
		return nil
	}
	return s.bindUser(ctx, src, userId)
}

func (s *Manager) Forget(ctx context.Context, userId, appId string) error {
	if cnt, err := s.countAll(ctx, userId); err != nil {
		return err
	} else if cnt < 2 {
		return ErrUnbindingFailed
	}
	return db.UseTx(ctx, s.db).Where("user_id = ? AND app_id = ?", userId, appId).Delete(&Connect{}).Error
}

func (s *Manager) forgetAll(ctx context.Context, userId string) error {
	return db.UseTx(ctx, s.db).Where("user_id = ?", userId).Delete(&Connect{}).Error
}
