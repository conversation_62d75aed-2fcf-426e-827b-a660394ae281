package ulink

import (
	"context"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(db *db.Client, um *user.Manager, vnd log.Vendor) (*Manager, Hook, Delegate) {
	mgr := newManager(db, um, vnd.Scope("ulink.mgr"))
	return mgr, mgr, mgr
}

func Invoke(evb ev.Bus, mgr *Manager) {
	evb.Watch(user.EvUserDeleted, "ulink.cleanup", ev.NewWatcher(func(ctx context.Context, acc *user.Account) error {
		return mgr.forgetAll(ctx, acc.UserId)
	}))
}
