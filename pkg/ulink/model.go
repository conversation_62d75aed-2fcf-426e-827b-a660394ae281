package ulink

import (
	"time"
)

type Connect struct {
	ID        uint      `gorm:"primaryKey"`
	AppId     string    `gorm:"not null;size:16;uniqueIndex:app_user"`  // 应用渠道
	OpenId    string    `gorm:"not null;size:255;uniqueIndex:app_user"` // 外部关联ID
	UserId    string    `gorm:"not null;size:32;index:user_id"`         // 内部用户ID
	Nickname  string    `gorm:"not null;size:100"`                      // 三方昵称
	Email     string    `gorm:"not null;size:200"`                      // 三方邮箱
	Avatar    string    `gorm:"not null;size:2000"`                     // 三方头像
	Password  string    `gorm:"not null;size:60"`                       // 内部密码
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (c *Connect) TableName() string {
	return "user_connects"
}
