package ulink

import (
	"context"
	"fmt"
)

type Provider interface {
	WWWAuth(ctx context.Context, state *State) (string, error)
	Profile(ctx context.Context, state *State, code string) (*Connect, error)
}

func (s *Manager) Provide(name string, vnd Provider) {
	if _, has := s.vnd[name]; has {
		panic(fmt.Sprintf("duplicated provider: %s", name))
	}
	s.vnd[name] = vnd
}

func (s *Manager) getVendor(name string) (Provider, error) {
	v, has := s.vnd[name]
	if !has {
		return nil, fmt.Errorf("unsupported provider: %s", name)
	}
	return v, nil
}
