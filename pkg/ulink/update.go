package ulink

import (
	"context"
	"errors"
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

var (
	ErrNoUpdateValue = errors.New("no update value")
)

type UpData = map[string]any

type UpOption func(m *Manager, old *Connect) (UpData, error)

func Update(to *Connect) UpOption {
	return func(m *Manager, old *Connect) (UpData, error) {
		up := make(UpData)

		upField(up, "nickname", old.Nickname, to.Nickname)
		upField(up, "email", old.Email, to.Email)
		upField(up, "avatar", old.Avatar, to.Avatar)

		// password
		if to.Password != "" {
			pwd, err := encPassword(to.Password)
			if err != nil {
				return nil, err
			}
			up["password"] = pwd
		}

		return up, nil
	}
}

func upField[T comparable](w UpData, name string, old, new T) {
	var nil T
	if new != nil && old != new {
		w[name] = new
	}
}

func (s *Manager) preUpdate(ctx context.Context, appId, openId string, opts ...UpOption) (*Connect, UpData, error) {
	old, err := s.Get(ctx, appId, openId)
	if err != nil {
		return nil, nil, err
	}

	data := make(UpData)
	for _, opt := range opts {
		if up, err := opt(s, old); err != nil {
			return nil, nil, err
		} else {
			for k, v := range up {
				data[k] = v
			}
		}
	}

	if len(data) == 0 {
		return nil, nil, ErrNoUpdateValue
	}

	return old, data, nil
}

func (s *Manager) Update(ctx context.Context, appId, openId string, opts ...UpOption) error {
	old, up, err := s.preUpdate(ctx, appId, openId, opts...)
	if err != nil {
		return fmt.Errorf("precheck failed: %w", err)
	}

	if err := db.UseTx(ctx, s.db).Model(old).Updates(up).Error; err != nil {
		return fmt.Errorf("update failed: %w", err)
	}

	return nil
}

func (s *Manager) Replace(ctx context.Context, appId, openId string, src *Connect) error {
	old, err := s.Get(ctx, appId, openId)
	if err != nil {
		return err
	}

	if err := setPassword(src); err != nil {
		return err
	}

	// force keep userId
	src.UserId = old.UserId

	if err := db.UseTx(ctx, s.db).Model(old).Updates(src).Error; err != nil {
		return fmt.Errorf("replace failed: %w", err)
	}

	return nil
}
