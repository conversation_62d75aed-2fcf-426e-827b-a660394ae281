package ulink

import (
	"fmt"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

func Resty() *resty.Client {
	return resty.New().
		SetDebug(true).
		SetLogger(newLogger(zap.L()))
}

func HTTP(resp *resty.Response, err error) error {
	if err != nil {
		return err
	}
	if resp != nil {
		if resp.StatusCode()/100 != 2 {
			return fmt.Errorf("http error: %s", resp.Status())
		}
	}
	return nil
}

func newLogger(l *zap.Logger) *logger {
	return &logger{l: l.With(zap.String("scope", "ulink.cli"))}
}

type logger struct {
	l *zap.Logger
}

func (l *logger) Errorf(format string, v ...interface{}) {
	l.l.<PERSON>r("call api", zap.String("request", l.getMessage(format, v)))
}

func (l *logger) Warnf(format string, v ...interface{}) {
	l.l.Warn("call api", zap.String("request", l.getMessage(format, v)))
}

func (l *logger) Debugf(format string, v ...interface{}) {
	l.l.Debug("call api", zap.String("request", l.getMessage(format, v)))
}

func (l *logger) getMessage(template string, fmtArgs []interface{}) string {
	if len(fmtArgs) == 0 {
		return template
	}

	if template != "" {
		return fmt.Sprintf(template, fmtArgs...)
	}

	if len(fmtArgs) == 1 {
		if str, ok := fmtArgs[0].(string); ok {
			return str
		}
	}

	return fmt.Sprint(fmtArgs...)
}
