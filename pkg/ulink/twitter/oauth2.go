package twitter

import (
	"context"

	"github.com/go-resty/resty/v2"
	"github.com/nats-io/nuid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"golang.org/x/oauth2"
)

const appId = "twitter"

func Invoke(h *api.Host, um *ulink.Manager, desc *conf.Setting) {
	um.Provide(appId, &auth{
		conf: &oauth2.Config{
			ClientID:     desc.Connect[appId].ClientID,
			ClientSecret: desc.Connect[appId].ClientSecret,
			Endpoint: oauth2.Endpoint{
				AuthURL:  "https://twitter.com/i/oauth2/authorize",
				TokenURL: "https://api.twitter.com/2/oauth2/token",
			},
			RedirectURL: h.URL("/connect/with/twitter"),
			Scopes: []string{
				"users.read", "tweet.read",
			},
		},
		api: ulink.Resty().SetBaseURL("https://api.twitter.com/2"),
	})
}

type auth struct {
	conf *oauth2.Config
	api  *resty.Client
}

func (s *auth) WWWAuth(ctx context.Context, state *ulink.State) (string, error) {
	state.Nonce = nuid.Next()
	return s.conf.AuthCodeURL(state.String(), oauth2.S256ChallengeOption(state.Nonce)), nil
}

type withData[T any] struct {
	Data T `json:"data"`
}

type profile struct {
	Id       string `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

func (s *auth) Profile(ctx context.Context, state *ulink.State, code string) (*ulink.Connect, error) {
	token, err := s.conf.Exchange(ctx, code, oauth2.VerifierOption(state.Nonce))
	if err != nil {
		return nil, err
	}

	var resp withData[profile]
	err = ulink.HTTP(s.api.R().SetAuthToken(token.AccessToken).SetResult(&resp).Get("/users/me"))
	if err != nil {
		return nil, err
	}
	user := resp.Data

	return &ulink.Connect{
		AppId:    appId,
		OpenId:   user.Id,
		Nickname: user.Name,
	}, nil
}
