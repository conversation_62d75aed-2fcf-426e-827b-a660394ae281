package ulink

import (
	"context"

	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func (s *Manager) AuthURL(ctx context.Context, name string, state string) (string, error) {
	p, err := s.getVendor(name)
	if err != nil {
		return "", err
	}
	return p.WWWAuth(ctx, newState(TypLogin, state))
}

func (s *Manager) BindURL(ctx context.Context, name string, userId string) (string, error) {
	if cnt, err := s.countApp(ctx, userId, name); err != nil {
		return "", err
	} else if cnt > 0 {
		return "", ErrConflictBinding
	}
	p, err := s.getVendor(name)
	if err != nil {
		return "", err
	}
	return p.WWWAuth(ctx, newState(TypBind, userId))
}

func (s *Manager) AuthCode(ctx context.Context, name string, state *State, code string) (*user.Account, error) {
	p, err := s.getVendor(name)
	if err != nil {
		return nil, err
	}

	c, err := p.Profile(ctx, state, code)
	if err != nil {
		return nil, err
	}

	return s.Linking(ctx, c)
}

func (s *Manager) BindCode(ctx context.Context, name string, state *State, code string, userId string) error {
	p, err := s.getVendor(name)
	if err != nil {
		return err
	}

	c, err := p.Profile(ctx, state, code)
	if err != nil {
		return err
	}

	return s.Binding(ctx, c, userId)
}
