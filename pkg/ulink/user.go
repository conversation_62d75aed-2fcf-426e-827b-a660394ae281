package ulink

import (
	"context"
	"errors"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	ErrInvalidAppInfo = errors.New("invalid app info")
)

type Delegate interface {
	Has(ctx context.Context, appId, openId string) (bool, error)
	Take(ctx context.Context, appId, openId string) (*user.Account, error)
	Create(ctx context.Context, src *Connect, acc *user.Account, opts ...user.RegisterOpt) (*user.Account, error)
}

func (s *Manager) Has(ctx context.Context, appId, openId string) (bool, error) {
	var cnt int64
	if err := db.UseTx(ctx, s.db).Model(&Connect{}).Where("app_id = ? AND open_id = ?", appId, openId).Count(&cnt).Error; err != nil {
		return false, err
	}
	return cnt > 0, nil
}

func (s *Manager) Take(ctx context.Context, appId, openId string) (*user.Account, error) {
	var src Connect
	if err := db.UseTx(ctx, s.db).Select("user_id").Where("app_id = ? AND open_id = ?", appId, openId).Take(&src).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, user.ErrAccountNotExists
		}
		return nil, err
	}
	return s.um.Account(ctx, src.UserId)
}

func (s *Manager) Create(ctx context.Context, src *Connect, acc *user.Account, opts ...user.RegisterOpt) (*user.Account, error) {
	return s.regUser(ctx, src, newOpt(getOptions(ctx)), acc, opts...)
}

func (s *Manager) regUser(ctx context.Context, src *Connect, opt *options, acc *user.Account, opts ...user.RegisterOpt) (*user.Account, error) {
	if src.AppId == "" || src.OpenId == "" {
		return nil, ErrInvalidAppInfo
	}

	if err := setPassword(src); err != nil {
		return nil, err
	}

	{
		if acc.Nickname == "" && !opt.privateNN {
			acc.Nickname = src.Nickname
		}
		if acc.Avatar == "" {
			acc.Avatar = src.Avatar
		}
	}

	{
		src.UserId = user.NewId()
		acc.UserId = src.UserId
	}

	if err := db.Transaction(ctx, s.db, func(ctx context.Context, tx *gorm.DB) error {
		if err := s.preCreate(ctx, src); err != nil {
			return err
		}
		if err := tx.Create(src).Error; err != nil {
			return err
		}
		var err error
		acc, err = s.um.Register(ctx, acc, opts...)
		return err
	}); err != nil {
		return nil, err
	}

	s.log.Debug("user created", zap.String("appId", src.AppId), zap.String("openId", src.OpenId),
		zap.String("userId", acc.UserId), zap.String("showId", acc.ShowId),
		zap.String("nickname", acc.Nickname), zap.Int("gender", acc.Gender),
		zap.String("country", acc.Country), zap.String("timezone", acc.Timezone),
	)

	return acc, nil
}

func (s *Manager) bindUser(ctx context.Context, src *Connect, userId string) error {
	if src.AppId == "" || src.OpenId == "" {
		return ErrInvalidAppInfo
	}

	if err := setPassword(src); err != nil {
		return err
	}

	{
		src.UserId = userId
	}

	if err := db.UseTx(ctx, s.db).Create(src).Error; err != nil {
		return err
	}

	s.log.Debug("user bind", zap.String("appId", src.AppId), zap.String("openId", src.OpenId),
		zap.String("userId", userId),
	)

	return nil
}
