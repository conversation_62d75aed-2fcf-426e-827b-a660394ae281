package ulink

import "context"

type hookFn func(ctx context.Context, src *Connect) error

type Hook interface {
	PreCreate(do hookFn)
}

type hooks struct {
	preCreates []hookFn
}

func (h *hooks) PreCreate(do hookFn) {
	h.preCreates = append(h.preCreates, do)
}

func (h *hooks) preCreate(ctx context.Context, src *Connect) error {
	for _, do := range h.preCreates {
		if err := do(ctx, src); err != nil {
			return err
		}
	}
	return nil
}
