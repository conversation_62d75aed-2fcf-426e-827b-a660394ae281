package facebook

import (
	"context"

	"github.com/go-resty/resty/v2"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/facebook"
)

const appId = "facebook"

func Invoke(h *api.Host, um *ulink.Manager, desc *conf.Setting) {
	um.Provide(appId, &auth{
		conf: &oauth2.Config{
			ClientID:     desc.Connect[appId].ClientID,
			ClientSecret: desc.Connect[appId].ClientSecret,
			Endpoint:     facebook.Endpoint,
			RedirectURL:  h.URL("/connect/with/facebook"),
			Scopes: []string{
				"public_profile",
			},
		},
		api: ulink.Resty().SetBaseURL("https://graph.facebook.com"),
	})
}

type auth struct {
	conf *oauth2.Config
	api  *resty.Client
}

func (s *auth) WWWAuth(ctx context.Context, state *ulink.State) (string, error) {
	return s.conf.AuthCodeURL(state.String()), nil
}

type profile struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (s *auth) Profile(ctx context.Context, state *ulink.State, code string) (*ulink.Connect, error) {
	token, err := s.conf.Exchange(ctx, code)
	if err != nil {
		return nil, err
	}

	var user profile
	err = ulink.HTTP(s.api.R().SetQueryParam("access_token", token.AccessToken).ForceContentType("application/json").SetResult(&user).Get("/me"))
	if err != nil {
		return nil, err
	}

	return &ulink.Connect{
		AppId:    appId,
		OpenId:   user.Id,
		Nickname: user.Name,
	}, nil
}
