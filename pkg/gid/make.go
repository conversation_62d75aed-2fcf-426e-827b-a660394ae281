package gid

import (
	"context"
	"fmt"
	"time"

	"gitlab.sskjz.com/go/redi"
)

const (
	keyScope     = "GID:%s:%s"
	defaultScope = "default"
	defaultSpan  = time.Minute
	defaultPads  = 6
	timeLayout   = "20060102150405"
)

type Generator interface {
	New(scope string, opts ...MakeOpt) Generator
	Next() (string, error)
}

func newMaker(rc *redi.Client) *maker {
	return &maker{rc: rc, opt: newMakeOpts(defaultScope, nil)}
}

type maker struct {
	rc  *redi.Client
	opt *makeOpts
}

func (g *maker) New(scope string, opts ...MakeOpt) Generator {
	return &maker{rc: g.rc, opt: newMakeOpts(scope, opts)}
}

func (g *maker) Next() (string, error) {
	var (
		ctx = context.TODO()
		now = time.Now()
		abs time.Time
		ttl time.Duration
	)

	if g.opt.daily {
		abs = endOfDay(now)
		ttl = abs.Sub(now)
	} else {
		abs = now.Truncate(g.opt.span)
		ttl = abs.Add(g.opt.span).Sub(now) + time.Second
	}

	key := fmt.Sprintf(keyScope, g.opt.scope, abs.Format(timeLayout))

	txp := g.rc.Pipeline()
	inc := txp.Incr(ctx, key)
	txp.Expire(ctx, key, ttl)
	if _, err := txp.Exec(ctx); err != nil {
		return "", err
	}

	next, _ := inc.Result()
	return fmt.Sprintf("%s%0*d", now.Format(timeLayout), g.opt.pads, next), nil
}

func endOfDay(now time.Time) time.Time {
	y, m, d := now.Date()
	return time.Date(y, m, d, 23, 59, 59, int(time.Second-time.Nanosecond), now.Location())
}
