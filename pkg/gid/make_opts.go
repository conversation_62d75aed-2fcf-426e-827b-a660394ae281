package gid

import (
	"time"
)

type makeOpts struct {
	scope string
	span  time.Duration
	daily bool
	pads  int
}

type MakeOpt func(*makeOpts)

func newMakeOpts(scope string, in []MakeOpt) *makeOpts {
	opts := &makeOpts{
		scope: scope,
		span:  defaultSpan,
		pads:  defaultPads,
	}

	for _, opt := range in {
		opt(opts)
	}

	return opts
}

func WithSpan(span time.Duration) MakeOpt {
	return func(opts *makeOpts) {
		opts.span = span
		if opts.span >= 24*time.Hour {
			opts.daily = true
		}
	}
}

func WithPads(pads int) MakeOpt {
	return func(opts *makeOpts) {
		opts.pads = pads
	}
}
