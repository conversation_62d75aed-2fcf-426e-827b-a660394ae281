package gid

import (
	"fmt"
	"testing"

	"github.com/alicebob/miniredis/v2"
	"gitlab.sskjz.com/go/redi"
	"go.uber.org/zap"
)

func TestMake(t *testing.T) {
	rc, err := redi.NewClient("redis://"+miniredis.RunT(t).Addr(), nil, zap.NewNop())
	if err != nil {
		t.Fatal(err)
	}
	mk := newMaker(rc)

	for i := 0; i < 100; i++ {
		next, _ := mk.Next()
		fmt.Println(next)
	}
}

func BenchmarkMake(b *testing.B) {
	rc, err := redi.NewClient("redis://"+miniredis.RunT(b).Addr(), nil, zap.NewNop())
	if err != nil {
		b.<PERSON>al(err)
	}
	mk := newMaker(rc)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		if _, err := mk.Next(); err != nil {
			b.<PERSON>al(err)
		}
	}
	b.Report<PERSON>llo<PERSON>()
}
