package est

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"io"
	"strconv"
	"time"
)

var (
	stateKey = []byte("10268A980575AFC3B131901848D6436F")
	stateExp = time.Hour
)

type State struct {
	Time unixTime `json:"t,omitempty"`
	Data string   `json:"d,omitempty"`
}

func (s *State) Valid() bool {
	return s.Data != ""
}

func (s *State) String() string {
	bs, _ := json.Marshal(s)
	return encrypt(bs)
}

func New(data string) *State {
	return &State{Time: unixTime(time.Now()), Data: data}
}

func Parse(raw string) *State {
	bs := decrypt(raw)
	var ss State
	_ = json.Unmarshal(bs, &ss)
	if time.Since(ss.Time.Raw()) > stateExp {
		return &State{}
	}
	return &ss
}

func encrypt(data []byte) string {
	block, _ := aes.NewCipher(stateKey)
	aesGCM, _ := cipher.NewGCM(block)
	nonce := make([]byte, aesGCM.NonceSize())
	_, _ = io.ReadFull(rand.Reader, nonce)
	ciphertext := aesGCM.Seal(nonce, nonce, data, nil)
	return base64.RawURLEncoding.EncodeToString(ciphertext)
}

func decrypt(data string) []byte {
	enc, _ := base64.RawURLEncoding.DecodeString(data)
	block, _ := aes.NewCipher(stateKey)
	aesGCM, _ := cipher.NewGCM(block)
	nonceSize := aesGCM.NonceSize()
	if len(enc) < nonceSize {
		return nil
	}
	nonce, ciphertext := enc[:nonceSize], enc[nonceSize:]
	plaintext, _ := aesGCM.Open(nil, nonce, ciphertext, nil)
	return plaintext
}

type unixTime time.Time

func (t unixTime) Raw() time.Time {
	return time.Time(t)
}

func (t unixTime) MarshalJSON() ([]byte, error) {
	return []byte(strconv.FormatInt(time.Time(t).Unix(), 10)), nil
}

func (t *unixTime) UnmarshalJSON(s []byte) (err error) {
	q, err := strconv.ParseInt(string(s), 10, 64)
	if err != nil {
		return err
	}
	*(*time.Time)(t) = time.Unix(q, 0)
	return
}
