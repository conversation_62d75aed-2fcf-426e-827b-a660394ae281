package app

const (
	HdrDeviceName = "X-App-Devicename"
	HdrDeviceType = "X-App-Devicetype"
	HdrDeviceId   = "X-App-Deviceid"
	HdrCountry    = "X-App-Country"
	HdrTimezone   = "X-App-Timezone"
	HdrLang       = "X-App-Lang"
	HdrLangEx     = "X-App-Lang-Excludes"
	HdrVersion    = "X-App-Version"
	HdrChannelId  = "X-App-Channelid"
	HdrBundleId   = "X-App-Bundleid"
	HdrBuildId    = "X-App-Buildid"
)

type HdrGetter interface {
	GetHeader(string) string
}

func AllowHeaders() []string {
	return []string{
		"Content-Length",
		"Content-Type",
		"Authorization",
		HdrDeviceId,
		HdrTimezone,
		HdrLang,
	}
}
