package app

import (
	"strconv"
	"time"

	"github.com/hashicorp/go-version"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func DeviceId(from HdrGetter) string {
	return from.GetHeader(HdrDeviceId)
}

func DeviceName(from HdrGetter) string {
	return from.GetHeader(HdrDeviceName)
}

func Country(from HdrGetter) string {
	c := from.GetHeader(HdrCountry)
	if c != "" && !ctz.ValidCountry(c) {
		c = ""
	}
	return c
}

func Timezone(from HdrGetter, user ...*user.Account) *time.Location {
	tz := from.GetHeader(HdrTimezone)
	if tz == "" && len(user) > 0 {
		tz = user[0].Timezone
	}
	return ctz.Timezone(tz)
}

func Version(from HdrGetter) *version.Version {
	ver, err := version.NewSemver(from.GetHeader(HdrVersion))
	if err != nil {
		return version.Must(version.NewSemver("1.0.0"))
	}
	return ver
}

func ChannelId(from HdrGetter) string {
	return from.GetHeader(HdrChannelId)
}

func BundleId(from HdrGetter) string {
	return from.GetHeader(HdrBundleId)
}

func BuildId(from HdrGetter) string {
	return from.GetHeader(HdrBuildId)
}

func MustBuildId(from HdrGetter) int64 {
	bid := from.GetHeader(HdrBuildId)
	if bid == "" {
		return 0
	}

	bid2, err := strconv.ParseInt(bid, 10, 64)
	if err != nil {
		return 0
	}
	return bid2
}

func DeviceType(from HdrGetter) string {
	return from.GetHeader(HdrDeviceType)
}
