package app

const (
	AppStore = "app store"
	Official = "official"
	Google   = "google"
)

const (
	Android = "android"
	IOS     = "ios"
)

func IsAppStore(from HdrGetter) bool {
	return ChannelId(from) == AppStore
}

func IsOfficial(from HdrGetter) bool {
	return ChannelId(from) == Official
}

func IsGoogle(from HdrGetter) bool {
	return ChannelId(from) == Google
}

func IsIOS(from HdrGetter) bool {
	return DeviceType(from) == IOS
}

func IsAndroid(from HdrGetter) bool {
	return DeviceType(from) == Android
}

func IsWeb(from HdrGetter) bool {
	return from.GetHeader("Referer") != ""
}
