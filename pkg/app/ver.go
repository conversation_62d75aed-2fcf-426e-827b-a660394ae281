package app

import (
	"github.com/hashicorp/go-version"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
)

var (
	V100   = version.Must(version.NewVersion("1.0.0"))
	V110   = version.Must(version.NewVersion("1.1.0"))
	V112   = version.Must(version.NewVersion("1.1.2"))
	V130   = version.Must(version.NewVersion("1.3.0"))
	V140   = version.Must(version.NewVersion("1.4.0"))
	V160   = version.Must(version.NewVersion("1.6.0"))
	V170   = version.Must(version.NewVersion("1.7.0"))
	V180   = version.Must(version.NewVersion("1.8.0"))
	V1100  = version.Must(version.NewVersion("1.10.0-0"))
	V1102  = version.Must(version.NewVersion("1.10.2"))
	V1110  = version.Must(version.NewVersion("1.11.0-0"))
	V11120 = version.Must(version.NewVersion("1.11.2-0"))
)

var (
	inReview = true
)

func InReview(ver *version.Version) bool {
	if dbg.Ing() {
		return false
	}
	return inReview && ver.Equal(V110)
}
