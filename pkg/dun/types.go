package dun

type Result struct {
	Status Status
}

type Status int

const (
	Pass    Status = iota // 通过
	Review                // 人工复审
	Blocked               // 未通过
)

type genericResp interface {
	GetCode() int
	GetMsg() string
}

type rawResult[T any] struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result T      `json:"result"`
}

func (r *rawResult[T]) GetCode() int {
	return r.Code
}

func (r *rawResult[T]) GetMsg() string {
	return r.Msg
}
