package dun

import (
	"strconv"

	"gitlab.sskjz.com/go/rng"
	"go.uber.org/zap"
)

var (
	defaultNonceGen = func() string { return strconv.Itoa(rng.Int()) }
)

type cliOpts struct {
	nonceGen func() string
	logger   *zap.Logger
}

type CliOpt func(*cliOpts)

func WithNonceGen(gen func() string) CliOpt {
	return func(opts *cliOpts) {
		opts.nonceGen = gen
	}
}

func WithLogger(logger *zap.Logger) CliOpt {
	return func(opts *cliOpts) {
		opts.logger = logger
	}
}

func makeCliOpts(opts []CliOpt) *cliOpts {
	o := &cliOpts{
		nonceGen: defaultNonceGen,
		logger:   zap.NewNop(),
	}
	for _, opt := range opts {
		opt(o)
	}
	return o
}
