package dun

import (
	"context"
	"fmt"
	"net/url"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

type Check interface {
	Text(ctx context.Context, input string, opts ...Option) (*Result, error)
	Image(ctx context.Context, addr string, opts ...Option) (*Result, error)
}

func new<PERSON>hecker(cfg conf.Dun, log *zap.Logger) *checker {
	return &checker{
		cfg: cfg,
		cli: NewClient("https://as.dun.163.com", cfg.SecretId, cfg.<PERSON>, WithLogger(log)),
		log: log,
	}
}

type checker struct {
	cfg conf.Dun
	cli *Client
	log *zap.Logger
}

type antispamT struct {
	TaskId     string `json:"taskId"`
	DataId     string `json:"dataId"`
	Suggestion Status `json:"suggestion"`
}

type textResult struct {
	Antispam *antispamT `json:"antispam"`
}

func (s *checker) Text(ctx context.Context, input string, opts ...Option) (*Result, error) {
	opt := newOptions(opts...)

	params := url.Values{
		"dataId":  []string{opt.dataId},
		"content": []string{input},
	}

	var data rawResult[textResult]
	if err := s.cli.Request(ctx, s.cfg.TextBizId, "/v5/text/check", "v5.2", params, &data); err != nil {
		return nil, fmt.Errorf("request error: %w", err)
	}

	s.log.Debug("text check result", zap.String("dataId", opt.dataId), zap.Any("result", data.Result))

	return &Result{Status: data.Result.Antispam.Suggestion}, nil
}

type antispamI struct {
	Name       string `json:"name"`
	TaskId     string `json:"taskId"`
	DataId     string `json:"dataId"`
	Status     int    `json:"status"`
	Failure    int    `json:"failureReason"`
	Suggestion Status `json:"suggestion"`
}

type imageResult struct {
	Antispam *antispamI `json:"antispam"`
}

func (s *checker) Image(ctx context.Context, addr string, opts ...Option) (*Result, error) {
	opt := newOptions(opts...)

	images := []map[string]string{{
		"name":   addr,
		"type":   "1",
		"data":   addr,
		"dataId": opt.dataId,
	}}

	bs, err := sonic.Marshal(images)
	if err != nil {
		return nil, fmt.Errorf("json marshal failed: %w", err)
	}

	params := url.Values{
		"images": []string{string(bs)},
	}

	var data rawResult[[]imageResult]
	if err := s.cli.Request(ctx, s.cfg.ImageBizId, "/v5/image/check", "v5.1", params, &data); err != nil {
		return nil, fmt.Errorf("request error: %w", err)
	}

	result, has := lo.Find(data.Result, func(v imageResult) bool { return v.Antispam.DataId == opt.dataId })
	if !has {
		return nil, fmt.Errorf("result not found")
	}

	if result.Antispam.Status != 2 {
		s.log.Info("image check failed",
			zap.String("dataId", opt.dataId),
			zap.String("name", result.Antispam.Name),
			zap.Int("status", result.Antispam.Status),
			zap.Int("failure", result.Antispam.Failure),
		)
		return nil, fmt.Errorf("image check failed: %d", result.Antispam.Failure)
	}

	s.log.Debug("image check result", zap.String("dataId", opt.dataId), zap.Any("result", result))

	return &Result{Status: result.Antispam.Suggestion}, nil
}
