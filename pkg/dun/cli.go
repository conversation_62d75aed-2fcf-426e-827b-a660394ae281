package dun

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"maps"
	"net/url"
	"sort"
	"strconv"
	"time"

	"github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

func NewClient(baseUrl, secretId, secretKey string, opts ...CliOpt) *Client {
	opt := makeCliOpts(opts)

	cli := resty.New().SetBaseURL(baseUrl)
	cli.JSONUnmarshal = sonic.Unmarshal

	return &Client{
		Http:      cli,
		Logger:    opt.logger,
		SecretId:  secretId,
		SecretKey: secretKey,
		nonceGen:  opt.nonceGen,
	}
}

type Client struct {
	Http      *resty.Client
	Logger    *zap.Logger
	SecretId  string
	SecretKey string
	nonceGen  func() string
}

func (s *Client) Request(ctx context.Context, bizId, api, version string, params url.Values, bind any) error {
	params = maps.Clone(params)
	params["secretId"] = []string{s.SecretId}
	params["businessId"] = []string{bizId}
	params["version"] = []string{version}
	params["timestamp"] = []string{strconv.FormatInt(time.Now().UnixNano()/1000000, 10)}
	params["nonce"] = []string{s.nonceGen()}
	params["signature"] = []string{s.signature(params)}

	resp, err := s.Http.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/x-www-form-urlencoded").
		SetBody(params.Encode()).
		SetResult(bind).
		Post(api)
	if err != nil {
		return fmt.Errorf("post error: %w", err)
	}

	if resp.StatusCode()/100 != 2 {
		return fmt.Errorf("http error: %s", resp.Status())
	}

	if v, is := bind.(genericResp); is {
		if v.GetCode() != 200 {
			s.Logger.Info("api error",
				zap.String("content", v.GetMsg()),
				zap.String("url", resp.Request.URL),
				zap.String("params", params.Encode()),
			)
			return fmt.Errorf("api error: %d %s", v.GetCode(), v.GetMsg())
		}
	}

	return nil
}

func (s *Client) signature(params url.Values) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var paramStr bytes.Buffer
	for _, key := range keys {
		paramStr.WriteString(key)
		paramStr.WriteString(params[key][0])
	}

	paramStr.WriteString(s.SecretKey)

	md5Reader := md5.New()
	md5Reader.Write(paramStr.Bytes())
	return hex.EncodeToString(md5Reader.Sum(nil))
}
