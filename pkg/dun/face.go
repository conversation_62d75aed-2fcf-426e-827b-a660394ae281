package dun

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/avast/retry-go"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

type PicType int

const (
	PicTypeUrl    PicType = 1
	PicTypeBase64 PicType = 2
)

type Face interface {
	// 活体检测
	LpCheck(ctx context.Context, token string, opts ...Option) (*LpCheckResult, error)
	// 人脸注册
	Add(ctx context.Context, faceId, name string, pt PicType, avatar string, opts ...Option) error
	// 人脸检索
	Check(ctx context.Context, pt PicType, avatar string, opts ...Option) ([]FaceMatch, error)
	// 人脸删除
	Delete(ctx context.Context, faceId string, opts ...Option) error
}

type Facer struct {
	cfg conf.Dun
	cli *Client
	log *zap.Logger
}

func newFacer(cfg conf.Dun, log *zap.Logger) *Facer {
	return &Facer{
		cfg: cfg,
		cli: NewClient("https://verify.dun.163.com", cfg.SecretId, cfg.SecretKey, WithLogger(log)),
		log: log,
	}
}

type LpCheckResult struct {
	LpCheckStatus int    `json:"lpCheckStatus"` // 1-通过 2-不通过 3-查无结果
	TaskId        string `json:"taskId"`
	ReasonType    int    `json:"reasonType"`
	PicType       int    `json:"picType"`
	Avatar        string `json:"avatar"`
	IsPayed       int    `json:"isPayed"`
}

func (f *Facer) LpCheck(ctx context.Context, token string, opts ...Option) (*LpCheckResult, error) {
	opt := newOptions(opts...)

	logger := f.log.With(
		zap.String("token", token),
		zap.String("dataId", opt.dataId),
	)

	var data rawResult[LpCheckResult]

	err := retry.Do(
		func() error {
			params := url.Values{
				"token":      []string{token},
				"needAvatar": []string{"true"},
				"picType":    []string{fmt.Sprintf("%d", PicTypeUrl)},
				"dataId":     []string{opt.dataId},
			}

			return f.cli.Request(ctx, f.cfg.LpBizId, "/v1/liveperson/recheck", "v5.1", params, &data)
		},
		retry.Attempts(3),
		retry.DelayType(retry.FixedDelay),
		retry.Delay(time.Millisecond*500),
	)

	if err != nil {
		logger.Error("活体检测失败", zap.Error(err))

		return nil, fmt.Errorf("request error: %w", err)
	}

	result := data.Result

	logger = logger.With(
		zap.Any("result", result),
	)

	// 统计收费调用次数
	if result.IsPayed == 1 {
		logger = logger.With(
			zap.Int("spend", result.IsPayed),
		)
	}

	if data.Result.LpCheckStatus != 1 {
		logger.Info("活体检测失败")

		return nil, fmt.Errorf("face check failed: %d", result.LpCheckStatus)
	}

	logger.Info("活体检测成功")

	return &result, nil
}

type faceAddResult struct {
	Status int `json:"status"` // 1-添加成功 2-添加失败 3-重复添加 4-图片下载失败，请重试 5-上传图片质量过低，无法解析到完整的人脸
}

func (f *Facer) Add(ctx context.Context, faceId, name string, pt PicType, avatar string, opts ...Option) error {
	opt := newOptions(opts...)

	logger := f.log.With(
		zap.String("faceId", faceId),
		zap.String("name", name),
		zap.String("avatar", avatar),
		zap.String("dataId", opt.dataId),
	)

	var data rawResult[faceAddResult]

	err := retry.Do(
		func() error {
			params := url.Values{
				"name":    []string{name},
				"faceId":  []string{faceId},
				"picType": []string{fmt.Sprintf("%d", pt)},
				"avatar":  []string{avatar},
				"dataId":  []string{opt.dataId},
			}

			return f.cli.Request(ctx, f.cfg.FaceBizId, "/v1/facerecg/addFace", "v5.1", params, &data)
		},
		retry.Attempts(3),
		retry.DelayType(retry.FixedDelay),
		retry.Delay(time.Millisecond*500),
	)

	if err != nil {
		logger.Error("人脸注册失败", zap.Error(err))

		return fmt.Errorf("request error: %w", err)
	}

	result := data.Result

	logger = logger.With(
		zap.Any("result", result),
		zap.Int("status", result.Status),
	)

	if data.Result.Status != 1 && data.Result.Status != 3 {
		logger.Error("人脸注册失败")

		return fmt.Errorf("face add failed: %d", result.Status)
	}

	logger.Info("人脸注册成功")

	return nil
}

type faceCheckResult struct {
	Status       int         `json:"status"` // 1-成功 2-图片下载失败 3-其他错误 5-上传图片质量过低，无法解析到完整的人脸
	Matched      bool        `json:"matched"`
	RequestId    string      `json:"requestId"`
	IsPayed      int         `json:"isPayed"`
	MatchedFaces []FaceMatch `json:"matchedFaces"`
}

type FaceMatch struct {
	Score   float64 `json:"score"`
	Name    string  `json:"name"`
	FaceId  string  `json:"faceId"`
	FaceUrl string  `json:"faceUrl"`
}

func (f *Facer) Check(ctx context.Context, pt PicType, avatar string, opts ...Option) ([]FaceMatch, error) {
	opt := newOptions(opts...)

	logger := f.log.With(
		zap.String("avatar", avatar),
		zap.String("dataId", opt.dataId),
	)

	var data rawResult[faceCheckResult]

	err := retry.Do(
		func() error {
			params := url.Values{
				"picType": []string{fmt.Sprintf("%d", pt)},
				"avatar":  []string{avatar},
				"dataId":  []string{opt.dataId},
			}

			return f.cli.Request(ctx, f.cfg.FaceBizId, "/v1/facerecognize/check", "v5.1", params, &data)
		},
		retry.Attempts(3),
		retry.DelayType(retry.FixedDelay),
		retry.Delay(time.Millisecond*500),
	)

	if err != nil {
		logger.Error("人脸检索失败", zap.Error(err))

		return nil, fmt.Errorf("request error: %w", err)
	}

	result := data.Result

	logger = logger.With(
		zap.Any("result", result),
	)

	// 统计收费调用次数
	if result.IsPayed == 1 {
		logger = logger.With(
			zap.Int("spend", result.IsPayed),
		)
	}

	if data.Result.Status != 1 {
		logger.Info("人脸检索失败")

		return nil, fmt.Errorf("face check failed: %d", result.Status)
	}

	logger.Info("人脸检索成功")

	ret := make([]FaceMatch, 0)

	for _, m := range result.MatchedFaces {
		if m.Score < 0.90 {
			continue
		}

		ret = append(ret, m)
	}

	return ret, nil
}

type faceDeleteResult struct {
	Status int `json:"status"` // 删除结果，1-删除成功 2-删除失败 3-图片不存在
}

func (f *Facer) Delete(ctx context.Context, faceId string, opts ...Option) error {
	opt := newOptions(opts...)

	logger := f.log.With(
		zap.String("faceId", faceId),
		zap.String("dataId", opt.dataId),
	)

	var data rawResult[faceDeleteResult]

	err := retry.Do(
		func() error {
			params := url.Values{
				"faceId": []string{faceId},
				"dataId": []string{opt.dataId},
			}

			return f.cli.Request(ctx, f.cfg.FaceBizId, "/v1/facerecg/deleteFace", "v5.1", params, &data)
		},
		retry.Attempts(3),
		retry.DelayType(retry.FixedDelay),
		retry.Delay(time.Millisecond*500),
	)

	if err != nil {
		logger.Error("人脸删除失败", zap.Error(err))

		return fmt.Errorf("request error: %w", err)
	}

	result := data.Result

	logger = logger.With(
		zap.Any("result", result),
		zap.Int("status", result.Status),
	)

	if data.Result.Status != 1 && data.Result.Status != 3 {
		logger.Error("人脸删除失败")

		return fmt.Errorf("face delete failed: %d", result.Status)
	}

	logger.Info("人脸删除成功")

	return nil
}
