package fcm

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
)

func Provide(evq mq.Queue, vnd log.Vendor) (Sender, Reporter, error) {
	cli, err := newClient(context.TODO(), vnd.Scope("fcm.cli"))
	if err != nil {
		return nil, nil, err
	}
	return newQueue(cli, evq), cli, nil
}

func Invoke(s Sender) error {
	q, is := s.(*queue)
	if !is {
		return errors.New("invalid sender type")
	}
	return q.start()
}
