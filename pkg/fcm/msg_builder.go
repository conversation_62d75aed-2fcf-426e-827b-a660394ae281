package fcm

import "firebase.google.com/go/v4/messaging"

func newMsg(tokens []string, msg *Notification, opts []SendOpt) *qMsg {
	opt := newSendOpts(opts)
	qm := &qMsg{
		TaskId: opt.taskId,
		Tokens: tokens,
		Message: &messaging.Message{
			Notification: (*messaging.Notification)(msg),
			Android: &messaging.AndroidConfig{
				Notification: &messaging.AndroidNotification{},
			},
			APNS: &messaging.APNSConfig{
				Payload: &messaging.APNSPayload{
					Aps: &messaging.Aps{},
				},
			},
			FCMOptions: &messaging.FCMOptions{},
		},
	}
	if opt.action != "" {
		if opt.platform == 0 || opt.platform == Android {
			qm.Message.Android.Data = map[string]string{
				"clickAction": opt.action,
			}
		}
		if opt.platform == 0 || opt.platform == APNS {
			qm.Message.APNS.Payload.Aps.Category = opt.action
		}
	}
	if opt.channelId != "" {
		qm.Message.Android.Notification.ChannelID = opt.channelId
	}
	if opt.analyticsId != "" {
		qm.Message.FCMOptions.AnalyticsLabel = opt.analyticsId
	}
	if len(opt.customData) > 0 {
		qm.Message.Data = opt.customData
	}
	return qm
}
