package fcm

import (
	"context"
	"maps"

	"firebase.google.com/go/v4/messaging"
)

type Notification messaging.Notification

func (n *Notification) Clone() *Notification {
	n2 := new(Notification)
	if n != nil {
		*n2 = *n
	}
	return n2
}

type Sender interface {
	Push(ctx context.Context, tokens []string, msg *Notification, opts ...SendOpt) error
}

type platform int

const (
	Android platform = 1
	APNS    platform = 2
)

func newSendOpts(opts []SendOpt) *sendOpts {
	o := new(sendOpts)
	for _, opt := range opts {
		opt(o)
	}
	return o
}

type sendOpts struct {
	taskId      string
	platform    platform
	action      string
	channelId   string
	analyticsId string
	customData  map[string]string
}

type SendOpt func(*sendOpts)

func TaskId(id string) SendOpt {
	return func(o *sendOpts) { o.taskId = id }
}

func Platform(p platform) SendOpt {
	return func(o *sendOpts) { o.platform = p }
}

func ClickAction(uri string) SendOpt {
	return func(o *sendOpts) { o.action = uri }
}

func ChannelId(id string) SendOpt {
	return func(o *sendOpts) { o.channelId = id }
}

func AnalyticsId(id string) SendOpt {
	return func(o *sendOpts) { o.analyticsId = id }
}

func CustomData(data map[string]string) SendOpt {
	return func(o *sendOpts) {
		if o.customData == nil {
			o.customData = make(map[string]string)
		}
		maps.Copy(o.customData, data)
	}
}
