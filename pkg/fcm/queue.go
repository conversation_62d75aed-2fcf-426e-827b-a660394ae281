package fcm

import (
	"context"

	"firebase.google.com/go/v4/messaging"
	"gitlab.sskjz.com/go/mg"
	"gitlab.sskjz.com/go/mq"
)

const (
	msgTopic = "fcm.messaging"
)

type qMsg struct {
	TaskId  string             `json:"i"`
	Tokens  []string           `json:"t"`
	Message *messaging.Message `json:"m"`
}

func newQueue(cli *client, evq mq.Queue) *queue {
	return &queue{
		cli: cli,
		msg: mg.New[*qMsg](evq),
	}
}

type queue struct {
	cli *client
	msg mg.Queue[*qMsg]
}

func (s *queue) start() error {
	return s.msg.Subscribe(msgTopic, "sender", s.processMsg,
		mq.ConcurrencySub(), mq.LogCost("fcm.messaging.send"),
	)
}

func (s *queue) processMsg(ctx context.Context, data *qMsg) error {
	s.cli.send2(ctx, data.TaskId, data.Tokens, data.Message)
	return nil
}

func (s *queue) Push(ctx context.Context, tokens []string, msg *Notification, opts ...SendOpt) error {
	return s.msg.Publish(ctx, msgTopic, newMsg(tokens, msg, opts))
}
