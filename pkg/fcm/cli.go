package fcm

import (
	"context"
	_ "embed"

	"firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/ob/meter"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

//go:embed key.json
var jsonKey []byte

func newClient(ctx context.Context, log *zap.Logger) (*client, error) {
	app, err := firebase.NewApp(ctx, nil, option.WithCredentialsJSON(jsonKey))
	if err != nil {
		return nil, err
	}
	mc, err := app.Messaging(ctx)
	if err != nil {
		return nil, err
	}
	return &client{
		mc:  mc,
		log: log,
		// metrics
		sendC: meter.NewCounter[int]("fcm.send.counter"),
	}, nil
}

type client struct {
	mc  *messaging.Client
	log *zap.Logger
	reporter
	sendC meter.Counter[int]
}

const (
	batchMax        = 500
	keyResult       = "result"
	retSuccess      = "success"
	retUnregistered = "unregistered"
	retFailure      = "failure"
)

func (s *client) send2(ctx context.Context, taskId string, tokens []string, raw *messaging.Message) {
	var success, failure, reject int
	defer func() {
		if taskId != "" {
			s.postBroadcast(taskId, success, failure, reject)
		}
	}()

	if len(tokens) == 1 {
		raw.Token = tokens[0]
		if _, err := s.mc.Send(ctx, raw); err != nil {
			if messaging.IsUnregistered(err) {
				reject++
				s.sendC.Add(1, keyResult, retUnregistered)
				s.onUnregistered(raw.Token)
			} else {
				failure++
				s.sendC.Add(1, keyResult, retFailure)
				s.log.Warn("send failed", zap.Error(err), zap.Any("message", raw))
			}
			return
		}
		success++
		s.sendC.Add(1, keyResult, retSuccess)
		return
	}

	for _, batch := range lo.Chunk(tokens, batchMax) {
		msgs := make([]*messaging.Message, 0, len(batch))
		for _, token := range batch {
			msgs = append(msgs, &messaging.Message{
				Data:         raw.Data,
				Notification: raw.Notification,
				Android:      raw.Android,
				Webpush:      raw.Webpush,
				APNS:         raw.APNS,
				FCMOptions:   raw.FCMOptions,
				Token:        token,
			})
		}

		resp, err := s.mc.SendEach(ctx, msgs)
		if err != nil {
			s.log.Error("batch send failed", zap.Error(err), zap.Any("message", raw))
			return
		}

		var unregistered int
		for i, msg := range resp.Responses {
			if msg.Success {
				continue
			}
			if messaging.IsUnregistered(msg.Error) {
				unregistered++
				s.onUnregistered(batch[i])
			} else {
				s.log.Warn("send failed", zap.Error(msg.Error), zap.Any("message", msgs[i]))
			}
		}

		{
			if resp.SuccessCount > 0 {
				s.sendC.Add(resp.SuccessCount, keyResult, retSuccess)
			}
			if unregistered > 0 {
				resp.FailureCount -= unregistered
				s.sendC.Add(unregistered, keyResult, retUnregistered)
			}
			if resp.FailureCount > 0 {
				s.sendC.Add(resp.FailureCount, keyResult, retFailure)
			}
		}

		{
			success += resp.SuccessCount
			failure += resp.FailureCount
			reject += unregistered
		}

		s.log.Info("batch send done", zap.Any("message", raw),
			zap.Int("success", resp.SuccessCount),
			zap.Int("failure", resp.FailureCount),
			zap.Int("unregistered", unregistered),
		)
	}
}
