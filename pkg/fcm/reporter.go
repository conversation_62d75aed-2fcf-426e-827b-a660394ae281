package fcm

import "gitlab.sskjz.com/go/co"

type onUnregisterFn func(token string)
type postBroadcastFn func(taskId string, success, failure, reject int)

type Reporter interface {
	OnUnregistered(onUnregisterFn)
	PostBroadcast(postBroadcastFn)
}

type reporter struct {
	callbackPool    co.Pool
	onUnregisterFn  onUnregisterFn
	postBroadcastFn postBroadcastFn
}

func (r *reporter) initPool() {
	r.callbackPool = co.Apply(co.Named("fcm.send.reporter"))
}

func (r *reporter) OnUnregistered(cb onUnregisterFn) {
	r.initPool()
	r.onUnregisterFn = cb
}

func (r *reporter) PostBroadcast(cb postBroadcastFn) {
	r.initPool()
	r.postBroadcastFn = cb
}

func (r *reporter) onUnregistered(token string) {
	if r.onUnregisterFn != nil {
		r.callbackPool.Submit(func() {
			r.onUnregisterFn(token)
		})
	}
}

func (r *reporter) postBroadcast(taskId string, success, failure, reject int) {
	if r.postBroadcastFn != nil {
		r.callbackPool.Submit(func() {
			r.postBroadcastFn(taskId, success, failure, reject)
		})
	}
}
