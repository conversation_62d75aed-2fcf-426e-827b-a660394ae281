package fcm

import (
	"context"
	"math/rand"
	"testing"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
)

func TestPush(t *testing.T) {
	lg, _ := zap.NewDevelopment()
	cli, err := newClient(context.TODO(), lg)
	if err != nil {
		t.Fatal(err)
	}
	cli.OnUnregistered(func(token string) {
		t.Logf("unregistered: %s", token)
	})
	if err := newQueue(cli, nil).processMsg(context.TODO(), newMsg(
		lo.Subset([]string{
			"d9UkG6b9RPqQ4UcKIjsGi-:APA91bEWyqS7YSZ2ZzXlPQPBCfpZJvHK-xxdRQ48ZlONbi0VxgA5awopnHvFMRxz_X4B_gGoPb_F0XrMod3U1P8JLKz3fRfLEcJnOhR4hC3YaWoavhRpo_0",
			"dcyC7G1PQo-vQIG4Q0Umnl:APA91bFYuo7LmM0T-mMDxx4IT5oX2z6IV8QzThOOaFWvPDJavHdlLTTagUkRx_PN31F8dHBH1dyR0viFZbxxRtScp5RriXFUT0vL-uk0h9Uy-t3dNJMnSAc",
		}, 0, uint(rand.Intn(2)+1)),
		&Notification{
			Title:    "Hello " + time.Now().Format(time.TimeOnly),
			Body:     "This is a push notification!",
			ImageURL: "https://godzilla-live-oss-test.sskjz.com/avatar/default/200x200",
		},
		[]SendOpt{
			Platform(Android),
			ClickAction("kakolive://moment?id=123"),
			ChannelId("updates"),
			AnalyticsId("test"),
			CustomData(map[string]string{
				"ignoreInForeground": "true",
			}),
		},
	)); err != nil {
		t.Fatal(err)
	}
}
