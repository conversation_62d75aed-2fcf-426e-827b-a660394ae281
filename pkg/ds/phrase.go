package ds

import (
	"fmt"
	"strings"
	"time"
)

func NewPhrase(start string) *Phrase {
	return &Phrase{
		msg:  start,
		at:   time.Now(),
		next: make([]Phrase, 0),
	}
}

type Phrase struct {
	msg  string
	at   time.Time
	next []Phrase
}

func (p *Phrase) Record(phrase string) {
	p.next = append(p.next, Phrase{
		msg: phrase,
		at:  time.Now(),
	})
}

func (p *Phrase) String() string {
	var (
		up = p.at
		sb strings.Builder
	)

	sb.WriteString(p.msg)
	for _, p2 := range p.next {
		d := p2.at.Sub(up)
		sb.WriteString("=>[")
		sb.WriteString(p2.msg)
		sb.WriteString(fmt.Sprintf("](%s)", d))
		up = p2.at
	}

	// total time
	sb.WriteString(fmt.Sprintf("(total: %s)", time.Since(p.at)))
	return sb.String()
}
