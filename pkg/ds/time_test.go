package ds

import (
	"testing"
	"time"
)

func TestTimestampToFloat64(t *testing.T) {
	ts := int64(1717745723)
	expected := float64(0.1717745723)
	result := UTCTimestampToFloat64(ts)
	if result != expected {
		t.<PERSON><PERSON><PERSON>("Expected %f, but got %f", expected, result)
	} else {
		t.Logf("Expected %f, got %f, passed", expected, result)
	}
}
func TestRevUTCFloat64ToTimestamp(t *testing.T) {
	f := int64(8282254277)
	expected := float64(0.1717745723)
	result := RevUTCTimestampToFloat64(f)

	// compare float64
	if Abs(expected-result) > 0.0000001 {
		t.<PERSON><PERSON><PERSON>("Expected %f, but got %f", expected, result)
	} else {
		t.Logf("Expected %f, got %f, passed", expected, result)
	}
}

func TestRevUTCFloat64ToTimestamp2(t *testing.T) {
	s1 := time.Now()
	s2 := s1.Add(time.Second)

	f1 := RevUTCTimestampToFloat64(s1.Unix())
	f2 := RevUTCTimestampToFloat64(s2.Unix())

	if f1 > 1 {
		t.E<PERSON>rf("Expected %f, but got %f", 0.0, f1)
	} else {
		t.Logf("Expected %f, got %f, passed", 0.0, f1)
	}

	if f2 < 0 {
		t.Errorf("Expected %f, but got %f", 1.0, f2)
	} else {
		t.Logf("Expected %f, got %f, passed", 1.0, f2)
	}

	if f1 <= f2 {
		t.Error("Expected f1 < f2, but got f1 >= f2")
	} else {
		t.Log("Expected f1 < f2, passed")
	}
}
