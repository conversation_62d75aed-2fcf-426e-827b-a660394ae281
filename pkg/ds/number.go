package ds

import (
	"fmt"
	"math"
	"strconv"
	"strings"
)

func NumberToUnitString(in float64) string {
	units := []string{"", "K", "M", "B"}
	unitIndex := 0

	for in >= 1000 && unitIndex < len(units)-1 {
		in /= 1000
		unitIndex++
	}

	in = math.Floor(in*10) / 10 // 向下取整到小数点后一位

	if in == float64(int(in)) {
		return fmt.Sprintf("%.0f%s", in, units[unitIndex])
	} else {
		return fmt.Sprintf("%.1f%s", in, units[unitIndex])
	}
}

func NumberToUnitString2(in float64, thousandSeparator, decimalPoint string) string {
	units := []string{"", "K", "M", "B"}
	unitIndex := 0

	// 检查是否为负数
	isNegative := false
	if in < 0 {
		isNegative = true
		in = -in // 取绝对值
	}

	for in >= 10000 && unitIndex < len(units)-1 {
		in /= 1000
		unitIndex++
	}

	in = math.Floor(in*10) / 10 // 向下取整到小数点后一位

	// 分离整数部分和小数部分
	intPart := int(in)
	decimalPart := int(math.Round(in*10)) % 10

	// 使用strings.Builder构建整数部分的字符串，包括千分位分隔符
	var builder strings.Builder
	intPartStr := strconv.Itoa(intPart)
	for i, rune := range intPartStr {
		if i > 0 && (len(intPartStr)-i)%3 == 0 {
			builder.WriteString(thousandSeparator)
		}
		builder.WriteRune(rune)
	}

	// 根据是否有小数部分来决定是否添加小数点和小数部分
	if decimalPart > 0 {
		builder.WriteString(decimalPoint)
		builder.WriteString(strconv.Itoa(decimalPart))
	}

	// 添加单位
	builder.WriteString(units[unitIndex])

	// 如果是负数，重新加上负号
	if isNegative {
		return "-" + builder.String()
	}
	return builder.String()
}

func AddThousandSeparator(in float64, sep, decimalPoint string) string {
	inStr := strconv.FormatFloat(in, 'f', -1, 64)
	var builder strings.Builder

	// 检查是否为负数
	isNegative := false
	if strings.HasPrefix(inStr, "-") {
		isNegative = true
		inStr = inStr[1:] // 去掉负号
	}

	// 分离整数部分和小数部分
	parts := strings.Split(inStr, ".")
	intPart := parts[0]
	var decPart string
	if len(parts) > 1 {
		decPart = parts[1]
	}

	// 添加千位分隔符到整数部分
	for i, r := range intPart {
		if i > 0 && (len(intPart)-i)%3 == 0 {
			builder.WriteString(sep)
		}
		builder.WriteRune(r)
	}

	// 如果有小数部分，添加小数点和小数部分
	if decPart != "" {
		builder.WriteString(decimalPoint)
		builder.WriteString(decPart)
	}

	// 如果是负数，重新加上负号
	if isNegative {
		return "-" + builder.String()
	}
	return builder.String()
}

type numberic interface {
	int | int8 | uint8 | int16 | uint16 | int32 | uint32 | int64 | uint64 | float32 | float64
}

func Abs[T numberic](a T) T {
	if a < 0 {
		return -a
	}
	return a
}
