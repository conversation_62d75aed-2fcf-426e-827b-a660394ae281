package ds

import (
	"testing"
)

func TestNumberToUnitString(t *testing.T) {
	tests := []struct {
		input    float64
		expected string
	}{
		{0, "0"},
		{999, "999"},
		{1000, "1K"},
		{1001, "1K"},
		{1499, "1.4K"},
		{1500, "1.5K"},
		{999999, "999.9K"},
		{1000000, "1M"},
		{1000001, "1M"},
		{1499999, "1.4M"},
		{1500000, "1.5M"},
	}

	for _, test := range tests {
		result := NumberToUnitString(test.input)
		if result != test.expected {
			t.<PERSON>rrorf("NumberToUnitString(%f) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestNumberToUnitString2(t *testing.T) {
	tests := []struct {
		input    float64
		expected string
	}{
		{8276, "8.276"},
		{-8276, "-8.276"},

		{10234, "10,2K"},
		{-10234, "-10,2K"},

		{8276235, "8.276,2K"},
		{-8276235, "-8.276,2K"},

		{19276235, "19,2M"},
		{-19276235, "-19,2M"},
	}

	for _, test := range tests {
		result := NumberToUnitString2(test.input, ".", ",")
		if result != test.expected {
			t.Errorf("NumberToUnitString2(%f) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestAddThousandSeparatorHandlesNegativeNumbers(t *testing.T) {
	tests := []struct {
		input    float64
		expected string
	}{
		{-1000, "-1,000"},
		{-1500000, "-1,500,000"},
	}

	for _, test := range tests {
		result := AddThousandSeparator(test.input, ",", ".")
		if result != test.expected {
			t.Errorf("AddThousandSeparator(%f) = %s, expected %s", test.input, result, test.expected)
		}
	}
}
func TestAddThousandSeparator(t *testing.T) {
	tests := []struct {
		input    float64
		sep      string
		expected string
	}{
		{1000, ",", "1,000"},
		{1500000, ",", "1,500,000"},
		{-1000, ",", "-1,000"},
		{-1500000, ",", "-1,500,000"},
		{1234567890, ".", "1.234.567.890"},
		{9876543210, ".", "9.876.543.210"},
		{-1234567890, ".", "-1.234.567.890"},
		{-9876543210, ".", "-9.876.543.210"},
		{1234.5678, ",", "1,234.5678"},
		{9876.5432, ",", "9,876.5432"},
		{-1234.5678, ",", "-1,234.5678"},
		{-9876.5432, ",", "-9,876.5432"},
	}

	for _, test := range tests {
		result := AddThousandSeparator(test.input, test.sep, ".")
		if result != test.expected {
			t.Errorf("AddThousandSeparator(%v, %s) = %s, expected %s", test.input, test.sep, result, test.expected)
		}
	}
}
