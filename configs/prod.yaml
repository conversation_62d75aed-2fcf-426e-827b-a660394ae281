debug: true
pprof: true

auth:
  jwt_key: osl2024.pre

otel:
  metrics: /metrics

admin:
  listen: 0.0.0.0:8081
  rolling: redis+c://redis-c1.0z4fbc.clustercfg.usw2.cache.amazonaws.com:6379

api_server:
  addr: 0.0.0.0:8080
  host: https://api-pre.kako.live

gateway:
  http: 0.0.0.0:8080
  rpc: 0.0.0.0:8082

worker:
  rpc: 0.0.0.0:8083

rpc:
  registry: p2p://?osl-gateway-ip-172-10-174-35.us-west-2.compute.internal=*************:8082&osl-gateway-ip-172-10-170-143.us-west-2.compute.internal=**************:8082

mq:
  dsn: nats://************:4222,nats://************:4222,nats://************:4222

dq:
  dsn: nsq://lookup@************:4161/************:4161/************:4161

db:
  dsn: mysql://admin:O5jIxe3dcMZB2uorbZMm@tcp(mysql-1.cluster-cvyuqco04cuc.us-west-2.rds.amazonaws.com:3306)/osl?charset=utf8mb4&parseTime=True&loc=Local
  cluster:
    cp: mysql://admin:O5jIxe3dcMZB2uorbZMm@tcp(mysql-1.cluster-ro-cvyuqco04cuc.us-west-2.rds.amazonaws.com:3306)/osl?charset=utf8mb4&parseTime=True&loc=Local
  prepare_stmt: true
  skip_default_tx: true
#  no_auto_migrate: true

mongo:
  dsn: mongodb://root:<EMAIL>:27017/?replicaSet=rs0&retryWrites=false
  db: osl

redis:
  dsn: redis://redis-s1.0z4fbc.ng.0001.usw2.cache.amazonaws.com:6379
  cluster:
    lock: redis+c://redis-c1.0z4fbc.clustercfg.usw2.cache.amazonaws.com:6379
    dist: redis+c://redis-c1.0z4fbc.clustercfg.usw2.cache.amazonaws.com:6379
    rank: redis+c://redis-c1.0z4fbc.clustercfg.usw2.cache.amazonaws.com:6379
    room: redis://redis-s1.0z4fbc.ng.0001.usw2.cache.amazonaws.com:6379

es:
  endpoint: https://elastic:<EMAIL>
#  no_auto_index: true

log:
  level: debug
  file: /var/log/kako/server.log
  buffer: true

payermax:
  endpoint: https://pay-gate-uat.payermax.com
  app_id: d872a2e91db847ef8d81daccc0ad7b4b
  merchant_no: SDP01010114255550
  callback_url: https://app-test.kako.live/app/pay_result.html
  private_key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  public_key: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqU8HqtnccPWvsM32Pgk8
    WznF+OOYfFmdspT1+u/9MHri/XMzsBtOr/PLw9h3lWZXW2zn/R84vufGUhM36/3s
    9GMb0dybvuoiW2js3y/vLzhapXVpYtzOwrhjo/1dZMcBOX5oygi2QB09BrlUCg3N
    5CK7m9DIttzBc4K15ruG6uducmxXVAEFG7cO30IhMml1+d8Dxa1dXTxmAT420nr9
    Xmeur3vL9H/Ioc1SnjaXKBFNa1CV5mys7volSPLDZ5B4oS+ViYq+kQo81xFrwhAK
    NTaqtb/ytunMTEHEMGCZ1eGgh1gxaqpbKnEBqp5cf/hFbslLNxFy6rzFlgPXJbDZ
    kwIDAQAB
    -----END PUBLIC KEY-----

ganopay:
  endpoint: https://pre.pay.haodamall.com
  merchant_id: 2000713000197336
  secret_key: 5cde8dfe0c894429bb6b86ec05f8406f
  callback_url: https://app-test.kako.live/app/pay_result.html
  private_key: |
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  public_key: |
    -----BEGIN PUBLIC KEY-----
    MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCToLb5QpQKyeT2c/94qo6Q8QA2
    CxESLxk9MGW6if5xRuZ4vSXXJ0gdOSrv6FbNS9m85z0+JP/C4QmSwSi3i/+wERA4
    Q2H3ZVxJHynqw/P3OwWFdg++8A5VjRoo6O3KceZekPQiK09Y6uwX22VZJD8BO6V5
    5XE1GrNmATEO84IjYwIDAQAB
    -----END PUBLIC KEY-----

binance:
  endpoint: https://bpay.binanceapi.com
  proxy: https://kako-rpx-iitejybluq.cn-hongkong.fcapp.run
  api_key: aesjabgq83nmiie7kv4mzbks7plxsvmdlxmrrur353rsip46ac74reixbkqb10al
  secret_key: f06zyk0k0aizrg9pb9zlas9z9bso3pp3vnsu7wjtozibmgo9lyjpnzqqc16btbfs
  callback_url: https://app-test.kako.live/app/pay_result.html

paypal:
  endpoint: https://api-m.sandbox.paypal.com
  client_id: AQmid3SUgE592WnyLicxekKnivaIAYgaewGIFHTuN603drpQqSLkOlBFyYeSG89p5QTA_w_41YNYjwEv
  secret_key: EGO2p-Occ256_ZFF0Dgk5wqSm8kQagkEOk265psa8s8_0G7qoUBXdrWJJTXjcaXUjLH9ai4-xUMMhqfF
  webhook_id: 9F755137CJ5224239
  callback_url: https://app-test.kako.live/app/pay_result.html

usolve:
  endpoint: https://mapi.shhntest.com/mapi/OpenAPI.do
  merchant_id: 25042217200895
  secret_key: mOTEXHGFG6uzZKl37j03kEIPeNC2x1iEzQzV1pniKWjZaOY2MnBL0SmVGqxDVftdXTKEGhQfbeiSxpA4ITmWB50t3pe0W2N12l31ZejwG7boThmSZlyju94TSBgCZTSY
  callback_url: https://app-test.kako.live/app/pay_result.html

oss:
  default:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    access_key_id: LTAI5tSNpo4Lr1LRPmeVqPHz
    access_key_secret: ******************************
    bucket: godzilla-live-test
    domain: godzilla-live-oss-test.sskjz.com
    region: cn-hangzhou

sts:
  default:
    endpoint: sts.cn-hangzhou.aliyuncs.com
    access_key_id: LTAI5tSNpo4Lr1LRPmeVqPHz
    access_key_secret: ******************************
    role_arn: acs:ram::1913526781769511:role/ramosspush

sms:
  vendor:
    0: fake://?code=123456

avatar:
  store: avatar
  style:
    thumb: 200x200
    large: 800x800

geoip:
  path: /app/conf/GeoLite2-City.mmdb

region:
  fallback: /app/live/configs/region.en.xml
  locale:
    zh: /app/live/configs/region.zh-cn.xml

locale:
  texts: /app/live/locales

volcengine:
  access_key_id: AKLTNTZjM2VjMTM2OTNjNGRlM2JkN2JmNTEzOWM3NjJiYTU
  secret_access_key: WTJKallqUXlZelEwTkdWak5HRXlOV0kzTVRnd1lqTmlZMlEyTkRJNVlURQ==
  rtc:
    app_id: 65fbffdddc8bdf0160d3a7a6
    app_key: be4f7c2aa2804f4b9829d61b3ae56228
    secret_key: BckQVj5daYBKtmQJgJW5Tg
  im:
    app_id: 889375
    app_key: 4APpJhEcPIUmM0efurSbkLhPt5hCjTu/Ec5wpQIR3Xg=
    region: ap-southeast-1
    secret_key: wbwamDg9XmqYb6lGNyJ3Cw
  translate:
    detector:
      region: cn-north-1
    translator:
      region: cn-north-1

dun:
  secretId: f7d4e041a105a085f5c86ae569b18c5a
  secretKey: aadac1e3efdcf68f191ec00fdb19f716
  lpBizId: 57701ca020624cd1a14a11361f9f2713
  faceBizId: 21beb4f952d341dfa37319ad17eee1bc

shortener:
  host: https://v-test.kako.live

share:
  host: https://app-test.kako.live

game:
  baishun:
    host: https://game-cn-test.jieyou.shop
    app_id: 2582112233
    app_key: SVHTJGLyewo6RH3JXhyaY9UXAgQhOjT9
    channel: momolive
    gsp: 101
    token_key: momolive20220815
  baishun2:
    host: https://game-cn-test.jieyou.shop
    app_id: 3024335435
    app_key: LkpMPe8XtYvIjOt3BtbF6AKVS0bOhLt5
    channel: kako
    gsp: 101
    token_key: momolive20220815
  # 自研游戏
  ss:
    host: https://test.com
    app_id: Ko123456
    app_key: 123456

# 内容安全
review:
  # 阿里云审核接入地址
  endpoint: green-cip.ap-southeast-1.aliyuncs.com
  access_key_id: LTAI5tSNpo4Lr1LRPmeVqPHz
  access_key_secret: ******************************

# 后台和数据相关
marketing:
  mongo:
    dsn: mongodb://root:<EMAIL>:27017/?replicaSet=rs0&retryWrites=false
    db: osl