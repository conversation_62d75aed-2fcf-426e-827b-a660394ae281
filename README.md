# OSL

## API Server

> API 服务

### 域名

`测试环境`：<https://osl-api-test.sskjz.com>

`正式环境`：<https://osl-api.sskjz.com>

### Swagger

```sh
sh ./scripts/swag.sh
```

<https://osl-api-test.sskjz.com/swagger/index/index.html>

### 运行

#### 开发环境

```sh
go run cmd/apiserver/main.go --config configs/config.yaml
```

#### 测试环境

```sh
chmod +x scripts/build-apiserver.sh
make deploy-apiserver-test
```

## WS Server

> WebSocket 服务

## 文档

[详细文档](./docs/README.md)
