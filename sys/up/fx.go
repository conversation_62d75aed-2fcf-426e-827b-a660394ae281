package up

import (
	"context"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/up"
	"gitlab.sskjz.com/go/up/rolling"
	_ "gitlab.sskjz.com/go/up/rolling/redis"
	_ "gitlab.sskjz.com/go/up/rolling/tair"
	"go.uber.org/fx"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Provide(desc *conf.Setting, shutdown fx.Shutdowner, vnd log.Vendor) (*up.Daemon, error) {
	up.SetLogger(vnd.Scope("daemon"))
	dm := up.New()

	if desc.Admin.Listen != "" {
		dm.SetOpts(up.WithAdminAPI(desc.Admin.Listen))
	}

	if desc.Admin.Rolling != "" {
		if locker, err := rolling.New(desc.Admin.Rolling); err != nil {
			return nil, err
		} else {
			dm.SetOpts(up.WithRolling(locker, env.App))
		}
	}

	dm.SetOpts(up.WithAppCloser(func(ctx context.Context) error { return shutdown.Shutdown() }))

	return dm, nil
}

func Invoke(lc fx.Lifecycle, dm *up.Daemon) {
	lc.Append(fx.Hook{OnStart: dm.Startup, OnStop: dm.Shutdown})
}
