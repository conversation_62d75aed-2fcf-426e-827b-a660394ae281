package gdk

import (
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

const (
	appWorker  = "osl-worker"
	appGateway = "osl-gateway"
)

var concurrency = map[string]int{
	appWorker:  co.Proc(),
	appGateway: 1,
}

var subOptions = map[string][]mq.SubOption{
	appGateway: {
		mq.LogCost("osl_gateway_broadcast"),
	},
}

func newConfig(name, listen string, desc *conf.Setting) gdk.Config {
	return gdk.Config{
		Concurrency:    concurrency[name],
		SubOptions:     subOptions[name],
		Name:           name,
		Listen:         listen,
		Registry:       desc.RPC.Registry,
		Queue:          desc.MQ.DSN,
		TopicUMsg:      "osl-messages",
		TopicEvent:     "osl-gw-event",
		TopicBroadcast: "osl-broadcast",
		EnableMetrics:  desc.OTEL.Metrics != "",
		EnableTracing:  desc.OTEL.Tracing != "",
	}
}
