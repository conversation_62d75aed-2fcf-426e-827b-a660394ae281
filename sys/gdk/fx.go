package gdk

import (
	"context"

	"gitlab.sskjz.com/go/gdk"
	_ "gitlab.sskjz.com/go/gdk/sdx/p2p"
	"gitlab.sskjz.com/go/log"
	_ "gitlab.sskjz.com/go/mq/nats"
	"go.uber.org/fx"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

func ProvideClient(c *conf.Setting, vnd log.Vendor) (gdk.Client, error) {
	cli, err := gdk.NewClient(newConfig(appWorker, "", c), vnd.Scope("gdk.client"))
	if err != nil {
		return nil, err
	}

	return cli, nil
}

func InvokeClient(lc fx.Lifecycle, c gdk.Client) error {
	lc.Append(fx.Hook{
		OnStop: c.Stop,
	})

	return c.Start(context.Background())
}

func ProvideWorker(c *conf.Setting, logic gdk.Logic, client gdk.Client, vnd log.Vendor) (gdk.Worker, error) {
	w, err := gdk.NewWorker(newConfig(appWorker, c.Worker.RPC, c), vnd.Scope("gdk.worker"), logic, client)
	if err != nil {
		return nil, err
	}

	return w, nil
}

func InvokeWorker(lc fx.Lifecycle, w gdk.Worker) error {
	lc.Append(fx.Hook{
		OnStop: w.Stop,
	})

	return w.Start(context.Background())
}

func ProvideServer(c *conf.Setting, gw gdk.Gateway, vnd log.Vendor) (gdk.Server, gdk.Proxy, error) {
	svc, err := gdk.NewServer(newConfig(appGateway, c.Gateway.RPC, c), vnd.Scope("gdk.server"), gw)
	if err != nil {
		return nil, nil, err
	}

	return svc, svc.Proxy(), nil
}

func InvokeServer(lc fx.Lifecycle, s gdk.Server) error {
	lc.Append(fx.Hook{
		OnStop: s.Stop,
	})

	return s.Start(context.Background())
}
