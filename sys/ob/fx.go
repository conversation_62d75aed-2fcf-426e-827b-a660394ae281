package ob

import (
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"gitlab.sskjz.com/go/up"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.21.0"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Invoke(desc *conf.Setting, dm *up.Daemon) error {
	if mp := desc.OTEL.Metrics; mp != "" {
		exporter, err := prometheus.New(prometheus.WithoutScopeInfo())
		if err != nil {
			return err
		}

		res, _ := resource.Merge(resource.Default(), resource.NewSchemaless(semconv.ServiceName(env.App)))
		otel.SetMeterProvider(metric.NewMeterProvider(metric.WithResource(res), metric.WithReader(exporter)))

		dm.AdminApi(func(g *gin.Engine) { g.GET(mp, gin.WrapH(promhttp.Handler())) })
	}

	return nil
}
