package sys

import (
	"time"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/pa"
	"gitlab.sskjz.com/go/up"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

func Initialize(desc *conf.Setting, dm *up.Daemon, vnd log.Vendor) {
	now.WeekStartDay = time.Monday

	pa.SetLogger(vnd.Scope("runtime"))

	dm.OperatorApi("/admin")

	if desc.PProf {
		dm.AdminApi(func(g *gin.Engine) { pprof.Register(g) })
	}
}
