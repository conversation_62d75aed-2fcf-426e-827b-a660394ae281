package cc

import (
	"context"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	_ "gitlab.sskjz.com/go/mq/nats"
	"go.uber.org/fx"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

func Provide(lc fx.Lifecycle, desc *conf.Setting, vnd log.Vendor) (cc.Sync, error) {
	logger := vnd.Scope("cc.sync")

	queue, err := mq.New(desc.MQ.DSN, mq.WithLogger(logger))
	if err != nil {
		return nil, err
	}

	sync := cc.NewSync(queue, cc.SyncLogger(logger))

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error { return sync.Start() },
		OnStop:  func(ctx context.Context) error { return queue.Close() },
	})

	return sync, nil
}
