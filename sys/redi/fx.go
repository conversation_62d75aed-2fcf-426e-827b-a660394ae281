package redi

import (
	"context"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
)

func Provide(lc fx.Lifecycle, desc *conf.Setting, vnd log.Vendor) (*redi.Client, *redi.Mutex, error) {
	cli, err := redi.NewClient(desc.Redis.DSN, desc.Redis.Cluster, vnd.Scope("redi"))
	if err != nil {
		return nil, nil, err
	}

	dc := desc.Redis.Cluster["lock"]
	if dc == "" {
		dc = desc.Redis.DSN
	}

	dm, err := redi.NewMutex(dc, vnd.Scope("redi.mux"))
	if err != nil {
		return nil, nil, err
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			return cli.Ping(ctx).Err()
		},
	})

	return cli, dm, nil
}
