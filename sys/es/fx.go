package es

import (
	"context"

	"gitlab.sskjz.com/go/es"
	_ "gitlab.sskjz.com/go/es/analyzer"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
)

func Provide(lc fx.Lifecycle, desc *conf.Setting, vnd log.Vendor) (*es.Client, error) {
	cli, err := es.NewClient(desc.ES.Endpoint, es.WithLogger(vnd.Scope("es.cli")))
	if err != nil {
		return nil, err
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			if desc.ES.NoAutoIndex {
				return nil
			}
			return cli.SyncIndex(ctx)
		},
	})

	return cli, nil
}
