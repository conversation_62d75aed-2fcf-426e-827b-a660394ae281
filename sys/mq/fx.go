package mq

import (
	"context"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	_ "gitlab.sskjz.com/go/mq/nsq"
	"go.uber.org/fx"

	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
)

func Provide(desc *conf.Setting, vnd log.Vendor) (mq.Queue, error) {
	return mq.New(desc.DQ.DSN, mq.WithLogger(vnd.Scope("queue")))
}

func Invoke(lc fx.Lifecycle, queue mq.Queue) {
	lc.Append(fx.Hook{
		OnStop: func(ctx context.Context) error { return queue.Close() },
	})
}
