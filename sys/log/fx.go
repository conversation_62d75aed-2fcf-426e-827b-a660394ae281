package log

import (
	"context"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func Provide(lc fx.Lifecycle, desc *conf.Setting) (log.Vendor, error) {
	var oo []log.Option
	if desc.Debug {
		oo = append(oo, log.Level(zapcore.DebugLevel))
		if desc.Log.Console {
			oo = append(oo, log.Console())
		}
	}

	if desc.Log.Level != "" {
		if lv, err := zapcore.ParseLevel(desc.Log.Level); err == nil {
			oo = append(oo, log.Level(lv))
		}
	}

	if desc.Log.File != "" {
		age := desc.Log.Age
		size := desc.Log.Size
		backup := desc.Log.Backup

		if age == 0 {
			age = 30
		}
		if size == 0 {
			size = 200
		}
		if backup == 0 {
			backup = 5
		}

		oo = append(oo, log.File(desc.Log.File, size, age, backup))

		if desc.Log.Buffer {
			oo = append(oo, log.Buffered())
		}
	}

	v, err := log.New(oo...)
	if err != nil {
		return nil, err
	}

	lc.Append(fx.Hook{OnStop: func(ctx context.Context) error {
		return v.Stop()
	}})

	zap.ReplaceGlobals(v.Logger())
	return v, nil
}
