package dq

import (
	"context"

	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	"go.uber.org/fx"
)

func Provide(q mq.Queue, vnd log.Vendor) *dq.Master {
	return dq.<PERSON>Master(q, dq.WithPrefix("osl-"), dq.<PERSON>(vnd.<PERSON>("dq.master")))
}

func Invoke(lc fx.Lifecycle, dm *dq.Master) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error { return dm.Start(ctx) },
		OnStop:  func(ctx context.Context) error { return dm.Stop(ctx) },
	})
}
