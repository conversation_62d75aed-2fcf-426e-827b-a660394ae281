package rpc

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/rpc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
)

func Provide(desc *conf.Setting, vnd log.Vendor) (rpc.Server, rpc.Client) {
	return rpc.NewServer(desc.MQ.DSN, vnd.Scope("rpc.server")), rpc.NewClient(desc.MQ.DSN)
}

func Invoke(lc fx.Lifecycle, rs rpc.Server, rc rpc.Client) {
	lc.Append(fx.Hook{OnStart: rs.Start, OnStop: func(ctx context.Context) error {
		return errors.Join(rs.Stop(ctx), rc.Close())
	}})
}
