module.exports = {
    apps: [
        {
            name: "osl-apiserver",
            script: "./bin/apiserver",
            cwd: "/home/<USER>/apps/osl",
            args: "-c /home/<USER>/apps/osl/configs/config.yaml",
            instance: 1,
            exec_interpreter: "none",
            env: {"OSL_LOG_FILE": "/home/<USER>/apps/osl/logs/apiserver.log", "OSL_ADMIN_LISTEN": ":8091"}
        },
        {
            name: "osl-scheduler",
            script: "./bin/scheduler",
            cwd: "/home/<USER>/apps/osl",
            args: "-c /home/<USER>/apps/osl/configs/config.yaml",
            instance: 1,
            exec_interpreter: "none",
            env: {"OSL_LOG_FILE": "/home/<USER>/apps/osl/logs/scheduler.log", "OSL_ADMIN_LISTEN": ":8095"}
        },
        {
            name: "osl-gateway",
            script: "./bin/gateway",
            cwd: "/home/<USER>/apps/osl",
            args: "-c /home/<USER>/apps/osl/configs/config.yaml",
            instance: 1,
            exec_interpreter: "none",
            env: {
                "HOSTNAME": "gw-dev1",
                "OSL_LOG_FILE": "/home/<USER>/apps/osl/logs/gateway.log",
                "OSL_ADMIN_LISTEN": ":8092",
            }
        },
        {
            name: "osl-public-gateway",
            script: "./bin/gateway",
            cwd: "/home/<USER>/apps/osl",
            args: "-c /home/<USER>/apps/osl/configs/config.yaml",
            instance: 1,
            exec_interpreter: "none",
            env: {
                "HOSTNAME": "gw-pub1",
                "OSL_LOG_FILE": "/home/<USER>/apps/osl/logs/public_gateway.log",
                "OSL_ADMIN_LISTEN": ":8093",
                "OSL_GATEWAY_PUBLIC": "true",
                "OSL_GATEWAY_HTTP": ":8079",
                "OSL_GATEWAY_RPC": ":8078",
            }
        },
        {
            name: "osl-roomsrv",
            script: "./bin/roomsrv",
            cwd: "/home/<USER>/apps/osl",
            args: "-c /home/<USER>/apps/osl/configs/config.yaml",
            instance: 1,
            exec_interpreter: "none",
            env: {"OSL_LOG_FILE": "/home/<USER>/apps/osl/logs/roomsrv.log", "OSL_ADMIN_LISTEN": ":8094"}
        }
    ]
}
