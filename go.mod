module gitlab.sskjz.com/overseas/live/osl

go 1.23

require (
	cloud.google.com/go/recaptchaenterprise/v2 v2.19.4
	firebase.google.com/go/v4 v4.14.1
	github.com/JGLTechnologies/gin-rate-limit v1.5.4
	github.com/RussellLuo/timingwheel v0.0.0-20220218152713-54845bda3108
	github.com/alibabacloud-go/darabonba-openapi/v2 v2.0.8
	github.com/alibabacloud-go/green-20220302/v2 v2.2.10
	github.com/alibabacloud-go/sts-20150401/v2 v2.0.2
	github.com/alibabacloud-go/tea v1.2.1
	github.com/alibabacloud-go/tea-utils/v2 v2.0.5
	github.com/alicebob/miniredis/v2 v2.33.0
	github.com/aliyun/aliyun-log-go-sdk v0.1.84
	github.com/aliyun/aliyun-oss-go-sdk v3.0.2+incompatible
	github.com/aliyun/fc-runtime-go-sdk v0.2.11
	github.com/avast/retry-go v3.0.0+incompatible
	github.com/bytedance/sonic v1.13.2
	github.com/caarlos0/env/v11 v11.1.0
	github.com/cespare/xxhash/v2 v2.3.0
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
	github.com/elastic/go-elasticsearch/v8 v8.16.0
	github.com/elastic/go-freelru v0.16.0
	github.com/forPelevin/gomoji v1.2.0
	github.com/gin-contrib/cors v1.7.2
	github.com/gin-contrib/pprof v1.5.0
	github.com/gin-contrib/zap v1.1.3
	github.com/gin-gonic/gin v1.10.0
	github.com/glebarez/sqlite v1.11.0
	github.com/go-playground/locales v0.14.1
	github.com/go-playground/universal-translator v0.18.1
	github.com/go-playground/validator/v10 v10.22.0
	github.com/go-resty/resty/v2 v2.13.1
	github.com/go-sql-driver/mysql v1.8.1
	github.com/golang-jwt/jwt/v4 v4.5.0
	github.com/google/go-querystring v1.1.0
	github.com/google/uuid v1.6.0
	github.com/gorilla/websocket v1.5.2
	github.com/hashicorp/go-version v1.7.0
	github.com/jedib0t/go-pretty/v6 v6.5.9
	github.com/jinzhu/now v1.1.5
	github.com/klassmann/cpfcnpj v0.0.0-20200907140233-a595c5fd8de1
	github.com/mitchellh/mapstructure v1.5.0
	github.com/nats-io/nuid v1.0.1
	github.com/nyaruka/phonenumbers v1.4.0
	github.com/olekukonko/tablewriter v0.0.5
	github.com/oschwald/geoip2-golang v1.11.0
	github.com/pkg/errors v0.9.1
	github.com/plutov/paypal/v4 v4.11.0
	github.com/prometheus/client_golang v1.19.1
	github.com/prometheus/common v0.54.0
	github.com/r3labs/sse/v2 v2.10.0
	github.com/redis/go-redis/v9 v9.8.0
	github.com/rs/xid v1.5.0
	github.com/samber/lo v1.47.0
	github.com/schollz/progressbar/v3 v3.14.4
	github.com/shopspring/decimal v1.4.0
	github.com/sourcegraph/conc v0.3.0
	github.com/spf13/cobra v1.8.1
	github.com/spf13/viper v1.19.0
	github.com/stretchr/testify v1.9.0
	github.com/swaggo/swag v1.16.3
	github.com/tidwall/btree v1.7.0
	github.com/tidwall/gjson v1.17.1
	github.com/tidwall/sjson v1.2.5
	github.com/valyala/bytebufferpool v1.0.0
	github.com/volcengine/volc-sdk-golang v1.0.163
	github.com/xuri/excelize/v2 v2.8.1
	gitlab.sskjz.com/go/appleid v0.0.0-20240430070656-4dfadbbd769b
	gitlab.sskjz.com/go/cc v0.0.0-20250211132825-9e45a29e5602
	gitlab.sskjz.com/go/co v0.0.0-20241016115532-199068e39eeb
	gitlab.sskjz.com/go/cron v0.0.0-20240320094450-795fbf6bbb0e
	gitlab.sskjz.com/go/dq v0.0.0-20240911073232-2ac92e7c2a75
	gitlab.sskjz.com/go/es v0.0.0-20241213123806-88439879781f
	gitlab.sskjz.com/go/ev v0.0.0-20240614143935-980ffd277397
	gitlab.sskjz.com/go/gdk v0.0.0-20240626141931-71bb4a2fe855
	gitlab.sskjz.com/go/gin-jwt v0.0.0-20240320092445-5b92e65ddfb4
	gitlab.sskjz.com/go/gin-metrics v0.0.0-20240320092842-9852292f4289
	gitlab.sskjz.com/go/i3n v0.0.0-20240702144835-bf907b05a313
	gitlab.sskjz.com/go/iap v0.0.0-20240812090800-1e219a348d43
	gitlab.sskjz.com/go/log v0.0.0-20240911080626-3416f521e307
	gitlab.sskjz.com/go/mg v0.0.0-20240911070654-5cce11c7d3d5
	gitlab.sskjz.com/go/mq v0.0.0-20241212083626-0d5ca0c42fa4
	gitlab.sskjz.com/go/ob v0.0.0-20240613094442-99d5438bf20a
	gitlab.sskjz.com/go/pa v0.0.0-20240320085910-7cab68d10ec9
	gitlab.sskjz.com/go/redi v0.0.0-20250522115036-49ceab90aa19
	gitlab.sskjz.com/go/rng v0.0.0-20240320120351-4b51a721ca7c
	gitlab.sskjz.com/go/rpc v0.0.0-20240914151510-457221dfd8a4
	gitlab.sskjz.com/go/sp v0.0.0-20240723091843-f4190b21c9a1
	gitlab.sskjz.com/go/unq v0.0.0-20250522114541-97d15516c29e
	gitlab.sskjz.com/go/up v0.0.0-20250522114435-66da05203697
	gitlab.sskjz.com/go/ws v0.0.0-20250421084102-ec8a44d6bda6
	go.mongodb.org/mongo-driver v1.16.0
	go.opentelemetry.io/otel v1.31.0
	go.opentelemetry.io/otel/exporters/prometheus v0.49.0
	go.opentelemetry.io/otel/sdk v1.31.0
	go.opentelemetry.io/otel/sdk/metric v1.31.0
	go.uber.org/atomic v1.11.0
	go.uber.org/fx v1.22.1
	go.uber.org/zap v1.27.0
	golang.org/x/crypto v0.32.0
	golang.org/x/exp v0.0.0-20240604190554-fc45aab8b7f8
	golang.org/x/net v0.34.0
	golang.org/x/oauth2 v0.25.0
	golang.org/x/sync v0.10.0
	golang.org/x/text v0.21.0
	golang.org/x/time v0.9.0
	google.golang.org/api v0.217.0
	gopkg.in/yaml.v2 v2.4.0
	gorm.io/driver/mysql v1.5.7
	gorm.io/gorm v1.25.10
	gorm.io/hints v1.1.2
	gorm.io/plugin/dbresolver v1.5.2
	moul.io/zapgorm2 v1.3.0
)

require (
	cloud.google.com/go v0.116.0 // indirect
	cloud.google.com/go/auth v0.14.0 // indirect
	cloud.google.com/go/auth/oauth2adapt v0.2.7 // indirect
	cloud.google.com/go/compute/metadata v0.6.0 // indirect
	cloud.google.com/go/firestore v1.17.0 // indirect
	cloud.google.com/go/iam v1.2.2 // indirect
	cloud.google.com/go/longrunning v0.6.2 // indirect
	cloud.google.com/go/storage v1.43.0 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/BurntSushi/toml v1.4.0 // indirect
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/MicahParks/keyfunc v1.9.0 // indirect
	github.com/akutz/memconn v0.1.0 // indirect
	github.com/alibaba/tair-go v1.2.2 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-spi v0.0.4 // indirect
	github.com/alibabacloud-go/debug v0.0.0-**************-9472017b5c68 // indirect
	github.com/alibabacloud-go/endpoint-util v1.1.0 // indirect
	github.com/alibabacloud-go/openapi-util v0.1.0 // indirect
	github.com/alibabacloud-go/tea-utils v1.4.4 // indirect
	github.com/alibabacloud-go/tea-xml v1.1.3 // indirect
	github.com/alicebob/gopher-json v0.0.0-**************-906a9b012302 // indirect
	github.com/alitto/pond v1.8.3 // indirect
	github.com/aliyun/credentials-go v1.3.1 // indirect
	github.com/apache/thrift v0.20.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.13.0 // indirect
	github.com/bluele/gcache v0.0.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cenk/backoff v2.2.1+incompatible // indirect
	github.com/cenkalti/backoff v2.2.1+incompatible // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/clbanning/mxj/v2 v2.5.5 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/dgryski/go-jump v0.0.0-20211018200510-ba001c3ffce0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dolthub/maphash v0.1.0 // indirect
	github.com/dolthub/swiss v0.2.1 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/edwingeng/doublejump v1.0.1 // indirect
	github.com/elastic/elastic-transport-go/v8 v8.6.0 // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/color v1.16.0 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.4 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/glebarez/go-sqlite v1.21.2 // indirect
	github.com/go-co-op/gocron v1.37.0 // indirect
	github.com/go-echarts/go-echarts/v2 v2.3.3 // indirect
	github.com/go-kit/kit v0.12.0 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.5.1 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-openapi/jsonpointer v0.19.5 // indirect
	github.com/go-openapi/jsonreference v0.20.0 // indirect
	github.com/go-openapi/spec v0.20.7 // indirect
	github.com/go-openapi/swag v0.22.3 // indirect
	github.com/go-ping/ping v1.1.0 // indirect
	github.com/go-redsync/redsync/v4 v4.12.1 // indirect
	github.com/go-task/slim-sprig/v3 v3.0.0 // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/gobwas/ws v1.4.0 // indirect
	github.com/goccy/go-json v0.10.3 // indirect
	github.com/godzie44/go-uring v0.0.0-20220926161041-69611e8b13d5 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/pprof v0.0.0-20240430035430-e4905b036c4e // indirect
	github.com/google/s2a-go v0.1.9 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.4 // indirect
	github.com/googleapis/gax-go/v2 v2.14.1 // indirect
	github.com/grandcat/zeroconf v1.0.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/jamiealquiza/tachymeter v2.0.0+incompatible // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/juju/ratelimit v1.0.2 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/kavu/go_reuseport v1.5.0 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.8 // indirect
	github.com/klauspost/reedsolomon v1.12.1 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/libp2p/go-sockaddr v0.2.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20240226150601-1dcf7310316a // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/miekg/dns v1.1.59 // indirect
	github.com/mitchellh/colorstring v0.0.0-20190213212951-d06e56a500db // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/montanaflynn/stats v0.7.1 // indirect
	github.com/mustafaturan/bus/v3 v3.0.3 // indirect
	github.com/nats-io/nats.go v1.37.0 // indirect
	github.com/nats-io/nkeys v0.4.7 // indirect
	github.com/nsqio/go-nsq v1.1.0 // indirect
	github.com/onsi/ginkgo/v2 v2.17.2 // indirect
	github.com/oschwald/maxminddb-golang v1.13.0 // indirect
	github.com/panjf2000/ants/v2 v2.10.0 // indirect
	github.com/panjf2000/gnet/v2 v2.5.9 // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/philhofer/fwd v1.1.2 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/procfs v0.15.0 // indirect
	github.com/quic-go/quic-go v0.43.1 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rs/cors v1.11.0 // indirect
	github.com/rubyist/circuitbreaker v2.2.1+incompatible // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/shirou/gopsutil/v3 v3.24.2 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/smallnest/quick v0.2.0 // indirect
	github.com/smallnest/rpcx v1.8.31 // indirect
	github.com/smallnest/statsview v1.0.1 // indirect
	github.com/soheilhy/cmux v0.1.5 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/templexxx/cpufeat v0.0.0-20180724012125-cef66df7f161 // indirect
	github.com/templexxx/xor v0.0.0-20191217153810-f85b25db303b // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tinylib/msgp v1.1.9 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/tklauser/go-sysconf v0.3.13 // indirect
	github.com/tklauser/numcpus v0.7.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/valyala/fastrand v1.1.0 // indirect
	github.com/vmihailenco/msgpack/v5 v5.4.1 // indirect
	github.com/vmihailenco/tagparser/v2 v2.0.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/xtaci/kcp-go v5.4.20+incompatible // indirect
	github.com/xuri/efp v0.0.0-20231025114914-d1ff6096ae53 // indirect
	github.com/xuri/nfp v0.0.0-20230919160717-d98342af3f05 // indirect
	github.com/youmark/pkcs8 v0.0.0-20240424034433-3c2c7870ae76 // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.54.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.54.0 // indirect
	go.opentelemetry.io/otel/metric v1.31.0 // indirect
	go.opentelemetry.io/otel/trace v1.31.0 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	go.uber.org/dig v1.17.1 // indirect
	go.uber.org/mock v0.4.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/mod v0.18.0 // indirect
	golang.org/x/sys v0.29.0 // indirect
	golang.org/x/term v0.28.0 // indirect
	golang.org/x/tools v0.22.0 // indirect
	google.golang.org/appengine/v2 v2.0.2 // indirect
	google.golang.org/genproto v0.0.0-20241118233622-e639e219e697 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250115164207-1a7da9e5054f // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250115164207-1a7da9e5054f // indirect
	google.golang.org/grpc v1.69.4 // indirect
	google.golang.org/protobuf v1.36.3 // indirect
	gopkg.in/cenkalti/backoff.v1 v1.1.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	modernc.org/libc v1.22.5 // indirect
	modernc.org/mathutil v1.5.0 // indirect
	modernc.org/memory v1.5.0 // indirect
	modernc.org/sqlite v1.23.1 // indirect
)
