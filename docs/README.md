# 文档

## 测试环境

> 服务器：149.129.250.251
> 域名：<https://osl-api-test.sskjz.com>
> 端口：8080

## Figma

<https://www.figma.com/file/OulRD1Js28WWDQUZSGgrBU/Godzilla-Live?type=design&node-id=210%3A0&mode=design&t=TrscUYwH1kYrutoq-1>

## 项目进度

<https://lfzebkjel9.feishu.cn/wiki/PrXbwU12GiMHyNkAN3Lcqb5enKd?table=tblGYODbHzT9avpL&view=vewBHLaAGe>

## 消息推送

<https://j5e7trzg7u.feishu.cn/wiki/Yzi0wtyVniN5ZPkNkXlcyFyYnng>

## 接口

> url不使用下划线，多个单词全小写
> 不甜提供正在直播的房间列表信息/主播信息
> <http://live-api.migowish.com/api/v2/test/getHotList_v2>

* 关注用户
* 取消关注用户
* 粉丝团面板信息
* 粉丝团任务
* 加入粉丝团
* 分发区-人气榜列表-根据人气票礼物等计数
* 分发区-小时榜列表-礼物数据
* 礼物列表-礼物分类、标签、红包
* 背包数据
* 礼物面板-余额/等级/经验信息
* 充值产品列表
* 预下单
* 查询支付结果
* 短触区-红包-信息
* 短触区-红包-参与
* 礼物展馆
