package main

import (
	"github.com/spf13/cobra"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/activity"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/adjust"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/data"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/device"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/draw"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/reindex"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/user"
)

func main() {
	command := &cobra.Command{
		Use:     "ctl",
		Version: "1.0.0",
		Long:    `The Control description`,
		RunE:    func(cmd *cobra.Command, args []string) error { return cmd.Help() },
	}

	command.PersistentFlags().String("config", "", "config file path")

	command.AddCommand(
		user.Command(),
		reindex.Command(),
		im.Command(),
		journal.Command(),
		live.Command(),
		agency.Command(),
		draw.Command(),
		anchor.Command(),
		fund.Command(),
		game.Command(),
		pay.Command(),
		adjust.Command(),
		props.Command(),
		device.Command(),
		dress.Command(),
		activity.Command(),
		seller.Command(),
		data.Command(),
	)

	if err := command.Execute(); err != nil {
		panic(err)
	}
}
