package main

import (
	"flag"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver"
	"gitlab.sskjz.com/overseas/live/osl/internal/goods"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"go.uber.org/fx"
)

func main() {
	var config string
	flag.StringVar(&config, "c", "", "-c config.yaml")
	flag.Parse()

	env.App = "osl-manage"

	app := fx.New(
		fx.Provide(
			conf.Provide(config),
		),
		apiserver.Module,
		goods.Module,
		manage.Module,
	)

	app.Run()
}
