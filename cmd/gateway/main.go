package main

import (
	"flag"

	"gitlab.sskjz.com/overseas/live/osl/internal/gateway"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/sys"
	"gitlab.sskjz.com/overseas/live/osl/sys/co"
	"gitlab.sskjz.com/overseas/live/osl/sys/gdk"
	"gitlab.sskjz.com/overseas/live/osl/sys/log"
	"gitlab.sskjz.com/overseas/live/osl/sys/ob"
	"gitlab.sskjz.com/overseas/live/osl/sys/up"
	"go.uber.org/fx"
)

func main() {
	var config string
	flag.StringVar(&config, "c", "", "-c config.yaml")
	flag.Parse()

	env.App = "osl-gateway"

	app := fx.New(
		fx.Provide(
			conf.Provide(config),
			gdk.ProvideServer,
			up.Provide,
			log.Provide,
			gateway.Provide,
		),
		fx.Invoke(
			sys.Initialize,
			ob.Invoke,
			co.Invoke,
			gateway.Invoke,
			gdk.InvokeServer,
			up.Invoke,
		),
	)

	app.Run()
}
