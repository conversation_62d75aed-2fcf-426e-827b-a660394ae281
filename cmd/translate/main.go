package main

import (
	"context"
	"encoding/csv"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/schollz/progressbar/v3"
	"gitlab.sskjz.com/overseas/live/osl/pkg/volc/mt"
	"golang.org/x/time/rate"
)

func main() {
	path := flag.String("path", "./translate.csv", "path to the file")
	limit := flag.Int("limit", 3, "limit records to translate")
	flag.Parse()

	// do csv parse

	file, err := os.Open(*path)
	if err != nil {
		log.Fatalf("open file failed: %v", err)
	}
	defer file.Close()

	cr := csv.NewReader(file)

	records, err := cr.ReadAll()
	if err != nil {
		log.Fatalf("read csv failed: %v", err)
	}

	if *limit != 0 && len(records) > *limit {
		records = records[:*limit]
	}

	var (
		limiter  = rate.NewLimiter(rate.Every(time.Second/10), 10)
		ak       = "AKLTNTZjM2VjMTM2OTNjNGRlM2JkN2JmNTEzOWM3NjJiYTU"
		sk       = "WTJKallqUXlZelEwTkdWak5HRXlOV0kzTVRnd1lqTmlZMlEyTkRJNVlURQ=="
		c        = mt.NewTranslator(ak, sk, `ap-southeast-1`, time.Second)
		dstLangs = []string{"en", "zh", "pt"}
		pb       = progressbar.Default(int64(len(records) * len(dstLangs)))
	)

	fmt.Printf("start translate %d records\n", len(records))

	out, err := os.Create("output.csv")
	if err != nil {
		log.Fatalf("create output file failed: %v", err)
	}
	defer out.Close()

	out.Write([]byte("\xEF\xBB\xBF"))
	tw := csv.NewWriter(out)
	tw.Write([]string{"No.", "RequestId", "Text", "Detected", "To", "Translation/Error", "Cost"})
	for i, record := range records {
		txt := record[0]

		for _, toLang := range dstLangs {
			limiter.Wait(context.Background())
			now := time.Now()
			resp, err := c.TranslateText(context.TODO(), &mt.TranslateTextInput{
				TargetLanguage: toLang,
				TextList:       []string{txt},
			})
			cost := time.Since(now)
			pb.Add(1)

			requestId := ""
			if resp != nil {
				requestId = resp.ResponseMetadata.RequestId
			}

			if err != nil {
				tw.Write([]string{fmt.Sprintf("%d", i), requestId, txt, "", toLang, err.Error(), cost.String()})
			} else {
				if len(resp.TranslationList) == 0 {
					tw.Write([]string{fmt.Sprintf("%d", i), requestId, txt, "", toLang, "empty", cost.String()})
				} else {
					tw.Write([]string{fmt.Sprintf("%d", i), requestId, txt, resp.TranslationList[0].DetectedSourceLanguage,
						toLang, resp.TranslationList[0].Translation, cost.String()})
				}
			}
		}
	}
	tw.Flush()
}
