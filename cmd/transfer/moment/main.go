package main

import (
	"flag"
	"time"

	ev2 "gitlab.sskjz.com/go/ev"
	log2 "gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/cmd/transfer/moment/pross"
	"gitlab.sskjz.com/overseas/live/osl/internal/recommender"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/log"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"go.uber.org/fx"
)

func main() {
	var filename, config string
	flag.StringVar(&filename, "file", "", ".bson file")
	flag.StringVar(&config, "c", "", "-c config.yaml")
	flag.Parse()

	app := fx.New(
		fx.Provide(
			conf.Provide(config),
			cron.Provide,
			unq.Provide,
			db.ProvideGORM,
			log.Provide,
			ev.Provide,
			redi.Provide,
			recommender.Provide,
		),
		fx.Invoke(
			recommender.Invoke,
			func(db *db.Client, ev ev2.Bus, log log2.Vendor) { pross.TransferMoment(filename, db, ev, log) },
			func(c fx.Shutdowner) error { return c.Shutdown() },
		),
		fx.StopTimeout(3*time.Second),
	)

	app.Run()
}
