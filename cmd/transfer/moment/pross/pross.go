package pross

import (
	"bufio"
	"bytes"
	"context"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	moment2 "gitlab.sskjz.com/overseas/live/osl/internal/moment"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type MomentBson struct {
	Id           primitive.ObjectID `bson:"_id"`
	ID           int                `bson:"id"`
	Mode         int                `bson:"mode"`
	Useridx      int                `bson:"useridx"`
	Content      string             `bson:"content"`
	Append       []Append           `bson:"append"`
	Likeusers    []LikeUser         `bson:"likesers"`
	Likecount    int                `bson:"likecount"`
	Comments     []Comment          `bson:"comments"`
	Commentcount int                `bson:"commentcount"`
	Date         time.Time          `bson:"date"`
}

type Append struct {
	Oriheight int    `bson:"oriheight"`
	Thumbnail string `bson:"thumbnail"`
	Thuwidth  int    `bson:"thuwidth"`
	Original  string `bson:"original"`
	Oriwidth  int    `bson:"oriwidth"`
	Video     string `bson:"video"`
	Sec       int64  `bson:"sec"`
}

type LikeUser struct {
	Useridx int `bson:"useridx"`
}

type Comment struct {
	Id        int        `bson:"id"`
	Useridx   int        `bson:"useridx"`
	LikeCount int        `bson:"likecount"`
	Content   string     `bson:"content"`
	Reply     int        `bson:"reply"`
	Date      time.Time  `bson:"date"`
	LikeUsers []LikeUser `bson:"likeusers"`
}

// TransferMoment
// 幂等性支持：写文件将处理过的id记录下来，实现幂等
func TransferMoment(filename string, db *db.Client, ev ev.Bus, log log.Vendor) {
	fileContent, err := os.ReadFile(filename)
	if err != nil {
		// Try adding ".bson" to the filename
		fileContent, err = os.ReadFile(filename + ".bson")
		if err != nil {
			log.Logger().Error("read file err", zap.Error(err))
			return
		}
	}

	recordFile, err := os.OpenFile("./record.txt", os.O_CREATE|os.O_APPEND|os.O_RDWR, os.ModePerm)
	if err != nil {
		log.Logger().Error("open record file err", zap.Error(err))
		return
	}

	var m = map[string]struct{}{}
	scanner := bufio.NewScanner(recordFile)
	for scanner.Scan() {
		m[scanner.Text()] = struct{}{}
	}

	idx := 0
	for {
		var (
			size int32
			doc  []byte
		)
		err := binary.Read(bytes.NewReader(fileContent), binary.LittleEndian, &size)
		if err == io.EOF {
			break
		}
		idx++

		doc, fileContent = fileContent[0:size], fileContent[size:]

		var value MomentBson
		if err := bson.Unmarshal(doc, &value); err != nil {
			log.Logger().Error("bson unmarshal err", zap.Error(err))
		}

		if value.Id.IsZero() {
			log.Logger().Info("doc is nul", zap.Any("value", value), zap.Int32("size", size))
			continue
		}

		if _, ok := m[fmt.Sprintf("%d", value.ID)]; ok {
			log.Logger().Info("doc is transferred", zap.Any("value", value), zap.Int32("size", size))
			continue
		}

		// 写入数据库
		// 1. moment库
		moment, err := convertMoment(&value)
		if err != nil {
			log.Logger().Error("convert moment err", zap.Any("value", value), zap.Error(err))
			continue
		}

		// 测试只迁移纯文字
		if moment.Type != moment2.TypeMomentCharacter {
			log.Logger().Warn("skip not character")
			continue
		}
		db.Save(&moment)

		// 1.1 gorse 动态发布事件
		ev.Emit(context.Background(), evt.MomentPublish, &evt.PublishMoment{
			MomentId: strconv.Itoa(int(moment.ID)),
			AnchorId: moment.UserId,
		})

		// 2. 评论
		if value.Commentcount > 0 && len(value.Comments) > 0 {
			err := transferComment(moment.ID, &value, db, log)
			if err != nil {
				log.Logger().Error("transfer moment comment err", zap.Any("value", value), zap.Error(err))
				continue
			}
		}

		// 3. 点赞
		if value.Likecount > 0 && len(value.Likeusers) > 0 {
			err := transferLike(moment.ID, &value, db, log)
			if err != nil {
				log.Logger().Error("transfer moment like err", zap.Any("value", value), zap.Error(err))
				continue
			}
		}

		// 记录record
		m[fmt.Sprintf("%d", value.ID)] = struct{}{}
		io.WriteString(recordFile, fmt.Sprintf("%d\n", value.ID))
		log.Logger().Info("transfer success", zap.Any("value", value), zap.Any("moment", moment))
		if len(value.Comments) > 0 {
			os.Exit(1)
		}
	}

	log.Logger().Info("done", zap.Int("count", idx))
}

func transferComment(mid uint, mb *MomentBson, db *db.Client, log log.Vendor) error {
	var (
		momentCommentCount int
	)

	res := make([]*moment2.MomentComments, 0, len(mb.Comments))
	cm := map[int]*moment2.MomentComments{}
	ocm := map[int]Comment{}
	for _, v := range mb.Comments {
		commentUserId := getUserId(v.Useridx)
		var comment *moment2.MomentComments
		if v.Reply > 0 {
			comment = &moment2.MomentComments{
				MomentId:   mid,
				ParentId:   0,
				Type:       moment2.TypeCommentReply,
				TargetId:   0,
				UserId:     commentUserId,
				ToUserId:   "",
				Text:       v.Content,
				TextExtra:  nil,
				ImageList:  nil,
				Status:     0,
				ReplyCount: 0,
				LikeCount:  v.LikeCount,
				CreatedAt:  v.Date,
				UpdatedAt:  v.Date,
			}
		} else {
			comment = &moment2.MomentComments{
				MomentId:   mid,
				ParentId:   0,
				Type:       moment2.TypeComment,
				TargetId:   0,
				UserId:     commentUserId,
				ToUserId:   "",
				Text:       v.Content,
				TextExtra:  nil,
				ImageList:  nil,
				Status:     1,
				ReplyCount: 0,
				LikeCount:  v.LikeCount,
				CreatedAt:  v.Date,
				UpdatedAt:  v.Date,
			}
		}

		res = append(res, comment)
		cm[v.Id] = comment
		ocm[v.Id] = v
	}

	var (
		firstComment uint
		commentCount int
	)
	for _, v := range mb.Comments {
		comment := cm[v.Id]
		if comment == nil {
			continue
		}

		if comment.Type == moment2.TypeCommentReply {
			var (
				originPid int = v.Reply
				parentId  uint
				pass      bool
			)
			for {
				if originPid <= 0 {
					break
				}
				p, ok := ocm[originPid]
				if !ok {
					pass = true
					break
				}
				parentId = cm[p.Id].ID
				originPid = p.Reply
			}

			if pass {
				log.Logger().Warn("pass reply", zap.Int("id", v.Id), zap.Any("comment", mb.Comments))
				continue
			}

			// parent
			comment.ParentId = parentId

			// target
			if target, ok := ocm[v.Reply]; ok {
				tc := cm[target.Id]
				comment.TargetId = tc.ID
				comment.ToUserId = tc.UserId
			}
		}

		// 写入db
		db.Save(&comment)
		commentCount++
		if firstComment == 0 && comment.UserId != getUserId(mb.Useridx) {
			firstComment = comment.ID
		}

		// 动态评论数+1
		momentCommentCount++

		// 评论点赞
		if v.LikeCount > 0 && len(v.LikeUsers) > 0 {
			likes := make([]moment2.MomentCommentLikes, 0)
			for _, likeUser := range v.LikeUsers {
				likes = append(likes, moment2.MomentCommentLikes{
					TargetId:  comment.ID,
					UserId:    getUserId(likeUser.Useridx),
					Type:      1,
					CreatedAt: time.Now(),
				})
			}

			// 批量写入db
			db.Save(likes)
			log.Logger().Info("insert comment like success", zap.Uint("cid", comment.ID), zap.Int("count", len(likes)))
		}
	}

	// 动态评论数、首评入库
	if firstComment > 0 {
		db.Model(&moment2.Moments{}).Where("id = ?", mid).Update("first_comment", firstComment)
	}

	log.Logger().Info("insert moment comment success", zap.Uint("mid", mid), zap.Int("count", commentCount))

	return nil
}

func transferLike(mid uint, mb *MomentBson, db *db.Client, log log.Vendor) error {
	res := make([]moment2.MomentLikes, 0, len(mb.Likeusers))
	for _, v := range mb.Likeusers {
		res = append(res, moment2.MomentLikes{
			MomentId:  mid,
			UserId:    getUserId(v.Useridx),
			CreatedAt: time.Now(),
		})
	}

	if err := db.Save(res).Error; err != nil {
		return err
	}

	log.Logger().Info("insert moment like success", zap.Uint("mid", mid), zap.Int("count", len(res)))
	return nil
}

func convertMoment(mb *MomentBson) (*moment2.Moments, error) {
	momentUserId := getUserId(mb.Useridx)
	var (
		desc       string
		data       string
		momentType int
	)
	switch mb.Mode {
	case 0:
		b, _ := sonic.Marshal(moment2.MomentCharacter{Data: mb.Content})
		data = string(b)
		momentType = moment2.TypeMomentCharacter
	case 1:
		var urls []string
		for _, v := range mb.Append {
			if v.Original == "" {
				continue
			}
			urls = append(urls, v.Original)
		}
		if len(urls) > 0 {
			b, _ := sonic.Marshal(moment2.MomentPicture{PictureUrls: urls})
			data = string(b)
			desc = mb.Content
			momentType = moment2.TypeMomentPicture
		} else if len(urls) == 0 && len(mb.Content) > 0 {
			// 切换为0
			b, _ := sonic.Marshal(moment2.MomentCharacter{Data: mb.Content})
			data = string(b)
			momentType = moment2.TypeMomentCharacter
		} else {
			return nil, errors.New("picture and content is empty")
		}
	case 3:
		var vid = moment2.MomentVideo{}
		for _, v := range mb.Append {
			if v.Thumbnail == "" || v.Video == "" {
				continue
			}
			vid.Cover = v.Thumbnail
			vid.VideoUrl = v.Video
			vid.Duration = v.Sec
		}
		if vid.VideoUrl != "" {
			b, _ := sonic.Marshal(vid)
			data = string(b)
			desc = mb.Content
			momentType = moment2.TypeMomentVideo
		} else if vid.VideoUrl == "" && len(mb.Content) > 0 {
			// 切换为0
			b, _ := sonic.Marshal(moment2.MomentCharacter{Data: mb.Content})
			data = string(b)
			momentType = moment2.TypeMomentCharacter
		} else {
			return nil, errors.New("video urls is empty")
		}
	}

	moment := moment2.Moments{
		Type:          momentType,
		UserId:        momentUserId,
		Title:         "",
		Desc:          desc,
		DescExtra:     nil,
		Data:          data,
		Status:        0,
		VisibleStatus: 0,
		TopValue:      0,
		FirstComment:  0,
		CreatedAt:     mb.Date,
		UpdatedAt:     mb.Date,
	}

	return &moment, nil
}

func getUserId(original int) string {
	// 获取用户id
	return fmt.Sprintf("%d", original)
}
