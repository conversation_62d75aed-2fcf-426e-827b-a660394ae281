package main

import (
	"fmt"
	"os"
	"slices"

	"gitlab.sskjz.com/overseas/live/osl/internal/ctl/draw"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"

	"gopkg.in/yaml.v2"
)

var templates = []draw2.Pool{
	{
		Total: 4830,
		Prizes: []draw2.Prize{
			{Mul: 500, Avg: 3, Random: 3},
			{Mul: 300, Avg: 1, Random: 1},
			{Mul: 100, Avg: 2, Random: 2},
			{Mul: 10, Avg: 20, Random: 10},
		},
	},
	{
		Total: 10300,
		Prizes: []draw2.Prize{
			{Mul: 1000, Avg: 1, Random: 0},
			{Mul: 500, Avg: 7, Random: 4},
			{Mul: 300, Avg: 3, Random: 2},
			{Mul: 100, Avg: 6, Random: 4},
			{Mul: 10, Avg: 20, Random: 25},
		},
	},
	{
		Total: 5830,
		Prizes: []draw2.Prize{
			{Mul: 500, Avg: 4, Random: 2},
			{Mul: 300, Avg: 2, Random: 2},
			{Mul: 100, Avg: 4, Random: 4},
			{Mul: 10, Avg: 20, Random: 5},
		},
	},
	{
		Total: 16530,
		Prizes: []draw2.Prize{
			{Mul: 1000, Avg: 1, Random: 0},
			{Mul: 500, Avg: 11, Random: 7},
			{Mul: 300, Avg: 5, Random: 4},
			{Mul: 100, Avg: 10, Random: 8},
			{Mul: 10, Avg: 10, Random: 5},
		},
	},
	{
		Total: 7350,
		Prizes: []draw2.Prize{
			{Mul: 1000, Avg: 1, Random: 0},
			{Mul: 500, Avg: 3, Random: 4},
			{Mul: 300, Avg: 2, Random: 2},
			{Mul: 100, Avg: 4, Random: 4},
			{Mul: 10, Avg: 15, Random: 20},
		},
	},
	{
		Total: 2930,
		Prizes: []draw2.Prize{
			{Mul: 500, Avg: 3, Random: 2},
			{Mul: 10, Avg: 6, Random: 4},
		},
	},
	{
		Total: 6330,
		Prizes: []draw2.Prize{
			{Mul: 1000, Avg: 1, Random: 0},
			{Mul: 500, Avg: 4, Random: 2},
			{Mul: 300, Avg: 2, Random: 1},
			{Mul: 100, Avg: 4, Random: 2},
			{Mul: 10, Avg: 12, Random: 3},
		},
	},
	{
		Total: 13030,
		Prizes: []draw2.Prize{
			{Mul: 1000, Avg: 1, Random: 0},
			{Mul: 500, Avg: 9, Random: 6},
			{Mul: 300, Avg: 4, Random: 2},
			{Mul: 100, Avg: 8, Random: 4},
			{Mul: 10, Avg: 10, Random: 10},
		},
	},
}

func main() {
	path := os.Args[1]

	bs, _ := os.ReadFile(path)
	var pools []draw2.Pool
	yaml.Unmarshal(bs, &pools)

	fmt.Printf(draw.ShowPools(pools, nil))

	var pathOut string
	if len(os.Args) > 2 {
		pathOut = os.Args[2]
	}

	slices.SortFunc(pools, func(i, j draw2.Pool) int {
		// id asc
		return int(i.ID) - int(j.ID)
	})

	for i := range pools {
		pools[i].Prizes = templates[i%8].Prizes
		pools[i].Total = templates[i%8].Total
	}

	fmt.Printf(draw.ShowPools(pools, nil))

	if pathOut != "" {
		bs, _ := yaml.Marshal(pools)
		os.WriteFile(pathOut, bs, 0644)
	}
}
