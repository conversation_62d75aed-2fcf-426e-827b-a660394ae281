package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

func main() {
	var (
		roomId  string
		msgType int
	)

	flag.StringVar(&roomId, "roomId", "66f7bff0c87f0fadc3bc8921", "-roomId 66f7bff0c87f0fadc3bc8921")
	flag.IntVar(&msgType, "msgType", 90001, "-msgType 90001")
	flag.Parse()

	fmt.Println("connect to", roomId)
	if msgType != 0 {
		fmt.Println("filter with", msgType)
	}

	ws, err := client.NewSocket(fmt.Sprintf("wss://h5-ws.kako.live/ws/v1?roomId=%s", roomId), client.WithEvListener(func(bytes []byte) {
		p, err := protocol.DecodePacket(bytes)
		if err != nil {
			fmt.Println(err)
			return
		}
		if msgType != 0 && p.MsgType != uint32(msgType) {
			return
		}
		fmt.Println("[", p.MsgType, "]", protocol.MsgType(p.MsgType).String())
		fmt.Println(string(p.Payload))
		fmt.Println(strings.Repeat("-", 30))
	}))
	if err != nil {
		panic(err)
	}
	defer ws.Close()

	stopCh := make(chan os.Signal)
	signal.Notify(stopCh, syscall.SIGINT, syscall.SIGTERM)
	<-stopCh
}
