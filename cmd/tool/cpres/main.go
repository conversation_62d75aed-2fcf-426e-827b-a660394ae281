package main

import (
	"encoding/json"
	"fmt"
	"hash/crc32"
	"os"
	"slices"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/api/swagger/docs"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/role"
)

type swagApi struct {
	Summary     string `json:"summary"`
	Description string `json:"description"`
}

type swagDoc struct {
	Paths map[string]map[string]swagApi
}

func main() {
	var doc swagDoc
	if err := sonic.UnmarshalString(docs.SwaggerInfo.ReadDoc(), &doc); err != nil {
		panic(err)
	}
	for k := range doc.Paths {
		if !strings.HasPrefix(k, "/api/v1/manage") {
			delete(doc.Paths, k)
			continue
		}
	}

	uris := lo.Keys(doc.Paths)
	slices.Sort(uris)

	var (
		ress []role.Res
		grps = make(map[string]struct{})
	)

	for _, uri := range uris {
		if len(doc.Paths[uri]) > 1 {
			panic(fmt.Sprintf("same uri with multiple methods: %s", uri))
		}
		for k := range doc.Paths[uri] {
			api := doc.Paths[uri][k]
			groups := strings.Split(api.Summary, "/")
			var (
				prefix string
				parent string
			)
			if len(groups) > 1 {
				gns := make([]string, 0, 1) // name
				gks := make([]string, 0, 1) // key
				for i := 0; i < len(groups)-1; i++ {
					gn := groups[i]
					gns = append(gns, gn)
					gks = append(gks, fmt.Sprintf("%08x", crc32.ChecksumIEEE([]byte(gn))))
					gkk := strings.Join(gks, ".")
					if _, ok := grps[gkk]; !ok {
						grps[gkk] = struct{}{}
						ress = append(ress, role.Res{
							Key:   gkk,
							Name:  strings.Join(gns, "/"),
							Desc:  "",
							Group: true,
						})
					}
				}
				prefix = strings.Join(gns, "/") + "/"
				parent = strings.Join(gks, ".")
			}
			ress = append(ress, role.Res{
				Parent: parent,
				Key: func(uri string) string {
					uri = strings.TrimPrefix(uri, "/api/v1/manage/")
					return strings.ReplaceAll(uri, "/", ".")
				}(uri),
				Name: strings.TrimPrefix(api.Summary, prefix),
				Desc: api.Description,
			})
			break
		}
	}

	bs, err := json.MarshalIndent(ress, "", "\t")
	if err != nil {
		panic(err)
	}

	if err := os.WriteFile("internal/manage/role/resource.json", bs, 0644); err != nil {
		panic(err)
	}
}
