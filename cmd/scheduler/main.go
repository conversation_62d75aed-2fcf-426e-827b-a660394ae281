package main

import (
	"flag"

	"gitlab.sskjz.com/overseas/live/osl/internal/activity/halloween"
	"gitlab.sskjz.com/overseas/live/osl/internal/adjust"
	"gitlab.sskjz.com/overseas/live/osl/internal/adm"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/binance"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/bs"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/bs2"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/jing"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/ss"
	"gitlab.sskjz.com/overseas/live/osl/internal/ganopay"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift/trade"
	"gitlab.sskjz.com/overseas/live/osl/internal/goods"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/redpacket"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/rs"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/rtc"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/sysroom"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data/dob"
	dobCharging "gitlab.sskjz.com/overseas/live/osl/internal/manage/data/dob/charging"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/remit"
	"gitlab.sskjz.com/overseas/live/osl/internal/marketing"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/usolve"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/internal/paypal"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing/cycle"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/pgift"
	"gitlab.sskjz.com/overseas/live/osl/internal/push"
	pushRE "gitlab.sskjz.com/overseas/live/osl/internal/push/pre"
	"gitlab.sskjz.com/overseas/live/osl/internal/recommender"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/online"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
	"gitlab.sskjz.com/overseas/live/osl/internal/ul"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/act"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/internal/withdraw"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fcm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/logic"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/translate"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/co"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/dq"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/gdk"
	"gitlab.sskjz.com/overseas/live/osl/sys/log"
	"gitlab.sskjz.com/overseas/live/osl/sys/mq"
	"gitlab.sskjz.com/overseas/live/osl/sys/ob"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/rpc"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"gitlab.sskjz.com/overseas/live/osl/sys/up"
	"go.uber.org/fx"
)

func main() {
	var config string
	flag.StringVar(&config, "c", "", "-c config.yaml")
	flag.Parse()

	env.App = "osl-scheduler"

	app := fx.New(
		fx.Provide(
			conf.Provide(config),
			log.Provide,
			redi.Provide,
			db.ProvideMongo,
			db.ProvideGORM,
			unq.Provide,
			ev.Provide,
			cc.Provide,
			mq.Provide,
			dq.Provide,
			up.Provide,
			cron.Provide,
			rpc.Provide,
			// biz
			gid.Provide,
			sto.Provide,
			user.Provide,
			fund.Provide,
			order.Provide,
			fclub.Provide,
			gift.Provide,
			live.Provide,
			recommender.Provide,
			trade.Provide,
			journal.Provide,
			act.Provide,
			pay.Provide,
			payc.Provide,
			payermax.Provide, ganopay.Provide, binance.Provide, paypal.Provide, usolve.Provide,
			withdraw.Provide,
			remit.Provide,
			salary.Provide,
			anchor.Provide,
			ls.Provide,
			task.Provide,
			rtc.Provide,
			pk.Provide,
			room.Provide,
			logic.Provide,
			online.Provide,
			level.Provide,
			device.Provide,
			dress.Provide,
			follow.Provide,
			rsd.Provide,
			gdk.ProvideClient,
			agency.Provide,
			avatar.Provide,
			privacy.Provide,
			bs.Provide, bs2.Provide, jing.Provide, ss.Provide,
			game.Provide, game.ProvideStore, game.ProvideNotifier,
			http.Provide, // dep by payermax notify
			review.Provide,
			ulink.Provide,
			patrol.Provide, // dep by room
			urm.Provide,
			ul.Provide,
			im.Provide,
			link.Provide,
			adm.Provide,
			seller.Provide,
			sysroom.Provide,
			rs.Provide,
			data.Provide,
			view.Provide,
			halloween.Provide,
			adjust.Provide,
			rocket.Provide, // dep by room
			fcm.Provide,
			push.Provide, pushRE.Provide,
			redpacket.Provide,
			props.Provide,
			profitsharing.Provide,
		),
		fx.Invoke(
			sys.Initialize,
			ob.Invoke,
			co.Invoke,
			db.ReleaseGORM,
			db.ReleaseMongo,
			mq.Invoke,
			dq.Invoke,
			// biz
			i18n.Invoke,
			evt.Invoke,
			link.InvokeRoomStatus, link.InvokeTask, link.RegisterPKChecker,
			pk.InvokeRoomStatus, pk.InvokeTasks,
			recommender.Invoke,
			recommender.InvokeScheduler,
			trade.Invoke,   // journal task
			journal.Invoke, // dep by salary
			payermax.Invoke, ganopay.Invoke, binance.Invoke, paypal.Invoke, usolve.Invoke,
			withdraw.Invoke,
			payc.Invoke,
			remit.Invoke,
			salary.InvokeInScheduler,
			live.Invoke,
			ls.Invoke,
			translate.Invoke,
			avatar.Invoke,
			privacy.Invoke,
			bs.Register, bs2.Register, jing.Register, ss.Register,
			game.InvokeSyncGames,
			game.InvokeSendGamePlaying,
			game.InvokeSendWinningNotify,
			review.Invoke,
			urm.InvokeTask,
			adm.InvokeInScheduler,
			sysroom.InvokeInScheduler,
			dob.Invoke, dobCharging.Invoke, marketing.Invoke,
			view.Invoke,
			halloween.Invoke,
			fcm.Invoke,
			push.Invoke, pushRE.InvokeInScheduler,
			pgift.Invoke, // props autoloader
			redpacket.Invoke,
			cycle.Invoke,
			fclub.InvokeInScheduler,
			game.InvokeEvt,
			// sys
			rpc.Invoke,
			cron.Invoke,
			up.Invoke,
		),
		goods.Module,
	)

	app.Run()
}
