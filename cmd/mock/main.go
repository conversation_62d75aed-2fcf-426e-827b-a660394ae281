package main

import (
	"flag"
	"fmt"
	"log"
	"net/http"
	"net/http/pprof"
	"os"
	"os/signal"
	"syscall"

	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/room"
	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
)

func init() {
	// add http pprof things here
	svr := http.NewServeMux()
	svr.HandleFunc("/debug/pprof/", pprof.Index)
	svr.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
	svr.HandleFunc("/debug/pprof/profile", pprof.Profile)
	svr.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
	svr.HandleFunc("/debug/pprof/trace", pprof.Trace)
	go http.ListenAndServe(":6060", svr)

	// init zap logger
	l, err := zap.NewDevelopment()
	if err != nil {
		log.Fatal(err)
	}

	zap.ReplaceGlobals(l)
}

type Config struct {
	ApiAddr string `yaml:"api"`
	WsAddr  string `yaml:"ws"`
	GsAddr  string `yaml:"gs"`
}

func main() {
	var (
		config  string
		guests  int
		players int
		anchors int
		prefix  string
		noPK    bool
		fast    bool
	)

	var (
		conf = Config{
			ApiAddr: "http://192.168.31.152:8080/api/v1",
			WsAddr:  "ws://192.168.31.152:8081/ws/v1",
			GsAddr:  "ws://192.168.31.152:8079/ws/v1",
		}
		stop = make(chan struct{})
	)

	flag.StringVar(&config, "c", "", "-c config.yaml")
	flag.IntVar(&guests, "g", 0, "-g 100 (guests)")
	flag.IntVar(&players, "p", 0, "-p 100 (players)")
	flag.IntVar(&anchors, "a", 0, "-a 100 (anchors)")
	flag.StringVar(&prefix, "prefix", "", "-prefix g1")
	flag.BoolVar(&noPK, "nopk", false, "-nopk")
	flag.BoolVar(&fast, "fast", false, "-fast")
	flag.StringVar(&conf.ApiAddr, "api", conf.ApiAddr, "-api http://192.168.31.152:8080/api/v1")
	flag.StringVar(&conf.WsAddr, "ws", conf.WsAddr, "-ws ws://192.168.31.152:8081/ws/v1")
	flag.StringVar(&conf.GsAddr, "gs", conf.GsAddr, "-gs ws://192.168.31.152:8079/ws/v1")
	flag.Parse()

	// load config from config file and override the default config
	if config != "" {
		bs, err := os.ReadFile(config)
		if err != nil {
			log.Fatal(err)
		}

		var c Config
		if err := yaml.Unmarshal(bs, &c); err != nil {
			log.Fatal(err)
		}

		if c.ApiAddr != "" {
			conf.ApiAddr = c.ApiAddr
		}

		if c.WsAddr != "" {
			conf.WsAddr = c.WsAddr
		}

		if c.GsAddr != "" {
			conf.GsAddr = c.GsAddr
		}
		log.Println("load config from file:", c, "override default config, api:", conf.ApiAddr, "ws:", conf.WsAddr, "gs:", conf.GsAddr)

	}

	stopCh := make(chan os.Signal)
	signal.Notify(stopCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		log.Println("signal:", <-stopCh)
		close(stop)
	}()

	if err := room.Play2(conf.ApiAddr, conf.WsAddr, conf.GsAddr, guests, players, anchors, prefix, !noPK, fast, stop); err != nil {
		fmt.Println(err)
	}
}
