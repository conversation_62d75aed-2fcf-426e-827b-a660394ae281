package room

import (
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
)

func newShared(anchors int, fast bool) *shared {
	return &shared{
		anchors: anchors,
		fast:    fast,
		rooms:   cc.New[string, *types.Room](1000, cc.LRU),
	}
}

type shared struct {
	anchors int
	fast    bool
	rooms   cc.Cache[string, *types.Room] // userId
}

func (s *shared) hasAnchor() bool {
	return s.anchors > 0
}

func (s *shared) addRoom(userId string, room *types.Room) {
	s.rooms.Set(userId, room)
}

func (s *shared) getRooms() []types.Room {
	out := make([]types.Room, 0, s.rooms.Len())
	for _, userId := range s.rooms.Keys() {
		if v, err := s.rooms.Get(userId); err == nil {
			out = append(out, *v)
		}
	}
	return out
}
