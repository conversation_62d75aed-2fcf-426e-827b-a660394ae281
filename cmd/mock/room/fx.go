package room

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/davecgh/go-spew/spew"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.uber.org/zap"
)

func Play() error {
	fmt.Println("mock room play")
	c1, err := client.NewHTTP("http://192.168.31.152:8080/api/v1")
	if err != nil {
		return err
	}

	var resp client.APIResp[auth.Payload]
	if err := c1.Get("/login/mock", map[string]any{"id": rng.Uint32()}, &resp); err != nil {
		return err
	}

	c2, err := client.NewSocket(fmt.Sprintf("ws://192.168.31.152:8081/ws/v1?token=%s", resp.Data.Token), client.WithEvListener(func(bytes []byte) {
		// p, err := protocol.DecodePacket(bytes)
		// if err != nil {
		// 	fmt.Println(err)
		// }

		// fmt.Printf("recv: %s\n%s\n", protocol.MsgType(p.MsgType).String(), string(p.Payload))
	}))
	if err != nil {
		return err
	}
	{
		var resp2 protocol.JoinRoomResp
		if err := c2.RPC(protocol.RpcJoinRoom, &protocol.JoinRoomReq{RoomId: "123"}, &resp2); err != nil {
			return err
		}

		fmt.Println("join room resp:", spew.Sdump(resp2))
	}

	c2.Close()

	return nil
}

type Actor interface {
	Act() error
	HearBeat() error
	Close()
}

func Play2(httpAddr, wsAddr, gsAddr string, guests, players, anchors int, prefix string, withPK, fast bool, stop chan struct{}) error {
	var (
		wg     sync.WaitGroup
		n      = players
		actors []Actor
	)

	id := func(i int) string {
		out := strconv.Itoa(i)
		if prefix != "" {
			out = prefix + "_" + out
		}
		return out
	}

	sd := newShared(anchors, fast)

	for i := 0; i < anchors; i++ {
		g := NewAnchor(wsAddr, httpAddr, "anchor_"+id(i), withPK, sd, stop)
		actors = append(actors, g)
	}

	for i := 0; i < n; i++ {
		g := NewGuest(wsAddr, httpAddr, "guest_"+id(i), sd, stop)
		actors = append(actors, g)
	}

	api, err := NewGAPI(httpAddr)
	if err != nil {
		return err
	}
	for range guests {
		g := NewAnonymous(api, gsAddr, sd, stop)
		actors = append(actors, g)
	}

	for _, p := range actors {
		p := p
		wg.Add(2)
		go func() {
			defer wg.Done()
			for {
				select {
				case <-time.After(rng.Duration(500, 1000, time.Millisecond)):
					if err := p.Act(); err != nil {
						zap.L().Error("actor act failed", zap.Error(err))
					}
				case <-stop:
					return
				}
			}
		}()

		go func() {
			defer wg.Done()
			t := time.NewTicker(time.Second * 3)
			defer t.Stop()
			for {
				select {
				case <-t.C:
					if err := p.HearBeat(); err != nil {
						zap.L().Error("actor heartbeat failed", zap.Error(err))
					}
				case <-stop:
					return
				}
			}
		}()
	}

	go func() {
		for {
			select {
			case <-stop:
				return
			case <-time.After(time.Second):
				gAnchors.Check()
				if dump := gAnchors.Dump(); dump != "" && withPK {
					zap.L().Sugar().Info("\n" + dump)
				}
			}
		}
	}()

	zap.L().Debug("all actors working")
	wg.Wait()

	zap.L().Debug("all actors working done, closing")
	for _, p := range actors {
		p.Close()
	}

	zap.L().Debug("all actors closed")

	return nil
}
