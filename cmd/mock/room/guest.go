package room

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

type Guest struct {
	userAct
	uid      string
	logged   atomic.Bool
	roomId   atomic.String
	loop     atomic.Int64
	joinAt   atomic.Int64
	wsAddr   string
	httpAddr string
	shared   *shared
	stopCh   chan struct{}
	logger   *zap.Logger
}

func NewGuest(wsAddr string, httpAddr string, uid string, shared *shared, stopCh chan struct{}) *Guest {
	return &Guest{wsAddr: wsAddr, httpAddr: httpAddr, uid: uid, shared: shared, stopCh: stopCh, logger: zap.L().With(zap.String("id", uid))}
}

func (g *Guest) Login() error {
	c1, err := client.NewHTTP(g.httpAddr)
	if err != nil {
		return err
	}

	var resp client.APIResp[auth.Payload]
	if err := c1.Get("/login/mock", map[string]any{"id": g.uid}, &resp); err != nil {
		return err
	}

	g.logger.Info("guest get token resp", zap.String("token", resp.Data.Token))

	c1.SetToken(resp.Data.Token)

	c2, err := client.NewSocket(fmt.Sprintf("%s?token=%s", g.wsAddr, resp.Data.Token), client.WithEvListener(g.onData))
	if err != nil {
		return err
	}

	g.hc = c1
	g.wc = c2
	return nil
}

func (g *Guest) RoomId() string {
	return g.roomId.Load()
}

func (g *Guest) onData(bs []byte) {
	p, err := protocol.DecodePacket(bs)
	if err != nil {
		g.logger.Error("decode packet error", zap.Error(err), zap.ByteString("data", bs))
		return
	}

	switch protocol.MsgType(p.MsgType) {
	case protocol.NotifyRoomChat:
		g.onChatNotify(p)
	default:
	}
}

func (g *Guest) onChatNotify(p *protocol.Packet) {
	var n protocol.RoomChatNotify
	if err := json.Unmarshal(p.Payload, &n); err != nil {
		g.logger.Error("unmarshal chat notify error", zap.Error(err), zap.ByteString("data", p.Payload))
		return
	}

	if index := strings.Index(n.Text, "TS:"); index != -1 {
		ts, err := strconv.ParseInt(n.Text[index+3:], 10, 64)
		if err != nil {
			g.logger.Error("parse ts error", zap.Error(err))
			return
		}
		g.logger.Info("system latency(ms)", zap.Int64("latency", (time.Now().UnixNano()-ts)/1e6))
	}
}

func (g *Guest) JoinRoom(roomId string) error {
	var resp protocol.JoinRoomResp
	if err := g.wc.RPC(protocol.RpcJoinRoom, &protocol.JoinRoomReq{RoomId: roomId}, &resp); err != nil {
		return err
	}

	g.logger.Info("join room resp", zap.Any("resp", resp))
	return nil
}

func (g *Guest) SendHeartbeat(roomId string) error {
	var resp protocol.HeartbeatResp
	if err := g.wc.RPC(protocol.RpcHeartbeat, &protocol.HeartbeatReq{RoomId: roomId}, &resp); err != nil {
		return err
	}

	return nil
}

func (g *Guest) HearBeat() error {
	if !g.logged.Load() {
		return nil
	}

	return g.SendHeartbeat(g.RoomId())
}

func (g *Guest) Close() {
	g.wc.Close()
}

func (g *Guest) Act() error {
	if !g.logged.Load() {
		if err := g.Login(); err != nil {
			return fmt.Errorf("login failed: %w", err)
		}
		g.logged.Store(true)
	}

	var changeRoom bool
	if g.roomId.Load() == "" || rng.Intn(100) < 20 || g.loop.Inc()%30 == 0 || time.Now().Unix()-g.joinAt.Load() > 60 {
		var rooms []types.Room
		if g.shared.hasAnchor() && g.shared.fast {
			rooms = g.shared.getRooms()
		} else {
			var err error
			rooms, err = g.GetFeedList()
			if err != nil {
				return fmt.Errorf("get feed list failed: %w", err)
			}
		}
		if len(rooms) == 0 {
			return nil
		}

		room := lo.Sample(rooms)

		testRoom := lo.Filter(rooms, func(r types.Room, _ int) bool {
			return strings.HasPrefix(r.Title, "test_live_")
		})

		if len(testRoom) > 0 {
			room = lo.Sample(testRoom)
		} else if g.shared.hasAnchor() {
			g.logger.Debug("wait for mock anchor online")
			Sleep(3*time.Second, g.stopCh)
			return nil
		}

		roomId := room.RoomId
		//roomId = rooms[0].RoomId
		// 霍克卡卡卡卡卡卡卡卡卡卡卡卡卡卡卡卡卡
		// roomId = "66277b317bf4a7456fc09cb2"

		changeRoom = g.roomId.Load() == roomId
		g.roomId.Store(roomId)

		if changeRoom {
			if err := g.JoinRoom(roomId); err != nil {
				return fmt.Errorf("join room failed: %w", err)
			}
			g.joinAt.Store(time.Now().Unix())

			if !room.Followed {
				if rng.Intn(100) < 70 {
					if err := g.Follow(room.User.UserId, roomId); err != nil {
						return fmt.Errorf("follow failed: %w", err)
					}
				}
			} else {
				if rng.Intn(100) < 10 {
					if err := g.UnFollow(room.User.UserId); err != nil {
						return fmt.Errorf("follow failed: %w", err)
					}
				}
			}
		}
	}

	roomId := g.roomId.Load()

	if err := g.sendChats(roomId, changeRoom); err != nil {
		return err
	}

	if err := g.sendGifts(roomId, g.stopCh); err != nil {
		return err
	}

	return nil
}
