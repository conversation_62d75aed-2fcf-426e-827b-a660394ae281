package room

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/gdk/host"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/ipk"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

var gAnchors = &AnchorStatus{}

type AnchorStatus struct {
	mu      sync.Mutex
	anchors []*Anchor
}

func (a *AnchorStatus) Add(anchor *Anchor) {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.anchors = append(a.anchors, anchor)
}

func (a *AnchorStatus) Remove(anchor *Anchor) {
	a.mu.Lock()
	defer a.mu.Unlock()
	for i, v := range a.anchors {
		if v == anchor {
			a.anchors = append(a.anchors[:i], a.anchors[i+1:]...)
			break
		}
	}
}

func (a *AnchorStatus) List() []*Anchor {
	a.mu.Lock()
	defer a.mu.Unlock()
	return a.anchors
}

func (a *AnchorStatus) Check() {
	var (
		inPK = make(map[string]int)
	)

	a.mu.Lock()
	for _, v := range a.anchors {
		if v.pks.Load() != nil {
			for _, aa := range v.pks.Load().Anchors {
				inPK[aa.User.UserId]++
			}
		}
	}
	a.mu.Unlock()
	for k, v := range inPK {
		if v != 2 {
			zap.L().Error("pk anchor not in 2 rooms", zap.String("userId", k), zap.Int("count", v))
		}
	}
}

func (a *AnchorStatus) Dump() string {
	a.mu.Lock()
	defer a.mu.Unlock()
	if len(a.anchors) == 0 {
		return ""
	}

	tableWriter := table.NewWriter()
	tableWriter.SetStyle(table.StyleColoredBlackOnGreenWhite)
	tableWriter.AppendHeader(table.Row{"UserId", "Waiting", "invite", "PKStatus"})
	for _, v := range a.anchors {
		row := table.Row{
			v.userId,
		}

		if s := v.waiting.Load(); s != "" {
			row = append(row, s)
		} else {
			row = append(row, "-")
		}

		if v.lastInvited.Load() != "" {
			row = append(row, v.lastInvited.Load())
		} else {
			row = append(row, "-")
		}

		if v.pks.Load() != nil {
			var sb strings.Builder
			sb.WriteString(v.pks.Load().SessionId)
			sb.WriteString(":")

			self, ok := lo.Find(v.pks.Load().Anchors, func(anchor protocol.PKAnchor) bool {
				return anchor.User.UserId == v.userId
			})

			if !ok {
				sb.WriteString("self not found")
			} else {
				sb.WriteString(self.User.UserId)
				sb.WriteString(":")
				sb.WriteString(strconv.FormatInt(self.Score, 10))
			}

			peer, ok := lo.Find(v.pks.Load().Anchors, func(anchor protocol.PKAnchor) bool {
				return anchor.User.UserId != v.userId
			})

			if !ok {
				sb.WriteString("peer not found")
			} else {
				sb.WriteString("->" + peer.User.UserId)
				sb.WriteString(":")
				sb.WriteString(strconv.FormatInt(peer.Score, 10))
			}

			row = append(row, sb.String())
		} else {
			row = append(row, "-")
		}

		tableWriter.AppendRow(row)
	}

	return tableWriter.Render()
}

type Anchor struct {
	userAct
	openId      string
	userId      string
	logged      atomic.Bool
	matching    atomic.Bool
	waiting     atomic.String
	lastInvited atomic.String
	room        atomic.Pointer[types.Room]
	loop        atomic.Int64
	joinAt      atomic.Int64
	withPK      bool

	status   atomic.Pointer[protocol.LStatus] // LStatus
	pks      atomic.Pointer[protocol.RoomPKStatusNotify]
	wsAddr   string
	httpAddr string
	shared   *shared
	stopCh   chan struct{}
	logger   *zap.Logger
}

func NewAnchor(wsAddr string, httpAddr string, openId string, withPK bool, shared *shared, stopCh chan struct{}) *Anchor {
	a := &Anchor{wsAddr: wsAddr, httpAddr: httpAddr, openId: openId, withPK: withPK, shared: shared, stopCh: stopCh, logger: zap.L().With(zap.String("id", openId))}

	gAnchors.Add(a)
	return a
}

var (
	loginLimiter = rate.NewLimiter(rate.Every(time.Second/100), 100)
)

func (g *Anchor) Login() error {
	c1, err := client.NewHTTP(g.httpAddr)
	if err != nil {
		return err
	}

	loginLimiter.Wait(context.Background())

	var resp client.APIResp[auth.Payload]
	if err := c1.Get("/login/mock", map[string]any{"id": g.openId}, &resp); err != nil {
		return err
	}

	g.userId = resp.Data.UserId
	g.logger = g.logger.With(zap.String("userId", g.userId))

	g.logger.Debug("login", zap.String("token", resp.Data.Token))

	c2, err := client.NewSocket(fmt.Sprintf("%s?token=%s", g.wsAddr, resp.Data.Token), client.WithEvListener(g.onData))
	if err != nil {
		return err
	}

	c1.SetToken(resp.Data.Token)
	g.hc = c1
	g.wc = c2

	g.checkPKStatusAftLogin()

	return nil
}

func (g *Anchor) checkPKStatusAftLogin() {
	if !g.withPK {
		return
	}
	resp, err := g.Matching()
	if err != nil {
		g.logger.Error("matching failed", zap.String("openId", g.openId), zap.Error(err))
		return
	}

	if resp.Matching {
		g.logger.Error("matching status not match", zap.String("openId", g.openId), zap.Bool("matching", resp.Matching))
	}

	if resp.InPK {
		g.logger.Error("pk status not match", zap.String("openId", g.openId), zap.Bool("inPK", resp.InPK))
	}
}

func (g *Anchor) RoomId() string {
	if g.room.Load() == nil {
		return "~"
	}
	return g.room.Load().RoomId
}

func (g *Anchor) onData(bs []byte) {
	p, err := protocol.DecodePacket(bs)
	if err != nil {
		fmt.Println(err)
	}

	switch protocol.MsgType(p.MsgType) {
	case protocol.NotifyPKConfirm:
		g.onPKConfirmNotify(p)
	case protocol.NotifyPKConfirmResult:
		g.onPKConfirmResult(p)
	case protocol.NotifyPKInviterCancel:
		g.onPKInviterCancel(p)
	case protocol.NotifyPKReadyAsk:
		g.onPKReadyAsk(p)
	case protocol.NotifyRoomStatus:
		g.onRoomStatus(p)
	default:
	}
}

func (g *Anchor) onRoomStatus(p *protocol.Packet) {
	var out protocol.RoomStatusNotify
	if err := json.Unmarshal(p.Payload, &out); err != nil {
		g.logger.Error("unmarshal room status failed", zap.Error(err))
		return
	}

	g.status.Store(&out.Status)
	g.pks.Store(out.PKStatus)
}

func (g *Anchor) onPKReadyAsk(p *protocol.Packet) {
	g.logger.Debug("recv pk ready ask", zap.String("payload", string(p.Payload)))
	var out protocol.PKAskReadyNotify
	if err := json.Unmarshal(p.Payload, &out); err != nil {
		g.logger.Error("unmarshal pk ready ask failed", zap.Error(err))
		return
	}

	s := rng.Intn(100)
	if s < 90 {
		if err := g.Ready(out.SessionId, true); err != nil {
			g.logger.Error("ready failed", zap.Error(err))
		}
	} else {
		if err := g.Ready(out.SessionId, false); err != nil {
			g.logger.Error("not ready failed", zap.Error(err))
		}
	}
}

func (g *Anchor) onPKInviterCancel(p *protocol.Packet) {
	var out protocol.PKInviterCancelNotify
	if err := json.Unmarshal(p.Payload, &out); err != nil {
		g.logger.Error("unmarshal pk inviter cancel failed", zap.Error(err))
		return
	}

	g.logger.Debug("pk inviter cancel", zap.String("payload", string(p.Payload)))
}

func (g *Anchor) onPKConfirmResult(p *protocol.Packet) {
	g.logger.Debug("recv pk confirm result", zap.String("payload", string(p.Payload)), zap.String("userId", g.userId))

	var out protocol.PKConfirmResultNotify
	if err := json.Unmarshal(p.Payload, &out); err != nil {
		g.logger.Error("unmarshal pk confirm result failed", zap.Error(err))
		return
	}

	if out.Result == 1 {
		if *g.status.Load()&protocol.LStatusPK > 0 {
			pks := g.pks.Load()

			var pTarget string
			for _, u := range pks.Anchors {
				if u.User.UserId != g.userId {
					pTarget = u.User.UserId
				}
			}

			if pTarget != out.User.UserId {
				g.logger.Error("BUG: pk already started",
					zap.Any("notify", out),
					zap.String("pStatus", g.status.Load().String()),
					zap.Any("pSession", g.pks.Load()),
				)
			}
		}
	}

	if inv := g.lastInvited.Load(); inv != "" {
		if inv != out.User.UserId {
			g.logger.Error("BUG: pk inviter not match", zap.String("inviter", inv), zap.String("userId", out.User.UserId))
		}
	}

	g.waiting.Store("")
	g.lastInvited.Store("")
}

func (g *Anchor) onPKConfirmNotify(p *protocol.Packet) {
	var out protocol.PKConfirmNotify
	if err := json.Unmarshal(p.Payload, &out); err != nil {
		g.logger.Error("unmarshal pk confirm notify failed", zap.Error(err))
		return
	}

	g.logger.Debug("recv pk confirm notify", zap.String("payload", string(p.Payload)))
	doing := func() {
		if rng.Intn(100) < 90 {
			// accept
			if err := g.Accept(out.SessionId); err != nil {
				g.logger.Error("accept failed", zap.Error(err))
			} else {
				g.logger.Debug("accept success")
			}
		} else if rng.Intn(100) < 70 {
			// reject
			if err := g.Reject(out.SessionId); err != nil {
				g.logger.Error("reject failed", zap.Error(err))
			} else {
				g.logger.Debug("reject success")
			}
		} else {
			g.logger.Debug("do nothing on pk confirm notify")
		}
	}

	delays := []time.Duration{0, time.Second, time.Second * 2, time.Second * 3, time.Second * 8, time.Second * 10}
	delay := delays[rng.Intn(len(delays))]
	time.AfterFunc(delay, doing)
}

func (g *Anchor) JoinRoom(roomId string) error {
	var resp protocol.JoinRoomResp
	if err := g.wc.RPC(protocol.RpcJoinRoom, &protocol.JoinRoomReq{RoomId: roomId}, &resp); err != nil {
		return err
	}

	return nil
}

func (g *Anchor) Matching() (*ipk.MatchingResp, error) {
	var out client.APIResp[ipk.MatchingResp]
	if err := g.hc.Get("pk/matching", nil, &out); err != nil {
		return nil, fmt.Errorf("matching failed: %w", err)
	}

	return &out.Data, nil
}

func (g *Anchor) Invite(usrId string) (*client.APIResp[ipk.InviteResp], error) {
	var (
		req = ipk.InviteReq{UserId: usrId, Source: 1}
		out client.APIResp[ipk.InviteResp]
	)

	if err := g.hc.Post("pk/matching/invite", nil, req, &out); err != nil {
		return nil, fmt.Errorf("invite %s failed: %w", usrId, err)
	}

	return &out, nil
}

func (g *Anchor) LiveHeartbeat() error {
	var (
		req = types.LiveHeartbeatRequest{
			Stream: 1,
		}
		out api.EmptyResp
	)
	if err := g.hc.Post("live/heartbeat", nil, req, &out); err != nil {
		return fmt.Errorf("live heartbeat failed: %w", err)
	}
	return nil
}

func (g *Anchor) Accept(letterId string) error {
	var out api.EmptyResp
	if err := g.hc.Post("pk/matching/accept", nil, ipk.AcceptInviteReq{SessionId: letterId}, &out); err != nil {
		return fmt.Errorf("accept failed: %w", err)
	}
	return nil
}

func (g *Anchor) Reject(letterId string) error {
	var out api.EmptyResp
	if err := g.hc.Post("pk/matching/reject", nil, ipk.RejectInviteReq{SessionId: letterId}, &out); err != nil {
		return fmt.Errorf("reject failed:%w", err)
	}
	return nil
}

func (g *Anchor) LiveStart() (*types.LiveStartResponse, error) {
	var (
		req = types.LiveStartRequest{
			Title: "test_live_" + g.userId,
			Cover: "https://godzilla-live-oss-test.sskjz.com/avatar/default/800x800",
			Ext: map[string]string{
				"deviceId": host.Id(),
			},
		}
		out client.APIResp[types.LiveStartResponse]
	)
start:
	if err := g.hc.Post("live/start", nil, &req, &out); err != nil {
		if client.IsApiError(err, biz.ErrRoomFaceUnchecked) {
			var out2 client.APIResp[types.LiveFaceCheckResponse]
			if err := g.hc.Post("live/face/check", nil, types.LiveFaceCheckRequest{Ticket: "www"}, &out2); err != nil {
				return nil, fmt.Errorf("live face check failed: %w", err)
			}
			g.logger.Debug("live face check done, retry live start")
			goto start
		}
		return nil, fmt.Errorf("live start failed: %w", err)
	}

	return &out.Data, nil
}

func (g *Anchor) LiveStop() error {
	var (
		req types.LiveStopRequest
		out client.APIResp[types.LiveStopResponse]
	)

	if err := g.hc.Post("live/stop", nil, &req, &out); err != nil {
		return fmt.Errorf("live stop failed: %w", err)
	}

	g.shared.rooms.Remove(g.userId)

	return nil
}

func (g *Anchor) Close() {
	if g.hc != nil {
		if err := g.LiveStop(); err != nil {
			g.logger.Error("live stop failed", zap.String("userId", g.userId), zap.Error(err))
		} else {
			g.logger.Debug("live stop success", zap.String("userId", g.userId))
		}
	}

	g.wc.Close()
}

func (g *Anchor) StartMatching() error {
	var out api.EmptyResp
	if err := g.hc.Post("pk/matching/start", nil, nil, &out); err != nil {
		return fmt.Errorf("start matching failed: %w", err)
	}

	return nil
}

func (g *Anchor) StopMatching() error {
	var out api.EmptyResp
	if err := g.hc.Post("pk/matching/stop", nil, nil, &out); err != nil {
		return fmt.Errorf("stop matching failed: %w", err)
	}
	return nil
}

func (g *Anchor) Ready(sess string, ready bool) error {
	var (
		req = ipk.ReadyReq{
			SessionId: sess,
			Ready:     ready,
		}
		out api.EmptyResp
	)
	if err := g.hc.Post("pk/matching/ready", nil, req, &out); err != nil {
		return fmt.Errorf("ready failed: %w", err)
	}
	return nil
}

func (g *Anchor) RoomInfo() (*types.Room, error) {
	var out client.APIResp[types.Room]
	if err := g.hc.Get("/live/room", map[string]any{
		"userId": g.userId,
	}, &out); err != nil {
		return nil, fmt.Errorf("get room info failed: %w", err)
	}

	return &out.Data, nil
}

func (g *Anchor) HearBeat() error {
	r := g.room.Load()
	if r == nil {
		return nil
	}

	if err := g.SendHeartbeat(g.RoomId()); err != nil {
		return err
	}

	if err := g.LiveHeartbeat(); err != nil {
		return err
	}

	return nil
}

func (g *Anchor) Act() error {
	if !g.logged.Load() {
		if err := g.Login(); err != nil {
			return fmt.Errorf("login failed: %w", err)
		}
		g.logged.Store(true)
	}

	if g.room.Load() == nil {
		ri, err := g.RoomInfo()
		if err != nil {
			return fmt.Errorf("get room info failed: %w", err)
		}

		g.logger.Debug("room info", zap.String("userId", g.userId), zap.String("roomId", ri.RoomId))
		if ri.RoomId == "" {
			return fmt.Errorf("room id is empty")
		}

		g.room.Store(ri)
		if ri.Status == live.LiveStatusLiving {
			g.shared.addRoom(g.userId, ri)
		}
	}

	if g.room.Load().Status != live.LiveStatusLiving {
		resp, err := g.LiveStart()
		if err != nil {
			return err
		}

		ri := g.room.Load()
		ri.Stream = resp.PushStream
		ri.RoomId = resp.RoomId
		ri.Status = live.LiveStatusLiving
		g.room.Store(ri)
		g.shared.addRoom(g.userId, ri)
		time.Sleep(time.Millisecond * 5)
	}

	if g.joinAt.Load() == 0 {
		g.logger.Debug("join room", zap.String("userId", g.userId), zap.String("roomId", g.room.Load().RoomId))
		if err := g.JoinRoom(g.room.Load().RoomId); err != nil {
			return fmt.Errorf("join room failed: %w", err)
		}
		g.joinAt.Store(time.Now().Unix())
	}

	st := g.status.Load()
	if st != nil && *st&protocol.LStatusPK == 0 && g.lastInvited.Load() == "" && g.withPK { // do invite
		matching, err := g.Matching()
		if err != nil {
			return fmt.Errorf("matching failed: %w", err)
		}

		notInInvite := func(userId string) bool {
			return -1 == slices.IndexFunc(matching.Invitees, func(invitee ipk.Invitee) bool {
				return userId == invitee.UserId
			})
		}

		invite := func(userId string) {
			resp, err := g.Invite(userId)
			if err != nil {
				return
			}

			if resp.Code == 0 {
				g.waiting.Store(resp.Data.SessionId)
				g.lastInvited.Store(userId)
			} else if resp.Code == 1 {
				if resp.Code >= 10000 && resp.Code <= 10999 {
					g.logger.Debug("invite failed", zap.String("userId", g.userId), zap.Error(err), zap.Any("resp", resp))
				}
			}
		}

		// fmt.Printf("matching: %s\n", spew.Sdump(matching))
		// f := func(peer ipk.Peer, _ int) bool {
		// 	return notInInvite(peer.UserId) && peer.LiveStatus.LStatus&protocol.LStatusPK == 0
		// }

		// targets := append(lo.Filter(matching.Recommends, f), lo.Filter(matching.Friends, f)...)
		// if len(targets) > 0 {
		// 	// pick some target to invite
		// 	peer := lo.Sample(targets)

		// 	log.Printf("invite %s from %d anchors\n", peer.Nickname, len(targets))

		// 	invite(peer.UserId)
		// } else {
		// dumpPeers := func(ps []ipk.Peer) string {
		// 	var sb strings.Builder
		// 	for _, p := range ps {
		// 		sb.WriteString(p.String())
		// 		sb.WriteString(",")
		// 	}
		// 	return sb.String()
		// }

		rr, err := g.GetSomeFeedList(1000)
		if err != nil {
			g.logger.Error("get feed list failed", zap.Error(err))
		} else {
			faked := lo.Filter(rr, func(r types.Room, _ int) bool {
				return strings.HasPrefix(r.Title, "test_live_") && notInInvite(r.User.UserId)
			})

			if len(faked) > 0 {
				to := lo.Sample(faked)
				g.logger.Debug("invite feed list anchor",
					zap.String("toUserId", to.User.UserId),
					zap.String("roomId", to.RoomId),
					zap.Int("living", len(rr)),
				)
				invite(to.User.UserId)
			} else if false {
				invitees := lo.Map(matching.Invitees, func(invitee ipk.Invitee, _ int) string {
					return invitee.String() + "@" + time.Unix(invitee.InviteAt, 0).Format("2006-01-02 15:04:05")
				})

				g.logger.Debug("no target to invite", zap.Int("living", len(rr)), zap.Strings("invitees", invitees))
			}
		}
		// }
	}

	if st != nil && *st&protocol.LStatusPKLinked > 0 && g.pks.Load() != nil && time.Now().Unix()-g.pks.Load().LinkedAt >= 10 && g.lastInvited.Load() == "" {
		pks := g.pks.Load()
		if rng.Intn(100) < 2 {
			if err := g.Terminate(pks.SessionId); err != nil {
				g.logger.Error("terminate failed", zap.String("userId", g.userId), zap.Error(err))
			}
		} else {
			if resp, err := g.Restart(pks.SessionId); err != nil {
				g.logger.Error("restart failed", zap.String("userId", g.userId), zap.Error(err))
			} else {
				var pTarget string
				for _, u := range pks.Anchors {
					if u.User.UserId != g.userId {
						pTarget = u.User.UserId
					}
				}

				g.waiting.Store(resp.SessionId)
				g.lastInvited.Store(pTarget)
			}
		}
	}

	roomId := g.room.Load().RoomId

	if err := g.sendChats(roomId, false); err != nil {
		return err
	}

	if err := g.sendGifts(roomId, g.stopCh); err != nil {
		return err
	}

	return nil

}

func (g *Anchor) SendHeartbeat(roomId string) error {
	var resp protocol.HeartbeatResp
	if err := g.wc.RPC(protocol.RpcHeartbeat, &protocol.HeartbeatReq{RoomId: roomId, Streamer: true}, &resp); err != nil {
		return err
	}

	return nil
}

func (g *Anchor) GetSomeFeedList(n int) ([]types.Room, error) {
	var out []types.Room
	for i := 0; i < 10000; i++ {
		rr, err := g.GetFeedList(i)
		if err != nil {
			return nil, fmt.Errorf("get feed list failed: %w", err)
		}

		if len(rr) == 0 {
			break
		}

		out = append(out, rr...)
		if len(rr) >= n || len(rr) == 0 {
			break
		}
	}
	return out, nil
}

func (g *Anchor) GetFeedList(page int) ([]types.Room, error) {
	var resp client.APIResp[types.FeedListResponse]
	if err := g.hc.Get("feed/list", map[string]any{
		"page": page,
	}, &resp); err != nil {
		return nil, fmt.Errorf("get feed list error: %w", err)
	}

	return resp.Data.List, nil
}

func (g *Anchor) Terminate(sid string) error {
	var out api.EmptyResp
	req := ipk.TerminateReq{
		SessionId: sid,
	}
	if err := g.hc.Post("pk/terminate", nil, req, &out); err != nil {
		return fmt.Errorf("terminate failed: %w", err)
	}

	return nil
}

func (g *Anchor) Restart(sid string) (*ipk.InviteResp, error) {
	var out ipk.InviteResp
	req := ipk.RestartReq{
		SessionId: sid,
	}
	if err := g.hc.Post("pk/restart", nil, req, &out); err != nil {
		return nil, fmt.Errorf("restart failed: %w", err)
	}

	return &out, nil
}
