package room

import (
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

var gInc atomic.Uint32

func NewAnonymous(api *GAPI, wsAddr string, shared *shared, stopCh chan struct{}) *Anonymous {
	return &Anonymous{api: api, wsAddr: wsAddr, shared: shared, stopCh: stopCh, logger: zap.L().With(zap.String("id", fmt.Sprintf("anonymous_%d", gInc.Inc())))}
}

type Anonymous struct {
	api    *GAPI
	wsAddr string
	wc     *client.WS
	roomId atomic.String
	loop   atomic.Int64
	joinAt atomic.Int64
	shared *shared
	stopCh chan struct{}
	logger *zap.Logger
}

func (g *Anonymous) JoinRoom(roomId string) (err error) {
	if g.wc != nil {
		g.wc.Close()
	}
	g.wc, err = client.NewSocket(fmt.Sprintf("%s?roomId=%s", g.wsAddr, roomId))
	return err
}

func (g *Anonymous) HearBeat() error {
	if g.wc == nil {
		return nil
	}
	return g.wc.Send(nil)
}

func (g *Anonymous) Close() {
	if g.wc != nil {
		g.wc.Close()
	}
}

func (g *Anonymous) Act() error {
	if g.roomId.Load() == "" || rng.Prob(100, 1) || g.loop.Inc()%30 == 0 || time.Now().Unix()-g.joinAt.Load() > 60 {
		var rooms []types.Room
		if g.shared.hasAnchor() && g.shared.fast {
			rooms = g.shared.getRooms()
		} else {
			var err error
			rooms, err = g.api.GetFeedList()
			if err != nil {
				return fmt.Errorf("get feed list failed: %w", err)
			}
		}
		if len(rooms) == 0 {
			return nil
		}

		room := lo.Sample(rooms)

		testRoom := lo.Filter(rooms, func(r types.Room, _ int) bool {
			return strings.HasPrefix(r.Title, "test_live_")
		})

		if len(testRoom) > 0 {
			room = lo.Sample(testRoom)
		} else if g.shared.hasAnchor() {
			g.logger.Debug("wait for mock anchor online")
			Sleep(3*time.Second, g.stopCh)
			return nil
		}

		if err := g.JoinRoom(room.RoomId); err != nil {
			return fmt.Errorf("join room failed: %w", err)
		}

		g.roomId.Store(room.RoomId)
		g.joinAt.Store(time.Now().Unix())
	}

	return nil
}
