package room

import (
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/rng"
)

func (g *userAct) sendChats(roomId string, forced bool) error {
	if !forced && rng.Intn(100) < 40 {
		return nil
	}

	texts := []string{
		"hello world",
		"恭喜发财",
		"新年快乐",
		"欢迎大家来到我的直播间，刷波520让我感受一下你们的热情。",
		"我是一个有梦想的人，我要做一个有梦想的人。",
		"欢迎新来的朋友不要着急马上走，人间自有真情在，点点红心都是爱，天若有情天亦老，来波关注好不好。",
		"订阅没有点，感情走不远，关注没有上，永远在闲逛。",
		"宝宝们，动动你们的小手，拉上你们的朋友，一起来支持我们可爱帅气的主播，我们直播间期待你们的光临。",
		"欢迎各位，来得潇洒走得酷，刷刷礼物显风度，喜欢主播点关注。",
	}
	text := lo.Sample(texts)

	if rng.Intn(100) < 50 {
		text = fmt.Sprintf("TS:%d", time.Now().UnixNano())
	}

	if err := g.SendChat(roomId, text); err != nil {
		return fmt.Errorf("send chat failed: %w", err)
	}

	return nil
}

var giftTypes = []*rng.Ratio[string]{
	rng.NewRatio(90, giftLucky),
	rng.NewRatio(10, giftExclusive),
}

type Combo struct {
	fake    bool
	GiftId  int
	GiftCnt int
	RoomId  string
	Id      string
}

func (g *userAct) sendGifts(roomId string, stopCh chan struct{}) error {
	if rng.Intn(100) < 90 {
		return nil
	}

	c := g.combo.Load()
	if c == nil {
		gifts, err := g.GiftList(rng.Pick(giftTypes...))
		if err != nil {
			return err
		}
		gft := lo.Sample(gifts)
		c = &Combo{
			fake:    !gft.Combo,
			GiftId:  int(gft.Id),
			GiftCnt: lo.Ternary(len(gft.GroupInfo) > 0, lo.Sample(gft.GroupInfo).Count, 1),
			RoomId:  roomId,
			Id:      fmt.Sprintf("%d", time.Now().Unix()),
		}
		g.combo.Store(c)
	}

	for i := 0; i < rng.Range(1, 999); i++ {
		if err := g.SendGift(roomId, c.GiftId, c.GiftCnt, c.Id); err != nil {
			return fmt.Errorf("send gift failed: %w", err)
		}

		if !Sleep(rng.Duration(50, 1200, time.Millisecond), stopCh) {
			return nil
		}
	}

	if g.combo.Load().fake || rng.Intn(100) < 45 {
		g.combo.Store(nil)
	}

	return nil
}
