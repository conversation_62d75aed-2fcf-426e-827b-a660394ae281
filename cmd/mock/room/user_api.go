package room

import (
	"fmt"
	"sync/atomic"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mock"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/recharge"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

const (
	giftLucky     = "lucky"
	giftExclusive = "exclusive"
)

type userAct struct {
	hc    *client.HTTP
	wc    *client.WS
	combo atomic.Pointer[Combo]
	gifts cc.Cache[string, []types.Gift]
}

func (g *userAct) Diamonds() (int, error) {
	var resp client.APIResp[types.GlobalWallet]
	if err := g.hc.Get("/wallet/info", nil, &resp); err != nil {
		return 0, fmt.Errorf("get diamond error: %w", err)
	}
	return int(resp.Data.Diamond), nil
}

func (g *userAct) GiftList(pageId string) ([]types.Gift, error) {
	if g.gifts == nil {
		g.gifts = cc.New[string, []types.Gift](10, cc.LRU, cc.LoaderFunc(func(pageId string) ([]types.Gift, error) {
			var resp client.APIResp[types.GiftListResponse]
			if err := g.hc.Get("/gift/list", nil, &resp); err != nil {
				return nil, fmt.Errorf("get gifts error: %w", err)
			}
			v, _ := lo.Find(resp.Data.Pages, func(page types.GiftPage) bool {
				return page.Key == pageId
			})
			return lo.Filter(v.Gifts, func(gft types.Gift, _ int) bool { return gft.Display }), nil
		}))
	}
	return g.gifts.Get(pageId)
}

func (g *userAct) SendGift(roomId string, giftId, giftCount int, comboId string) error {
	var resp client.APIResp[types.GiftSendResponse]
start:
	if err := g.hc.Post("/gift/send", nil, &types.GiftSendRequest{
		RoomId:    roomId,
		GiftId:    giftId,
		GiftCount: giftCount,
		ComboId:   comboId,
	}, &resp); err != nil {
		if client.IsApiError(err, biz.ErrBalanceNotEnough) {
			if err := g.Recharge(); err != nil {
				return fmt.Errorf("recharge fail: %w", err)
			}
			goto start
		}
		return err
	}
	return nil
}

func (g *userAct) SendChat(roomId string, text string) error {
	var resp client.APIResp[types.SendChatMessageResponse]
	return g.hc.Post("/chat/send", nil, &types.SendChatMessageRequest{
		RoomId:  roomId,
		Content: text,
	}, &resp)
}

func (g *userAct) Recharge() error {
	var resp1 client.APIResp[recharge.ConfigResponse]
	if err := g.hc.Get("/recharge/diamond", nil, &resp1); err != nil {
		return fmt.Errorf("get recharge config error: %w", err)
	}
	diamonds := resp1.Data.Customized.Max
	var resp client.APIResp[mock.MakeOrderResponse]
	if err := g.hc.Post("/pay/mock/order", nil, &mock.MakeOrderRequest{
		Scene: "recharge",
		SKU:   fmt.Sprintf("%d", diamonds),
	}, &resp); err != nil {
		return fmt.Errorf("recharge error: %w", err)
	}
	return nil
}

func (g *userAct) GetFeedList() ([]types.Room, error) {
	var resp client.APIResp[types.FeedListResponse]
	if err := g.hc.Get("feed/list", nil, &resp); err != nil {
		return nil, fmt.Errorf("get feed list error: %w", err)
	}

	return resp.Data.List, nil
}

type FollowUserRequest struct {
	UserId string `form:"userId"` // 用户Id
	RoomId string `form:"roomId"` // 房间Id（直播间内需要传）
}

func (g *userAct) Follow(toUserId, roomId string) error {
	var resp client.APIResp[api.EmptyResp]
	if err := g.hc.Post("user/follow", nil, &FollowUserRequest{
		UserId: toUserId,
		RoomId: roomId,
	}, &resp); err != nil {
		return fmt.Errorf("follow error: %w", err)
	}
	return nil
}

type UnfollowUserRequest struct {
	UserId string `json:"userId"`
}

func (g *userAct) UnFollow(toUserId string) error {
	var resp client.APIResp[api.EmptyResp]
	if err := g.hc.Post("user/unfollow", nil, &UnfollowUserRequest{
		UserId: toUserId,
	}, &resp); err != nil {
		return fmt.Errorf("unfollow error: %w", err)
	}
	return nil
}
