package room

import (
	"fmt"
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/cmd/mock/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
)

func NewGAPI(httpAddr string) (*GAPI, error) {
	hc, err := client.NewHTTP(httpAddr)
	if err != nil {
		return nil, fmt.Errorf("new http client error: %w", err)
	}
	return &GAPI{hc: hc, rooms: cc.New[int, []types.Room](100, cc.LRU, cc.LoaderFunc(func(page int) ([]types.Room, error) {
		var resp client.APIResp[types.FeedListResponse]
		if err := hc.Get("feed/list", map[string]any{"page": page}, &resp); err != nil {
			return nil, fmt.Errorf("get feed list error: %w", err)
		}
		return resp.Data.List, nil
	}), cc.Expiration(5*time.Second))}, nil
}

type GAPI struct {
	hc    *client.HTTP
	rooms cc.Cache[int, []types.Room]
}

func (g *GAPI) GetFeedList() ([]types.Room, error) {
	return g.rooms.Get(1)
}
