package client

import "errors"

type APIResp[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

func (r *APIResp[T]) GetCode() int {
	return r.Code
}

func (r *APIResp[T]) GetMsg() string {
	return r.Msg
}

type APIError struct {
	error
	Code int
	Msg  string
}

func newAPIError(resp GenericResp, err error) *APIError {
	return &APIError{
		error: err,
		Code:  resp.GetCode(),
		Msg:   resp.GetMsg(),
	}
}

func IsApiError(err error, code int) bool {
	var apiErr *APIError
	return errors.As(err, &apiErr) && apiErr.Code == code
}
