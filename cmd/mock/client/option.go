package client

import (
	"go.uber.org/zap"
)

func newOptions() *options {
	return &options{
		codec:  JsonCodec,
		logger: zap.L(),
	}
}

type options struct {
	codec      Codec
	logger     *zap.Logger
	evListener evListener
}

type Option func(*options)

func WithCodec(c Codec) Option {
	return func(o *options) {
		o.codec = c
	}
}

func WithLogger(logger *zap.Logger) Option {
	return func(o *options) {
		o.logger = logger
	}
}

type evListener func([]byte)

func WithEvListener(evListener evListener) Option {
	return func(o *options) {
		o.evListener = evListener
	}
}
