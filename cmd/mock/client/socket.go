package client

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"gitlab.sskjz.com/go/co"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

func NewSocket(target string, opts ...Option) (*WS, error) {
	opt := newOptions()
	for _, o := range opts {
		o(opt)
	}

	ws := &WS{
		url:      target,
		opt:      opt,
		log:      opt.logger,
		evl:      opt.evListener,
		codec:    opt.codec,
		coEv:     co.Apply(),
		sendQ:    make(chan []byte, 100),
		stopCh:   make(chan struct{}),
		inFlight: make(map[uint32]func([]byte)),
	}

	return ws, ws.reconnect()
}

type WS struct {
	url    string
	conn   atomic.Pointer[websocket.Conn]
	opt    *options
	log    *zap.Logger
	evl    evListener
	codec  Codec
	coEv   co.Pool
	sendQ  chan []byte
	wg     sync.WaitGroup
	stopCh chan struct{}
	closed atomic.Bool

	inFlight     map[uint32]func([]byte)
	inFlightLock sync.Mutex

	connecting bool
	rcLock     sync.Mutex

	seq atomic.Uint32
}

func (w *WS) reconnect() error {
	w.wg.Wait()

	w.rcLock.Lock()
	defer w.rcLock.Unlock()
	if w.connecting || w.closed.Load() {
		return nil
	}

	w.connecting = true
	defer func() { w.connecting = false }()

	if conn := w.conn.Load(); conn != nil {
		w.conn.Store(nil)
		_ = conn.Close()
	}

	conn, _, err := (&websocket.Dialer{EnableCompression: true}).Dial(w.url, nil)
	if err != nil {
		time.AfterFunc(time.Second, func() {
			_ = w.reconnect()
		})

		return fmt.Errorf("ws dial fail: %w", err)
	}

	w.conn.Store(conn)

	w.wg.Add(3)
	go w.readLoop()
	go w.writeLoop()
	go w.pingLoop()

	return nil
}

func (w *WS) readLoop() {
	defer w.wg.Done()
	for !w.closed.Load() {
		conn := w.conn.Load()
		if conn == nil {
			return
		}

		_, bs, err := conn.ReadMessage()
		if err != nil {
			go w.checkError(err)
			return
		}

		if len(bs) >= 16 {
			p, err := protocol.DecodePacket(bs)
			if err != nil {
				w.log.Error("decode packet failed", zap.Error(err), zap.ByteString("data", bs))
				continue
			}

			if p.Flags&protocol.FlagAck != 0 {
				w.sendQ <- bs[:16]
			}

			w.inFlightLock.Lock()
			if fn, ok := w.inFlight[p.Sequence]; ok {
				delete(w.inFlight, p.Sequence)
				w.inFlightLock.Unlock()
				fn(bs)
				continue
			}
			w.inFlightLock.Unlock()
		}

		if w.evl != nil {
			w.coEv.Submit(func() { w.evl(bs) })
		}
	}
}

func (w *WS) writeLoop() {
	defer w.wg.Done()

	for !w.closed.Load() {
		select {
		case <-w.stopCh:
			return
		case bs := <-w.sendQ:
			conn := w.conn.Load()
			if conn == nil {
				return
			}

			if err := conn.WriteMessage(websocket.BinaryMessage, bs); err != nil {
				go w.checkError(err)
				return
			}
		}
	}
}

func (w *WS) pingLoop() {
	defer w.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	for !w.closed.Load() {
		select {
		case <-w.stopCh:
			return
		case <-ticker.C:
			conn := w.conn.Load()
			if conn == nil {
				return
			}

			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				go w.checkError(err)
				return
			}
		}
	}
}

func (w *WS) checkError(_ error) {
	if w.closed.Load() {
		return
	}

	go func() {
		if err := w.reconnect(); err != nil {
			w.log.Warn("reconnect failed", zap.Error(err))
		}
	}()
}

func (w *WS) Close() {
	w.closed.Store(true)
	close(w.stopCh)

	if conn := w.conn.Load(); conn != nil {
		_ = conn.Close()
	}
}

func (w *WS) Send(msg any) error {
	bs, err := w.codec.Marshal(msg)
	if err != nil {
		return err
	}
	w.sendQ <- bs
	return nil
}

func (w *WS) RPC(mt protocol.MsgType, data any, resp any) error {
	packet := &protocol.Packet{
		Length:   16,
		MsgType:  uint32(mt),
		Sequence: w.seq.Inc(),
	}

	if data != nil {
		bs, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("rpc: marshal data failed: %w", err)
		}

		packet.Payload = bs
		packet.Length = 16 + uint32(len(bs))
	}

	sig := make(chan struct{})
	w.inFlightLock.Lock()
	w.inFlight[packet.Sequence] = func(bs []byte) {
		p, err := protocol.DecodePacket(bs)
		if err != nil {
			w.log.Error("rpc: decode packet failed", zap.Error(err))
			return
		}

		if resp != nil {
			_ = json.Unmarshal(p.Payload, resp)
		}
		close(sig)
	}
	w.inFlightLock.Unlock()

	bs := packet.Encode()
	w.sendQ <- bs

	select {
	case <-sig:
		return nil
	case <-time.After(time.Second * 5):
		return fmt.Errorf("rpc: call %s timeout", mt.String())
	}
}
