package client

import (
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

func NewHTTP(endpoint string, opts ...Option) (*HTTP, error) {
	opt := newOptions()
	for _, o := range opts {
		o(opt)
	}

	d := net.Dialer{
		Timeout:   15 * time.Second,
		KeepAlive: 30 * time.Second,
	}
	cli := resty.NewWithClient(&http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			DialContext:         d.<PERSON>al<PERSON>ontext,
			IdleConnTimeout:     90 * time.Second,
			MaxIdleConns:        1,
			MaxIdleConnsPerHost: 1,
		},
	}).SetBaseURL(endpoint)

	cli.SetTimeout(time.Second * 15)

	cli.SetHeader("X-App-Id", "mock")

	return &HTTP{
		cli:   cli,
		log:   opt.logger,
		codec: opt.codec,
	}, nil
}

type GenericResp interface {
	GetCode() int
	GetMsg() string
}

type HTTP struct {
	cli    *resty.Client
	log    *zap.Logger
	token  string
	header map[string]string
	codec  Codec
}

func (h *HTTP) SetToken(str string) {
	h.token = str
}

func (h *HTTP) SetHeaders(kvs map[string]string) {
	h.header = kvs
}

func (h *HTTP) Get(path string, params map[string]any, bind any) error {
	return h.call(http.MethodGet, path, params, nil, bind)
}

func (h *HTTP) Post(path string, params map[string]any, data any, bind any) error {
	return h.call(http.MethodPost, path, params, data, bind)
}

func (h *HTTP) call(method string, path string, params map[string]any, data any, bind any) error {
	req := h.cli.R().
		SetHeader("Authorization", "Bearer "+h.token).
		SetHeader("Content-Type", "application/json").
		SetHeaders(h.header).
		ExpectContentType("application/json")

	if len(params) > 0 {
		p2 := make(map[string]string, len(params))
		for k, v := range params {
			p2[k] = fmt.Sprint(v)
		}
		req.SetQueryParams(p2)
	}

	if data != nil {
		if bs, err := h.codec.Marshal(data); err != nil {
			return fmt.Errorf("req marshal failed: %w", err)
		} else {
			req.SetBody(bs)
		}
	}

	resp, err := req.Execute(method, path)
	if err != nil {
		return fmt.Errorf("http get fail: %w", err)
	}

	if resp.StatusCode()/100 != 2 {
		return fmt.Errorf("http error: %s", resp.Status())
	}

	if err := h.codec.Unmarshal(resp.Body(), bind); err != nil {
		return fmt.Errorf("resp unmarshal fail: %w", err)
	}

	if v, is := bind.(GenericResp); is {
		if v.GetCode() != 0 {
			return newAPIError(v, fmt.Errorf("api error: %s", v.GetMsg()))
		}
	}

	return nil
}
